<?php

return [

    //medical devices
    "medical_devices_returned" => "Medical devices returned successfully",
    "medical_devices_created" => "Medical devices created successfully",
    "medical_devices_updated" => "Medical devices updated successfully",
    "medical_devices_deleted" => "Medical devices deleted successfully",
    "template_questions_returned_successfully" => "Template questions returned successfully",
    "template_questions_created_successfully" => "Template questions created successfully",
    "template_questions_updated_successfully" => "Template questions updated successfully",
    "template_questions_deleted_successfully" => "Template questions deleted successfully",


    //company document
    'company_documents_updated' => 'Company documents updated successfully',
    'company_documents_created' => 'Company documents created successfully',
    'company_documents_returned' => 'Company documents returned successfully',
    "company_documents_logs_returned" => "Company documents logs returned successfully",
    "company_documents_inactivated" => "Company documents inactivated successfully",
    "company_documents_restored" => "Company documents restored successfully",
    "company_documents_restoration_failed" => "Company documents restoration failed",
    "does_not_exists_any_company_document" => "Does not exists any company document with the specified identification",

    'mail' => [
        'company_data' => 'Company data',
        "management_plan" => "This company has subscribed to a management plan.",
        "details_of_the_company_data" => "Below are the details of the company data we have submitted for verification:",
    ],

    "medical_devices_restored" => "Medical devices restored successfully",
    "medical_devices_restoration_failed" => "Medical devices restoration failed",
    "does_not_exists_any_medical_device" => "Does not exists any medical device with the specified identification",

    "medical_device_maintenances_returned" => "Medical device maintenances returned successfully",
    "medical_device_maintenance_created" => "Medical device maintenance created successfully",
    'unable_to_access' => 'Unable to access',
    "medical_devices_logs_returned" => "Medical devices logs returned successfully",
];
