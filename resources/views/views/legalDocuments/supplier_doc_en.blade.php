<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SUPPLIER AGREEMENT</title>
    <style>
        @page {
            margin: 20mm;
        }

        body {
            font-family: sans-serif;
            color: #000;
            font-size: 15px;
            line-height: 1.7;
        }

        h1 {
            color: #4B0082;
            font-weight: bold;
            font-size: 22px;
            margin-top: 40px;
            margin-bottom: 5px;
        }

        h2 {
            color: #4B0082;
            font-weight: bold;
            font-size: 17px;
            margin-top: 30px;
        }

        .section {
            margin-bottom: 25px;
        }

        .title-line {
            border: none;
            border-top: 1px solid #999;
            margin: 10px 0 30px;
        }

        .signature {
            margin-top: 50px;
        }

        ul {
            list-style-type: disc;
            margin-left: 20px;
            padding-left: 0;
        }

        ul li {
            margin-bottom: 5px;
        }
    </style>
</head>

<body>

    <h1>SUPPLIER AGREEMENT</h1>

    <div class="section">
        <p><strong>Between:</strong></p>
        <p><strong>Supplier:</strong> MERIDIQ AB<br>
            Address: Fornbyvägen 37, 174 41 Sundbyberg, Sweden<br>
            Email: <EMAIL><br>
            (hereinafter referred to as "the Supplier")</p>

        <p><strong>and</strong></p>
        <p><strong>Customer:</strong> {{ $data['company_name'] }}<br>
            Address: {{ $data['company_address'] }}<br>
            Email: {{ $data['company_email'] }}<br>
            (hereinafter referred to as "the Customer")</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>1. Purpose and Scope of the Agreement</h2>
        <p><strong>1.1 Purpose:</strong> This agreement governs the terms for the Supplier’s provision of a SAAS-based
            service for record documentation (hereinafter "the Service") to the Customer.</p>
        <p><strong>1.2 Service Functionality:</strong> The Service enables the Customer to store, process, and manage
            record information in compliance with applicable laws and regulations, including but not limited to patient
            information, logging, and reporting.</p>
        <p><strong>1.3 License:</strong> The Supplier grants the Customer a non-exclusive, non-transferable right to use
            the Service during the agreement period for the purposes defined in the agreement.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>2. Provision of the Service and Support</h2>
        <p><strong>2.1 Availability:</strong> The Supplier shall make every reasonable effort to ensure that the
            platform is available to the Customer but cannot guarantee uninterrupted availability.</p>
        <p><strong>2.2 Support:</strong> The Supplier is responsible for ensuring that all technical support and
            maintenance are provided with the best possible effort.</p>
        <p><strong>2.3 Updates and Maintenance:</strong> The Supplier reserves the right to update and improve the
            platform during the agreement period, including implementing changes to enhance security and functionality,
            provided that such changes do not materially affect the Customer’s use of the platform.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>3. Customer Responsibilities</h2>
        <p><strong>3.1 Use of the Service:</strong> The Customer shall use the Service in accordance with the
            instructions and guidance provided by the Supplier. The Customer may not use the Service for purposes other
            than those expressly stated in this agreement.</p>
        <p><strong>3.2 Customer Security:</strong> The Customer is responsible for maintaining the security of its user
            accounts, passwords, and other authentication measures. The Customer shall immediately inform the Supplier
            of any security breaches or suspected intrusions.</p>
        <p><strong>3.3 Regulations and Compliance:</strong> The Customer must ensure that the use of the Service
            complies with applicable legislation, including but not limited to data protection regulations such as GDPR
            or local healthcare regulations (if applicable).</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>4. Security and Data Protection</h2>
        <p><strong>4.1 Data Security:</strong> The Supplier undertakes to implement all reasonable technical and
            organizational measures to protect the data handled within the framework of the Service, including but not
            limited to encryption, firewalls, and regular security audits.</p>
        <p><strong>4.2 Data Protection:</strong> The Supplier and the Customer shall enter into a separate Data
            Processing Agreement (DPA) for the processing of personal data. The Supplier shall process personal data
            only on the Customer's behalf and in compliance with GDPR.</p>
        <p><strong>4.3 Data Access:</strong> The Supplier shall ensure that access to the data stored in the Service is
            limited to authorized personnel and in accordance with agreed access levels.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>5. Disaster Recovery and Restoration</h2>
        <p><strong>5.1 Disaster Plan:</strong> The Supplier shall have a documented disaster recovery plan to handle
            serious incidents affecting the operation of the Service. The plan shall include measures to ensure service
            continuity and the management of incidents such as system crashes, data breaches, or natural disasters.</p>
        <p><strong>5.2 Data Restoration:</strong> The Supplier shall ensure that routines are in place for regular
            backups of all relevant data. In the event of a disaster, data restoration shall occur as soon as possible
            after the incident, and the Supplier shall inform the Customer about the status of the restoration.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>6. Incident Management and Data Breaches</h2>
        <p><strong>6.1 Reporting Security Incidents:</strong> The Supplier shall immediately inform the Customer of any
            security incidents that may affect personal data or service availability. This includes but is not limited
            to data breaches, system failures, or unauthorized access.</p>
        <p><strong>6.2 Measures for Breaches:</strong> In the event of a data breach affecting personal data, the
            Supplier shall promptly take actions to address the breach, restore integrity, and provide relevant
            information to facilitate any notifications to supervisory authorities and data subjects, as required under
            GDPR.</p>
        <p><strong>6.3 Follow-Up and Reporting:</strong> The Supplier shall document all handling of security incidents
            and make this documentation available to the Customer as needed.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>7. Liability and Warranties</h2>
        <p><strong>7.1 Service Liability:</strong> The Supplier is responsible for providing the Service in accordance
            with the features and guarantees specified in the agreement. The Supplier guarantees that the Service will
            not contain serious defects that impact the Customer’s use.</p>
        <p><strong>7.2 Indirect Damages:</strong> The Supplier is not liable for indirect damages, lost profits, lost
            data, or other consequential damages arising from the use of the Service, except in cases of gross
            negligence or willful misconduct.</p>
        <p><strong>7.3 Warranty Period:</strong> Any defects discovered within 1 month of delivery shall only be
            remedied if not caused by the Customer’s use or handling of the Service, and the costs of such remedies
            shall be borne by the Customer.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>8. Agreement Term and Termination</h2>
        <p><strong>8.1 Agreement Term:</strong> This agreement is valid from registration date and continues
            indefinitely, with an initial binding period of 1 month for monthly subscriptions and 12 months for annual
            subscriptions.</p>
        <p><strong>8.2 Termination of the Agreement:</strong> Both parties may terminate the agreement with 1 month’s
            written notice for monthly subscriptions and 3 months for annual subscriptions.</p>
        <p><strong>8.3 Termination for Breach:</strong> If either party breaches essential terms of the agreement, the
            other party may terminate the agreement immediately with immediate effect.</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>9. Dispute Resolution and Governing Law</h2>
        <p><strong>9.1 Governing Law:</strong> This agreement is governed by Swedish law.</p>
        <p><strong>9.2 Dispute Resolution:</strong> Any disputes arising from this agreement shall, if negotiations do
            not lead to a resolution, be settled through arbitration under the rules of the [Stockholm Chamber of
            Commerce Arbitration Institute (SCC)].</p>
    </div>
    <hr class="title-line">
    <div class="section">
        <h2>10. Other Conditions</h2>
        <p><strong>10.1 Force Majeure:</strong> Neither party shall be held responsible for loss or damage caused by
            circumstances beyond their control, such as natural disasters, war, or other force majeure events.</p>
        <p><strong>10.2 Confidentiality:</strong> The parties undertake to keep all confidential information obtained
            through this agreement secret and not disclose it to third parties without prior written consent.</p>
    </div>
    <hr class="title-line">
    <div class="signature">
        <p><strong>Signatures</strong></p>
        <br>
        <p><strong>For the Supplier:</strong></p>
        <p>Name: Rickard Nurlin<br>Date: {{ $data['date_signed'] }}</p>

        <p><strong>For the Customer:</strong></p>
        <p>Name: {{ $data['superuser_name'] }}<br>Date: {{ $data['date_signed'] }}</p>
    </div>
    <hr class="title-line">
</body>

</html>
