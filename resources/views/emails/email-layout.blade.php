<!DOCTYPE html>
<html lang="{{ $lang ?? 'en' }}" xmlns:v="urn:schemas-microsoft-com:vml" style="font-family: Inter, sans-serif; font-feature-settings: 'liga' 1, 'calt' 1;">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <title>{{ $title ?? '' }}</title>
  <link rel="preconnect" href="https://rsms.me/">
  <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
  <style>
    :root {
      font-family: Inter, sans-serif;
      font-feature-settings: 'liga' 1, 'calt' 1;
    }
    @supports (font-variation-settings: normal) {
      :root {
        font-family: InterVariable, sans-serif;
      }
    }
    @media (max-width: 768px) {
      .md-ml-0 {
        margin-left: 0 !important
      }
      .md-table {
        display: table !important
      }
      .md-table-row {
        display: table-row !important
      }
      .md-pb-5 {
        padding-bottom: 20px !important
      }
      .md-pt-5 {
        padding-top: 20px !important
      }
      .md-text-center {
        text-align: center !important
      }
    }
  </style>
</head>
<body style="margin: 0; width: 100%; background-color: #f8fafc; padding: 0; -webkit-font-smoothing: antialiased; word-break: break-word">
  @if($preheader ?? null)
  <div style="display: none">
    {{ $preheader ?? '' }}
    &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847; &#8199;&#65279;&#847;
  </div>
  @endif
  <div role="article" aria-roledescription="email" aria-label="{{ $title ?? '' }}" lang="{{ $lang ?? 'en' }}">
    <div style="background-color: #fffffe; color: #1A1B1C">
      <table align="center" cellpadding="0" cellspacing="0" role="none">
        <tr>
          <td style="width: 600px; max-width: 100%">
            <div style="margin-top: 48px; margin-bottom: 48px; overflow: hidden; border-radius: 16px; border: 4px solid #5551ce">
              <table style="width: 100%; table-layout: fixed" cellpadding="0" cellspacing="0" role="none">
                <tr>
                  <td colspan="2">
                    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0" style="padding-left: 32px; padding-right: 32px">
                      <tr class="md-table" style="width: 100%">
                        <td class="md-table-row" style="width: 100%; text-align: left">
                          <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
                            <tr class="md-table" style="width: 100%">
                              @if(isset($logo) && $logo)
                              <td style="text-align: left;">
                                <div class="md-pb-5" style="margin: 0; padding-top: 28px; padding-bottom: 28px">
                                  <img src="{{ urldecode($logo) }}" onerror="this.closest('td').remove()" style="vertical-align: middle; margin-right: 16px; display: inline; max-height: 56px; max-width: 56px" alt="">
                                </div>
                              </td>
                              @endif
                              <td style="width: 100%; text-align: left;">
                                <div class="md-pb-5" style="margin: 0; padding-top: 28px; padding-bottom: 28px">
                                  <p style="display: inline; vertical-align: middle; font-size: 24px; font-weight: 900; color: #000001">{{ $company_name ?? "" }}</p>
                                </div>
                              </td>
                            </tr>
                          </table>
                        </td>
                        @if(isset($video_call_url) && $video_call_url)
                        <td class="md-text-center md-table-row" style="width: 100%; text-align: right">
                          <div class="md-ml-0 md-pt-5" style="margin-left: 32px; padding-top: 28px; padding-bottom: 28px">
                            <a target="_blank" href="{{ $video_call_url }}" style="display: inline-block; cursor: pointer; outline: 2px solid transparent; outline-offset: 2px; text-decoration: none">
                              <button style="display: block; cursor: pointer; user-select: none; white-space: nowrap; border-radius: 8px; border-width: 1px; border-color: transparent; background-color: #5551CE; padding: 8px 16px; font-size: 14px; font-weight: 500; color: #fffffe; outline: 2px solid transparent; outline-offset: 2px; text-decoration: none">
                                {{ __('strings.join_video_call') }}
                              </button>
                            </a>
                          </div>
                        </td>
                        @endif
                      </tr>
                    </table>
                  </td>
                </tr>
                <tr role="separator">
                  <td colspan="2">
                    <div role="separator" style="height: 1px; line-height: 1px; margin: 0px 32px; background-color: #E9EAFF">&zwj;</div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" style="border-radius: 4px; padding: 32px; font-size: 16px">
                    @if(!isset($showIntro) || $showIntro === null || $showIntro === true)
                    <h1 style="margin: 0 0 24px; font-size: 18px; font-weight: 500; color: #1A1B1C">
                      {{ __('strings.hello') }} {{ $name ?? '' }},
                    </h1>
                    @endif
                    <div style="color: #1A1B1C;"> @yield('content',"")
                      @if (isset($should_show_url) && $should_show_url)
                      <div style="text-align: center;">
                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
                          <tr>
                            <td align="center" style="padding: 10px;">
                              <a target="_blank" href="{{ $add_calendar_url }}" style="color: #1a1b1c; font-size: 14px; text-decoration: underline">
                                {{ __('strings.generic_booking_mail_add_calendar') }}
                              </a>
                            </td>
                            <td align="center" style="padding: 10px;">
                              <a target="_blank" href="{{ $cancel_url }}" style="color: #1a1b1c; font-size: 14px; text-decoration: underline;">
                                {{ __('strings.generic_booking_cancel_booking') }}
                              </a>
                            </td>
                            @if (!$group_booking)
                            <td align="center" style="padding: 10px;">
                              <a target="_blank" href="{{ $modify_url }}" style="color: #1a1b1c; font-size: 14px; text-decoration: underline;">
                                {{ __('strings.generic_booking_modify_booking') }}
                              </a>
                            </td>
                            @endif
                          </tr>
                        </table>
                      </div>
                      @endif
                    </div>
                    @if(!isset($showOutro) || $showOutro === null || $showOutro === true)
                    <p style="margin-top: 32px; text-align: start; color: #1A1B1C">
                      <span style="display: block; font-weight: 400">{{ __('strings.best_regards') }},</span>
                      <span style="margin-top: 4px; display: block; font-weight: 700; color: #333333">{{ $company_name ?? '' }} Team</span>
                    </p>
                    @endif
                  </td>
                </tr>
                <tr role="separator">
                  <td colspan="2">
                    <div role="separator" style="height: 1px; line-height: 1px; margin: 0px 32px; background-color: #E9EAFF;">&zwj;</div>
                  </td>
                </tr>
                <tr style="font-size: 16px;">
                  <td colspan="2" style="padding: 24px; text-align: center; font-size: 12px">
                    <p style="margin: 0; font-style: italic">
                      {{ __('strings.email_data.you_are_receiving_email_beacause',['company_name' => $company_name ?? '']) }}
                    </p>
                    <p style="margin: 0; font-style: italic;">
                      {{ __('strings.email_data.copyright_year',['year' => date('Y')]) }}
                    </p>
                  </td>
                </tr>
              </table>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>