@component('mail::message')
# Rate Limit Reached Alert

A rate limit has been hit for the first time.

@if ($request->user())
A user with Company Email **{{ $request->user()?->company?->email }}** has hit the custom throttle limit.
@else
A user with IP address **{{ $request->ip() }}** has hit the custom throttle limit.
@endif

@if ($request->user())
**User:** {{ $request->user()?->email }}
@endif

@if ($request->user())
**Company:** {{ $request->user()?->company?->email }}
@endif

**IP:** {{ $request->ip() }}

**Request URL:** {{ $request->url() }}

**Time:** {{ now() }}
@endcomponent
