<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Subscription Cancellation</title>
    <style>
        h2 {
            margin-bottom: 10px;
            text-decoration: underline;
        }

        body {
            font-family: sans-serif;
        }

        .spacediv {
            height: 20px;
            width: 100%;
        }

        .spacediv.small {
            height: 10px;
            width: 100%;
        }

        .tal {
            text-align: left;
        }

        table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .tr_treatment.images,
        .tr_body {
            border-bottom: solid 20px transparent;
            box-shadow: 0px 1px 1px #eee;
        }

        table.data_table th,
        table.data_table td {
            padding: 6px;
        }

        table.data_table th {
            background: #eee;
            padding-top: 12px;
            padding-bottom: 12px;
        }

        table td,
        table th {
            text-align: left;
        }

        .tac {
            text-align: center;
        }

        .w100p {
            width: 100%;
        }

        body {
            color: #232323;
        }

        .text-gold {
            color: #BF9663;
        }

        .small-p {
            font-size: 13px;
        }

        .mid-p {
            font-size: 14px;
        }

        .p-18 {
            font-size: 18px;
        }

        .big-p {
            font-size: 20px;
        }

        .mt-0 {
            margin-top: 0px;
        }

        .mt-n5 {
            margin-top: -8px;
        }

        .grid4Table p {
            margin-bottom: 0px;
            font-weight: bold;
        }

        .grid4Table tr {
            border-top: solid 12px transparent;
        }

        .page-break {
            page-break-inside: avoid;
        }
    </style>
</head>
@php
    use App\Setting;
    use Carbon\Carbon;
@endphp

<body style="margin: 15px;">
    <table>
        <tr>
            <td>
                <h2>{{ __('strings.company_detail') }}</h2>
            </td>
        </tr>
    </table>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.Name') }}</th>
            <th colspan="1">{{ __('strings.sign_up_mobile_number') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $company->company_name }}</p>
            </td>
            <td colspan="1">
                <p>{{ $company->mobile_number }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
        <tr>
            <th colspan="1">{{ __('strings.Email') }}</th>
            <th colspan="1">{{ __('strings.Country') }}</th>
        </tr>
        <tr>
            <td colspan="1">
                <p>{{ $company->email }}</p>
            </td>
            <td colspan="1">
                <p>{{ $company->country }}</p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <td>
                <h2>{{ __('strings.plan_details') }}</h2>
            </td>
        </tr>
    </table>
    <div class="spacediv"></div>
    <table>
        <tr>
            <th colspan="1">{{ __('strings.plans') }}</th>
            <th colspan="1">{{ __('strings.Users') }}</th>
            <th colspan="1">{{ __('strings.price') }}</th>
            <th colspan="1">{{ __('strings.plan_ends_at') }}</th>
        </tr>
        @foreach ($cancellations as $cancellation)
        <tr>
            <td colspan="1">
                <p>{{ $cancellation['name'] }}</p>
            </td>
            <td colspan="1">
                <p>{{ $cancellation['quantity'] }}</p>
            </td>
            <td colspan="1">
                @if ($cancellation['is_free_trial'])
                    <p>{{ __('strings.free_trail') }}</p>
                @else
                    @php
                        $plan = isset($cancellation['item']['plan']) ? $cancellation['item']['plan'] : null;
                    @endphp
                    <p>{{ $plan->currency }} {{ $plan->price * $cancellation['quantity'] }} /
                        {{ $plan->is_yearly ? __('strings.year') : __('strings.month') }}</p>
                @endif
            </td>
            <td colspan="1">
                <p>{{ Setting::formateDate($company, Carbon::parse($cancellation['canceled_at'])) }}</p>
            </td>
        </tr>
        @endforeach
        <tr>
            <td>
                <div class="spacediv small"></div>
            </td>
        </tr>
    </table>
</body>

</html>
