(function(e){function t(t){for(var a,r,o=t[0],l=t[1],c=t[2],d=0,h=[];d<o.length;d++)r=o[d],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&h.push(i[r][0]),i[r]=0;for(a in l)Object.prototype.hasOwnProperty.call(l,a)&&(e[a]=l[a]);u&&u(t);while(h.length)h.shift()();return n.push.apply(n,c||[]),s()}function s(){for(var e,t=0;t<n.length;t++){for(var s=n[t],a=!0,o=1;o<s.length;o++){var l=s[o];0!==i[l]&&(a=!1)}a&&(n.splice(t--,1),e=r(r.s=s[0]))}return e}var a={},i={app:0},n=[];function r(t){if(a[t])return a[t].exports;var s=a[t]={i:t,l:!1,exports:{}};return e[t].call(s.exports,s,s.exports,r),s.l=!0,s.exports}r.m=e,r.c=a,r.d=function(e,t,s){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:s})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var s=Object.create(null);if(r.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(s,a,function(t){return e[t]}.bind(null,a));return s},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],l=o.push.bind(o);o.push=t,o=o.slice();for(var c=0;c<o.length;c++)t(o[c]);var u=l;n.push([0,"chunk-vendors"]),s()})({0:function(e,t,s){e.exports=s("56d7")},"0340":function(e,t,s){},"0774":function(e,t,s){"use strict";s("3413")},"0877":function(e,t,s){"use strict";s("68f4")},"0935":function(e,t,s){},"09a0":function(e,t,s){},"0f8a":function(e,t,s){"use strict";s("c677")},"101e":function(e,t,s){"use strict";s("3191")},"105c":function(e,t,s){},1746:function(e,t,s){"use strict";s("e01a5")},"1bca":function(e,t,s){},"1c62":function(e,t,s){"use strict";s("3898")},2081:function(e,t,s){"use strict";s("82a0")},"23b2":function(e,t,s){},"288a":function(e,t,s){"use strict";s("d0e3")},"28e9":function(e,t,s){"use strict";s("f939")},2958:function(e,t,s){"use strict";s("0935")},"2fa3":function(e,t,s){},"313b":function(e,t,s){"use strict";s("9fca")},3191:function(e,t,s){},"334f":function(e,t,s){},"337f":function(e,t,s){"use strict";s("85ae")},3413:function(e,t,s){},3898:function(e,t,s){},"39de":function(e,t,s){},"3c48":function(e,t,s){},"3df1":function(e,t,s){},"3f73":function(e,t,s){},5518:function(e,t,s){"use strict";s("5773")},"56d7":function(e,t,s){"use strict";s.r(t);var a=s("3835"),i=(s("e260"),s("e6cf"),s("cca6"),s("a79d"),s("4fad"),function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"application split-view",class:e.appearance},[s("requests-list",{directives:[{name:"show",rawName:"v-show",value:e.showRequestsList,expression:"showRequestsList"}]}),s("request-details"),s("request-sidebar",{directives:[{name:"show",rawName:"v-show",value:e.showRequestSidebar,expression:"showRequestSidebar"}]}),s("whats-new")],1)}),n=[],r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"split-view-pane split-view-details popover-viewport"},[s("div",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("tab-bar"),expression:"$platform.hasFeature('tab-bar')"}],staticClass:"details-header"},[s("div",{staticClass:"icons"},[s("a",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("requests-list"),expression:"$platform.hasFeature('requests-list')"}],attrs:{href:"#",title:"Toggle requests"},on:{click:function(t){return t.preventDefault(),e.toggleRequestsList.apply(null,arguments)}}},[s("icon",{attrs:{name:e.$settings.global.requestsListCollapsed?"chevron-right":"chevron-left"}})],1)]),s("tab-bar",{attrs:{tabs:e.tabs,"active-tab":e.activeTab},on:{"tab-selected":e.showTab}}),s("div",{staticClass:"icons"},[s("a",{class:{active:e.$settings.shown},attrs:{href:"#",title:"Settings"},on:{click:function(t){return t.preventDefault(),e.toggleSettingsModal.apply(null,arguments)}}},[s("icon",{attrs:{name:"settings"}})],1),s("a",{attrs:{href:"#",title:"Toggle sidebar"},on:{click:function(t){return t.preventDefault(),e.toggleRequestSidebar.apply(null,arguments)}}},[s("icon",{attrs:{name:e.$settings.global.requestSidebarCollapsed?"chevron-left":"chevron-right"}})],1)])],1),!e.$request||e.$request.loading||e.$request.error?e._e():s("div",{staticClass:"details-content"},[s("div",{staticClass:"content-header"}),e.$platform.hasFeature("details-request")?s("details-request"):e._e(),s("div",{staticClass:"content-content"},[e.shownTabs.events?s("events-tab",{attrs:{active:"events"==e.activeTab}}):e._e(),e.shownTabs.models?s("models-tab",{attrs:{active:"models"==e.activeTab}}):e._e(),e.shownTabs.database?s("database-tab",{attrs:{active:"database"==e.activeTab}}):e._e(),e.shownTabs.cache?s("cache-tab",{attrs:{active:"cache"==e.activeTab}}):e._e(),e.shownTabs.redis?s("redis-tab",{attrs:{active:"redis"==e.activeTab}}):e._e(),e.shownTabs.queue?s("queue-tab",{attrs:{active:"queue"==e.activeTab}}):e._e(),e.shownTabs.log?s("log-tab",{attrs:{active:"log"==e.activeTab}}):e._e(),s("performance-tab",{attrs:{active:"performance"==e.activeTab}}),e.shownTabs.views?s("views-tab",{attrs:{active:"views"==e.activeTab}}):e._e(),e.shownTabs.notifications?s("notifications-tab",{attrs:{active:"notifications"==e.activeTab}}):e._e(),e.shownTabs.routes?s("routes-tab",{attrs:{active:"routes"==e.activeTab}}):e._e(),e._l(e.$get(e.$request,"userData"),(function(t,a){return s("user-tab",{key:e.$request.id+"-"+a,attrs:{"user-tab":t,active:e.activeTab=="user-"+t.key}})})),e.shownTabs.output?s("output-tab",{attrs:{active:"output"==e.activeTab}}):e._e()],2)],1),s("div",{directives:[{name:"show",rawName:"v-show",value:e.$get(e.$request,"loading")&&!e.$authentication.shown,expression:"$get($request, 'loading') && ! $authentication.shown"}],staticClass:"details-loading-overlay"},[s("spinner",{attrs:{name:"fading-circle",color:"dark"==e.$settings.appearance?"#f27e02":"#258cdb"}})],1),s("div",{directives:[{name:"show",rawName:"v-show",value:e.$get(e.$request,"error")&&"requires-authentication"!=e.$get(e.$request,"error.error"),expression:"$get($request, 'error') && $get($request, 'error.error') != 'requires-authentication'"}],staticClass:"details-error-overlay"},[s("icon",{attrs:{name:"alert-circle"}}),s("p",{staticClass:"title"},[e._v("Error loading request metadata.")]),s("p",{staticClass:"message"},[e._v(e._s(e.$get(e.$request,"error.message")))])],1),s("div",{directives:[{name:"show",rawName:"v-show",value:e.$authentication.shown,expression:"$authentication.shown"}],staticClass:"details-authentication-overlay",class:{failed:e.$authentication.failed}},[s("form",{on:{submit:function(t){return t.preventDefault(),e.$authentication.attempt()}}},[s("icon",{attrs:{name:"lock"}}),s("p",{staticClass:"title"},[e._v("This site requires authentication.")]),s("p",{staticClass:"title failed"},[e._v("Authentication failed.")]),s("p",{directives:[{name:"show",rawName:"v-show",value:e.$authentication.message,expression:"$authentication.message"}],staticClass:"message"},[e._v(e._s(e.$authentication.message))]),s("p",{directives:[{name:"show",rawName:"v-show",value:e.$authentication.requires.includes("username"),expression:"$authentication.requires.includes('username')"}]},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$authentication.username,expression:"$authentication.username"}],attrs:{type:"text",placeholder:"Username"},domProps:{value:e.$authentication.username},on:{input:function(t){t.target.composing||e.$set(e.$authentication,"username",t.target.value)}}})]),s("p",{directives:[{name:"show",rawName:"v-show",value:e.$authentication.requires.includes("password"),expression:"$authentication.requires.includes('password')"}]},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$authentication.password,expression:"$authentication.password"}],attrs:{type:"password",placeholder:"Password"},domProps:{value:e.$authentication.password},on:{input:function(t){t.target.composing||e.$set(e.$authentication,"password",t.target.value)}}})]),e._m(0)],1)]),s("settings-modal"),s("credits-modal"),s("sharing-modal"),s("sharing-delete-modal"),s("messages-overlay")],1)},o=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("p",[s("button",{attrs:{type:"submit"}},[e._v("Submit")])])}],l=(s("4de4"),s("99af"),s("d81d"),function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("modal",{attrs:{icon:"info",title:"Credits",shown:e.$credits.shown},on:{"update:shown":function(t){return e.$set(e.$credits,"shown",t)}}},[s("div",{staticClass:"credits-modal"},[s("h1",[e._v("Clockwork App")]),s("p",{staticClass:"credits-version"},[e._v("Version "+e._s(e.$credits.version))]),s("div",[s("h2",[e._v("Created by")]),s("div",{staticClass:"credits-authors"},e._l(e.$credits.authors,(function(t){return s("div",{staticClass:"authors-author"},[s("div",{staticClass:"author-avatar",style:"background-image:url("+t.avatarUrl+";"}),s("div",[s("div",{staticClass:"author-name"},[e._v(e._s(t.name))]),s("div",{staticClass:"author-links"},[s("a",{attrs:{href:t.twitterUrl,target:"_blank"}},[s("icon",{attrs:{name:"twitter"}})],1),s("a",{attrs:{href:t.githubUrl,target:"_blank"}},[s("icon",{attrs:{name:"github"}})],1),s("a",{attrs:{href:t.sponsorUrl,target:"_blank"}},[s("icon",{attrs:{name:"heart"}})],1)])])])})),0),e.credits.app.contributors.length||e.credits.php.contributors.length?s("div",{staticClass:"credits-contributors"},[e._l(e.credits.app.contributors,(function(e){return s("a",{staticClass:"contributors-contributor",attrs:{href:e.url,target:"_blank",title:e.userName}},[s("div",{staticClass:"contributor-avatar",style:"background-image:url("+e.avatarUrl+";"})])})),e._l(e.credits.php.contributors,(function(e){return s("a",{staticClass:"contributors-contributor",attrs:{href:e.url,target:"_blank",title:e.userName}},[s("div",{staticClass:"contributor-avatar",style:"background-image:url("+e.avatarUrl+";"})])}))],2):e._e()]),e.credits.app.sponsors.length?s("div",[s("h2",[e._v("Sponsored by")]),s("div",{staticClass:"credits-sponsors"},e._l(e.credits.app.sponsors,(function(e){return s("a",{staticClass:"sponsors-sponsor",attrs:{href:e.url,target:"_blank",title:e.userName}},[s("div",{staticClass:"sponsor-avatar",style:"background-image:url("+e.avatarUrl+";"})])})),0)]):e._e(),e.credits.app.dependencies.length||e.credits.php.dependencies.length?s("div",[s("h2",[e._v("Built with")]),s("div",{staticClass:"credits-dependencies"},[s("h3",[e._v("Clockwork App")]),e._l(e.credits.app.dependencies,(function(t){return s("div",{staticClass:"dependencies-dependency"},[s("div",[s("a",{attrs:{href:t.url,target:"_blank"}},[e._v(e._s(t.name))]),e._v(" by "),s("a",{attrs:{href:t.authorUrl,target:"_blank"}},[e._v(e._s(t.author))])]),s("div",{staticClass:"dependency-description"},[e._v(" "+e._s(t.description)+" ")])])})),s("h3",[e._v("Clockwork PHP")]),e._l(e.credits.php.dependencies,(function(t){return s("div",{staticClass:"dependencies-dependency"},[s("div",[s("a",{attrs:{href:t.url,target:"_blank"}},[e._v(e._s(t.name))]),e._v(" by "),s("a",{attrs:{href:t.authorUrl,target:"_blank"}},[e._v(e._s(e._f("join")(t.authors)))])]),s("div",{staticClass:"dependency-description"},[e._v(" "+e._s(t.description)+" ")])])}))],2)]):e._e()])])}),c=[],u=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("transition",{attrs:{name:"modal"}},[s("div",{directives:[{name:"show",rawName:"v-show",value:e.shown,expression:"shown"}],staticClass:"modal-backdrop",on:{click:function(t){return t.target!==t.currentTarget?null:e.close.apply(null,arguments)}}},[s("div",{staticClass:"modal"},[s("div",{staticClass:"modal-header"},[s("div",{staticClass:"header-title"},[e.icon?s("icon",{attrs:{name:e.icon}}):e._e(),e._v(" "+e._s(e.title)+" ")],1),s("a",{staticClass:"header-close",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.close.apply(null,arguments)}}},[s("icon",{attrs:{name:"x"}})],1)]),e._t("default")],2)])])},d=[],h={name:"Modal",props:["icon","shown","title","onClose"],methods:{close:function(){if(this.onClose)return this.onClose();this.$emit("update:shown",!1)}}},v=h,f=(s("7807"),s("2877")),m=Object(f["a"])(v,u,d,!1,null,null,null),p=m.exports,g={name:"CreditsModal",components:{Modal:p},computed:{credits:function(){return this.$credits.credits}}},b=g,w=(s("c419"),Object(f["a"])(b,l,c,!1,null,null,null)),_=w.exports,y=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"content-request"},[s("div",{staticClass:"counters-row"},[s("div",{directives:[{name:"show",rawName:"v-show",value:e.$request.isCommand()||e.$request.isQueueJob()||e.$request.isTest()||e.$request.isAjax(),expression:"$request.isCommand() || $request.isQueueJob() || $request.isTest() || $request.isAjax()"}],staticClass:"counter request-type"},[e.$request.isCommand()?[s("span",{staticClass:"type-text"},[e._v("CMD")])]:e.$request.isQueueJob()?[s("span",{staticClass:"type-text"},[e._v("QUEUE")])]:e.$request.isTest()?[s("span",{staticClass:"type-text"},[e._v("TEST")])]:e.$request.isAjax()?[s("span",{staticClass:"type-text"},[e._v("AJAX")])]:e._e()],2),s("div",{staticClass:"counter request-main"},[e.$request.isCommand()?[e._v(" "+e._s(e.$request.commandName)+" ")]:e.$request.isQueueJob()?[e._v(" "+e._s(e.$request.jobName)+" ")]:e.$request.isTest()?[e._v(" "+e._s(e.$request.testGroup)+" ")]:[s("span",{staticClass:"method-text"},[e._v(e._s(e.$request.method))]),e._v(" "+e._s(e.$request.uri)+" ")]],2),s("div",{staticClass:"counter request-details"},[e.$request.isCommand()?[e._v(" "+e._s(e.$request.commandLine)+" ")]:e.$request.isQueueJob()?[e._v(" "+e._s(e.$request.jobDescription)+" ")]:e.$request.isTest()?[e._v(" "+e._s(e.$request.testName)+" ")]:[e._v(" "+e._s(e.$request.controller)+" ")]],2),s("div",{staticClass:"counters-group right-aligned"},[e.$request.errorsCount||e.$request.warningsCount?s("div",{staticClass:"counter request-alerts"},[e.$request.errorsCount?s("icon",{staticClass:"header-alert alert-errors",attrs:{name:"alert-circle"}}):e.$request.warningsCount?s("icon",{staticClass:"header-alert alert-warnings",attrs:{name:"alert-triangle"}}):e._e()],1):e._e(),s("div",{staticClass:"counter request-status"},[e.$request.isCommand()?[s("span",{class:{"status-text":!0,"client-error":e.$request.isCommandWarning(),"server-error":e.$request.isCommandError()},attrs:{title:e.$request.commandExitCode}},[e._v(e._s(e.$request.commandExitCode))])]:e.$request.isQueueJob()?[s("span",{class:{"status-text":!0,"status-text-small":!0,"client-error":e.$request.isQueueJobWarning(),"server-error":e.$request.isQueueJobError()},attrs:{title:e.$request.jobStatus}},[e._v(e._s(e.$request.jobStatus))])]:e.$request.isTest()?[s("span",{class:{"status-text":!0,"status-text-small":!0,"client-error":e.$request.isTestWarning(),"server-error":e.$request.isTestError()},attrs:{title:e.$request.testStatus}},[e._v(e._s(e.$request.testStatus))])]:[s("span",{class:{"status-text":!0,"client-error":e.$request.isClientError(),"server-error":e.$request.isServerError()},attrs:{title:e.$request.responseStatus}},[e._v(e._s(e.$request.responseStatus))])]],2)])])])},C=[],q={name:"DetailsRequest"},$=q,k=(s("b72e"),Object(f["a"])($,y,C,!1,null,null,null)),x=k.exports,T=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"messages-overlay"},[s("parent-request",{directives:[{name:"show",rawName:"v-show",value:e.$settings.global.requestSidebarCollapsed,expression:"$settings.global.requestSidebarCollapsed"}],attrs:{compact:"true"}}),s("exception-section",{directives:[{name:"show",rawName:"v-show",value:e.$settings.global.requestSidebarCollapsed,expression:"$settings.global.requestSidebarCollapsed"}],attrs:{compact:"true"}}),e.updateNotification?s("div",{staticClass:"update-notification"},[s("icon",{attrs:{name:"info"}}),s("span",[e._v(" A new Clockwork server-side version "),s("strong",[e._v(e._s(e.updateNotification.version))]),e._v(" is available, you are using "),s("strong",[e._v(e._s(e.updateNotification.currentVersion))]),e._v(". "),e.updateNotification.url?s("a",{attrs:{href:e.updateNotification.url,target:"_blank"}},[e._v("Read more")]):e._e()]),s("span",{staticClass:"updateNotification-close"},[s("a",{attrs:{href:"#"},on:{click:e.closeUpdateNotification}},[e._v("Close")])])],1):e._e()],1)},S=[],j=function(){var e=this,t=e.$createElement,s=e._self._c||t;return e.$request&&e.$request.exceptions.length?s("div",{staticClass:"exception-section",class:{compact:e.compact}},e._l(e.$request.exceptions,(function(t,a){return s("div",{key:e.$request.id+"-"+a,staticClass:"exception-info"},[s("div",{staticClass:"exception-message"},[e.compact?s("icon",{attrs:{name:"alert-circle"}}):e._e(),s("h3",[e._v(e._s(t.type)+" "),t.code?s("small",[e._v("#"+e._s(t.code))]):e._e()]),e._v(" "+e._s(t.message)+" ")],1),s("div",[t.previous?s("a",{staticClass:"exception-previous",attrs:{href:"#",title:"Show previous"},on:{click:function(s){return s.preventDefault(),e.showPreviousException(t)}}},[s("icon",{attrs:{name:"arrow-down-circle"}})],1):e._e(),s("stack-trace",{staticClass:"exception-trace",attrs:{trace:t.trace}})],1)])})),0):e._e()},O=[],P=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"stack-trace popover-container"},[e.file||e.trace&&e.trace.length?s("a",{attrs:{href:e._f("editorLink")(e.file||e.trace[0].file,e.line)}},[s("shortened-text",{attrs:{full:e.trace?e.shortPath:e.fullPath},nativeOn:{click:function(t){return e.togglePopover(t)}}},[e._v(e._s(e.shortPath))])],1):e._e(),e.trace?s("popover",{ref:"popover"},e._l(e.trace,(function(t){return s("div",{staticClass:"stack-frame",class:{"is-vendor":t.isVendor}},[s("div",{staticClass:"stack-frame-call"},[e._v(e._s(t.call))]),s("div",{staticClass:"stack-frame-file"},[s("a",{attrs:{href:e._f("editorLink")(t.file,t.line)}},[s("shortened-text",{attrs:{full:e.makeFullPath(t.file,t.line)}},[e._v(" "+e._s(e.makeShortPath(t.file,t.line))+" ")])],1)])])})),0):e._e()],1)},D=[],R=(s("ac1f"),s("1276"),function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.shown,expression:"shown"},{name:"click-outside",rawName:"v-click-outside",value:e.close,expression:"close"}],staticClass:"popover",class:e.classList},[s("div",{staticClass:"popover-content"},[e._t("default")],2)])}),E=[],N={name:"Popover",data:function(){return{shown:!1,classList:[]}},methods:{close:function(e){e&&this.$el.closest(".popover-container").contains(e.target)||(this.shown=!1)},open:function(){this.shown=!0;var e=this.$el.closest(".popover-viewport"),t=e.getBoundingClientRect(),s=this.$el.closest(".popover-container"),a=s.getBoundingClientRect();this.classList=[],t.left>a.left+a.width/2-200?this.classList=["left-aligned"]:t.right<a.left+a.width/2+200&&(this.classList=["right-aligned"])},toggle:function(){this.shown?this.close():this.open()}}},A=N,I=(s("af36"),Object(f["a"])(A,R,E,!1,null,null,null)),M=I.exports,L=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("span",{on:{click:function(t){return e.expand(t)}}},[e.expanded?[e._v(" "+e._s(e.full)+" ")]:[e._t("default")]],2)},Q=[],U={name:"ShortenedText",props:["full"],data:function(){return{expanded:!1}},methods:{expand:function(e){this.expanded||(e.stopPropagation(),e.preventDefault()),this.expanded=!0}}},F=U,J=Object(f["a"])(F,L,Q,!1,null,null,null),H=J.exports,V={name:"StackTrace",components:{Popover:M,ShortenedText:H},props:["trace","file","line"],computed:{fullPath:function(){var e,t;return this.makeFullPath(this.file||(null===(e=this.trace[0])||void 0===e?void 0:e.file),this.line||(null===(t=this.trace[0])||void 0===t?void 0:t.line))},shortPath:function(){var e,t;return this.makeShortPath(this.file||(null===(e=this.trace[0])||void 0===e?void 0:e.file),this.line||(null===(t=this.trace[0])||void 0===t?void 0:t.line))}},methods:{togglePopover:function(e){this.trace&&(e.preventDefault(),this.$refs.popover.toggle())},makeFullPath:function(e,t){return"".concat(e,":").concat(t)},makeShortPath:function(e,t){return this.makeFullPath(e,t).split(/[\/\\]/).pop()}}},W=V,B=Object(f["a"])(W,P,D,!1,null,null,null),z=B.exports,G={name:"ExceptionSection",components:{StackTrace:z},props:["compact"],methods:{showPreviousException:function(e){this.$request.exceptions.push(e.previous),e.previous=void 0}}},K=G,X=(s("9b34"),Object(f["a"])(K,j,O,!1,null,null,null)),Z=X.exports,Y=function(){var e=this,t=e.$createElement,s=e._self._c||t;return e.parentRequest?s("div",{staticClass:"parent-request",class:{compact:e.compact}},[s("div",[s("div",{staticClass:"parent-title"},[e._v(" Subrequest of ")]),e.parentRequest.isCommand()?s("div",{staticClass:"parent-name"},[s("span",{staticClass:"type-text"},[e._v("CMD")]),e._v(" "+e._s(e.parentRequest.commandName)+" ")]):e.parentRequest.isQueueJob()?s("div",{staticClass:"parent-name"},[s("span",{staticClass:"type-text"},[e._v("QUEUE")]),e._v(" "+e._s(e.parentRequest.jobName)+" ")]):s("div",{staticClass:"parent-name"},[s("span",{staticClass:"parent-method"},[e._v(e._s(e.parentRequest.method))]),e._v(" "),s("span",{staticClass:"parent-uri"},[e._v(e._s(e.parentRequest.uri))])])]),s("div",{staticClass:"parent-close"},[s("a",{attrs:{href:"#"},on:{click:function(t){return e.showRequestById(e.parentRequest.id)}}},[e._v("Show")])])]):e._e()},ee=[],te=s("1da1"),se=(s("96cf"),{name:"ParentRequest",props:["compact"],data:function(){return{parentRequest:null}},methods:{showRequestById:function(e){this.global.$request=this.$requests.findId(e)}},watch:{$request:{handler:function(){var e=this;return Object(te["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.parentRequest=null,e.$request&&e.$request.parent&&e.$request.parent.id){t.next=3;break}return t.abrupt("return");case 3:if(t.t0=e.$requests.findId(e.$request.parent.id),t.t0){t.next=8;break}return t.next=7,e.$requests.loadId(e.$request.parent.id);case 7:t.t0=t.sent;case 8:e.parentRequest=t.t0;case 9:case"end":return t.stop()}}),t)})))()},immediate:!0}}}),ae=se,ie=(s("1746"),Object(f["a"])(ae,Y,ee,!1,null,null,null)),ne=ie.exports,re={name:"MessagesOverlay",components:{ExceptionSection:Z,ParentRequest:ne,StackTrace:z},data:function(){return{updateNotification:!1}},methods:{closeUpdateNotification:function(){this.$updateNotification.ignoreUpdate(this.$requests.remoteUrl),this.updateNotification=!1}},watch:{"$requests.remoteUrl":{handler:function(){this.updateNotification=this.$updateNotification.show(this.$requests.remoteUrl)},immediate:!0},"$updateNotification.serverVersion":function(){this.updateNotification=this.$updateNotification.show(this.$requests.remoteUrl)}}},oe=re,le=(s("9cad"),Object(f["a"])(oe,T,S,!1,null,null,null)),ce=le.exports,ue=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("modal",{attrs:{icon:"settings",title:"Settings",shown:e.$settings.shown},on:{"update:shown":function(t){return e.$set(e.$settings,"shown",t)}}},[s("div",{staticClass:"settings-modal"},[e.$settings.persistent?e._e():s("div",{staticClass:"settings-warning"},[s("div",{staticClass:"warning-text"},[s("span",{staticClass:"warning-label"},[e._v("Warning")]),s("span",[e._v("Settings can't be saved.")]),s("a",{attrs:{href:"#"},on:{click:function(t){t.preventDefault(),e.showPersistWarning=!0}}},[e._v("More info")])]),e.showPersistWarning?s("div",{staticClass:"warning-details"},[e._v(' Clockwork uses the "local storage" api to persist your settings. Please make sure the access to "local storage" is allowed for this app. ')]):e._e()]),s("div",{staticClass:"controls-group"},[s("label",{attrs:{for:"settings-editor"}},[e._v("Appearance")]),s("div",{staticClass:"controls"},[s("div",{staticClass:"appearance-controls"},[s("div",{staticClass:"option",class:{selected:"auto"==e.$settings.global.appearance},on:{click:function(t){return e.setAppearance("auto")}}},[s("img",{attrs:{src:"img/appearance-auto-icon.png"}}),e._v(" Auto ")]),s("div",{staticClass:"option",class:{selected:"light"==e.$settings.global.appearance},on:{click:function(t){return e.setAppearance("light")}}},[s("img",{attrs:{src:"img/appearance-light-icon.png"}}),e._v(" Light ")]),s("div",{staticClass:"option",class:{selected:"dark"==e.$settings.global.appearance},on:{click:function(t){return e.setAppearance("dark")}}},[s("img",{attrs:{src:"img/appearance-dark-icon.png"}}),e._v(" Dark ")])])])]),s("div",{staticClass:"controls-group"},[s("label",{attrs:{for:"settings-editor"}},[e._v("Code editor")]),s("div",{staticClass:"controls"},[s("select",{directives:[{name:"model",rawName:"v-model",value:e.$settings.global.editor,expression:"$settings.global.editor"}],attrs:{id:"settings-editor"},on:{change:[function(t){var s=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.$set(e.$settings.global,"editor",t.target.multiple?s:s[0])},e.save]}},[s("option",{attrs:{value:"atom"}},[e._v("Atom")]),s("option",{attrs:{value:"phpstorm"}},[e._v("PhpStorm")]),s("option",{attrs:{value:"sublime"}},[e._v("Sublime Text")]),s("option",{attrs:{value:"textmate"}},[e._v("Textmate")]),s("option",{attrs:{value:"vs-code"}},[e._v("Visual Studio Code")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:"atom"==e.$settings.global.editor,expression:"$settings.global.editor == 'atom'"}],staticClass:"help-text"},[e._v(" Requires "),s("a",{attrs:{href:"https://atom.io/packages/open",target:"_blank"}},[e._v("Atom Open")]),e._v(" package. ")]),s("div",{directives:[{name:"show",rawName:"v-show",value:"sublime"==e.$settings.global.editor,expression:"$settings.global.editor == 'sublime'"}],staticClass:"help-text"},[e._v(" Requires "),s("a",{attrs:{href:"https://github.com/inopinatus/sublime_url",target:"_blank"}},[e._v("sublime_url")]),e._v(" on MacOS. Not supported on other platforms. ")])])]),s("div",{staticClass:"controls-group"},[s("label",{attrs:{for:"settings-local-path-map"}},[e._v("Code path")]),s("div",{staticClass:"controls"},[s("div",{staticClass:"controls-input-vgroup"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.site.localPathMap.real,expression:"$settings.site.localPathMap.real"}],attrs:{type:"text",name:"settings-local-path-map-real",placeholder:"/real/path"},domProps:{value:e.$settings.site.localPathMap.real},on:{change:e.save,input:function(t){t.target.composing||e.$set(e.$settings.site.localPathMap,"real",t.target.value)}}}),s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.site.localPathMap.local,expression:"$settings.site.localPathMap.local"}],attrs:{type:"text",name:"settings-local-path-map-local",placeholder:"/local/path"},domProps:{value:e.$settings.site.localPathMap.local},on:{change:e.save,input:function(t){t.target.composing||e.$set(e.$settings.site.localPathMap,"local",t.target.value)}}})]),s("div",{staticClass:"help-text"},[e._v(" Map a remote path to a local path when developing on a remote server or a virtual machine. ")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("requests-list"),expression:"$platform.hasFeature('requests-list')"}],staticClass:"controls-group"},[s("label",{attrs:{for:"settings-on-demand-secret"}},[e._v("On-demand")]),s("div",{staticClass:"controls"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.site.onDemandSecret,expression:"$settings.site.onDemandSecret"}],attrs:{type:"text",name:"settings-on-demand-secret",placeholder:"secret"},domProps:{value:e.$settings.site.onDemandSecret},on:{change:e.save,input:function(t){t.target.composing||e.$set(e.$settings.site,"onDemandSecret",t.target.value)}}}),s("div",{staticClass:"help-text"},[e._v(" Only used if the server-side has on-demand mode with secret enabled. ")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("requests-list"),expression:"$platform.hasFeature('requests-list')"}],staticClass:"controls-group"},[s("label"),s("div",{staticClass:"controls"},[s("label",{staticClass:"controls-checkbox"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.global.showIncomingRequests,expression:"$settings.global.showIncomingRequests"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.$settings.global.showIncomingRequests)?e._i(e.$settings.global.showIncomingRequests,null)>-1:e.$settings.global.showIncomingRequests},on:{change:[function(t){var s=e.$settings.global.showIncomingRequests,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&e.$set(e.$settings.global,"showIncomingRequests",s.concat([n])):r>-1&&e.$set(e.$settings.global,"showIncomingRequests",s.slice(0,r).concat(s.slice(r+1)))}else e.$set(e.$settings.global,"showIncomingRequests",i)},e.save]}}),e._v(" Show incoming requests ")]),s("label",{staticClass:"controls-checkbox"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.global.preserveLog,expression:"$settings.global.preserveLog"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.$settings.global.preserveLog)?e._i(e.$settings.global.preserveLog,null)>-1:e.$settings.global.preserveLog},on:{change:[function(t){var s=e.$settings.global.preserveLog,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&e.$set(e.$settings.global,"preserveLog",s.concat([n])):r>-1&&e.$set(e.$settings.global,"preserveLog",s.slice(0,r).concat(s.slice(r+1)))}else e.$set(e.$settings.global,"preserveLog",i)},e.save]}}),e._v(" Keep requests log ")])])]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("requests-list"),expression:"$platform.hasFeature('requests-list')"}],staticClass:"controls-group"},[s("label"),s("div",{staticClass:"controls"},[s("label",{staticClass:"controls-checkbox"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.global.hideCommandTypeRequests,expression:"$settings.global.hideCommandTypeRequests"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.$settings.global.hideCommandTypeRequests)?e._i(e.$settings.global.hideCommandTypeRequests,null)>-1:e.$settings.global.hideCommandTypeRequests},on:{change:[function(t){var s=e.$settings.global.hideCommandTypeRequests,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&e.$set(e.$settings.global,"hideCommandTypeRequests",s.concat([n])):r>-1&&e.$set(e.$settings.global,"hideCommandTypeRequests",s.slice(0,r).concat(s.slice(r+1)))}else e.$set(e.$settings.global,"hideCommandTypeRequests",i)},e.save]}}),e._v(" Hide commands in requests list ")]),s("label",{staticClass:"controls-checkbox"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.global.hideQueueJobTypeRequests,expression:"$settings.global.hideQueueJobTypeRequests"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.$settings.global.hideQueueJobTypeRequests)?e._i(e.$settings.global.hideQueueJobTypeRequests,null)>-1:e.$settings.global.hideQueueJobTypeRequests},on:{change:[function(t){var s=e.$settings.global.hideQueueJobTypeRequests,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&e.$set(e.$settings.global,"hideQueueJobTypeRequests",s.concat([n])):r>-1&&e.$set(e.$settings.global,"hideQueueJobTypeRequests",s.slice(0,r).concat(s.slice(r+1)))}else e.$set(e.$settings.global,"hideQueueJobTypeRequests",i)},e.save]}}),e._v(" Hide queue jobs in requests list ")]),s("label",{staticClass:"controls-checkbox"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$settings.global.hideTestTypeRequests,expression:"$settings.global.hideTestTypeRequests"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.$settings.global.hideTestTypeRequests)?e._i(e.$settings.global.hideTestTypeRequests,null)>-1:e.$settings.global.hideTestTypeRequests},on:{change:[function(t){var s=e.$settings.global.hideTestTypeRequests,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&e.$set(e.$settings.global,"hideTestTypeRequests",s.concat([n])):r>-1&&e.$set(e.$settings.global,"hideTestTypeRequests",s.slice(0,r).concat(s.slice(r+1)))}else e.$set(e.$settings.global,"hideTestTypeRequests",i)},e.save]}}),e._v(" Hide tests in requests list ")])])]),s("div",{staticClass:"settings-footer"},[e._v(" Version "+e._s(e.$credits.version)+" • "),s("a",{attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showCredits.apply(null,arguments)}}},[e._v("Credits")])])])])},de=[],he={name:"SettingsModal",components:{Modal:p},data:function(){return{showPersistWarning:!1}},methods:{setAppearance:function(e){this.$settings.global.appearance=e,this.save()},save:function(){this.$settings.save(),this.$onDemand.enableProfiling()},showCredits:function(){this.$settings.toggle(),this.$credits.toggle()}}},ve=he,fe=(s("28e9"),Object(f["a"])(ve,ue,de,!1,null,null,null)),me=fe.exports,pe=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("modal",{attrs:{icon:"share",title:"Share",shown:e.$sharing.shown},on:{"update:shown":function(t){return e.$set(e.$sharing,"shown",t)}}},[s("div",{staticClass:"sharing-modal"},[e.$sharing.termsAccepted?[s("div",{staticClass:"sharing-content"},[s("label",{staticClass:"content-item item-all",class:{active:e.filterAll}},[s("span",{staticClass:"item-text"},[e._v("All")]),s("input",{directives:[{name:"model",rawName:"v-model",value:e.filterAll,expression:"filterAll"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.filterAll)?e._i(e.filterAll,null)>-1:e.filterAll},on:{change:function(t){var s=e.filterAll,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&(e.filterAll=s.concat([n])):r>-1&&(e.filterAll=s.slice(0,r).concat(s.slice(r+1)))}else e.filterAll=i}}})]),e._l(e.sections,(function(t){return s("label",{staticClass:"content-item",class:{active:e.filter[t.name]&&t.available,unavailable:!t.available}},[s("icon",{attrs:{name:t.icon}}),s("span",{staticClass:"item-text"},[e._v(e._s(t.text))]),s("input",{directives:[{name:"model",rawName:"v-model",value:e.filter[t.name],expression:"filter[section.name]"}],attrs:{type:"checkbox",disabled:t.readonly||!t.available},domProps:{checked:Array.isArray(e.filter[t.name])?e._i(e.filter[t.name],null)>-1:e.filter[t.name]},on:{change:function(s){var a=e.filter[t.name],i=s.target,n=!!i.checked;if(Array.isArray(a)){var r=null,o=e._i(a,r);i.checked?o<0&&e.$set(e.filter,t.name,a.concat([r])):o>-1&&e.$set(e.filter,t.name,a.slice(0,o).concat(a.slice(o+1)))}else e.$set(e.filter,t.name,n)}}})],1)}))],2),e.error?s("p",{staticClass:"error-message"},[e._v(e._s(e.errorMessage))]):e._e(),s("a",{staticClass:"button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.share.apply(null,arguments)}}},[e.isCopied?[e._v(" Copied to clipboard! ")]:e.$sharing.inProgress?[s("spinner",{attrs:{name:"fading-circle",width:"18",height:"18","no-fade-in":!0,color:"dark"==e.$settings.appearance?"#f27e02":"#258cdb"}}),e._v(" Sharing... ")]:[e._v(" Share ")]],2)]:[s("div",{staticClass:"sharing-terms"},[s("h1",[s("icon",{attrs:{name:"users"}})],1),s("p",[e._v("Request will be uploaded to the Clockwork Cloud servers and a unique public link generated for you.")]),s("h2",[e._v("Deleting")]),s("ul",[s("li",[e._v("You can delete the request anytime via the shared link.")]),s("li",[e._v("Shared links might expire after a few weeks.")])]),s("h2",[e._v("Privacy")]),s("ul",[s("li",[e._v("Shared requests are hosted on DigitalOcean servers.")]),s("li",[e._v("Collected data is never shared with third-parties.")])]),s("a",{staticClass:"button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.$sharing.acceptTerms()}}},[e._v("Continue")])])]],2)])},ge=[],be=(s("b0c0"),s("159b"),{name:"SharingModal",components:{Modal:p},data:function(){return{filter:{performance:!0,log:!0,events:!0,models:!0,database:!0,cache:!0,redis:!0,queue:!0,views:!0,notifications:!0,routes:!0,output:!0,userData:!0},isCopied:!1,error:!1}},computed:{sections:function(){var e,t;return[{text:"Performance",name:"performance",icon:"activity",available:!0,readonly:!0},{text:"Log",name:"log",icon:"edit-2",available:this.shownSections.log},{text:"Events",name:"events",icon:"zap",available:this.shownSections.events},{text:"Models",name:"models",icon:"disc",available:this.shownSections.models},{text:"Database",name:"database",icon:"database",available:this.shownSections.database},{text:"Cache",name:"cache",icon:"paperclip",available:this.shownSections.cache},{text:"Redis",name:"redis",icon:"layers",available:this.shownSections.redis},{text:"Queue",name:"queue",icon:"clock",available:this.shownSections.queue},{text:"Views",name:"views",icon:"image",available:this.shownSections.views},{text:"Notifications",name:"notifications",icon:"mail",available:this.shownSections.notifications},{text:"Routes",name:"routes",icon:"map",available:this.shownSections.routes},{text:"Output",name:"output",icon:"terminal",available:this.shownSections.output},{text:"Custom tabs",name:"userData",icon:"menu",available:null===(e=this.$request)||void 0===e||null===(t=e.userData)||void 0===t?void 0:t.length}]},shownSections:function(){var e,t,s,a,i,n,r,o,l,c,u,d,h,v,f,m,p,g,b,w,_,y=this;return{log:(null===(e=this.$request)||void 0===e||null===(t=e.log)||void 0===t?void 0:t.length)>0,models:["modelsRetrieved","modelsCreated","modelsUpdated","modelsDeleted"].some((function(e){var t;return null===(t=y.$request)||void 0===t?void 0:t[e]}))||(null===(s=this.$request)||void 0===s?void 0:s.modelsActions.length)>0,database:(null===(a=this.$request)||void 0===a?void 0:a.databaseQueriesCount)>0||(null===(i=this.$request)||void 0===i||null===(n=i.databaseQueries)||void 0===n?void 0:n.length)>0,cache:["cacheReads","cacheHits","cacheWrites","cacheDeletes","cacheTime"].some((function(e){var t;return null===(t=y.$request)||void 0===t?void 0:t[e]}))||(null===(r=this.$request)||void 0===r?void 0:r.cacheQueries.length)>0,redis:(null===(o=this.$request)||void 0===o||null===(l=o.redisCommands)||void 0===l?void 0:l.length)>0,queue:(null===(c=this.$request)||void 0===c||null===(u=c.queueJobs)||void 0===u?void 0:u.length)>0,events:(null===(d=this.$request)||void 0===d||null===(h=d.events)||void 0===h?void 0:h.length)>0,views:(null===(v=this.$request)||void 0===v||null===(f=v.viewsData)||void 0===f?void 0:f.events.length)>0,notifications:(null===(m=this.$request)||void 0===m||null===(p=m.notifications)||void 0===p?void 0:p.length)>0,routes:(null===(g=this.$request)||void 0===g||null===(b=g.routes)||void 0===b?void 0:b.length)>0,output:(null===(w=this.$request)||void 0===w||null===(_=w.commandOutput)||void 0===_?void 0:_.length)>0}},filterAll:{get:function(){var e=this;return this.sections.every((function(t){return!t.available||e.filter[t.name]}))},set:function(e){var t=this;this.sections.filter((function(e){return e.available&&!e.readonly})).forEach((function(s){return t.filter[s.name]=e}))}},errorMessage:function(){return"metadata-too-large"==this.error?"Shared request metadata is too large, please try selecting fewer sections.":"temporarily-unavailable"==this.error?"Share service is temporarily unavailable, please try again later.":"Unexpected error, please try again later."}},methods:{share:function(){var e=this;this.$sharing.share(this.$request,this.filter).then((function(t){if(t&&t.error)return e.error=t.error;e.$copyText(e.$request.shareUrl).then((function(){return e.isCopied=!0}))}))}},watch:{filter:{handler:function(){this.$sharing.clear(this.$request),this.isCopied=!1},deep:!0},$request:function(){this.isCopied=!1},"$sharing.shown":function(){this.isCopied=this.error=!1}}}),we=be,_e=(s("d901"),Object(f["a"])(we,pe,ge,!1,null,null,null)),ye=_e.exports,Ce=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("modal",{attrs:{icon:"trash-2",title:"Delete",shown:e.$sharing.shownDelete},on:{"update:shown":function(t){return e.$set(e.$sharing,"shownDelete",t)}}},[s("div",{staticClass:"delete-shared-modal"},[s("h1",[e.deleted?[s("icon",{attrs:{name:"check-circle"}})]:[s("icon",{attrs:{name:"trash-2"}})]],2),s("p",[e.deleted?[e._v(" Shared request was successfully deleted. ")]:[e._v(" Are you sure you want to delete this shared request? ")]],2),s("a",{staticClass:"button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.deleteShared.apply(null,arguments)}}},[e.deleted?[e._v(" Deleted! ")]:e.deleting?[s("spinner",{attrs:{name:"fading-circle",width:"18",height:"18","no-fade-in":!0,color:"dark"==e.$settings.appearance?"#f27e02":"#258cdb"}}),e._v(" Deleting... ")]:[e._v(" Delete ")]],2)])])},qe=[],$e={name:"SharingDeleteModal",components:{Modal:p},data:function(){return{deleting:!1,deleted:!1}},methods:{deleteShared:function(){var e=this;this.deleting=!0,this.$sharing.deleteShared().then((function(){return e.deleted=!0}))}}},ke=$e,xe=(s("7e04"),Object(f["a"])(ke,Ce,qe,!1,null,null,null)),Te=xe.exports,Se=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"details-header-tabs"},e._l(e.tabs,(function(t){return t.shown?s("tab-handle",{key:t.name,attrs:{text:t.text,name:t.name,icon:t.icon,active:t.name==e.activeTab,short:e.shortTabs.includes(t),full:e.fullTabs.includes(t)},on:{"tab-selected":function(t){return e.$emit("tab-selected",t)}}},[e._v(e._s(t.text))]):e._e()})),1)},je=[],Oe=(s("7db0"),s("4e82"),s("fb6a"),s("caad"),s("2532"),function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("a",{staticClass:"details-header-tab",class:{active:e.active,short:e.short,full:e.full},attrs:{href:"#"},on:{click:e.selectTab}},[e.icon?s("icon",{attrs:{name:e.icon,title:e.text}}):e._e(),s("div",{directives:[{name:"show",rawName:"v-show",value:!e.short,expression:"! short"}],staticClass:"tab-title"},[e._v(e._s(e.text))]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.badge&&!e.short,expression:"badge && ! short"}],staticClass:"tab-badge"},[e._v(e._s(e.badge))])],1)}),Pe=[],De={name:"TabHandle",props:["text","name","icon","badge","active","short","full"],methods:{selectTab:function(){this.$emit("tab-selected",this.name)}}},Re=De,Ee=(s("0877"),Object(f["a"])(Re,Oe,Pe,!1,null,null,null)),Ne=Ee.exports,Ae=s("2fd4"),Ie=s.n(Ae),Me={name:"TabBar",components:{TabHandle:Ne},props:{tabs:{default:function(){return[]}},activeTab:{}},data:function(){return{shortTabs:[],fullTabs:[]}},methods:{hideOverflowingTabs:function(){var e=this;this.shortTabs=[],this.fullTabs=[],this.$nextTick((function(){return e.hideNextOverflowingTab()}))},hideNextOverflowingTab:function(){var e=this;if(!(this.$el.scrollWidth<=this.$el.clientWidth)){var t=this.tabs.indexOf(this.tabs.find((function(t){return t.name==e.activeTab}))),s=this.tabs.slice().sort((function(s,a){return Math.abs(t-e.tabs.indexOf(a))-Math.abs(t-e.tabs.indexOf(s))})).find((function(t){return!e.shortTabs.includes(t)}));if(!s||s.name==this.activeTab)return this.fullTabs=this.tabs.filter((function(t){return!e.shortTabs.includes(t)}));this.shortTabs.push(s),this.$nextTick((function(){return e.hideNextOverflowingTab()}))}}},watch:{tabs:function(){this.hideOverflowingTabs()},activeTab:function(){this.hideOverflowingTabs()}},mounted:function(){var e=this;this.resizeObserver=new ResizeObserver(Ie()((function(){return e.hideOverflowingTabs()}),10)),this.resizeObserver.observe(this.$el)}},Le=Me,Qe=Object(f["a"])(Le,Se,je,!1,null,null,null),Ue=Qe.exports,Fe=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("div",{staticClass:"counters-row"},[s("div",{directives:[{name:"show",rawName:"v-show",value:e.$request.cacheQueries.length,expression:"$request.cacheQueries.length"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheQueries.length))]),s("div",{staticClass:"counter-title"},[e._v("queries")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:null!==e.$request.cacheReads,expression:"$request.cacheReads !== null"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheReads))]),s("div",{staticClass:"counter-title"},[e._v("reads")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:null!==e.$request.cacheHits,expression:"$request.cacheHits !== null"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheHits))]),s("div",{staticClass:"counter-title"},[e._v("hits")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:null!==e.$request.cacheMisses,expression:"$request.cacheMisses !== null"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheMisses))]),s("div",{staticClass:"counter-title"},[e._v("misses")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:null!==e.$request.cacheWrites,expression:"$request.cacheWrites !== null"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheWrites))]),s("div",{staticClass:"counter-title"},[e._v("writes")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:null!==e.$request.cacheDeletes,expression:"$request.cacheDeletes !== null"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheDeletes))]),s("div",{staticClass:"counter-title"},[e._v("deletes")])]),s("div",{directives:[{name:"show",rawName:"v-show",value:null!==e.$request.cacheTime,expression:"$request.cacheTime !== null"}],staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.cacheTime))]),s("div",{staticClass:"counter-title"},[e._v("time")])])]),e.$request.cacheQueries.length?s("details-table",{attrs:{title:"Queries",icon:"paperclip",columns:e.columns,items:e.$request.cacheQueries,filter:e.filter,"filter-example":"<EMAIL> action:miss key:lastRequest file:Controller.php"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[e.columns.includes("Connection")?s("td",[e._v(e._s(t.connection))]):e._e(),s("td",{staticClass:"cache-query-type"},[e._v(e._s(t.type))]),s("td",[e._v(e._s(t.key))]),s("td",[s("div",{staticClass:"database-query"},[s("div",{staticClass:"database-query-content"},[s("pretty-print",{attrs:{data:t.value}})],1),s("stack-trace",{staticClass:"database-query-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",[t.expiration?s("span",[e._v(e._s(t.expiration))]):e._e()]),e.columns.includes("Duration")?s("td",[e._v(e._s(e._f("round")(t.duration,3))+" ms")]):e._e()])}))}}],null,!1,1651104168)}):e._e()],1)},Je=[],He=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"details-table"},[e.noHeader?e._e():s("div",{staticClass:"table-header"},[s("div",{staticClass:"header-title"},[s("icon",{attrs:{name:e.icon}}),e._v(" "+e._s(e.title)+" "),e.badge?s("span",{staticClass:"title-badge"},[e._v(e._s(e.badge))]):e._e()],1),e._t("toolbar",(function(){return[s("div",{staticClass:"header-group"},[e.filter?s("div",{staticClass:"header-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filter.input,expression:"filter.input"}],attrs:{type:"search",placeholder:"Search..."},domProps:{value:e.filter.input},on:{input:function(t){t.target.composing||e.$set(e.filter,"input",t.target.value)}}}),s("icon",{attrs:{name:"search"}})],1):e._e()])]}),{filter:e.filter})],2),s("div",{staticClass:"table-content"},[s("table",[s("thead",[e.noTableHead?e._e():s("tr",[e._t("header",(function(){return e._l(e.columns,(function(t,a){return s("th",{class:t.class,on:{click:function(s){e.filter.sortBy(t.sortBy||t.toLowerCase())}}},[e._v(" "+e._s(t.name||t)+" "),s("icon",{directives:[{name:"show",rawName:"v-show",value:e.filter.sortedBy==(t.sortBy||t.toLowerCase()),expression:"filter.sortedBy == (column.sortBy || column.toLowerCase())"}],attrs:{name:e.filter.sortedDesc?"chevron-down":"chevron-up"}})],1)}))}),{filter:e.filter})],2)]),s("tbody",[e.hasPreviousItems?s("tr",{staticClass:"pagination-controls"},[s("td",{attrs:{colspan:e.columns.length}},[s("a",{attrs:{href:"#"},on:{click:e.showPreviousItems}},[e._v("Show "+e._s(e.previousItemsCount)+" previous")])])]):e._e(),e._t("body",null,{items:e.shownItems}),e.hasNextItems?s("tr",{staticClass:"pagination-controls"},[s("td",{attrs:{colspan:e.columns.length}},[s("a",{attrs:{href:"#"},on:{click:e.showNextItems}},[e._v("Show "+e._s(e.nextItemsCount)+" more")])])]):e._e()],2)])])])},Ve=[],We=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("a",{staticClass:"toggle-filter",attrs:{href:"#"},on:{click:function(t){return t.stopPropagation(),e.filter.toggle(t)}}},[s("icon",{attrs:{name:"search"}})],1)},Be=[],ze={name:"DetailsTableFilterToggle",props:["filter"]},Ge=ze,Ke=Object(f["a"])(Ge,We,Be,!1,null,null,null),Xe=Ke.exports,Ze=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div")},Ye=[],et=s("53ca"),tt=s("d4ec"),st=s("bee2"),at=(s("b64b"),s("07ac"),s("5319"),s("d3b7"),s("25f0"),function(){function e(t){if(Object(tt["a"])(this,e),t instanceof Object||(t=this.parseJson(t)),!(t instanceof Object))throw new it("Input does not contain serialized object.");this.data=t}return Object(st["a"])(e,[{key:"parseJson",value:function(e){try{return JSON.parse(e)}catch(t){throw new it("Input is not a valid JSON string.",t)}}},{key:"print",value:function(e){e.innerHTML=this.generateHtml()}},{key:"generateHtml",value:function(){var e=this,t=this.resolveValueAndType(this.data),s=Object(a["a"])(t,2),i=s[0];s[1];return this.createElement("ul",{class:"pretty-jason"},[this.createElement("li",{},[this.createElement("span",{data:{rendered:!0},click:function(t){return e.objectNodeClickedCallback(t)}},[this.createElement("span",{class:"pretty-jason-icon",html:'<i class="pretty-jason-icon-closed"></i>'}),this.createElement("span",{text:"".concat(i," ")})]),this.generateHtmlPreview(this.data),this.generateHtmlNode(this.data)])])}},{key:"generateHtmlNode",value:function(e){var t=this;return this.createElement("ul",{style:{display:"none"}},Object.keys(e).filter((function(e){return!["__class__","__type__","__hash__"].includes(e)})).map((function(s){var i=t.resolveValueAndType(e[s]),n=Object(a["a"])(i,2),r=n[0],o=n[1];return t.createElement("li",{data:{key:s}},[t.createElement("span",{click:"object"==o?function(e){return t.objectNodeClickedCallback(e)}:void 0},[t.createElement("span",{class:"pretty-jason-icon",html:"object"==o?'<i class="pretty-jason-icon-closed"></i>':void 0}),t.createElement("span",{class:"pretty-jason-key",text:"".concat(s,": ")}),t.createElement("span",{class:"pretty-jason-value-".concat(o),text:r})])])})))}},{key:"generateHtmlPreview",value:function(e){var t=this;return this.createElement("span",{class:"pretty-jason-preview"},Object.keys(e).filter((function(e){return!["__class__","__type__","__hash__"].includes(e)})).slice(0,3).map((function(s){var i=t.resolveValueAndType(e[s]),n=Object(a["a"])(i,2),r=n[0],o=n[1];return t.createElement("span",{class:"pretty-jason-preview-item"},[t.createElement("span",{class:"pretty-jason-key",text:"".concat(s,": ")}),t.createElement("span",{class:"pretty-jason-value-".concat(o),text:r})])})).concat(Object.keys(e).length>3?[this.createElement("span",{class:"pretty-jason-preview-item",text:"..."})]:[]))}},{key:"resolveValueAndType",value:function(e){return null===e?["null","null"]:void 0===e?["undefined","undefined"]:"boolean"==typeof e?[e?"true":"false","boolean"]:"string"==typeof e?['"'.concat(e,'"'),"string"]:"object"==Object(et["a"])(e)?"array"==e.__type__?["Array(".concat(Object.values(e).length-1,")"),"object"]:e.__type__&&"object"!=e.__type__?[e.__type__,e.__type__.replace(" ","-")]:[e.__class__||"Object","object"]:[e.toString(),Object(et["a"])(e)]}},{key:"objectNodeClickedCallback",value:function(e){var t=e.currentTarget;this.isNodeExpanded(t)?this.collapseNode(t,e.ctrlKey||e.metaKey):this.expandNode(t,e.ctrlKey||e.metaKey)}},{key:"isNodeExpanded",value:function(e){var t=e.parentNode.querySelector("ul");return t&&"none"!=t.style.display}},{key:"expandNode",value:function(e,t){var s=this;this.renderObjectNode(e);var a=e.parentNode.querySelector("ul"),i=e.querySelector("i");i.classList.remove("pretty-jason-icon-closed","pretty-jason-icon-open"),a.style.display="block",i.classList.add("pretty-jason-icon-open"),t&&a.querySelectorAll(".pretty-jason-icon-closed").forEach((function(e){s.expandNode(e.parentNode.parentNode,t)}))}},{key:"collapseNode",value:function(e,t){var s=this,a=e.parentNode.querySelector("ul"),i=e.querySelector("i");i.classList.remove("pretty-jason-icon-closed","pretty-jason-icon-open"),a.style.display="none",i.classList.add("pretty-jason-icon-closed"),t&&a.querySelectorAll(".pretty-jason-icon-open").forEach((function(e){s.collapseNode(e.parentNode.parentNode,t)}))}},{key:"renderObjectNode",value:function(e){if(!e.dataset.rendered){var t=[],s=e;while(s=s.parentNode)if("LI"==s.tagName&&"key"in s.dataset){if(s.classList.contains("pretty-jason"))break;var a=s.dataset.key;t.unshift(isNaN(parseInt(a,10))?a:parseInt(a,10))}e.parentNode.append(this.generateHtmlNode(this.getDataFromPath(t))),e.dataset.rendered=!0}}},{key:"getDataFromPath",value:function(e){var t,s=this.data;while(void 0!==(t=e.shift()))s=s[t];return s}},{key:"createElement",value:function(e,t,s){var a=document.createElement(e);return t.html&&(a.innerHTML=t.html),t.text&&(a.innerText=t.text),t.class&&a.classList.add(t.class),t.style instanceof Object&&Object.keys(t.style).forEach((function(e){return a.style[e]=t.style[e]})),t.data instanceof Object&&Object.keys(t.data).forEach((function(e){return a.dataset[e]=t.data[e]})),t.click&&a.addEventListener("click",t.click),s instanceof Array&&s.forEach((function(e){return a.append(e)})),a}}]),e}()),it=function e(t,s){Object(tt["a"])(this,e),this.message=t,this.exception=s},nt=s("1901"),rt=s.n(nt),ot={name:"PrettyPrint",props:["data","expanded","linkify"],methods:{render:function(){var e=this.data,t=document.createElement("div");if(!0===e)t.innerHTML="<i>true</i>";else if(!1===e)t.innerHTML="<i>false</i>";else if(void 0===e)t.innerHTML="<i>undefined</i>";else if(null===e)t.innerHTML="<i>null</i>";else if("number"===typeof e)t.innerText=e;else try{t.append(new at(e).generateHtml())}catch(s){t.innerText=e,this.linkify&&(t.innerHTML=rt()(t.innerHTML))}this.$el.firstChild&&this.$el.firstChild.remove(),this.$el.append(t),this.expanded&&this.$el.querySelector(".pretty-jason > li > span").click()}},mounted:function(){this.render()},watch:{data:function(){this.render()}}},lt=ot,ct=Object(f["a"])(lt,Ze,Ye,!1,null,null,null),ut=ct.exports,dt={name:"DetailsTable",components:{DetailsTableFilterToggle:Xe,PrettyPrint:ut},props:{badge:{},columns:{},filter:{},filterExample:{},icon:{default:"menu"},items:{},noHeader:{},noTableHead:{},perPage:{default:30},title:{}},data:function(){return{firstShown:0}},computed:{filteredItems:function(){return this.filter?this.filter.filter(this.items):this.items},shownItems:function(){return this.firstShown>this.filteredItems.length&&(this.firstShown=Math.max(this.filteredItems.length-this.perPage,0)),this.filteredItems.slice(this.firstShown,this.firstShown+this.perPage)},hasPreviousItems:function(){return this.firstShown>0},previousItemsCount:function(){return this.firstShown},hasNextItems:function(){return this.firstShown+this.perPage<this.filteredItems.length},nextItemsCount:function(){return this.filteredItems.length-this.perPage-this.firstShown}},methods:{showPreviousItems:function(){this.firstShown-=this.perPage},showNextItems:function(){this.firstShown+=this.perPage;var e=this.$el.parentElement;while(e&&0==e.scrollTop)e=e.parentElement;var t=this.$el.offsetTop-e.offsetTop-5;e&&e.scrollTop>t&&(e.scrollTop=t)}},watch:{filteredItems:function(){this.firstShown=0}}},ht=dt,vt=(s("2958"),Object(f["a"])(ht,He,Ve,!1,null,null,null)),ft=vt.exports,mt=(s("466d"),s("7677")),pt=s("460c"),gt=s("e3ee"),bt=s("b166"),wt=s("1212"),_t=s("cc73"),yt=s("2b0e"),Ct=function(){function e(t,s){Object(tt["a"])(this,e),this.tags=t,this.map=s,this.shown=!1,this.sortedBy=void 0,this.sortedDesc=!1,this.input=""}return Object(st["a"])(e,[{key:"toggle",value:function(e){this.shown=!this.shown,this.shown&&yt["a"].nextTick((function(){var t=e.target;while(t=t.parentNode)if("TABLE"==t.tagName)break;t.querySelector(".filter input").focus()}))}},{key:"sortBy",value:function(e){this.sortedBy==e?this.sortedDesc=!this.sortedDesc:(this.sortedBy=e,this.sortedDesc=!0)}},{key:"filter",value:function(e){var t=this,s=this.tokenize(this.input),a=s.terms,i=s.tags;if(e=e.filter((function(e){var s=t.map?t.map(e):e;return t.matchesTerms(s,a)&&t.matchesTags(e,i)})),this.sortedBy){var n=this.tags.find((function(e){return e.tag==t.sortedBy}));e.sort((function(e,s){var a,i;return n&&"number"==n.type?e[t.sortedBy]-s[t.sortedBy]:null===(a=e[t.sortedBy])||void 0===a?void 0:a.toString().localeCompare(null===(i=s[t.sortedBy])||void 0===i?void 0:i.toString())}))}return this.sortedDesc&&(e=e.reverse()),e}},{key:"matchesTerms",value:function(e,t){var s=this;return!t.length||("object"==Object(et["a"])(e)&&null!==e?Object.values(e).find((function(e){return s.matchesTerms(e,t)})):"string"==typeof e&&t.find((function(t){return e.toLowerCase().includes(t.toLowerCase())})))}},{key:"matchesTags",value:function(e,t){var s=this;return!Object.keys(t).length||Object.keys(t).every((function(a){return a=s.tags.find((function(e){return e.tag==a})),!!a&&("number"==a.type||"date"==a.type?t[a.tag].every((function(t){return s.isTagApplicable(a,e,t)})):t[a.tag].find((function(t){return s.isTagApplicable(a,e,t)})))}))}},{key:"isTagApplicable",value:function(e,t,s){return e.apply?e.apply(t,s):(t=e.map?e.map(t):t[e.tag],"number"==e.type?(a=s.match(/^<(\d+(?:\.\d+)?)$/))?t<parseFloat(a[1]):(a=s.match(/^>(\d+(?:\.\d+)?)$/))?parseFloat(a[1])<t:(a=s.match(/^(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)$/))?parseFloat(a[1])<t&&t<parseFloat(a[2]):t==s:"date"==e.type?(i=s.match(/^<(.+)$/))?Object(mt["a"])(Object(pt["a"])(new Date(t),0),Object(gt["a"])(i[1].match(/^\d+:\d+(:\d+)?$/)?Object(bt["a"])(new Date,"yyyy-MM-dd ")+i[1]:i[1])):(i=s.match(/^>(.+)$/))?Object(wt["a"])(Object(pt["a"])(new Date(t),0),Object(gt["a"])(i[1].match(/^\d+:\d+(:\d+)?$/)?Object(bt["a"])(new Date,"yyyy-MM-dd ")+i[1]:i[1])):Object(_t["a"])(Object(pt["a"])(new Date(t),0),Object(gt["a"])(s.match(/^\d+:\d+(:\d+)?$/)?Object(bt["a"])(new Date,"yyyy-MM-dd ")+s:s)):"string"==typeof t&&t.toLowerCase().includes(s.toLowerCase()));var a,i}},{key:"tokenize",value:function(e){var t,s=[],a={},i=/(\w+:)?("[^"]*"|[^\s]+)/g;while(t=i.exec(e)){var n=t[1]?t[1].substr(0,t[1].length-1):void 0,r=t[2];(t=r.match(/^"(.+?)"$/))&&(r=t[1]),n?(a[n]||(a[n]=[]),a[n].push(r)):s.push(r)}return{terms:s,tags:a}}}]),e}(),qt={name:"CacheTab",components:{DetailsTable:ft,PrettyPrint:ut,StackTrace:z},props:["active"],data:function(){return{filter:new Ct([{tag:"action",apply:function(e,t){if(["read","write","delete","miss"].includes(t.toLowerCase()))return e.type.toLowerCase()==t.toLowerCase()}},{tag:"key"},{tag:"file",map:function(e){return e.shortPath}}])}},computed:{columns:function(){var e=[{name:"Action",sortBy:"type"},"Key","Value","Expires"];return this.$request.cacheQueries.some((function(e){return e.connection}))&&e.unshift("Connection"),this.$request.cacheQueries.some((function(e){return e.duration}))&&e.push("Duration"),e}}},$t=qt,kt=Object(f["a"])($t,Fe,Je,!1,null,null,null),xt=kt.exports,Tt=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("div",{staticClass:"counters-row"},[s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseQueriesCount))]),s("div",{staticClass:"counter-title"},[e._v("queries")])]),e.$request.databaseSlowQueries?s("div",{staticClass:"counter database-slow-query"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseSlowQueries))]),s("div",{staticClass:"counter-title has-mark"},[e._v("slow")])]):e._e(),e.$request.databaseSelects?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseSelects))]),s("div",{staticClass:"counter-title"},[e._v("selects")])]):e._e(),e.$request.databaseInserts?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseInserts))]),s("div",{staticClass:"counter-title"},[e._v("inserts")])]):e._e(),e.$request.databaseUpdates?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseUpdates))]),s("div",{staticClass:"counter-title"},[e._v("updates")])]):e._e(),e.$request.databaseDeletes?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseDeletes))]),s("div",{staticClass:"counter-title"},[e._v("deletes")])]):e._e(),e.$request.databaseOthers?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseOthers))]),s("div",{staticClass:"counter-title"},[e._v("other")])]):e._e(),s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.databaseDurationRounded)+" ms")]),s("div",{staticClass:"counter-title"},[e._v("time")])])]),e.$request.databaseQueries.length?s("details-table",{attrs:{title:"Queries",icon:"database",columns:e.columns,items:e.$request.databaseQueries,filter:e.filter,"filter-example":"where request_id model:request type:select file:Controller.php duration:>100"},scopedSlots:e._u([{key:"toolbar",fn:function(t){var a=t.filter;return[s("div",{staticClass:"header-group"},[s("label",{staticClass:"header-toggle"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.prettify,expression:"prettify"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.prettify)?e._i(e.prettify,null)>-1:e.prettify},on:{change:function(t){var s=e.prettify,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&(e.prettify=s.concat([n])):r>-1&&(e.prettify=s.slice(0,r).concat(s.slice(r+1)))}else e.prettify=i}}}),e._v(" Prettify ")])]),s("div",{staticClass:"header-group"},[s("div",{staticClass:"header-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:a.input,expression:"filter.input"}],attrs:{type:"search",placeholder:"Search..."},domProps:{value:a.input},on:{input:function(t){t.target.composing||e.$set(a,"input",t.target.value)}}}),s("icon",{attrs:{name:"search"}})],1)])]}},{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a,class:{"database-slow-query":t.tags.includes("slow")}},[s("td",[s("shortened-text",{attrs:{full:t.model}},[e._v(e._s(t.shortModel))])],1),e.columns.includes("Connection")?s("td",[e._v(e._s(t.connection))]):e._e(),s("td",[s("div",{staticClass:"database-query"},[s("div",{staticClass:"database-query-content"},[s("highlighted-code",{attrs:{language:"sql",code:e.prettify?t.prettifiedQuery:t.query}}),t.bindings?s("div",{staticClass:"database-query-bindings"},[s("pretty-print",{attrs:{data:t.bindings}})],1):e._e()],1),s("stack-trace",{staticClass:"database-query-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",{staticClass:"database-duration"},[t.duration?s("span",[e._v(e._s(e._f("round")(t.duration,3))+" ms")]):e._e()])])}))}}],null,!1,475677615)}):e._e()],1)},St=[],jt=(s("4d63"),s("6062"),s("3ca3"),s("ddb0"),s("a434"),function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("pre",{staticClass:"highlighted-code"},[s("code",{class:e.languageClass,domProps:{innerHTML:e._s(e.html)}})])}),Ot=[],Pt=s("8c7a"),Dt=s.n(Pt),Rt=(s("af8a"),{name:"HighlightedCode",props:["code","language"],computed:{languageClass:function(){return"language-".concat(this.language)},html:function(){return Dt.a.highlight(this.code,Dt.a.languages.sql,"sql")}}}),Et=Rt,Nt=(s("0774"),Object(f["a"])(Et,jt,Ot,!1,null,null,null)),At=Nt.exports,It={name:"DatabaseTab",components:{DetailsTable:ft,HighlightedCode:At,PrettyPrint:ut,ShortenedText:H,StackTrace:z},props:["active"],data:function(){return{prettify:!1,filter:new Ct([{tag:"model"},{tag:"type",apply:function(e,t){if(["select","update","insert","delete"].includes(t.toLowerCase()))return e.query.match(new RegExp("^".concat(t.toLowerCase()),"i"))}},{tag:"file",map:function(e){return e.shortPath}},{tag:"duration",type:"number"}])}},computed:{columns:function(){var e=["Model","Query","Duration"],t=new Set(this.$request.databaseQueries.map((function(e){return e.connection}))).size>1;return t&&e.splice(1,0,"Connection"),e}},watch:{prettify:function(e,t){void 0!==t&&(this.$settings.global.databasePrettified=this.prettify,this.$settings.save())}},mounted:function(){this.prettify=this.$settings.global.databasePrettified||!1}},Mt=It,Lt=(s("c563"),Object(f["a"])(Mt,Tt,St,!1,null,null,null)),Qt=Lt.exports,Ut=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("details-table",{attrs:{title:"Events",icon:"zap",columns:["Time","Event",""],items:e.$request.events,filter:e.filter,"filter-example":'"user registered" file:Controller.php time:<13:08:30'},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[s("td",[e._v(e._s(e._f("date")(t.time,"HH:mm:ss")))]),s("td",[s("div",{staticClass:"fired-event"},[s("div",{staticClass:"fired-event-content"},[t.objectEvent?s("div",[s("pretty-print",{attrs:{data:t.data}})],1):e._e(),t.objectEvent?e._e():s("div",[e._v(" "+e._s(t.event)+" ")])]),s("stack-trace",{staticClass:"fired-query-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1),s("div",{directives:[{name:"show",rawName:"v-show",value:e.isEventExpanded(t),expression:"isEventExpanded(event)"}],staticClass:"fired-event-details"},[t.objectEvent?e._e():s("div",{staticClass:"fired-event-parameters"},[s("h4",[e._v("Parameters")]),s("pretty-print",{attrs:{data:t.data}})],1),s("div",{staticClass:"fired-event-listeners"},[s("h4",[e._v("Listeners")]),e._l(t.listeners,(function(t,a){return s("shortened-text",{key:e.$request.id+"-"+a,attrs:{full:t.name}},[e._v(" "+e._s(t.shortName)+" ")])}))],2)])]),s("td",{staticClass:"fired-event-actions"},[s("a",{attrs:{href:"#"},on:{click:function(s){return s.preventDefault(),e.toggleEvent(t)}}},[s("icon",{attrs:{name:e.isEventExpanded(t)?"chevron-up":"chevron-down"}})],1)])])}))}}])})],1)},Ft=[],Jt={name:"EventsTab",components:{DetailsTable:ft,PrettyPrint:ut,ShortenedText:H,StackTrace:z},props:["active"],data:function(){return{filter:new Ct([{tag:"time",type:"date"},{tag:"file",map:function(e){return e.shortPath}}]),expandedEvents:[]}},methods:{toggleEvent:function(e){this.isEventExpanded(e)?this.expandedEvents=this.expandedEvents.filter((function(t){return t!=e})):this.expandedEvents.push(e)},isEventExpanded:function(e){return-1!==this.expandedEvents.indexOf(e)}}},Ht=Jt,Vt=Object(f["a"])(Ht,Ut,Ft,!1,null,null,null),Wt=Vt.exports,Bt=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("details-table",{attrs:{title:"Messages",icon:"edit-2",columns:["Time","Level","Message"],items:e.log,filter:e.filter,"filter-example":"query failed level:error file:Controller.php time:>13:08:29"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a,class:{"log-row":!0,error:["emergency","alert","critical","error"].includes(t.level),warning:"warning"==t.level}},[s("td",{staticClass:"log-date"},[e._v(e._s(e._f("date")(t.time,"HH:mm:ss")))]),s("td",{staticClass:"log-level"},[e._v(e._s(t.level))]),s("td",[s("div",{staticClass:"log-message"},[s("div",{staticClass:"log-message-content"},[s("pretty-print",{attrs:{data:t.message,linkify:!0}}),s("div",{directives:[{name:"show",rawName:"v-show",value:t.context,expression:"message.context"}],staticClass:"log-message-context"},[s("pretty-print",{attrs:{data:t.context}})],1)],1),t.exception?s("div",{staticClass:"log-message-exception"},[t.exception.previous?s("a",{staticClass:"exception-previous",attrs:{href:"#"},on:{click:function(s){return s.preventDefault(),e.showPreviousException(t)}}},[e._v("Show previous")]):e._e(),s("span",{staticClass:"exception-type"},[e._v(e._s(t.exception.type))]),t.exception.code?s("span",{staticClass:"exception-code"},[e._v("#"+e._s(t.exception.code))]):e._e()]):e._e(),s("stack-trace",{staticClass:"log-message-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)])])}))}}])})],1)},zt=[],Gt={name:"LogTab",components:{DetailsTable:ft,PrettyPrint:ut,StackTrace:z},props:["active"],data:function(){return{filter:new Ct([{tag:"time",type:"date"},{tag:"level"},{tag:"file",map:function(e){return e.shortPath}}],(function(e){return e.message}))}},computed:{log:function(){return this.$request.log.filter((function(e){var t;return!(null!==(t=e.context)&&void 0!==t&&t.performance)}))}},methods:{showPreviousException:function(e){var t=this.$request.log.indexOf(e);this.$request.log.splice(t+1,0,{time:e.time,message:e.exception.previous.message,exception:e.exception.previous,level:"error",shortPath:"".concat(e.exception.previous.file.split(/[\/\\]/).pop(),":").concat(e.exception.previous.line),trace:e.exception.previous.trace}),e.exception.previous=void 0}}},Kt=Gt,Xt=(s("f16e"),Object(f["a"])(Kt,Bt,zt,!1,null,null,null)),Zt=Xt.exports,Yt=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("div",{staticClass:"counters-row models-counters"},[e.totals.retrieved?s("div",{staticClass:"counter counter-retrieved"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.totals.retrieved))]),s("div",{staticClass:"counter-title has-mark"},[e._v("retrieved")])]):e._e(),e.totals.created?s("div",{staticClass:"counter counter-created"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.totals.created))]),s("div",{staticClass:"counter-title has-mark"},[e._v("created")])]):e._e(),e.totals.updated?s("div",{staticClass:"counter counter-updated"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.totals.updated))]),s("div",{staticClass:"counter-title has-mark"},[e._v("updated")])]):e._e(),e.totals.deleted?s("div",{staticClass:"counter counter-deleted"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.totals.deleted))]),s("div",{staticClass:"counter-title has-mark"},[e._v("deleted")])]):e._e()]),e.$request.modelsActions.length?s("div",{staticClass:"models-tabs"},[s("a",{staticClass:"models-tab",class:{active:"actions"==e.activeModelsTab},attrs:{href:"#"},on:{click:function(t){t.preventDefault(),e.selectedModelsTab="actions"}}},[s("icon",{attrs:{name:"activity"}}),e._v(" Actions ")],1),s("a",{staticClass:"models-tab",class:{active:"models"==e.activeModelsTab},attrs:{href:"#"},on:{click:function(t){t.preventDefault(),e.selectedModelsTab="models"}}},[s("icon",{attrs:{name:"hash"}}),e._v(" Models ")],1)]):e._e(),"actions"==e.activeModelsTab?s("details-table",{staticClass:"models-actions",attrs:{title:"Actions",icon:"activity",columns:["Model","",""],items:e.$request.modelsActions,filter:e.actionsFilter,"filter-example":"App\\User action:updated"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return[e._l(a,(function(t,a){return[s("tr",{key:e.$request.id+"-models-actions-"+a,staticClass:"actions-action"},[s("td",{staticClass:"action-model"},[s("div",{staticClass:"model-content"},[s("div",{staticClass:"content-text"},[s("shortened-text",{attrs:{full:t.model}},[e._v(e._s(t.shortModel))]),t.key?s("span",{staticClass:"action-key"},[s("span",{staticClass:"key-hash"},[e._v("#")]),e._v(e._s(t.key)+" ")]):e._e()],1),s("stack-trace",{staticClass:"content-trace",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",{staticClass:"database-duration"},[s("span",{staticClass:"action-action",class:"action-"+t.action},[e._v(e._s(t.action))])]),s("td",[t.attributes||t.changes?s("a",{attrs:{href:"#",title:"Show details"},on:{click:function(e){e.preventDefault(),t.isShowingDetails=!t.isShowingDetails}}},[s("icon",{attrs:{name:t.isShowingDetails?"chevron-up":"chevron-down"}})],1):e._e()])]),s("tr",{directives:[{name:"show",rawName:"v-show",value:t.isShowingDetails,expression:"action.isShowingDetails"}],key:e.$request.id+"-models-actions-details-"+a,staticClass:"actions-details"},[s("td",{attrs:{colspan:"3"}},[t.attributes?s("div",{staticClass:"details-row"},[s("div",{staticClass:"row-group"},[s("h4",[e._v("Attributes")]),s("pretty-print",{attrs:{data:t.attributes}})],1)]):e._e(),t.changes?s("div",{staticClass:"details-row"},[s("div",{staticClass:"row-group"},[s("h4",[e._v("Changes")]),s("pretty-print",{attrs:{data:t.changes}})],1)]):e._e(),t.query?s("div",{staticClass:"details-row"},[t.query?s("div",{staticClass:"row-group group-query"},[s("h4",[e._v("Query")]),s("highlighted-code",{attrs:{language:"sql",code:t.query}})],1):e._e(),t.duration?s("div",{staticClass:"row-group"},[s("h4",[e._v("Duration")]),s("span",[e._v(e._s(e._f("round")(t.duration,3))+" ms")])]):e._e(),t.connection?s("div",{staticClass:"row-group"},[s("h4",[e._v("Connection")]),s("span",[e._v(e._s(t.connection))])]):e._e()]):e._e()])])]}))]}}],null,!1,1918269834)}):e._e(),"models"==e.activeModelsTab?s("details-table",{staticClass:"models-counts",attrs:{title:"Models",icon:"hash",columns:["Model","Retrieved","Created","Updated","Deleted"],items:e.modelsCounts,filter:e.countsFilter,"filter-example":"App\\User retrieved:>10"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return[e._l(a,(function(t,a){return[s("tr",{key:e.$request.id+"-models-counts-"+a},[s("td",{staticClass:"counts-model"},[s("shortened-text",{attrs:{full:t.model}},[e._v(e._s(t.shortModel))])],1),s("td",{staticClass:"counts-count"},[t.retrieved?s("span",{staticClass:"count-text count-retrieved"},[e._v(e._s(t.retrieved))]):s("span",[e._v("-")])]),s("td",{staticClass:"counts-count"},[t.created?s("span",{staticClass:"count-text count-created"},[e._v(e._s(t.created))]):s("span",[e._v("-")])]),s("td",{staticClass:"counts-count"},[t.updated?s("span",{staticClass:"count-text count-updated"},[e._v(e._s(t.updated))]):s("span",[e._v("-")])]),s("td",{staticClass:"counts-count"},[t.deleted?s("span",{staticClass:"count-text count-deleted"},[e._v(e._s(t.deleted))]):s("span",[e._v("-")])])])]}))]}}],null,!1,639342335)}):e._e()],1)},es=[],ts=s("2909"),ss=(s("a630"),{name:"ModelsTab",components:{DetailsTable:ft,HighlightedCode:At,PrettyPrint:ut,ShortenedText:H,StackTrace:z},props:["active"],data:function(){return{selectedModelsTab:"actions",actionsFilter:new Ct([{tag:"model"},{tag:"action"},{tag:"file",map:function(e){return e.shortPath}}]),countsFilter:new Ct([{tag:"model"},{tag:"retrieved",type:"number"},{tag:"created",type:"number"},{tag:"updated",type:"number"},{tag:"deleted",type:"number"}])}},computed:{totals:function(){return{retrieved:Object.values(this.$request.modelsRetrieved).reduce((function(e,t){return e+t}),0),created:Object.values(this.$request.modelsCreated).reduce((function(e,t){return e+t}),0),updated:Object.values(this.$request.modelsUpdated).reduce((function(e,t){return e+t}),0),deleted:Object.values(this.$request.modelsDeleted).reduce((function(e,t){return e+t}),0)}},activeModelsTab:function(){return"actions"!=this.selectedModelsTab||this.$request.modelsActions.length?this.selectedModelsTab:"models"},modelsCounts:function(){var e=this,t=new Set([].concat(Object(ts["a"])(Object.keys(this.$request.modelsRetrieved)),Object(ts["a"])(Object.keys(this.$request.modelsCreated)),Object(ts["a"])(Object.keys(this.$request.modelsUpdated)),Object(ts["a"])(Object.keys(this.$request.modelsDeleted))));return Array.from(t).map((function(t){return{model:t,shortModel:t.split("\\").pop(),retrieved:e.$request.modelsRetrieved[t],created:e.$request.modelsCreated[t],updated:e.$request.modelsUpdated[t],deleted:e.$request.modelsDeleted[t]}}))}},methods:{isTabActive:function(e){if(""==e)return this.selectedModelsTab==e},showTab:function(e){this.selectedModelsTab=e}}}),as=ss,is=(s("d3ec"),Object(f["a"])(as,Yt,es,!1,null,null,null)),ns=is.exports,rs=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("details-table",{staticClass:"notifications-notifications",attrs:{title:"Notifications",icon:"mail",columns:e.columns,items:e.$request.notifications,filter:e.filter,"filter-example":'"User Registration" to:<EMAIL>'},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return[e._l(a,(function(t,a){return[s("tr",{key:e.$request.id+"-notifications-"+a},[e.columns.includes("Type")?s("td",[e._v(e._s(e._f("title")(t.type)))]):e._e(),s("td",[e._v(e._s(e._f("join")(t.to,", ")))]),s("td",{staticClass:"notification-subject"},[s("div",{staticClass:"subject-content"},[s("div",{staticClass:"content-text"},[e._v(" "+e._s(t.subject)+" ")]),s("stack-trace",{staticClass:"content-trace",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",{staticClass:"notification-actions"},[t.content?s("a",{attrs:{href:"#",title:"Show message"},on:{click:function(s){s.preventDefault(),e.showNotification=t}}},[s("icon",{attrs:{name:"search"}})],1):e._e(),s("a",{attrs:{href:"#",title:"Show details"},on:{click:function(e){e.preventDefault(),t.isShowingDetails=!t.isShowingDetails}}},[s("icon",{attrs:{name:t.isShowingDetails?"chevron-up":"chevron-down"}})],1)])]),s("tr",{directives:[{name:"show",rawName:"v-show",value:t.isShowingDetails,expression:"notification.isShowingDetails"}],key:e.$request.id+"-notifications-details-"+a,staticClass:"notifications-details"},[s("td",{attrs:{colspan:e.columns.length}},[s("div",{staticClass:"details-row"},[t.to?s("div",{staticClass:"row-group"},[s("h4",[e._v("To")]),s("span",[e._v(e._s(e._f("join")(t.to,", ")))])]):e._e(),t.data.cc?s("div",{staticClass:"row-group"},[s("h4",[e._v("CC")]),s("span",[e._v(e._s(e._f("join")(t.data.cc,", ")))])]):e._e(),t.data.bcc?s("div",{staticClass:"row-group"},[s("h4",[e._v("BCC")]),s("span",[e._v(e._s(e._f("join")(t.data.bcc,", ")))])]):e._e(),t.from?s("div",{staticClass:"row-group"},[s("h4",[e._v("From")]),s("span",[e._v(e._s(e._f("join")(t.from,", ")))])]):e._e(),t.data.replyTo?s("div",{staticClass:"row-group"},[s("h4",[e._v("Reply To")]),s("span",[e._v(e._s(e._f("join")(t.data.replyTo,", ")))])]):e._e()]),s("div",{staticClass:"details-row"},[s("div",{staticClass:"row-group"},[s("h4",[e._v("Subject")]),s("pretty-print",{attrs:{data:t.subject}})],1)]),e._l(e.additionalData(t.data),(function(t,a){return t?s("div",{staticClass:"details-row"},[s("div",{staticClass:"row-group"},[s("h4",[e._v(e._s(e._f("title")(a)))]),s("pretty-print",{attrs:{data:t}})],1)]):e._e()}))],2)])]}))]}}])}),s("message-modal",{attrs:{message:e.showNotification},on:{"update:message":function(t){e.showNotification=t}}})],1)},os=[],ls=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("modal",{attrs:{icon:"mail",title:"Message",shown:e.messageLocal},on:{"update:shown":function(t){e.messageLocal=t}}},[s("div",{staticClass:"email-message"},[s("div",{staticClass:"message-info"},[e.message?s("div",{staticClass:"info-row"},[s("span",{staticClass:"row-label"},[e._v("To: ")]),s("span",{staticClass:"row-value"},[e._v(e._s(e.message.to.join(", ")))])]):e._e(),e.message?s("div",{staticClass:"info-row"},[s("span",{staticClass:"row-label"},[e._v("Subject: ")]),s("span",{staticClass:"row-value"},[e._v(e._s(e.message.subject))])]):e._e()]),s("div",{staticClass:"message-content"},[s("iframe",{ref:"content"})])])])},cs=[],us={name:"EmailMessageModal",components:{Modal:p},props:["message"],computed:{messageLocal:{get:function(){return this.message},set:function(e){this.$emit("update:message",e)}}},watch:{message:function(e){if(e){var t=this.$refs.content.contentWindow.document;t.open(),t.write(this.message.content),t.close()}}}},ds=us,hs=(s("337f"),Object(f["a"])(ds,ls,cs,!1,null,null,null)),vs=hs.exports,fs=s("57b8"),ms=s.n(fs),ps={name:"notificationsTab",components:{DetailsTable:ft,DetailsTableFilterToggle:Xe,MessageModal:vs,PrettyPrint:ut,StackTrace:z},props:["active"],data:function(){return{filter:new Ct([{tag:"to"}]),showNotification:null}},computed:{columns:function(){var e=["To","Subject",""],t=new Set(this.$request.notifications.map((function(e){return e.type}))).size>1;return t&&e.splice(1,0,"Type"),e}},methods:{additionalData:function(e){return ms()(e,["cc","bcc","replyTo"])}}},gs=ps,bs=(s("5518"),Object(f["a"])(gs,rs,os,!1,null,null,null)),ws=bs.exports,_s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("div",{staticClass:"command-output",domProps:{innerHTML:e._s(e.formattedOutput)}})])},ys=[],Cs=s("61ab"),qs=s.n(Cs),$s={name:"OutputTab",props:["active"],computed:{formattedOutput:function(){return this.ansiToHtml.toHtml(this.$request.commandOutput||"")}},created:function(){this.ansiToHtml=new qs.a({fg:"#c7c7c7",bg:"#000000",escapeXML:!0,colors:{0:"#000000",1:"#c91b00",2:"#00c200",3:"#c7c400",4:"#0225c7",5:"#c930c7",6:"#00c5c7",7:"#c7c7c7",8:"#676767",9:"#ff6d67",10:"#5ff967",11:"#fefb67",12:"#6871ff",13:"#ff76ff",14:"#5ffdff",15:"#feffff"}})}},ks=$s,xs=(s("288a"),Object(f["a"])(ks,_s,ys,!1,null,null,null)),Ts=xs.exports,Ss=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("div",{staticClass:"counters-row performance-metrics"},[e.$request.responseDurationRounded?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.responseDurationRounded)+" ms")]),s("div",{staticClass:"counter-title"},[e._v("Response time")])]):e._e(),e.$request.memoryUsage?s("div",{staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(e.$request.memoryUsageFormatted))]),s("div",{staticClass:"counter-title"},[e._v("Memory")])]):e._e(),s("div",{staticClass:"counters-group right-aligned"},e._l(e.$request.performanceMetrics,(function(t,a){return s("div",{key:e.$request.id+"-"+a,staticClass:"counter performance-chart-legend"},[s("div",{staticClass:"counter-value"},[e._v(e._s(t.value)+" ms")]),s("div",{staticClass:"counter-title has-mark",class:"mark-"+t.color},[e._v(e._s(t.name))])])})),0)]),s("performance-chart",{attrs:{metrics:e.$request.performanceMetrics}}),s("div",{attrs:{tabs:"performance"}},[s("div",{staticClass:"performance-tabs"},[e.databaseSlowQueries.length||e.performanceIssues.length?s("a",{staticClass:"performance-tab",class:{active:e.isTabActive("issues")},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showTab("issues")}}},[s("icon",{attrs:{name:"alert-triangle"}}),e._v(" Issues ")],1):e._e(),s("a",{staticClass:"performance-tab",class:{active:e.isTabActive("timeline")},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showTab("timeline")}}},[s("icon",{attrs:{name:"pie-chart"}}),e._v(" Timeline ")],1),e.isClientSideTabAvailable?s("a",{staticClass:"performance-tab",class:{active:e.isTabActive("client-side")},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showTab("client-side")}}},[s("icon",{attrs:{name:"smile"}}),e._v(" Client-side ")],1):e._e(),s("a",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("profiler"),expression:"$platform.hasFeature('profiler')"}],staticClass:"performance-tab",class:{active:e.isTabActive("profiler")},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.showTab("profiler")}}},[s("icon",{attrs:{name:"clock"}}),e._v(" Profiler ")],1)]),s("performance-log",{directives:[{name:"show",rawName:"v-show",value:e.isTabActive("issues"),expression:"isTabActive('issues')"}],attrs:{issues:e.performanceIssues,"slow-queries":e.databaseSlowQueries}}),s("timeline",{directives:[{name:"show",rawName:"v-show",value:e.isTabActive("timeline"),expression:"isTabActive('timeline')"}],attrs:{name:"performance",timeline:e.$request.timeline,tags:e.timelineTags}}),s("performance-client-side",{directives:[{name:"show",rawName:"v-show",value:e.isTabActive("client-side"),expression:"isTabActive('client-side')"}],attrs:{metrics:e.$request.clientMetrics,vitals:e.$request.webVitals}}),s("profiler",{directives:[{name:"show",rawName:"v-show",value:e.isTabActive("profiler"),expression:"isTabActive('profiler')"}]})],1)],1)},js=[],Os=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"performance-chart"},e._l(e.sections,(function(e){return s("div",{staticClass:"chart-section",class:e.class,style:e.style})})),0)},Ps=[],Ds={name:"PerformanceChart",props:{metrics:{default:function(){return[]}}},data:function(){return{sections:[]}},methods:{refreshSections:function(){var e=this.$el.offsetWidth,t=this.metrics.reduce((function(e,t){return e+t.value}),0);this.sections=this.metrics.map((function(s){return{class:"section-".concat(s.color),style:"width: ".concat(e*s.value/t,"px")}}))}},mounted:function(){var e=this;this.refreshSections(),this.resizeObserver=new ResizeObserver(Ie()((function(t){return e.refreshSections()}),10)),this.resizeObserver.observe(this.$el)},watch:{metrics:function(){this.refreshSections()}}},Rs=Ds,Es=(s("6ff6"),Object(f["a"])(Rs,Os,Ps,!1,null,null,null)),Ns=Es.exports,As=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"performance-client-side"},[e.metrics.filter((function(e){return e.value})).length?s("div",{staticClass:"counters-row performance-metrics"},[e._l(e.metrics.filter((function(e){return!e.dom})),(function(t){return t.value?s("div",{staticClass:"counter performance-chart-legend"},[s("div",{staticClass:"counter-value"},[e._v(e._s(t.value)+" ms")]),s("div",{staticClass:"counter-title",class:t.color?"has-mark mark-"+t.color:""},[e._v(e._s(t.name))])]):e._e()})),s("div",{staticClass:"counters-group right-aligned"},e._l(e.metrics.filter((function(e){return e.dom})),(function(t){return t.value?s("div",{staticClass:"counter performance-chart-legend"},[s("div",{staticClass:"counter-value"},[e._v(e._s(t.value)+" ms")]),s("div",{staticClass:"counter-title",class:t.color?"has-mark mark-"+t.color:""},[e._v(e._s(t.name))])]):e._e()})),0)],2):e._e(),e.metrics.filter((function(e){return e.value&&e.onChart})).length?s("performance-chart",{attrs:{metrics:e.metrics.filter((function(e){return e.onChart}))}}):e._e(),Object.values(e.vitals).filter((function(e){return e.value})).length?s("details-table",{staticClass:"performance-vitals",attrs:{title:"Vitals",icon:"activity",items:[]}},[s("template",{slot:"toolbar"},[s("div",{staticClass:"header-group"},[s("a",{staticClass:"header-item",class:{active:e.showVitalsInfo},attrs:{href:"#",title:"Show info"},on:{click:function(t){return t.preventDefault(),e.toggleVitalsInfo.apply(null,arguments)}}},[s("icon",{attrs:{name:"help-circle"}})],1)])]),s("template",{slot:"body"},[s("tr",[s("td",[s("div",{staticClass:"vitals-row"},[s("div",{staticClass:"vitals-metric"},[s("div",{staticClass:"metric-name"},[e._v("Time To First Byte")]),e.vitals.ttfb.available?s("div",{staticClass:"metric-value",class:"value-"+e.vitals.ttfb.score},[e._v(" "+e._s(e._f("round")(e.vitals.ttfb.value))+" ms ")]):s("div",{staticClass:"metric-value value-unavailable"},[e._v("—")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showVitalsInfo,expression:"showVitalsInfo"}],staticClass:"metric-info"},[e._v(" Time at which your server sends a response. "),s("a",{attrs:{href:"https://web.dev/time-to-first-byte/",target:"_blank"}},[e._v("Learn more")])])]),s("div",{staticClass:"vitals-metric"},[s("div",{staticClass:"metric-name"},[e._v("First Input Delay")]),e.vitals.fid.available?s("div",{staticClass:"metric-value",class:"value-"+e.vitals.fid.score},[e._v(" "+e._s(e._f("round")(e.vitals.fid.value))+" ms ")]):s("div",{staticClass:"metric-value value-unavailable"},[e._v("—")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showVitalsInfo,expression:"showVitalsInfo"}],staticClass:"metric-info"},[e._v(" Time from when a user first interacts with a page to the time when the browser is actually able to respond to that interaction. "),s("a",{attrs:{href:"https://web.dev/fid/",target:"_blank"}},[e._v("Learn more")])])])])])]),s("tr",[s("td",[s("div",{staticClass:"vitals-row"},[s("div",{staticClass:"vitals-metric"},[s("div",{staticClass:"metric-name"},[e._v("First Contentful Paint")]),e.vitals.fcp.available?s("div",{staticClass:"metric-value",class:"value-"+e.vitals.fcp.score},[e._v(" "+e._s(e._f("round")(e.vitals.fcp.value))+" ms ")]):s("div",{staticClass:"metric-value value-unavailable"},[e._v("—")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showVitalsInfo,expression:"showVitalsInfo"}],staticClass:"metric-info"},[e._v(" First Contentful Paint marks the time at which the first text or image is painted. "),s("a",{attrs:{href:"https://web.dev/first-contentful-paint/",target:"_blank"}},[e._v("Learn more")])])]),s("div",{staticClass:"vitals-metric"},[s("div",{staticClass:"metric-name"},[e._v("Largest Contentful Paint")]),e.vitals.lcp.available?s("div",{staticClass:"metric-value",class:"value-"+e.vitals.lcp.score},[e._v(" "+e._s(e._f("round")(e.vitals.lcp.value))+" ms ")]):s("div",{staticClass:"metric-value value-unavailable"},[e._v("—")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showVitalsInfo,expression:"showVitalsInfo"}],staticClass:"metric-info"},[e._v(" Largest Contentful Paint marks the time at which the largest text or image is painted. "),s("a",{attrs:{href:"https://web.dev/lighthouse-largest-contentful-paint/",target:"_blank"}},[e._v("Learn more")])])])])])]),s("tr",[s("td",[s("div",{staticClass:"vitals-row"},[s("div",{staticClass:"vitals-metric"},[s("div",{staticClass:"metric-name"},[e._v("Cumulative Layout Shift")]),e.vitals.cls.available?s("div",{staticClass:"metric-value",class:"value-"+e.vitals.cls.score},[e._v(" "+e._s(e._f("round")(e.vitals.cls.value))+" ms ")]):s("div",{staticClass:"metric-value value-unavailable"},[e._v("—")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showVitalsInfo,expression:"showVitalsInfo"}],staticClass:"metric-info"},[e._v(" Cumulative Layout Shift measures the movement of visible elements within the viewport. "),s("a",{attrs:{href:"https://web.dev/cls/",target:"_blank"}},[e._v("Learn more")])])]),s("div",{staticClass:"vitals-metric"},[s("div",{staticClass:"metric-name"},[e._v("Speed Index")]),e.vitals.si.available?s("div",{staticClass:"metric-value",class:"value-"+e.vitals.si.score},[e._v(" "+e._s(e._f("round")(e.vitals.si.value))+" ms ")]):s("div",{staticClass:"metric-value value-unavailable"},[e._v("—")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showVitalsInfo,expression:"showVitalsInfo"}],staticClass:"metric-info"},[e._v(" Speed Index shows how quickly the contents of a page are visibly populated. "),s("a",{attrs:{href:"https://web.dev/speed-index/",target:"_blank"}},[e._v("Learn more")])])])])])])])],2):e._e()],1)},Is=[],Ms={name:"PerformanceClientSide",components:{DetailsTable:ft,PerformanceChart:Ns},props:["metrics","vitals"],computed:{showVitalsInfo:function(){return this.$settings.global.performanceVitalsInfoShown}},methods:{toggleVitalsInfo:function(){this.$settings.global.performanceVitalsInfoShown=!this.$settings.global.performanceVitalsInfoShown,this.$settings.save()}}},Ls=Ms,Qs=(s("5a57"),Object(f["a"])(Ls,As,Is,!1,null,null,null)),Us=Qs.exports,Fs=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"performance-log"},[e.issues.length?s("details-table",{attrs:{title:"Performance issues",icon:"alert-triangle",badge:e.issues.length,columns:["Message"],items:e.issues,filter:e.performanceLogFilter,"filter-example":"query failed file:Controller.php time:>13:08:29","no-table-head":!0},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a,staticClass:"log-row"},[s("td",[s("div",{staticClass:"log-message"},[s("div",{staticClass:"log-message-content"},[s("pretty-print",{attrs:{data:t.message}}),s("div",{directives:[{name:"show",rawName:"v-show",value:t.context&&t.context.length,expression:"message.context && message.context.length"}]},[s("pretty-print",{attrs:{data:t.context}})],1)],1),s("stack-trace",{staticClass:"log-message-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)])])}))}}],null,!1,938518525)}):e._e(),e.slowQueries.length?s("details-table",{attrs:{title:"Slow database queries",icon:"database",badge:e.slowQueries.length,columns:e.databaseSlowQueriesColumns,items:e.slowQueries,filter:e.databaseSlowQueriesFilter,"filter-example":"where request_id model:request type:select file:Controller.php duration:>100"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[s("td",[s("shortened-text",{attrs:{full:t.model}},[e._v(e._s(t.shortModel))])],1),e.databaseSlowQueriesColumns.includes("Connection")?s("td",[e._v(e._s(t.connection))]):e._e(),s("td",[s("div",{staticClass:"database-query"},[s("div",{staticClass:"database-query-content"},[s("highlighted-code",{attrs:{language:"sql",code:t.query}})],1),s("stack-trace",{staticClass:"database-query-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",{staticClass:"database-duration"},[e._v(e._s(t.duration)+" ms")])])}))}}],null,!1,453533967)}):e._e()],1)},Js=[],Hs={name:"PerformanceLog",components:{DetailsTable:ft,DetailsTableFilterToggle:Xe,HighlightedCode:At,PrettyPrint:ut,ShortenedText:H,StackTrace:z},props:["issues","slowQueries"],data:function(){return{databaseSlowQueriesFilter:new Ct([{tag:"model"},{tag:"type",apply:function(e,t){if(["select","update","insert","delete"].includes(t.toLowerCase()))return e.query.match(new RegExp("^".concat(t.toLowerCase()),"i"))}},{tag:"file",map:function(e){return e.shortPath}},{tag:"duration",type:"number"}]),performanceLogFilter:new Ct([{tag:"time",type:"date"},{tag:"file",map:function(e){return e.shortPath}}],(function(e){return e.message}))}},computed:{databaseSlowQueriesColumns:function(){var e=["Model","Query","Duration"],t=new Set(this.slowQueries.map((function(e){return e.connection}))).size>1;return t&&e.splice(1,0,"Connection"),e}}},Vs=Hs,Ws=Object(f["a"])(Vs,Fs,Js,!1,null,null,null),Bs=Ws.exports,zs=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("details-table",{staticClass:"profiler",attrs:{title:"Profiler",icon:"clock",columns:["Self","Inclusive","Function"],items:e.$profiler.functions,filter:e.filter,"per-page":100},scopedSlots:e._u([{key:"toolbar",fn:function(t){var a=t.filter;return[s("div",{staticClass:"header-group"},[s("label",{staticClass:"header-toggle"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.enabled,expression:"enabled"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.enabled)?e._i(e.enabled,null)>-1:e.enabled},on:{change:function(t){var s=e.enabled,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&(e.enabled=s.concat([n])):r>-1&&(e.enabled=s.slice(0,r).concat(s.slice(r+1)))}else e.enabled=i}}}),e._v(" Enabled ")])]),s("div",{staticClass:"header-group"},[s("a",{staticClass:"header-item item-text",class:{active:!e.$profiler.percentual},attrs:{href:"#",title:"Exact"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.$profiler.showPercentual(!1)}}},[0==e.$profiler.metric?s("span",[e._v("ms")]):e._e(),1==e.$profiler.metric?s("span",[e._v("kB")]):e._e()]),s("a",{staticClass:"header-item",class:{active:e.$profiler.percentual},attrs:{href:"#",title:"Percentual"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.$profiler.showPercentual()}}},[s("icon",{attrs:{name:"percent"}})],1)]),s("div",{staticClass:"header-group"},[s("a",{staticClass:"header-item item-text",class:{active:.5==e.$profiler.shownFraction},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.$profiler.setShownFraction(.5)}}},[e._v(" 50% ")]),s("a",{staticClass:"header-item item-text",class:{active:.9==e.$profiler.shownFraction},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.$profiler.setShownFraction(.9)}}},[e._v(" 90% ")]),s("a",{staticClass:"header-item item-text",class:{active:1==e.$profiler.shownFraction},attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.$profiler.setShownFraction(1)}}},[e._v(" 100% ")])]),s("div",{staticClass:"header-group"},[s("div",{staticClass:"header-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:a.input,expression:"filter.input"}],attrs:{type:"search",placeholder:"Search..."},domProps:{value:a.input},on:{input:function(t){t.target.composing||e.$set(a,"input",t.target.value)}}}),s("icon",{attrs:{name:"search"}})],1)])]}},{key:"body",fn:function(t){var a=t.items;return[e._l(e.filterXdebug(a),(function(t,a){return e.$profiler.ready?s("tr",{key:e.$request.id+"-"+a},[s("td",{staticClass:"profiler-metric"},[e._v(e._s(e.$profiler.formatMetric(t.self)))]),s("td",{staticClass:"profiler-metric"},[e._v(e._s(e.$profiler.formatMetric(t.inclusive)))]),s("td",{staticClass:"profiler-function"},[s("div",{staticClass:"profiler-function-name"},[e._v(" "+e._s(t.name)+" ")]),s("div",{staticClass:"profiler-path"},[s("shortened-text",{attrs:{full:t.fullPath}},[e._v(e._s(t.shortPath))])],1)])]):e._e()})),e.$profiler.loading||e.$profiler.parsing?s("tr",[s("td",{attrs:{colspan:"3"}},[s("div",{staticClass:"profiler-content"},[s("spinner",{attrs:{name:"fading-circle","no-fade-in":!0,color:"dark"==e.$settings.appearance?"#f27e02":"#258cdb"}}),s("p",[e._v(" "+e._s(e.$profiler.loading?"Loading profile...":"Processing profile...")+" ")])],1)])]):e._e(),e.$profiler.available?e._e():s("tr",[s("td",{attrs:{colspan:"3"}},[s("div",{staticClass:"profiler-content"},[s("h1",[e._v(" Profile is not present for current request. ")]),s("p",[e._v(" Profiling requires the Xdebug php extension."),s("br"),s("a",{attrs:{href:"https://underground.works/clockwork/#docs-xdebug-profiler",target:"_blank"}},[e._v("Read more about how to set up Xdebug")])]),s("p",{staticClass:"content-actions"},[s("a",{directives:[{name:"show",rawName:"v-show",value:!e.$profiler.isProfiling,expression:"! $profiler.isProfiling"}],staticClass:"button",attrs:{href:"#"},on:{click:function(t){return e.$profiler.enableProfiling()}}},[e._v(" Enable profiler ")]),s("a",{directives:[{name:"show",rawName:"v-show",value:e.$profiler.isProfiling,expression:"$profiler.isProfiling"}],staticClass:"button",attrs:{href:"#"},on:{click:function(t){return e.$profiler.disableProfiling()}}},[e._v(" Disable profiler ")])])])])])]}}])})],1)},Gs=[],Ks={name:"Profiler",components:{DetailsTable:ft,ShortenedText:H},data:function(){return{filter:function(){var e=new Ct([{tag:"model"},{tag:"file",map:function(e){return e.shortPath}},{tag:"self",type:"number"},{tag:"inclusive",type:"number"}],(function(e){return e.name}));return e.sortedBy="self",e.sortedDesc=!0,e}()}},computed:{enabled:{get:function(){return this.$profiler.isProfiling},set:function(e){e?this.$profiler.enableProfiling():this.$profiler.disableProfiling()}}},methods:{filterXdebug:function(e){return e?this.filter.filter(e):[]}}},Xs=Ks,Zs=(s("2081"),Object(f["a"])(Xs,zs,Gs,!1,null,null,null)),Ys=Zs.exports,ea=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{ref:"timeline",staticClass:"timeline",class:{"show-details":e.showDetails}},[s("details-table",{attrs:{title:"Timeline",icon:"pie-chart",columns:e.columns,items:e.presentedEvents,filter:e.filter,"no-table-head":!e.showDetails,"filter-example":"database query duration:>50","per-page":100},scopedSlots:e._u([{key:"toolbar",fn:function(t){var a=t.filter;return[s("div",{staticClass:"header-group"},[s("label",{staticClass:"header-toggle"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.condense,expression:"condense"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(e.condense)?e._i(e.condense,null)>-1:e.condense},on:{change:function(t){var s=e.condense,a=t.target,i=!!a.checked;if(Array.isArray(s)){var n=null,r=e._i(s,n);a.checked?r<0&&(e.condense=s.concat([n])):r>-1&&(e.condense=s.slice(0,r).concat(s.slice(r+1)))}else e.condense=i}}}),e._v(" Condense ")])]),e.availableTags.length?s("div",{staticClass:"header-group"},e._l(e.availableTags,(function(t){return s("a",{staticClass:"header-item",class:{active:e.hiddenTags&&!e.hiddenTags.includes(t.tag)},attrs:{href:"#",title:t.title},on:{click:function(s){return e.toggleTag(t.tag)}}},[s("icon",{attrs:{name:t.icon}})],1)})),0):e._e(),s("div",{staticClass:"header-group"},[s("div",{staticClass:"header-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:a.input,expression:"filter.input"}],attrs:{type:"search",placeholder:"Search..."},domProps:{value:a.input},on:{input:function(t){t.target.composing||e.$set(a,"input",t.target.value)}}}),s("icon",{attrs:{name:"search"}})],1)]),s("div",{staticClass:"header-group"},[s("a",{staticClass:"header-item",class:{active:e.showDetails},attrs:{href:"#",title:"Toggle details"},on:{click:function(t){return t.preventDefault(),e.toggleDetails.apply(null,arguments)}}},[s("icon",{attrs:{name:"list"}})],1)])]}},{key:"body",fn:function(t){var a=t.items;return[e._l(a,(function(t,a){return s("tr",[s("td",{staticClass:"timeline-chart"},[s("div",{staticClass:"chart-event-group popover-container",style:t.groupStyle,on:{click:function(t){return e.showPopover(a)}}},[s("div",{staticClass:"group-label",class:t.labelClass,style:t.labelStyle},[t.tags?s("span",{staticClass:"label-tags"},e._l(e.resolveTags(t.tags),(function(e){return s("span",[s("icon",{attrs:{name:e.icon,title:e.title}})],1)})),0):e._e(),e._v(" "+e._s(t.name)+" "),t.condensed?e._e():s("span",[e._v(e._s(e._f("formatTiming")(t.duration)))])]),e._l(t.events,(function(t,a){return s("div",{staticClass:"group-event",class:t.eventClass,style:t.eventStyle},[s("div",{staticClass:"event-bar"},e._l(t.childrenSections,(function(e){return s("div",{staticClass:"bar-light",style:e.style})})),0)])})),s("popover",{ref:"popovers",refInFor:!0,staticClass:"timeline-popover"},e._l(t.events,(function(t){return s("div",{staticClass:"popover-event",class:t.eventClass},[s("div",{staticClass:"event-header"},[s("h1",[e._v(e._s(t.name))]),s("div",{staticClass:"header-tags"},e._l(e.resolveTags(t.tags),(function(e){return s("span",[s("icon",{attrs:{name:e.icon,title:e.title}})],1)})),0)]),t.description!=t.name?s("div",{staticClass:"event-description"},[t.tags&&t.tags.indexOf("databaseQueries")>-1?s("highlighted-code",{attrs:{language:"sql",code:t.description}}):s("div",[e._v(e._s(t.description))])],1):e._e(),s("div",{staticClass:"event-timings"},[s("div",{staticClass:"timings-timing timing-total"},[s("div",{staticClass:"timing-value"},[e._v(" "+e._s(e._f("formatTiming")(t.duration))+" ")]),s("div",{staticClass:"timing-label"},[e._v(" Total ")])]),s("div",{staticClass:"timings-timing timing-self"},[s("div",{staticClass:"timing-value"},[e._v(" "+e._s(e._f("formatTiming")(t.durationSelf))+" ")]),s("div",{staticClass:"timing-label"},[e._v(" Self ")])]),s("div",{staticClass:"timings-timing timing-children"},[s("div",{staticClass:"timing-value"},[e._v(" "+e._s(e._f("formatTiming")(t.durationChildren,"ms","–"))+" ")]),s("div",{staticClass:"timing-label"},[e._v(" Children ")])])])])})),0)],2)]),s("td",{staticClass:"timeline-description"},[e._t("table-description",(function(){return[s("div",{staticClass:"description-content"},[t.tags&&t.tags.length?s("span",{staticClass:"description-tags"},e._l(e.resolveTags(t.tags),(function(e){return s("span",[s("icon",{attrs:{name:e.icon,title:e.title}})],1)})),0):e._e(),t.tags&&t.tags.indexOf("databaseQueries")>-1?s("highlighted-code",{attrs:{language:"sql",code:t.description}}):s("div",[e._v(e._s(t.description))])],1)]}),{item:t})],2),s("td",{staticClass:"timeline-timing timing-total"},[e._v(e._s(e._f("formatTiming")(t.duration)))]),s("td",{staticClass:"timeline-timing"},[e._v(e._s(e._f("formatTiming")(t.durationSelf)))]),s("td",{staticClass:"timeline-timing"},[e._v(e._s(e._f("formatTiming")(t.durationChildren,"ms",t.condensed?"":"–")))])])})),s("tr",{staticClass:"timeline-size-monitor"},[s("td",{ref:"timelineChart",staticClass:"timeline-graph"}),s("td",{staticClass:"timeline-description"}),s("td",{staticClass:"timeline-timing"}),s("td",{staticClass:"timeline-timing"}),s("td",{staticClass:"timeline-timing"})])]}}],null,!0)})],1)},ta=[],sa=(s("a4d3"),s("e01a"),{name:"Timeline",components:{DetailsTable:ft,HighlightedCode:At,Popover:M},props:{name:{},timeline:{},tags:{default:function(){return[]}}},data:function(){return{condense:void 0,showDetails:!1,hiddenTags:void 0,presentedEvents:[],filter:new Ct([{tag:"duration",type:"number"}],(function(e){return e.description}))}},computed:{availableTags:function(){var e=this;return this.tags.filter((function(t){return e.timeline.events.find((function(e){return e.tags&&e.tags.includes(t.tag)}))}))},columns:function(){return this.showDetails?[{name:" ",sortBy:"start",class:"timeline-chart"},{name:"Event",sortBy:"name",class:"timeline-description"},{name:"Total",sortBy:"duration",class:"timeline-timing"},{name:"Self",sortBy:"durationSelf",class:"timeline-timing"},{name:"Child",sortBy:"durationChild",class:"timeline-timing"}]:[]}},methods:{toggleTag:function(e){this.hiddenTags.includes(e)?this.hiddenTags=this.hiddenTags.filter((function(t){return t!=e})):this.hiddenTags.push(e)},resolveTags:function(e){var t=this;return e.map((function(e){return t.tags.find((function(t){return t.tag==e}))})).filter((function(e){return e}))},toggleDetails:function(){this.showDetails=!this.showDetails},showPopover:function(e){this.$refs.popovers[e].toggle()},refreshEvents:function(e){var t=this;return Object(te["a"])(regeneratorRuntime.mark((function e(){var s,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.timeline&&t.$refs.timelineChart){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$nextTick();case 4:if(s=t.$refs.timelineChart.offsetWidth-16,!(s<=0)){e.next=7;break}return e.abrupt("return");case 7:a=t.timeline.filter(t.filter,t.hiddenTags),t.condense&&(a=a.condense()),t.presentedEvents=a.present(s);case 10:case"end":return e.stop()}}),e)})))()},observeResizing:function(){var e=this,t=this.$refs.timeline.offsetWidth;this.resizeObserver=new ResizeObserver(Ie()((function(s){var i=Object(a["a"])(s,1),n=i[0];t!=n.contentRect.width&&(t=n.contentRect.width,e.refreshEvents(n))}),10)),this.resizeObserver.observe(this.$refs.timeline)}},filters:{formatTiming:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ms",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return null===e||void 0===e?s:e<=0||e>1?"".concat(Math.round(e)," ").concat(t):"<1 ".concat(t)}},watch:{condense:function(e,t){void 0!==t&&(this.refreshEvents(),this.$settings.global.timelineCondensed[this.name]=this.condense,this.$settings.save())},hiddenTags:function(e,t){void 0!==t&&(this.refreshEvents(),this.$settings.global.timelineHiddenTags[this.name]=this.hiddenTags,this.$settings.save())},showDetails:function(){this.refreshEvents()},timeline:function(){this.refreshEvents()}},mounted:function(){var e=this;return Object(te["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.condense=e.$settings.global.timelineCondensed[e.name]||!1,e.hiddenTags=e.$settings.global.timelineHiddenTags[e.name]||[],t.next=4,e.refreshEvents();case 4:e.observeResizing();case 5:case"end":return t.stop()}}),t)})))()}}),aa=sa,ia=(s("8161"),Object(f["a"])(aa,ea,ta,!1,null,null,null)),na=ia.exports,ra=s("e0e5"),oa=s.n(ra),la={name:"PerformanceTab",components:{PerformanceChart:Ns,PerformanceClientSide:Us,PerformanceLog:Bs,Profiler:Ys,Timeline:na},props:["active"],data:function(){return{selectedPerformanceTab:null,timelineTags:[{tag:"events",icon:"zap",title:"Events"},{tag:"databaseQueries",icon:"database",title:"Database"},{tag:"cacheQueries",icon:"paperclip",title:"Cache"},{tag:"redisCommands",icon:"layers",title:"Redis"},{tag:"queueJobs",icon:"clock",title:"Queue"},{tag:"views",icon:"image",title:"Views"},{tag:"notifications",icon:"mail",title:"Notifications"}]}},computed:{activePerformanceTab:function(){var e=this.selectedPerformanceTab||"issues";return("issues"!=e||this.databaseSlowQueries.length||this.performanceIssues.length)&&("client-side"!=e||this.isClientSideTabAvailable)?e:"timeline"},databaseSlowQueries:function(){return this.$request.databaseQueries.filter((function(e){return e.tags.includes("slow")}))},isClientSideTabAvailable:function(){return this.$request.clientMetrics.filter((function(e){return e.value})).length||Object.values(this.$request.webVitals).filter((function(e){return e.value})).length},performanceIssues:function(){return this.$request.log.filter((function(e){var t;return null===(t=e.context)||void 0===t?void 0:t.performance})).map((function(e){return oa()({},e,{context:ms()(e.context,["performance","trace"])})}))}},methods:{isTabActive:function(e){return this.activePerformanceTab==e},showTab:function(e){this.selectedPerformanceTab=e,"profiler"==e&&this.$profiler.loadRequest(this.$request)},refreshRequest:function(){this.active&&this.$request&&(this.$platform.hasFeature("load-client-metrics")&&this.$request.loadClientMetrics(this.$requests),"profiler"==this.activePerformanceTab&&this.$profiler.loadRequest(this.$request))}},watch:{active:function(){this.refreshRequest()},$request:function(){this.refreshRequest()}}},ca=la,ua=(s("8e9b"),Object(f["a"])(ca,Ss,js,!1,null,null,null)),da=ua.exports,ha=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("details-table",{attrs:{title:"Commands",icon:"layers",columns:e.columns,items:e.$request.redisCommands,filter:e.filter,"filter-example":"command:zrange connection:eshop file:StatsController.php duration:>50"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[e.columns.includes("Connection")?s("td",[e._v(e._s(t.connection))]):e._e(),s("td",[e._v(e._s(t.command))]),s("td",[s("div",{staticClass:"database-query"},[s("div",{staticClass:"database-query-content"},[s("pretty-print",{attrs:{data:t.parameters}})],1),s("stack-trace",{staticClass:"database-query-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",{staticClass:"database-duration"},[e._v(e._s(t.duration)+" ms")])])}))}}])})],1)},va=[],fa={name:"RedisTab",components:{DetailsTable:ft,PrettyPrint:ut,StackTrace:z},props:["active"],data:function(){return{filter:new Ct([{tag:"connection"},{tag:"command"},{tag:"file",map:function(e){return e.shortPath}},{tag:"duration",type:"number"}])}},computed:{columns:function(){var e=["Command","Parameters","Duration"],t=new Set(this.$request.redisCommands.map((function(e){return e.connection}))).size>1;return t&&e.splice(0,0,"Connection"),e}}},ma=fa,pa=Object(f["a"])(ma,ha,va,!1,null,null,null),ga=pa.exports,ba=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("details-table",{attrs:{title:"Jobs",icon:"clock",columns:e.columns,items:e.queueJobs,filter:e.filter,"filter-example":"Underground.works name:GenerateInvoice queue:priority"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[e.columns.includes("Queue")?s("td",[e._v(e._s(t.queue))]):e._e(),e.columns.includes("Connection")?s("td",[e._v(e._s(t.connection))]):e._e(),s("td",[s("shortened-text",{attrs:{full:t.name}},[e._v(e._s(t.shortName))])],1),s("td",[s("div",{staticClass:"database-query"},[s("div",{staticClass:"database-query-content"},[s("pretty-print",{attrs:{data:t.data}})],1),t.maxTries||t.timeout?s("div",{staticClass:"job-options"},[t.maxTries?s("span",[e._v(e._s(t.maxTries)+" tries")]):e._e(),t.timeout?s("span",[e._v(e._s(t.timeout)+"s timeout")]):e._e()]):e._e(),s("stack-trace",{staticClass:"database-query-path",attrs:{trace:t.trace,file:t.file,line:t.line}})],1)]),s("td",[s("span",{class:{"job-status-text":!0,"is-success":t.request&&"done"==t.request.jobStatus,"is-error":t.request&&"failed"==t.request.jobStatus}},[e._v(" "+e._s(t.request?t.request.jobStatus:"waiting")+" ")])]),s("td",[t.request?s("a",{attrs:{href:"#",title:"Show details"},on:{click:function(s){return s.preventDefault(),e.showJob(t)}}},[s("icon",{attrs:{name:"search"}})],1):e._e()])])}))}}])})],1)},wa=[],_a={name:"QueueTab",components:{DetailsTable:ft,PrettyPrint:ut,ShortenedText:H,StackTrace:z},props:["active"],data:function(){return{filter:new Ct([{tag:"connection"},{tag:"queue"},{tag:"name"}]),jobRequests:{}}},computed:{columns:function(){var e=["Name","Data"],t=new Set(this.$request.queueJobs.map((function(e){return e.queue}))).size>1,s=new Set(this.$request.queueJobs.map((function(e){return e.connection}))).size>1;return t&&e.splice(0,0,"Queue"),s&&e.splice(0,0,"Connection"),e.concat(["Status",""])},queueJobs:function(){var e=this;return this.$request?this.$request.queueJobs.map((function(t){return oa()({request:e.jobRequests[t.id]},t)})):[]}},methods:{showJob:function(e){this.global.$request=this.$requests.findId(e.id)},loadQueueJobRequest:function(e){var t=arguments,s=this;return Object(te["a"])(regeneratorRuntime.mark((function a(){var i,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i=t.length>1&&void 0!==t[1]?t[1]:0,12!=i){a.next=3;break}return a.abrupt("return");case 3:if(!e.loadRequestTimeout){a.next=5;break}return a.abrupt("return");case 5:if(a.t0=s.$requests.findId(e.id),a.t0){a.next=10;break}return a.next=9,s.$requests.loadId(e.id,null,!1);case 9:a.t0=a.sent;case 10:if(n=a.t0,n){a.next=13;break}return a.abrupt("return",e.loadRequestTimeout=setTimeout((function(){e.loadRequestTimeout=null,s.loadQueueJobRequest(e,i+1)}),5e3));case 13:s.$set(s.jobRequests,e.id,n);case 14:case"end":return a.stop()}}),a)})))()}},watch:{active:function(e){var t=this;e&&this.$request.queueJobs.forEach((function(e){e.id&&t.loadQueueJobRequest(e)}))}}},ya=_a,Ca=(s("70b4"),Object(f["a"])(ya,ba,wa,!1,null,"51ab4b8e",null)),qa=Ca.exports,$a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("details-table",{attrs:{title:"Routes",icon:"map",columns:e.columns,items:e.$request.routes,filter:e.filter,"filter-example":"OrderController method:post uri:order"},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[s("td",[e._v(e._s(t.method))]),s("td",[e._v(e._s(t.uri))]),s("td",[e._v(e._s(t.action))]),e.columns.includes("Name")?s("td",[e._v(e._s(t.name))]):e._e(),e.columns.includes("Middleware")?s("td",e._l(t.middleware,(function(a,i){return s("span",[e._v(" "+e._s(a)+e._s(i==t.middleware.length-1?"":", ")+" ")])})),0):e._e(),e.columns.includes("Before")?s("td",[e._v(e._s(t.before))]):e._e(),e.columns.includes("After")?s("td",[e._v(e._s(t.after))]):e._e()])}))}}])})],1)},ka=[],xa={name:"RoutesTab",components:{DetailsTable:ft},props:["active"],data:function(){return{filter:new Ct([{tag:"method",apply:function(e,t){if(["get","post","put","delete","head","patch"].includes(t.toLowerCase()))return e.method.toLowerCase()==t.toLowerCase()}},{tag:"uri"}])}},computed:{columns:function(){var e=["Methods","URI","Action"];return this.$request.routes.some((function(e){return e.name}))&&e.push("Name"),this.$request.routes.some((function(e){return e.middleware}))&&e.push("Middleware"),this.$request.routes.some((function(e){return e.before}))&&e.push("Before"),this.$request.routes.some((function(e){return e.after}))&&e.push("After"),e}}},Ta=xa,Sa=Object(f["a"])(Ta,$a,ka,!1,null,null,null),ja=Sa.exports,Oa=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},e._l(e.userTab.sections,(function(t,a){return s("div",{key:e.$request.id+"-"+a},["counters"==t.showAs?s("div",{staticClass:"counters-row"},e._l(t.data,(function(t,a){return s("div",{key:e.$request.id+"-"+a,staticClass:"counter"},[s("div",{staticClass:"counter-value"},[e._v(e._s(t.value))]),s("div",{staticClass:"counter-title"},[e._v(e._s(t.key))])])})),0):e._e(),"table"==t.showAs?s("details-table",{attrs:{title:t.title,columns:t.data[0].map((function(e){return e.key})),items:t.data,filter:e.filters[a]},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t){return s("tr",e._l(t,(function(e){return s("td",[s("pretty-print",{attrs:{data:e.value}})],1)})),0)}))}}],null,!0)}):e._e()],1)})),0)},Pa=[],Da={name:"UserTab",components:{DetailsTable:ft,PrettyPrint:ut},props:["active","userTab"],data:function(){return{filters:[]}},watch:{userTab:{handler:function(e){this.filters=e.sections.map((function(e){if("table"==e.showAs)return new Ct(e.data[0].map((function(e){return{tag:e.key}})))}))},immediate:!0}}},Ra=Da,Ea=Object(f["a"])(Ra,Oa,Pa,!1,null,null,null),Na=Ea.exports,Aa=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}]},[s("timeline",{attrs:{name:"views",icon:"image",timeline:e.$request.viewsData},scopedSlots:e._u([{key:"table-description",fn:function(t){var a=t.item;return[s("div",{staticClass:"views-view-name"},[e._v(e._s(a.description))]),a.data?s("pretty-print",{attrs:{data:a.data}}):e._e()]}}])})],1)},Ia=[],Ma={name:"ViewsTab",components:{PrettyPrint:ut,Timeline:na},props:["active"]},La=Ma,Qa=(s("a5d3"),Object(f["a"])(La,Aa,Ia,!1,null,null,null)),Ua=Qa.exports,Fa=s("5817"),Ja=s.n(Fa),Ha={name:"RequestDetails",components:{CreditsModal:_,DetailsRequest:x,MessagesOverlay:ce,SettingsModal:me,SharingModal:ye,SharingDeleteModal:Te,TabBar:Ue,CacheTab:xt,DatabaseTab:Qt,EventsTab:Wt,LogTab:Zt,ModelsTab:ns,NotificationsTab:ws,OutputTab:Ts,PerformanceTab:da,RedisTab:ga,QueueTab:qa,RoutesTab:ja,UserTab:Na,ViewsTab:Ua},computed:{tabs:function(){var e,t;return[{text:"Performance",name:"performance",icon:"activity",shown:!0},{text:"Log",name:"log",icon:"edit-2",shown:this.shownTabs.log},{text:"Events",name:"events",icon:"zap",shown:this.shownTabs.events},{text:"Models",name:"models",icon:"disc",shown:this.shownTabs.models},{text:"Database",name:"database",icon:"database",shown:this.shownTabs.database},{text:"Cache",name:"cache",icon:"paperclip",shown:this.shownTabs.cache},{text:"Redis",name:"redis",icon:"layers",shown:this.shownTabs.redis},{text:"Queue",name:"queue",icon:"clock",shown:this.shownTabs.queue},{text:"Views",name:"views",icon:"image",shown:this.shownTabs.views},{text:"Notifications",name:"notifications",icon:"mail",shown:this.shownTabs.notifications},{text:"Routes",name:"routes",icon:"map",shown:this.shownTabs.routes}].concat(null===(e=this.$request)||void 0===e||null===(t=e.userData)||void 0===t?void 0:t.map((function(e){return{text:e.title,name:"user-".concat(e.key),icon:"menu",shown:!0}}))).concat([{text:"Output",name:"output",icon:"terminal",shown:this.shownTabs.output}]).filter(Boolean)},activeTab:function(){if(this.$request)return!1===this.shownTabs[this.global.activeDetailsTab]?"performance":this.global.activeDetailsTab},shownTabs:function(){var e,t,s,a,i,n,r,o,l,c,u,d,h,v,f,m,p,g,b,w,_,y=this;return{log:(null===(e=this.$request)||void 0===e||null===(t=e.log)||void 0===t?void 0:t.length)>0,models:["modelsRetrieved","modelsCreated","modelsUpdated","modelsDeleted"].some((function(e){var t;return!Ja()(null===(t=y.$request)||void 0===t?void 0:t[e])}))||(null===(s=this.$request)||void 0===s?void 0:s.modelsActions.length)>0,database:(null===(a=this.$request)||void 0===a?void 0:a.databaseQueriesCount)>0||(null===(i=this.$request)||void 0===i||null===(n=i.databaseQueries)||void 0===n?void 0:n.length)>0,cache:["cacheReads","cacheHits","cacheWrites","cacheDeletes","cacheTime"].some((function(e){var t;return null===(t=y.$request)||void 0===t?void 0:t[e]}))||(null===(r=this.$request)||void 0===r?void 0:r.cacheQueries.length)>0,redis:(null===(o=this.$request)||void 0===o||null===(l=o.redisCommands)||void 0===l?void 0:l.length)>0,queue:(null===(c=this.$request)||void 0===c||null===(u=c.queueJobs)||void 0===u?void 0:u.length)>0,events:(null===(d=this.$request)||void 0===d||null===(h=d.events)||void 0===h?void 0:h.length)>0,views:(null===(v=this.$request)||void 0===v||null===(f=v.viewsData)||void 0===f?void 0:f.events.length)>0,notifications:(null===(m=this.$request)||void 0===m||null===(p=m.notifications)||void 0===p?void 0:p.length)>0,routes:(null===(g=this.$request)||void 0===g||null===(b=g.routes)||void 0===b?void 0:b.length)>0,output:(null===(w=this.$request)||void 0===w||null===(_=w.commandOutput)||void 0===_?void 0:_.length)>0}}},methods:{showTab:function(e){this.global.activeDetailsTab=e,this.global.showIncomingRequests=!1},toggleRequestsList:function(){this.$settings.global.requestsListCollapsed=!this.$settings.global.requestsListCollapsed,this.$settings.save()},toggleRequestSidebar:function(){this.$settings.global.requestSidebarCollapsed=!this.$settings.global.requestSidebarCollapsed,this.$settings.save()},toggleSettingsModal:function(){this.$settings.toggle()}}},Va=Ha,Wa=(s("0f8a"),Object(f["a"])(Va,r,o,!1,null,null,null)),Ba=Wa.exports,za=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"popover-viewport",class:{"request-sidebar":!0,large:e.$settings.global.requestsListCollapsed}},[s("parent-request"),e.$request?s("div",{staticClass:"sidebar-header"},[s("div",{staticClass:"header-info",attrs:{title:e.$request.tooltip}},[s("div",{staticClass:"info-main"},[e.$request.isCommand()?[s("span",{staticClass:"type-text"},[e._v("CMD")]),e._v(" "+e._s(e.$request.commandName)+" ")]:e.$request.isQueueJob()?[s("span",{staticClass:"type-text"},[e._v("QUEUE")]),e._v(" "+e._s(e.$request.jobName)+" ")]:e.$request.isTest()?[s("span",{staticClass:"type-text"},[e._v("TEST")]),e._v(" "+e._s(e.$request.testGroup)+" ")]:[e.$request.isAjax()?s("span",{staticClass:"type-text"},[e._v("AJAX")]):e._e(),s("span",{staticClass:"method-text"},[e._v(e._s(e.$request.method))]),e._v(" "+e._s(e.$request.uri)+" ")],e.$request&&e.$request.url?s("a",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.$request.url,expression:"$request.url",arg:"copy"}],staticClass:"info-copy",attrs:{href:"#",title:"Copy url"}},[s("icon",{attrs:{name:"link"}})],1):e._e()],2),s("div",{staticClass:"info-details"},[e.$request.isCommand()?[e._v(" "+e._s(e.$request.commandLine)+" ")]:e.$request.isQueueJob()?[e._v(" "+e._s(e.$request.jobDescription)+" ")]:e.$request.isTest()?[e._v(" "+e._s(e.$request.testName)+" ")]:[e._v(" "+e._s(e.$request.controller)+" ")]],2)]),e.$request.errorsCount?s("icon",{staticClass:"header-alert alert-errors",attrs:{name:"alert-circle"}}):e.$request.warningsCount?s("icon",{staticClass:"header-alert alert-warnings",attrs:{name:"alert-triangle"}}):e._e(),s("div",{staticClass:"header-status"},[e.$request.isCommand()?[s("span",{class:{"status-text":!0,"client-error":e.$request.isCommandWarning(),"server-error":e.$request.isCommandError()},attrs:{title:e.$request.commandExitCode}},[e._v(e._s(e.$request.commandExitCode))])]:e.$request.isQueueJob()?[s("span",{class:{"status-text":!0,"status-text-small":!0,"client-error":e.$request.isQueueJobWarning(),"server-error":e.$request.isQueueJobError()},attrs:{title:e.$request.jobStatus}},[e._v(e._s(e.$request.jobStatus))])]:e.$request.isTest()?[s("span",{class:{"status-text":!0,"status-text-small":!0,"client-error":e.$request.isTestWarning(),"server-error":e.$request.isTestError()},attrs:{title:e.$request.testStatus}},[e._v(e._s(e.$request.testStatus))])]:[s("span",{class:{"status-text":!0,"client-error":e.$request.isClientError(),"server-error":e.$request.isServerError()},attrs:{title:e.$request.responseStatus}},[e._v(e._s(e.$request.responseStatus))])]],2)],1):e._e(),s("exception-section"),s("div",{staticClass:"sidebar-content"},[e.$request&&e.$request.isCommand()?s("command-tab"):e._e(),e.$request&&e.$request.isQueueJob()?s("queue-job-tab"):e._e(),e.$request&&e.$request.isTest()?s("test-tab"):e.$request?s("request-tab"):e._e(),s("div",{staticClass:"content-actions"},[s("a",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("sharing"),expression:"$platform.hasFeature('sharing')"}],staticClass:"button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.$sharing.toggle()}}},[s("icon",{attrs:{name:"share"}}),e._v(" Share ")],1),s("a",{directives:[{name:"show",rawName:"v-show",value:e.$platform.hasFeature("delete-shared"),expression:"$platform.hasFeature('delete-shared')"}],staticClass:"button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.$sharing.toggleDelete()}}},[s("icon",{attrs:{name:"trash-2"}}),e._v(" Delete ")],1)]),s("div",{staticClass:"content-meta"},[e.$request&&e.$request.time?s("div",{staticClass:"meta-date"},[e._v(" "+e._s(e._f("date")(1e3*e.$request.time,"Y-MM-dd HH:mm:ss"))+" ")]):e._e(),e.$request?s("div",{staticClass:"meta-id"},[s("a",{attrs:{href:e.shareUrl}},[e._v(e._s(e.$request.id))])]):e._e()])],1)],1)},Ga=[],Ka=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"command-tab"},[s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.commandArgumentsMerged.length,expression:"$request.commandArgumentsMerged.length"}],attrs:{title:"Arguments",name:"arguments",items:e.$request.commandArgumentsMerged,"filter-example":'"Mike Jones" name:name'}}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.commandOptionsMerged.length,expression:"$request.commandOptionsMerged.length"}],attrs:{title:"Options",name:"options",items:e.$request.commandOptionsMerged,"filter-example":'"Mike Jones" name:name'}})],1)},Xa=[],Za=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"sidebar-section"},[s("div",{staticClass:"section-header"},[s("div",{staticClass:"header-title",on:{click:e.toggle}},[s("icon",{attrs:{name:e.expanded?"chevron-down":"chevron-up"}}),e._v(" "+e._s(e.title)+" ")],1),s("div",{staticClass:"header-group"},[e.expandedSearch?s("div",{staticClass:"header-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.filter.input,expression:"filter.input"}],ref:"searchInput",attrs:{type:"search",placeholder:"Search..."},domProps:{value:e.filter.input},on:{input:function(t){t.target.composing||e.$set(e.filter,"input",t.target.value)}}}),s("icon",{attrs:{name:"search"}})],1):s("a",{staticClass:"header-item",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.expandSearch.apply(null,arguments)}}},[s("icon",{attrs:{name:"search"}})],1)])]),e._t("content",(function(){return[s("div",{directives:[{name:"show",rawName:"v-show",value:e.expanded,expression:"expanded"}]},[e._t("above-table")],2),e._t("table",(function(){return[s("details-table",{directives:[{name:"show",rawName:"v-show",value:e.expanded,expression:"expanded"}],attrs:{columns:["Key","Value"],items:e.items,filter:e.filter,"filter-example":e.filterExample,"no-header":!0,"no-table-head":!0},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[s("td",{attrs:{colspan:"2"}},[s("div",{staticClass:"key"},[e._v(e._s(t.name))]),s("div",{staticClass:"value"},[s("pretty-print",{attrs:{data:t.value}})],1)])])}))}}])})]}),{items:e.items,filter:e.filter,filterExample:e.filterExample,expanded:e.expanded})]}),{expanded:e.expanded})],2)},Ya=[],ei={name:"SidebarSection",components:{DetailsTable:ft,PrettyPrint:ut},props:["title","name","filterExample","items"],data:function(){return{filter:new Ct([{tag:"name"}]),expandedSearch:!1}},computed:{expanded:function(){return!1!==this.$settings.global.requestSidebarCollapsedSections[this.name]}},methods:{toggle:function(){this.$settings.global.requestSidebarCollapsedSections[this.name]=!this.expanded,this.$settings.save()},expandSearch:function(){var e=this;this.expandedSearch=!0,this.$nextTick((function(){return e.$refs.searchInput.focus()}))}}},ti=ei,si=(s("101e"),Object(f["a"])(ti,Za,Ya,!1,null,null,null)),ai=si.exports,ii={name:"CommandTab",components:{SidebarSection:ai}},ni=ii,ri=(s("ef62"),Object(f["a"])(ni,Ka,Xa,!1,null,null,null)),oi=ri.exports,li=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"queue-job-tab"},[e.$request.jobPayload?s("sidebar-section",{attrs:{title:"Payload",name:"payload"},scopedSlots:e._u([{key:"content",fn:function(t){var a=t.expanded;return[s("div",{directives:[{name:"show",rawName:"v-show",value:a,expression:"expanded"}],staticClass:"data-value"},[s("pretty-print",{attrs:{data:e.$request.jobPayload,expanded:!0}})],1)]}}],null,!1,1651862303)}):e._e(),e.$request.jobQueue?s("sidebar-section",{attrs:{title:"Queue",name:"queue"},scopedSlots:e._u([{key:"content",fn:function(t){var a=t.expanded;return[s("div",{directives:[{name:"show",rawName:"v-show",value:a,expression:"expanded"}],staticClass:"data-value"},[e._v(" "+e._s(e.$request.jobQueue)+" ")])]}}],null,!1,3453129276)}):e._e(),e.$request.jobConnection?s("sidebar-section",{attrs:{title:"Connection",name:"connection"},scopedSlots:e._u([{key:"content",fn:function(t){var a=t.expanded;return[s("div",{directives:[{name:"show",rawName:"v-show",value:a,expression:"expanded"}],staticClass:"data-value"},[e._v(" "+e._s(e.$request.jobConnection)+" ")])]}}],null,!1,2188469499)}):e._e(),e.$request.jobOptions.length?s("sidebar-section",{attrs:{title:"Options",name:"options",items:e.$request.jobOptions,"filter-example":'"Mike Jones" name:name'}}):e._e()],1)},ci=[],ui={name:"QueueJobTab",components:{PrettyPrint:ut,SidebarSection:ai}},di=ui,hi=(s("6b0a"),Object(f["a"])(di,li,ci,!1,null,null,null)),vi=hi.exports,fi=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"request-tab"},[s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.headers.length,expression:"headers.length"}],attrs:{title:"Headers",name:"headers",items:e.headers,"filter-example":"text/html name:Accept"}}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.requestData,expression:"$request.requestData"}],attrs:{title:"Data",name:"data",items:e.$request.requestData,"filter-example":"420 name:price"},scopedSlots:e._u([e.$request.requestData instanceof Object?null:{key:"content",fn:function(t){var a=t.expanded;return[s("div",{directives:[{name:"show",rawName:"v-show",value:a,expression:"expanded"}],staticClass:"data-raw"},[e._v(" "+e._s(e.$request.requestData)+" ")])]}}],null,!0)}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.getData.length,expression:"$request.getData.length"}],attrs:{title:"GET data",name:"getData",items:e.$request.getData,"filter-example":"created_at name:orderBy"}}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.postData.length,expression:"$request.postData.length"}],attrs:{title:"POST data",name:"postData",items:e.$request.postData,"filter-example":'"Mike Jones" name:name'}}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.cookies.length,expression:"$request.cookies.length"}],attrs:{title:"Cookies",name:"cookies",items:e.$request.cookies,"filter-example":'"Mike Jones" name:name'}}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.middleware.length,expression:"$request.middleware.length"}],attrs:{title:"Middleware",name:"middleware",items:e.$request.middleware,"filter-example":"auth:admin"},scopedSlots:e._u([{key:"table",fn:function(t){var a=t.items,i=t.filter,n=t.filterExample,r=t.expanded;return[s("details-table",{directives:[{name:"show",rawName:"v-show",value:r,expression:"expanded"}],attrs:{columns:["Value"],items:a,filter:i,"filter-example":n,"no-header":!0,"no-table-head":!0},scopedSlots:e._u([{key:"header",fn:function(e){e.filter}},{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[s("td",{staticClass:"value"},[e._v(e._s(t))])])}))}}],null,!0)})]}}])}),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.$request.sessionData.length||e.$request.authenticatedUser,expression:"$request.sessionData.length || $request.authenticatedUser"}],attrs:{title:"Session",name:"session",items:e.$request.sessionData,"filter-example":"registration successful name:_token"}},[s("template",{slot:"above-table"},[e.$request.authenticatedUser?s("div",{staticClass:"session-user"},[s("icon",{attrs:{name:"user"}}),s("div",[e.$request.authenticatedUser.name&&e.$request.authenticatedUser.name.trim()?s("span",{staticClass:"name"},[e._v(e._s(e.$request.authenticatedUser.name))]):e._e(),s("span",{class:e.$request.authenticatedUser.name&&e.$request.authenticatedUser.name.trim()?"dimmed":""},[e._v(e._s(e.$request.authenticatedUser.username))])]),e.$request.authenticatedUser.email||e.$request.authenticatedUser.id?s("span",{staticClass:"session-user-details"},[e.$request.authenticatedUser.id?s("span",{staticClass:"dimmed"},[e._v("#"+e._s(e.$request.authenticatedUser.id))]):e._e()]):e._e()],1):e._e()])],2)],1)},mi=[],pi={name:"RequestTab",components:{DetailsTable:ft,SidebarSection:ai},computed:{headers:function(){return this.$request.cookies.length?this.$request.headers.filter((function(e){return"Cookie"!=e.name})):this.$request.headers}}},gi=pi,bi=(s("9a64"),Object(f["a"])(gi,fi,mi,!1,null,null,null)),wi=bi.exports,_i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"request-tab test-tab"},[e.$request.testStatusMessage?s("div",{staticClass:"test-status-message",class:{error:e.$request.isTestError(),warning:e.$request.isTestWarning()}},[e._v(" "+e._s(e.$request.testStatusMessage)+" ")]):e._e(),s("sidebar-section",{directives:[{name:"show",rawName:"v-show",value:e.asserts.length,expression:"asserts.length"}],attrs:{title:"Asserts",name:"asserts",items:e.asserts,"filter-example":"text/html name:Accept"},scopedSlots:e._u([{key:"table",fn:function(t){var a=t.items,i=t.filter,n=t.filterExample,r=t.expanded;return[s("details-table",{directives:[{name:"show",rawName:"v-show",value:r,expression:"expanded"}],attrs:{columns:["Assert"],items:a,filter:i,"filter-example":n,"no-header":!0,"no-table-head":!0},scopedSlots:e._u([{key:"body",fn:function(t){var a=t.items;return e._l(a,(function(t,a){return s("tr",{key:e.$request.id+"-"+a},[s("td",{staticClass:"value test-assert"},[s("div",{staticClass:"assert-name"},[s("div",{staticClass:"assert-name-content",class:{"assert-failed":!t.passed}},[s("span",{staticClass:"assert-name-text"},[e._v(e._s(t.name))])]),s("div",{staticClass:"assert-name-trace"},[s("stack-trace",{attrs:{trace:t.trace}})],1)]),s("pretty-print",{attrs:{data:t.arguments}})],1)])}))}}],null,!0)})]}}])})],1)},yi=[],Ci={name:"TestTab",components:{DetailsTable:ft,PrettyPrint:ut,SidebarSection:ai,StackTrace:z},computed:{asserts:function(){return this.$request.testAsserts.reverse()}}},qi=Ci,$i=(s("86a4"),Object(f["a"])(qi,_i,yi,!1,null,null,null)),ki=$i.exports,xi={name:"RequestSidebar",components:{CommandTab:oi,ExceptionSection:Z,ParentRequest:ne,QueueJobTab:vi,RequestTab:wi,TestTab:ki},computed:{shareUrl:function(){return this.$request?"".concat(window.location.origin,"#").concat(this.$request.id):"#"}}},Ti=xi,Si=(s("313b"),Object(f["a"])(Ti,za,Ga,!1,null,null,null)),ji=Si.exports,Oi=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"popover-viewport",class:{"split-view-pane split-view-requests":!0,large:e.$settings.global.requestSidebarCollapsed}},[s("div",{staticClass:"requests-header",attrs:{id:"requests-header"}},[s("table",[s("thead",[s("tr",[e._m(0),s("th",{staticClass:"status"},[e._v(" Status ")]),s("th",{staticClass:"duration"},[e._v(" Time"),s("br"),e.showDatabaseTime?s("small",[e._v("Database")]):e._e()])])])])]),s("div",{ref:"requestsContainer",staticClass:"requests-container"},[s("div",{staticClass:"requests-content"},[s("div",{ref:"contentAbove",staticClass:"content-above"},[s("div",{staticClass:"requests-search"},[s("input",{directives:[{name:"model",rawName:"v-model",value:e.$requestsSearch.input,expression:"$requestsSearch.input"}],attrs:{type:"search",placeholder:"Search..."},domProps:{value:e.$requestsSearch.input},on:{input:[function(t){t.target.composing||e.$set(e.$requestsSearch,"input",t.target.value)},e.$requestsSearch.searchDebounced]}}),s("icon",{attrs:{name:"search"}})],1),s("a",{staticClass:"button",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.loadMoreRequests.apply(null,arguments)}}},[e._v(" "+e._s(e.loadingMoreRequests?"Loading...":"Load more")+" ")])]),s("div",{ref:"requestsTable",staticClass:"requests-table"},[s("table",{attrs:{id:"requests"}},e._l(e.requests,(function(t){return s("tr",{key:t.id,class:{selected:e.isActive(t.id)},on:{click:function(s){return e.showRequest(t)}}},[s("td",{staticClass:"controller",attrs:{title:t.tooltip}},[s("big",[t.errorsCount?s("icon",{staticClass:"request-alert alert-errors",attrs:{name:"alert-circle"}}):t.warningsCount?s("icon",{staticClass:"request-alert alert-warnings",attrs:{name:"alert-triangle"}}):e._e(),t.isCommand()?[s("span",{staticClass:"type-text"},[e._v("CMD")]),e._v(" "+e._s(t.commandName)+" ")]:t.isQueueJob()?[s("span",{staticClass:"type-text"},[e._v("QUEUE")]),e._v(" "+e._s(t.jobName)+" ")]:t.isTest()?[s("span",{staticClass:"type-text"},[e._v("TEST")]),e._v(" "+e._s(t.testGroup)+" ")]:[t.isAjax()?s("span",{staticClass:"type-text"},[e._v("AJAX")]):e._e(),s("span",{staticClass:"method-text"},[e._v(e._s(t.method))]),e._v(" "+e._s(t.uri)+" ")]],2),s("br"),t.isCommand()?[s("small",[e._v(e._s(t.commandLine))])]:t.isQueueJob()?[s("small",[e._v(e._s(t.jobDescription))])]:t.isTest()?[s("small",[e._v(e._s(t.testName))])]:[e.$settings.global.requestSidebarCollapsed?s("small",[e._v(e._s(t.controller))]):s("small",[e._v(e._s(e._f("shortClass")(t.controller)))])]],2),t.isCommand()?[s("td",{staticClass:"status",attrs:{title:t.commandExitCode}},[s("span",{class:{"status-text":!0,"client-error":t.isCommandWarning(),"server-error":t.isCommandError()}},[e._v(e._s(t.commandExitCode))])])]:t.isQueueJob()?[s("td",{staticClass:"status",attrs:{title:t.jobStatus}},[s("span",{class:{"status-text":!0,"status-text-small":!0,"client-error":t.isQueueJobWarning(),"server-error":t.isQueueJobError()}},[e._v(e._s(t.jobStatus))])])]:t.isTest()?[s("td",{staticClass:"status",attrs:{title:t.testStatus}},[s("span",{class:{"status-text":!0,"status-text-small":!0,"client-error":t.isTestWarning(),"server-error":t.isTestError()}},[e._v(e._s(t.testStatus))])])]:[s("td",{staticClass:"status",attrs:{title:t.responseStatus}},[s("span",{class:{"status-text":!0,"client-error":t.isClientError(),"server-error":t.isServerError()}},[e._v(e._s(t.responseStatus))])])],s("td",{staticClass:"duration",attrs:{title:t.responseDurationRounded+" ms ("+t.databaseDurationRounded+" ms)"}},[e._v(" "+e._s(t.responseDurationRounded)+" ms"),s("br"),e.showDatabaseTime?s("small",[e._v(e._s(t.databaseDurationRounded)+" ms")]):e._e()])],2)})),0)]),s("a",{staticClass:"button requests-clear",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.clear.apply(null,arguments)}}},[s("icon",{attrs:{name:"slash"}}),e._v(" Clear ")],1)])])])},Pi=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("th",{staticClass:"controller"},[e._v(" Path"),s("br"),s("small",[e._v("Controller")])])}],Di={name:"RequestsList",components:{},data:function(){return{loadingMoreRequests:!1}},computed:{requests:function(){var e=this.$requests.items;return this.$settings.global.hideCommandTypeRequests&&(e=e.filter((function(e){return"command"!=e.type}))),this.$settings.global.hideQueueJobTypeRequests&&(e=e.filter((function(e){return"queue-job"!=e.type}))),this.$settings.global.hideTestTypeRequests&&(e=e.filter((function(e){return"test"!=e.type}))),e},showDatabaseTime:function(){return this.requests.find((function(e){return e.databaseDuration>0}))}},mounted:function(){this.$refs.requestsContainer.scrollTop=this.$refs.requestsTable.offsetTop},methods:{isActive:function(e){var t;return(null===(t=this.$request)||void 0===t?void 0:t.id)==e},showRequest:function(e){this.global.$request=e},loadMoreRequests:function(){var e=this;this.loadingMoreRequests=!0,this.$requests.loadPrevious(10).then((function(){e.loadingMoreRequests=!1}))},shouldShowFirstRequest:function(){return!this.$settings.global.preserveLog&&(!this.$request||!this.$requests.findId(this.$request.id))},shouldShowIncomingRequest:function(){return this.$settings.global.preserveLog&&(!this.$request||this.$settings.global.showIncomingRequests&&this.global.showIncomingRequests)},clear:function(){this.$requests.clear()}},watch:{requests:function(e){this.shouldShowFirstRequest()?this.showRequest(this.$requests.first()):this.shouldShowIncomingRequest()&&(this.showRequest(this.$requests.last((function(e){return!e.isAjax()}))||this.$requests.last()),this.$refs.requestsContainer.scrollTop=this.$refs.requestsTable.offsetHeight+this.$refs.requestsTable.offsetTop)},$request:{handler:function(e){var t,s;"requires-authentication"==(null===(t=this.$request)||void 0===t||null===(s=t.error)||void 0===s?void 0:s.error)&&this.$authentication.request(this.$request.error.message,this.$request.error.requires);var a=this.$requests.last((function(e){return!e.isAjax()}))||this.$requests.last(),i=this.$requests.all().indexOf(a);this.global.showIncomingRequests=this.$requests.all().slice(i).includes(e)}},"$request.loading":{handler:function(e){var t,s;e||"requires-authentication"==(null===(t=this.$request)||void 0===t||null===(s=t.error)||void 0===s?void 0:s.error)&&this.$authentication.request(this.$request.error.message,this.$request.error.requires)}}}},Ri=Di,Ei=(s("d852"),Object(f["a"])(Ri,Oi,Pi,!1,null,null,null)),Ni=Ei.exports,Ai=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("modal",{attrs:{icon:"star",title:"What's new",shown:e.$whatsNew.show,"on-close":e.close}},[s("div",{staticClass:"whats-new"},[s("div",{staticClass:"whats-new-content"},[s("h1",[e._v("Clockwork has just been updated!")]),e._l(e.release.notes,(function(t){return[s("div",{staticClass:"whats-new-section",class:"image-"+(t.imagePlacement||"bottom")},[t.image&&"bottom"!=(t.imagePlacement||"bottom")?s("img",{attrs:{src:"img/whats-new/"+e.release.version+"/"+t.image}}):e._e(),s("h2",[e._v(e._s(t.title))]),e._l(t.text,(function(t){return s("p",[e._v(e._s(t))])})),t.image&&"bottom"==(t.imagePlacement||"bottom")?s("img",{attrs:{src:"img/whats-new/"+e.release.version+"/"+t.image}}):e._e()],2)]})),s("div",{staticClass:"whats-new-actions"},[s("a",{staticClass:"actions-show-more",attrs:{href:e.release.url,target:"_blank"}},[e._v("Learn more")]),s("a",{staticClass:"actions-close",attrs:{href:"#"},on:{click:function(t){return t.preventDefault(),e.close.apply(null,arguments)}}},[e._v("Close")]),s("p",[s("small",[e._v("Some of the new features might require server-side Clockwork library update.")])])])],2)])])},Ii=[],Mi=function(){function e(t,s){Object(tt["a"])(this,e),this.platform=t,this.settings=s}return Object(st["a"])(e,[{key:"show",get:function(){return this.settings.global.seenReleaseNotesVersion!=e.latestRelease.version&&this.settings.persistent&&this.settings.loaded&&this.platform.hasFeature("whats-new")}},{key:"seen",value:function(){this.settings.global.seenReleaseNotesVersion=e.latestRelease.version,this.settings.save()}}],[{key:"latestRelease",get:function(){return e.releases[0]}},{key:"releases",get:function(){return[{version:"5.1",url:"https://underground.works/blog/clockwork-5.1-released-with-database-queries-highlighting-and-more",notes:[{title:"Database Queries Highlighting",text:["Database queries are now easier to read with SQL syntax highlighting. You can also enable the prettify mode to reformat the queries for even more readibility."],image:"database-queries.png",imagePlacement:"top"},{title:"Server-side Library Updated",text:["The server-side library was also updated with support for Laravel Octane, collecting cache values and more.",'For the full list of improvements, changes, and fixes click on the "learn more" button.']}]},{version:"5.0",url:"https://underground.works/blog/clockwork-5.0-released-with-client-side-metrics-toolbar-and-more",notes:[{title:"UI refinements",text:["Almost every part of the Clockwork UI was touched up and improved. From simplified requests list, new tab bar, counters and tables to reworked dark theme colors.",'You will also find a new "credits" link in the settings modal. This opens a credits modal with a little shout-out to all contributors, sponsors and used third-party dependencies.'],image:"clockwork-5.png",imagePlacement:"top"},{title:"Timeline",text:["Timeline was rebuilt from the ground up in this release.","The new timeline makes it easier than ever before to figure out what's happening in your application.","The condense option makes the timeline more compact and clicking on an event now reveals a popover with more details."],image:"timeline.png",imagePlacement:"right"},{title:"Client-side metrics and Web Vitals",text:["Clockwork helps you to keep your server-side performance in check. Optimizing the backend is just half of the battle though.","Clockwork can now collect client-side performance metrics. Supported are both navigation timings and Web Vitals","Collecting these metrics requires installing a tiny javascript library from npm or via cdn. Check the docs for more details."],image:"client-metrics.png",imagePlacement:"left"},{title:"Models",text:["Models tab is a new tool in your toolbelt for dealing with database issues.","Models actions give you a different point of view at your database usage.","Models counts will show you how many of each model you've retrieved, created, updated and deleted."],image:"models-tab.png",imagePlacement:"right"},{title:"Notifications",text:["Notifications tab is a new tab replacing the emails tab.","This time we support not only emails, but all kinds of notifications, like SMS or Slack messages.","With details like subject, recipient, sender, but also notifiable, notified and mailable objects in Larvel."],image:"notifications-tab.png",imagePlacement:"left"},{title:"Sharing",text:["Have you ever wanted to share a Clockwork profile with someone else? Maybe you'd like to share details of a crash from a local environment with a co-worker. Or before and after optimisation metrics with your boss.","Sharing a request uploads the metadata to a Clockwork share service and gives you a public link to share with others. On this link you will find a fully working Clockwork app showing the request you shared.","The share service is free to use, click on the share button in the sidebar to start."]},{title:"Toolbar",text:["Clockwork now gives you an option to show basic request information in the form of a toolbar in your app.","A tiny browser component has to be installed from npm or via cdn. See the docs for full installation instructions."],image:"toolbar.png",imagePlacement:"top"}]}]}}]),e}(),Li={name:"WhatsNew",components:{Modal:p},computed:{release:function(){return Mi.latestRelease}},methods:{close:function(){this.$whatsNew.seen()}}},Qi=Li,Ui=(s("f407"),Object(f["a"])(Qi,Ai,Ii,!1,null,null,null)),Fi=Ui.exports,Ji={name:"App",components:{RequestDetails:Ba,RequestSidebar:ji,RequestsList:Ni,WhatsNew:Fi},computed:{appearance:function(){return this.$settings.appearance},showRequestsList:function(){return!this.$settings.global.requestsListCollapsed&&this.$platform.hasFeature("requests-list")},showRequestSidebar:function(){return!this.$settings.global.requestSidebarCollapsed&&this.$platform.hasFeature("request-sidebar")}}},Hi=Ji,Vi=(s("5c0b"),Object(f["a"])(Hi,i,n,!1,null,null,null)),Wi=Vi.exports,Bi=s("4eb5"),zi=s.n(Bi),Gi=s("f035"),Ki=s.n(Gi),Xi=s("c28b"),Zi=s.n(Xi),Yi=s("9b02"),en=s.n(Yi);yt["a"].config.devtools=!0,yt["a"].config.performance=!0,yt["a"].config.productionTip=!1,yt["a"].use(zi.a),yt["a"].use(Zi.a),yt["a"].component("spinner",Ki.a),yt["a"].mixin({methods:{$get:en.a}});s("498a"),s("a15b"),s("5db7"),s("73d9");var tn,sn=s("1160"),an=s.n(sn),nn=s("a501"),rn=s.n(nn),on=function(){function e(t,s,a){var i=this;Object(tt["a"])(this,e),this.startTime=s,this.endTime=a,this.events=[],t.map((function(e){return i.append(e)}))}return Object(st["a"])(e,[{key:"append",value:function(e){e.start=e.start||this.startTime,e.start<this.startTime&&(e.start=this.startTime),e.end>this.endTime&&(e.end=this.endTime),this.events.push(new ln(e)),this.sort()}},{key:"appendTotalEvent",value:function(){this.append({description:"Total time",start:this.startTime,duration:this.endTime-this.startTime,color:"grey"})}},{key:"merge",value:function(e){this.events=this.events.concat(e.events),this.sort()}},{key:"copy",value:function(){return new e(an()(this.events),this.startTime,this.endTime)}},{key:"sort",value:function(){return this.events=this.events.sort((function(e,t){return e.start-t.start})),this}},{key:"filter",value:function(e,t){var s=this.copy();return s.events=e.filter(s.events),s.events=s.events.filter((function(e){return!rn()(e.tags,t).length})),s}},{key:"condense",value:function(){var e=this.copy(),t=(e.endTime-e.startTime)/64;return e.events=e.events.reduce((function(e,s){if(s.duration>=t)return[].concat(Object(ts["a"])(e),[s]);var a=e[e.length-1];return a instanceof cn&&a.end<=s.start?(a.push(s),e):[].concat(Object(ts["a"])(e),[new cn(s)])}),[]),e}},{key:"present",value:function(e){var t=this;return this.events.map((function(s){return s instanceof cn||(s=new cn(s)),s.present(t,e)}))}},{key:"findChildren",value:function(e){return this.events.flatMap((function(e){return e instanceof cn?e.events:e})).reduce((function(t,s){return s!==e&&e.contains(s)&&t.every((function(e){return!e.contains(s)}))&&t.push(s),t}),[])}}]),e}(),ln=function(){function e(t){Object(tt["a"])(this,e),this.name=t.name||t.description,this.description=t.description||"",this.start=t.start instanceof Date?t.start.getTime()/1e3:t.start,this.duration=t.duration||0,this.color=t.color||"blue",this.end=this.start+this.duration/1e3,this.tags=t.tags||[],this.data=t.data}return Object(st["a"])(e,[{key:"present",value:function(e,t){var s=this;if(this.presented)return this;this.startRelative=1e3*(this.start-e.startTime)/(e.endTime-e.startTime),this.durationRelative=this.duration/(e.endTime-e.startTime),this.offset=this.startRelative*t,this.width=this.durationRelative*t,this.width<3&&(this.width=3),this.width>t&&(this.width=t),this.width+this.offset>t&&(this.offset=t-this.width),this.eventClass=this.color,this.eventStyle={left:"0px",width:"".concat(this.width,"px")},this.labelWidth=this.startRelative>.5?this.offset:t-this.width-this.offset,this.labelClass=this.startRelative>.5?["before",this.color]:["after",this.color],this.labelStyle={width:"".concat(this.labelWidth,"px")},this.width>200&&(this.labelClass=["inside",this.color]);var a=e.findChildren(this).map((function(s){return s.present(e,t)}));return this.childrenSections=a.map((function(e){return{style:{left:"".concat(e.offset-s.offset,"px"),width:"".concat(e.width,"px")}}})),this.durationChildren=a.length?a.reduce((function(e,t){return e+t.duration}),0):null,this.durationSelf=this.duration-this.durationChildren,this.presented=!0,this}},{key:"contains",value:function(e){return this.start<e.start&&e.end<this.end}}]),e}(),cn=function(){function e(t){Object(tt["a"])(this,e),this.events=t instanceof Array?t:[t],this.name=this.firstEvent.name,this.description=this.firstEvent.description,this.start=this.firstEvent.start,this.duration=this.lastEvent.end-this.firstEvent.start,this.color=this.firstEvent.color,this.end=this.lastEvent.end}return Object(st["a"])(e,[{key:"condensed",get:function(){return this.events.length>1}},{key:"firstEvent",get:function(){return this.events[0]}},{key:"lastEvent",get:function(){return this.events[this.events.length-1]}},{key:"push",value:function(e){this.events.push(e),this.name=this.description="".concat(this.events.length," events"),this.duration=this.lastEvent.end-this.firstEvent.start,this.end=this.lastEvent.end}},{key:"present",value:function(e,t){var s=this;return this.events.forEach((function(a){a.present(e,t),a.eventStyle.left="".concat(a.offset-s.events[0].offset,"px")})),this.duration=this.condensed?null:this.firstEvent.duration,this.durationSelf=this.condensed?null:this.firstEvent.durationSelf,this.durationChildren=this.condensed?null:this.firstEvent.durationChildren,this.offset=this.firstEvent.offset,this.width=this.lastEvent.width+this.lastEvent.offset-this.firstEvent.offset,this.color=this.offset>t/2?this.firstEvent.color:this.lastEvent.color,this.labelWidth=this.offset>t/2?this.offset:t-this.width-this.offset,this.labelClass=this.offset>t/2?["before",this.color]:["after",this.color],this.labelStyle={width:"".concat(this.labelWidth,"px")},this.width>200&&!this.condensed&&(this.labelClass=["inside",this.color]),this.groupStyle={"margin-left":"".concat(this.offset,"px"),width:"".concat(this.width,"px")},this.tags=this.condensed?[]:this.firstEvent.tags,this.data=this.condensed?void 0:this.firstEvent.data,this}}]),e}(),un=s("f7c2"),dn=s.n(un),hn=s("e4c9"),vn=s.n(hn),fn=s("c909"),mn=s.n(fn),pn=function(){function e(t){Object(tt["a"])(this,e),Object.assign(this,t),this.original=t,this.time=parseFloat(this.time),this.responseDuration=parseFloat(this.responseDuration),this.responseDurationRounded=this.responseDuration?Math.round(this.responseDuration):0,this.databaseDurationRounded=this.databaseDuration?Math.round(this.databaseDuration):0,this.memoryUsageFormatted=this.memoryUsage?this.formatBytes(this.memoryUsage):void 0,this.processCacheStats(),this.cacheQueries=this.processCacheQueries(this.cacheQueries),this.cookies=this.createKeypairs(this.cookies),this.middleware=this.middleware instanceof Array?this.middleware:[],this.processDatabase(),this.processModels(),this.notifications=this.processNotifications(this.notifications,this.emailsData),this.events=this.processEvents(this.events),this.getData=this.createKeypairs(this.getData),this.requestData=this.requestData instanceof Object?this.createKeypairs(this.requestData,!1):this.requestData,this.headers=this.processHeaders(this.headers),this.log=this.processLog(this.log),this.postData=this.createKeypairs(this.postData),this.queueJobs=this.processQueueJobs(this.queueJobs),this.redisCommands=this.processRedisCommands(this.redisCommands),this.sessionData=this.createKeypairs(this.sessionData),this.performanceMetrics=this.processPerformanceMetrics(this.performanceMetrics),this.viewsData=this.processViews(this.viewsData),this.userData=this.processUserData(this.userData),this.timeline=this.processTimeline(this.timelineData),this.clientMetrics=this.processClientMetrics(this.clientMetrics),this.webVitals=this.processWebVitals(this.webVitals),this.processCommand(),this.processQueueJob(),this.processTest(),this.errorsCount=this.getErrorsCount(),this.warningsCount=this.getWarningsCount(),this.exceptions=this.processExceptions(),this.loadClientMetricsAttempts=0}return Object(st["a"])(e,[{key:"resolve",value:function(e,t){return Object.assign(this,t?dn()(e,t):e,{loading:!1,error:void 0,original:Object.assign(this.original,e.original)})}},{key:"resolveWithError",value:function(e){return Object.assign(this,{loading:!1,error:e})}},{key:"loadClientMetrics",value:function(e){var t=this;this.isRequest()&&(this.clientMetrics.some((function(e){return e.value}))&&Object.values(this.webVitals).some((function(e){return e.value}))||this.loadClientMetricsTimeout||++this.loadClientMetricsAttempts>4||(this.loadClientMetricsTimeout=setTimeout((function(){e.findId(t.id)&&e.loadId(t.id,["clientMetrics","webVitals"]).then((function(){t.loadClientMetricsTimeout=void 0,t.loadClientMetrics(e)}))}),2500)))}},{key:"isClientError",value:function(){return this.responseStatus>=400&&this.responseStatus<500}},{key:"isServerError",value:function(){return this.responseStatus>=500&&this.responseStatus<600}},{key:"isAjax",value:function(){return this.headers.find((function(e){return"X-Requested-With"==e.name&&"XMLHttpRequest"==e.value}))}},{key:"isRequest",value:function(){return"request"==this.type||!this.type}},{key:"isCommand",value:function(){return"command"==this.type}},{key:"isCommandError",value:function(){return 1==this.commandExitCode}},{key:"isCommandWarning",value:function(){return this.commandExitCode>1}},{key:"isQueueJob",value:function(){return"queue-job"==this.type}},{key:"isQueueJobError",value:function(){return"failed"==this.jobStatus}},{key:"isQueueJobWarning",value:function(){return"released"==this.jobStatus}},{key:"isTest",value:function(){return"test"==this.type}},{key:"isTestError",value:function(){return["failed","error"].includes(this.testStatus)}},{key:"isTestWarning",value:function(){return["warning"].includes(this.testStatus)}},{key:"tooltip",get:function(){return this.isCommand()?"[CMD] ".concat(this.commandName," (").concat(this.commandLine,")"):this.isQueueJob()?"[QUEUE] ".concat(this.jobName," (").concat(this.jobDescription,")"):this.isTest()?"[TEST] ".concat(this.testGroup," (").concat(this.testName,")"):"".concat(this.method," ").concat(this.uri," (").concat(this.controller,")")}},{key:"createKeypairs",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!(e instanceof Object))return[];var s=Object.keys(e).map((function(t){return{name:t,value:e[t]}}));return t&&(s=s.sort((function(e,t){return e.name.localeCompare(t.name)}))),s}},{key:"processCacheStats",value:function(){this.cacheDeletes&&(this.cacheDeletes=parseInt(this.cacheDeletes)),this.cacheHits&&(this.cacheHits=parseInt(this.cacheHits)),this.cacheReads&&(this.cacheReads=parseInt(this.cacheReads)),this.cacheWrites&&(this.cacheWrites=parseInt(this.cacheWrites)),this.cacheMisses=this.cacheReads&&this.cacheHits?this.cacheReads-this.cacheHits:null}},{key:"processCacheQueries",value:function(e){var t=this;return e instanceof Array?e.map((function(e){return e.expiration=e.expiration?t.formatTime(e.expiration):void 0,e.value=void 0!==e.value?e.value:"",e})):[]}},{key:"processClientMetrics",value:function(e){return e=this.enforceObject(e),[{name:"Redirect",value:e.redirect},{name:"DNS",value:e.dns,color:"purple",onChart:!0},{name:"Connection",value:e.connection,color:"blue",onChart:!0},{name:"Waiting",value:e.waiting,color:"red",onChart:!0},{name:"Receiving",value:e.receiving,color:"green",onChart:!0},{name:"To interactive",value:e.domInteractive,color:"blue",onChart:!0,dom:!0},{name:"To complete",value:e.domComplete,color:"purple",onChart:!0,dom:!0}]}},{key:"processDatabase",value:function(){this.databaseQueries=this.processDatabaseQueries(this.databaseQueries),this.databaseQueriesCount=parseInt(this.databaseQueriesCount)||this.databaseQueries.length,this.databaseSlowQueries=parseInt(this.databaseSlowQueries)||this.databaseQueries.filter((function(e){return e.tags.includes("slow")})).length,this.databaseSelects=parseInt(this.databaseSelects)||this.databaseQueries.filter((function(e){return e.query.match(/^select /i)})).length,this.databaseInserts=parseInt(this.databaseInserts)||this.databaseQueries.filter((function(e){return e.query.match(/^insert /i)})).length,this.databaseUpdates=parseInt(this.databaseUpdates)||this.databaseQueries.filter((function(e){return e.query.match(/^update /i)})).length,this.databaseDeletes=parseInt(this.databaseDeletes)||this.databaseQueries.filter((function(e){return e.query.match(/^delete /i)})).length,this.databaseOthers=parseInt(this.databaseOthers)||this.databaseQueries.filter((function(e){return!e.query.match(/^(select|insert|update|delete) /i)})).length}},{key:"processDatabaseQueries",value:function(e){var t=this;return e instanceof Array?e.map((function(e){e.model=e.model||"-",e.shortModel=e.model?e.model.split("\\").pop():"-",e.tags=e.tags instanceof Array?e.tags:[],e.bindings=t.optionalNonEmptyObject(e.bindings),e.prettifiedQuery=vn.a.format(e.query);var s,a=e.query.trim();return(s=a.match(/^SELECT\s[\s\S]*?\sFROM\s[^A-Za-z-_]?([A-Za-z-_]+)/i))?e.shortQuery="SELECT FROM ".concat(s[1]):(s=a.match(/^INSERT\s+INTO\s+[^A-Za-z-_]?([A-Za-z-_]+)/i))?e.shortQuery="INSERT INTO ".concat(s[1]):(s=a.match(/^UPDATE\s+[^A-Za-z-_]?([A-Za-z-_]+)/i))?e.shortQuery="UPDATE ".concat(s[1]):(s=a.match(/^DELETE\s+FROM\s+[^A-Za-z-_]?([A-Za-z-_]+)/i))?e.shortQuery="DELETE FROM ".concat(s[1]):e.shortQuery=a,e})):[]}},{key:"processNotifications",value:function(e,t){return t=Object.values(this.optionalNonEmptyObject(t,{})).filter((function(e){return e.data instanceof Object})).map((function(e){return{subject:e.data.subject,to:[e.data.to],from:[e.data.from],time:e.start,duration:e.duration,type:"mail",data:[]}})),this.enforceArray(e).concat(t).map((function(e){return e.isShowingDetails=!1,e}))}},{key:"processEvents",value:function(e){return e instanceof Array?e.map((function(e){return e.objectEvent=e.data instanceof Object&&e.event==e.data.__class__,e.time=e.time?new Date(1e3*e.time):void 0,e.listeners=e.listeners instanceof Array?e.listeners:[],e.listeners=e.listeners.map((function(e){var t,s;return t=(s=e.match(/Closure \(.*[\/\\](.+?:\d+)-\d+\)/))?"Closure ("+s[1]+")":e.split(/[\/\\]/).pop(),{name:e,shortName:t}})),e})):[]}},{key:"processExceptions",value:function(){var e=this.log.length?this.log[this.log.length-1].exception:null;if(this.isRequest()&&!this.isServerError()||!e)return[];e=an()(e);var t=e;do{t.trace=[{call:"".concat(t.type,"()"),file:t.file,line:t.line,isVendor:!1}].concat(Object(ts["a"])(t.trace))}while(t=t.previous);return[e]}},{key:"processHeaders",value:function(e){return e instanceof Object?Object.keys(e).map((function(t){var s=e[t];return t=t.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()})).join("-"),{name:t,value:s}})).reduce((function(e,t){return t=t.value instanceof Array?t.value.map((function(e){return{name:t.name,value:e}})):[t],e.concat(t)}),[]).sort((function(e,t){return e.name.localeCompare(t.name)})):[]}},{key:"processLog",value:function(e){return e instanceof Array?e.map((function(e){return e.exception&&(e.file=e.exception.file,e.line=e.exception.line,e.trace=e.exception.trace),e.time=new Date(1e3*e.time),e.context=e.context instanceof Object&&Object.keys(e.context).filter((function(e){return"__type__"!=e})).length?e.context:void 0,e})):[]}},{key:"processModels",value:function(){this.modelsActions=this.processModelsActions(this.modelsActions)}},{key:"processModelsActions",value:function(e){var t=this;return this.enforceArray(e).map((function(e){return e.shortModel=e.model?e.model.split("\\").pop():"",e.attributes=t.optionalNonEmptyObject(e.attributes),e.changes=t.optionalNonEmptyObject(e.changes),e.tags=t.enforceArray(e.tags),e.bindings=t.optionalNonEmptyObject(e.bindings),e.isShowingDetails=!1,e}))}},{key:"processPerformanceMetrics",value:function(e){if(!e)return[{name:"App",value:(this.responseDurationRounded||0)-(this.databaseDurationRounded||0)-(this.cacheTime||0),color:"blue"},{name:"DB",value:this.databaseDurationRounded,color:"red"},{name:"Cache",value:this.cacheTime,color:"green"}].filter((function(e){return e.value>0}));e=e.filter((function(e){return e instanceof Object})).map((function(e,t){return e.color=e.color||"purple",e}));var t=e.reduce((function(e,t){return e+t.value}),0);return e.push({name:"Other",value:this.responseDurationRounded-t,color:"purple"}),e}},{key:"processQueueJobs",value:function(e){return e instanceof Array?e.map((function(e){return e.shortName=e.name.split("\\").pop(),e})):[]}},{key:"processRedisCommands",value:function(e){return e instanceof Array?e:[]}},{key:"processTest",value:function(){if(this.testName){var e=this.testName.includes("::")?this.testName.split("::"):[this.testName,""],t=Object(a["a"])(e,2);this.testGroup=t[0],this.testName=t[1]}}},{key:"processTimeline",value:function(e){var t=new on(Object.values(this.optionalNonEmptyObject(e,{})),this.time,this.time+this.responseDuration);return e&&!e.total&&t.appendTotalEvent(),this.databaseQueries.forEach((function(e){return t.append({start:e.time,duration:e.duration,name:e.shortQuery,description:e.query,color:"red",tags:["databaseQueries"]})})),this.events.forEach((function(e){return t.append({start:e.time,duration:e.duration,description:e.event,color:"purple",tags:["events"]})})),this.cacheQueries.forEach((function(e){return t.append({start:e.time,duration:e.duration,description:"".concat(e.type.toUpperCase()," ").concat(e.key),color:"green",tags:["cacheQueries"]})})),this.redisCommands.forEach((function(e){return t.append({start:e.time,duration:e.duration,description:"".concat(e.command," ").concat(Object.values(e.parameters).join(" ")),color:"green",tags:["redisCommands"]})})),this.queueJobs.forEach((function(e){return t.append({start:e.time,duration:e.duration,description:e.name,color:"purple",tags:["queueJobs"]})})),this.notifications.forEach((function(e){return t.append({start:e.time,duration:e.duration,description:"".concat(e.to," - ").concat(e.subject),color:"purple",tags:["notifications"]})})),t.merge(this.viewsData),t}},{key:"processViews",value:function(e){var t=this,s=Object.values(this.optionalNonEmptyObject(e,{})).map((function(e){var s,a,i,n;return{start:e.start,duration:e.duration,name:(null===(s=e.data)||void 0===s?void 0:s.name)||e.description,description:((null===(a=e.data)||void 0===a?void 0:a.name)||e.description)+(null!==(i=e.data)&&void 0!==i&&i.memoryUsage?" (".concat(t.formatBytes(e.data.memoryUsage),")"):""),data:t.optionalNonEmptyObject(null===(n=e.data)||void 0===n?void 0:n.data),color:"purple",tags:["views"]}}));return new on(s,this.time,this.time+this.responseDuration)}},{key:"processUserData",value:function(e){if(!(e instanceof Object))return[];var t=function(e){var t=Object(a["a"])(e,2),s=t[0];t[1];return"__meta"!=s},s=function(e){return function(t){var s=Object(a["a"])(t,2),i=s[0],n=s[1];return{key:e[i]||i,value:n}}};return Object.entries(e).filter((function(e){var t=Object(a["a"])(e,2),s=(t[0],t[1]);return s instanceof Object||s.__meta||s.__meta.title})).map((function(e){var i=Object(a["a"])(e,2),n=i[0],r=i[1];return{key:n,title:r.__meta.title,sections:Object.entries(r).filter(t).map((function(e){var i=Object(a["a"])(e,2),n=(i[0],i[1]),r=n.__meta.labels||{},o="counters"==n.__meta.showAs?Object.entries(n).filter(t).map(s(r)):Object.entries(n).filter(t).map((function(e){var t=Object(a["a"])(e,2),i=(t[0],t[1]);return Object.entries(i).map(s(r))}));return{data:o,showAs:n.__meta.showAs,title:n.__meta.title}}))}}))}},{key:"processWebVitals",value:function(e){e=this.enforceObject(e);var t={cls:{slow:7300,moderate:3800},fid:{slow:300,moderate:100},lcp:{slow:4e3,moderate:2e3},fcp:{slow:4e3,moderate:2e3},ttfb:{slow:600,moderate:600},si:{slow:5800,moderate:4300}};return Object.keys(t).forEach((function(s){var a=e[s],i="fast",n=!isNaN(parseFloat(a));a>t[s].slow?i="slow":a>t[s].moderate&&(i="moderate"),e[s]={value:a,score:i,available:n}})),e}},{key:"processCommand",value:function(){this.commandLine="",this.commandLine+=Object.values(this.commandArguments||{}).filter((function(e){return e})).join(" "),this.commandLine+=Object.entries(this.commandOptions||{}).reduce((function(e,t){var s=Object(a["a"])(t,2),i=s[0],n=s[1];return e+(!0===n?" --".concat(i):" --".concat(i,"=").concat(n))}),""),this.commandArgumentsMerged=this.createKeypairs(Object.assign({},this.commandArgumentsDefaults||{},this.commandArguments||{}),!1),this.commandOptionsMerged=this.createKeypairs(Object.assign({},this.commandOptionsDefaults||{},this.commandOptions||{}),!1)}},{key:"processQueueJob",value:function(){this.jobOptions=this.createKeypairs(this.jobOptions)}},{key:"getErrorsCount",value:function(){return this.log.reduce((function(e,t){return"error"==t.level?e+1:e}),0)}},{key:"getWarningsCount",value:function(){return this.log.filter((function(e){return"warning"==e.level})).length+this.databaseSlowQueries}},{key:"formatTime",value:function(e){var t=Math.floor(e/60),s=Math.floor(t/60);e%=60,t%=60;var a=[];return s&&a.push(s+"h"),t&&a.push(t+"min"),e&&a.push(e+"sec"),a.join(" ")}},{key:"formatBytes",value:function(e){var t=["B","kB","MB","GB","TB","PB"],s=Math.floor(Math.log(e)/Math.log(1024));return"".concat(Math.round(e/Math.round(Math.pow(1024,s)))," ").concat(t[s])}},{key:"enforceArray",value:function(e){return e instanceof Array?e:[]}},{key:"enforceObject",value:function(e){return e instanceof Object&&Object.keys(e).filter((function(e){return"__type__"!=e})).length?e:{}}},{key:"optionalNonEmptyObject",value:function(e,t){return e instanceof Object&&Object.keys(e).filter((function(e){return"__type__"!=e})).length?e:t}}],[{key:"placeholder",value:function(t,s,a){return Object.assign(new e({loading:!0,id:t,uri:s?new mn.a(s.url).pathname():"/",controller:"Waiting...",method:s?s.method:"GET",responseStatus:"?",parent:a}),{responseDurationRounded:"?",databaseDurationRounded:"?"})}}]),e}(),gn=function(){function e(){Object(tt["a"])(this,e)}return Object(st["a"])(e,[{key:"api",get:function(){return chrome||browser}},{key:"init",value:function(e){this.global=e,this.requests=e.$requests,this.profiler=e.$profiler,this.settings=e.$settings,this.updateNotification=e.$updateNotification,this.lastPolledId=null,this.useProperTheme(),this.setMetadataUrl(),this.setMetadataClient(),this.listenToRequests(),this.throttlePolling(),this.loadLastRequest()}},{key:"useProperTheme",value:function(){"dark"===this.api.devtools.panels.themeName&&(this.settings.defaultAppearance="dark")}},{key:"setMetadataUrl",value:function(){var e=this;this.resolveTabUrl().then((function(t){return e.requests.setRemote(t)}))}},{key:"setMetadataClient",value:function(){var e=this;this.requests.setClient((function(t,s,a,i){return e.profiler.withoutProfiling((function(){return e.fetch(t,s,a,i).then((function(e){var t=e.response,s=e.data;if(403==t.status)throw{error:"requires-authentication",message:s.message,requires:s.requires};if(200!=t.status)throw{error:"error-response",message:"Server returned an error response."};if(!(s instanceof Array)&&(!(s instanceof Object)||!Object.keys(s).length))throw{error:"empty-response",message:"Server returned an empty metadata."};return s}))}))}))}},{key:"fetch",value:function(e,t){var s=this,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return new Promise((function(n,r){s.api.runtime.sendMessage({action:"fetch",method:e,url:t,data:a,headers:i},(function(e){return n(e)}))}))}},{key:"setCookie",value:function(e,t,s){var a=this;return this.resolveTabUrl().then((function(i){a.api.runtime.sendMessage({action:"setCookie",url:i,name:e,value:t,path:"/",expirationDate:Math.floor(Date.now()/1e3)+s})}))}},{key:"getCookie",value:function(e){var t=this;return this.resolveTabUrl().then((function(s){return new Promise((function(a,i){t.api.runtime.sendMessage({action:"setCookie",url:s,name:e},(function(e){return a(e)}))}))}))}},{key:"resolveTabUrl",value:function(){var e=this;return new Promise((function(t,s){e.api.runtime.sendMessage({action:"getTabUrl",tabId:e.api.devtools.inspectedWindow.tabId},(function(e){return t(e)}))}))}},{key:"listenToRequests",value:function(){var e=this;this.api.runtime.onMessage.addListener((function(t){if("requestCompleted"===t.action&&(-1!==navigator.userAgent.toLowerCase().indexOf("firefox/57.0")||t.request.tabId==e.api.devtools.inspectedWindow.tabId)){var s=e.parseHeaders(t.request.responseHeaders);if(s){e.updateNotification.serverVersion=s.version,e.requests.setRemote(t.request.url,s);var a=pn.placeholder(s.id,t.request);e.requests.loadId(s.id,null,a).then((function(){return e.retryLoading(a)})),s.subrequests.forEach((function(t){e.requests.setRemote(t.url,{path:t.path}),e.requests.loadId(t.id,null,pn.placeholder(t.id,t,a))})),e.requests.setRemote(t.request.url,s)}}})),this.settings.global.hideCommandTypeRequests&&this.settings.global.hideQueueJobTypeRequests&&this.settings.global.hideTestTypeRequests||this.startPollingRequests(),this.api.runtime.onMessage.addListener((function(t){"navigationStarted"===t.action&&(e.settings.global.preserveLog||t.details.tabId==e.api.devtools.inspectedWindow.tabId&&e.requests.clear())}))}},{key:"loadLastRequest",value:function(){var e=this;this.api.runtime.sendMessage({action:"getLastClockworkRequestInTab",tabId:this.api.devtools.inspectedWindow.tabId},(function(t){if(t){var s=e.parseHeaders(t.responseHeaders);e.updateNotification.serverVersion=s.version,e.requests.setRemote(t.url,s),e.requests.loadId(s.id,null,pn.placeholder(s.id,t)),e.settings.global.hideCommandTypeRequests&&e.settings.global.hideQueueJobTypeRequests&&e.settings.global.hideTestTypeRequests||e.startPollingRequests()}}))}},{key:"parseHeaders",value:function(e){var t,s=(t=e.find((function(e){return"x-clockwork-id"==e.name.toLowerCase()})))?t.value:void 0,a=(t=e.find((function(e){return"x-clockwork-path"==e.name.toLowerCase()})))?t.value:void 0,i=(t=e.find((function(e){return"x-clockwork-version"==e.name.toLowerCase()})))?t.value:void 0;if(s){var n={};e.forEach((function(e){if(0===e.name.toLowerCase().indexOf("x-clockwork-header-")){var t=e.name.replace(/^x-clockwork-header-/i,"");n[t]=e.value}}));var r=e.filter((function(e){return"x-clockwork-subrequest"==e.name.toLowerCase()})).reduce((function(e,t){return e.concat(t.value.split(",").map((function(e){var t=e.trim().split(";");return{id:t[0],url:decodeURIComponent(t[1]),path:decodeURIComponent(t[2])}})))}),[]);return{id:s,path:a,version:i,headers:n,subrequests:r}}}},{key:"retryLoading",value:function(e){var t=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e.error&&(s>3||setTimeout((function(){return t.requests.loadId(e.id).then((function(){return t.retryLoading(e,s+1)}))}),(s+1)*(s+1)*100))}},{key:"startPollingRequests",value:function(){var e;this.pollingInterval=1e3,this.isPolling=!0,this.lastPolledId=null===(e=this.requests.last())||void 0===e?void 0:e.id,this.pollTimeout||this.pollRequests()}},{key:"stopPollingRequests",value:function(){this.isPolling=!1,clearTimeout(this.pollTimeout),this.pollTimeout=null}},{key:"pollRequests",value:function(){var e=this;clearTimeout(this.pollTimeout);var t=[this.settings.global.hideCommandTypeRequests?null:"command",this.settings.global.hideQueueJobTypeRequests?null:"queue-job",this.settings.global.hideTestTypeRequests?null:"test"].filter(Boolean);this.requests.withQuery({"type[]":t},(function(){e.requests.loadNext().then((function(t){var s;e.lastPolledId=(null===(s=e.requests.last())||void 0===s?void 0:s.id)||e.lastPolledId,e.isPolling&&(e.pollTimeout=setTimeout((function(){return e.pollRequests()}),e.updatePollingInterval(t.length)))})).catch((function(){e.isPolling&&(e.pollTimeout=setTimeout((function(){return e.pollRequests()}),e.updatePollingInterval(!1)))}))}))}},{key:"throttlePolling",value:function(){var e=this;document.addEventListener("visibilitychange",(function(){e.pollingInterval=document.hidden?6e4:1e3,!document.hidden&&e.isPolling&&e.pollRequests()}))}},{key:"updatePollingInterval",value:function(e){var t=(new Date).getTime();return!e&&this.pollingLastReceived||(this.pollingLastReceived=t),t-this.pollingLastReceived>6e4?this.pollingInterval=5e3:t-this.pollingLastReceived>3e4?this.pollingInterval=2500:this.pollingInterval=1e3}},{key:"hasFeature",value:function(e){var t=["delete-shared","details-request"];return!t.includes(e)}},{key:"settingsChanged",value:function(){this.settings.global.hideCommandTypeRequests&&this.settings.global.hideQueueJobTypeRequests&&this.settings.global.hideTestTypeRequests?this.stopPollingRequests():this.startPollingRequests()}}],[{key:"runningAsExtension",value:function(){return"object"==("undefined"===typeof chrome?"undefined":Object(et["a"])(chrome))&&chrome.devtools||"object"==("undefined"===typeof browser?"undefined":Object(et["a"])(browser))&&browser.devtools}}]),e}(),bn=function(){function e(){Object(tt["a"])(this,e)}return Object(st["a"])(e,[{key:"init",value:function(e){this.global=e,this.requests=e.$requests,this.authentication=e.$authentication,this.profiler=e.$profiler,this.settings=e.$settings,this.lastPolledId=null,this.useProperTheme(),this.setMetadataUrl(),this.setMetadataClient(),this.loadRequestFromUri(),this.startPollingRequests(),this.throttlePolling()}},{key:"useProperTheme",value:function(){window.matchMedia("(prefers-color-scheme: dark)").matches&&(this.settings.defaultAppearance="dark")}},{key:"setMetadataUrl",value:function(){if(this.settings.global.metadataPath)return this.requests.setRemote(window.location.href,{path:this.settings.global.metadataPath});this.requests.setRemote(window.location.href,{path:new mn.a(window.location.href).path().split("/").slice(0,-2).join("/")+"/__clockwork/"})}},{key:"setMetadataClient",value:function(){var e=this;this.requests.setClient((function(t,s,a,i){return e.profiler.withoutProfiling((function(){return e.fetch(t,s,a,i).then((function(e){var t=e.response,s=e.data;if(403==t.status)throw{error:"requires-authentication",message:s.message,requires:s.requires};if(200!=t.status)throw{error:"error-response",message:"Server returned an error response."};if(!(s instanceof Array)&&(!(s instanceof Object)||!Object.keys(s).length))throw{error:"empty-response",message:"Server returned an empty metadata."};return s}))}))}))}},{key:"fetch",value:function(e){function t(t,s){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=new FormData;return Object.entries(s).forEach((function(e){var t=Object(a["a"])(e,2),s=t[0],i=t[1];return n.append(s,i)})),fetch(t,{method:e,body:Object.keys(s).length?n:null,headers:i}).then((function(e){return e.json().then((function(t){return{response:e,data:t}}))}))}))},{key:"setCookie",value:function(e,t,s){return document.cookie="".concat(e,"=").concat(t,";path=/;max-age=").concat(s),Promise.resolve()}},{key:"getCookie",value:function(e){var t=document.cookie.match(new RegExp("(?:^| )".concat(e,"=([^;]*)")));return Promise.resolve(t?t[1]:void 0)}},{key:"loadRequestFromUri",value:function(){var e=mn()(window.location.href).fragment();e&&this.requests.loadId(e,!1)}},{key:"startPollingRequests",value:function(){var e=this;this.pollingInterval=1e3,this.requests.loadLatest().then((function(){var t;if(!(e.lastPolledId=null===(t=e.requests.last())||void 0===t?void 0:t.id))throw new Error;e.pollRequests()})).catch((function(t){"requires-authentication"==t.error?e.authentication.request(t.message,t.requires).then((function(){e.startPollingRequests()})):setTimeout((function(){return e.startPollingRequests()}),e.pollingInterval)}))}},{key:"pollRequests",value:function(){var e,t=this;clearTimeout(this.pollTimeout),this.requests.loadNext(null,(null===(e=this.requests.last())||void 0===e?void 0:e.id)||this.lastPolledId).then((function(e){var s;if(!t.settings.global.preserveLog){var a=t.requests.last((function(e){return e.isRequest()&&!e.isAjax()})),i=t.requests.all().indexOf(a);t.requests.setItems(t.requests.all().slice(i))}t.lastPolledId=(null===(s=t.requests.last())||void 0===s?void 0:s.id)||t.lastPolledId,t.pollTimeout=setTimeout((function(){return t.pollRequests()}),t.updatePollingInterval(e.length))})).catch((function(){t.pollTimeout=setTimeout((function(){return t.pollRequests()}),t.updatePollingInterval(!1))}))}},{key:"throttlePolling",value:function(){var e=this;document.addEventListener("visibilitychange",(function(){e.pollingInterval=document.hidden?6e4:1e3,document.hidden||e.pollRequests()}))}},{key:"updatePollingInterval",value:function(e){var t=(new Date).getTime();return!e&&this.pollingLastReceived||(this.pollingLastReceived=t),t-this.pollingLastReceived>6e4?this.pollingInterval=5e3:t-this.pollingLastReceived>3e4?this.pollingInterval=2500:this.pollingInterval=1e3}},{key:"hasFeature",value:function(e){var t=["delete-shared","details-request"];return!t.includes(e)}},{key:"settingsChanged",value:function(){this.settings.global.metadataPath&&this.setMetadataUrl()}}]),e}(),wn=function(){function e(){Object(tt["a"])(this,e)}return Object(st["a"])(e,[{key:"init",value:function(e){this.global=e,this.requests=e.$requests,this.settings=e.$settings,this.useProperTheme(),this.setMetadataUrl("/data/"),this.setMetadataClient(),this.loadRequestFromUri(),this.isTakingScreenshot=Object.keys(mn()(window.location.href).query(!0)).includes("screenshot")}},{key:"loadRequest",value:function(e){this.requests.clear(),this.requests.loadId(e)}},{key:"useProperTheme",value:function(){window.matchMedia("(prefers-color-scheme: dark)").matches&&(this.settings.defaultAppearance="dark")}},{key:"setMetadataUrl",value:function(e){this.requests.setRemote(mn()(e).path("").toString(),{path:mn()(e).path()+"/"})}},{key:"setMetadataClient",value:function(){var e=this;this.requests.setClient((function(t,s,i,n){var r=new FormData;return Object.entries(i).forEach((function(e){var t=Object(a["a"])(e,2),s=t[0],i=t[1];return r.append(s,i)})),e.fetch(t,s,i,n).then((function(e){var t=e.response,s=e.data;if(403==t.status)throw{error:"requires-authentication",message:s.message,requires:s.requires};if(200!=t.status)throw{error:"error-response",message:"Server returned an error response."};if(!(s instanceof Object)||!Object.keys(s).length)throw{error:"empty-response",message:"Server returned an empty metadata."};return s}))}))}},{key:"fetch",value:function(e){function t(t,s){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e,t){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=new FormData;return Object.entries(s).forEach((function(e){var t=Object(a["a"])(e,2),s=t[0],i=t[1];return n.append(s,i)})),fetch(t,{method:e,body:Object.keys(s).length?n:null,headers:i}).then((function(e){return e.json().then((function(t){return{response:e,data:t}}))})).catch((function(e){throw{error:"empty-response",message:"Shared request was not found."}}))}))},{key:"setCookie",value:function(e,t,s){return document.cookie="".concat(e,"=").concat(t,";path=/;max-age=").concat(s),Promise.resolve()}},{key:"getCookie",value:function(e){var t=document.cookie.match(new RegExp("(?:^| )".concat(e,"=([^;]*)")));return Promise.resolve(t?t.groups[0]:void 0)}},{key:"loadRequestFromUri",value:function(){var e=mn()(window.location.href).path().split("/").slice(-1)[0];this.requests.loadId("".concat(e,".json"),!1)}},{key:"hasFeature",value:function(e){var t=["load-client-metrics","profiler","requests-list","sharing","whats-new"];return t=this.isTakingScreenshot?[].concat(Object(ts["a"])(t),["request-sidebar","tab-bar"]):[].concat(Object(ts["a"])(t),["details-request"]),!t.includes(e)}},{key:"settingsChanged",value:function(){}}]),e}(),_n=function(){function e(t){Object(tt["a"])(this,e),this.requests=t,this.username=this.password="",this.shown=!1,this.failed=!1,this.requires=[]}return Object(st["a"])(e,[{key:"attempt",value:function(){var e=this,t={username:this.username,password:this.password};return this.username=this.password="",this.failed=!1,this.requests.client("POST","".concat(this.requests.remoteUrl,"auth"),t).then((function(t){e.shown=!1,e.requests.setAuthenticationToken(t.token),e.requests.items.forEach((function(t){if(t.error&&"requires-authentication"==t.error.error)return e.requests.loadId(t.id)})),e.accept()})).catch((function(t){e.failed=!0}))}},{key:"request",value:function(e,t){var s=this;return this.shown=!0,this.requires=t,this.message=e,new Promise((function(e,t){s.accept=e,s.reject=t}))}}]),e}(),yn=function(){function e(t){Object(tt["a"])(this,e),this.platform=t,this.shown=!1,this.loaded=!1,this.version="5.1.0",this.credits={app:{contributors:[],dependencies:[],sponsors:[]},php:{contributors:[],dependencies:[],sponsors:[]}},this.authors=[{name:"its",avatarUrl:"https://avatars.githubusercontent.com/u/821582?v=3",twitterUrl:"https://twitter.com/itsgoingd",githubUrl:"https://github.com/itsgoingd",sponsorUrl:"https://github.com/sponsors/itsgoingd"}]}return Object(st["a"])(e,[{key:"toggle",value:function(){this.shown=!this.shown,this.load()}},{key:"load",value:function(){var e=this;if(!this.loaded){var t=["app","php"],s=["contributors","dependencies","sponsors"],a=[];t.forEach((function(t){s.forEach((function(s){a.push(e.platform.fetch("GET","".concat("https://meta.underground.works","/clockwork-").concat(t,"/").concat(s,".json")).then((function(a){var i=a.data;return e.credits[t][s]=i})))}))})),Promise.all(a).then((function(){return e.loaded=!0}))}}}]),e}(),Cn=function(){function e(t){Object(tt["a"])(this,e),this.settings=t}return Object(st["a"])(e,[{key:"register",value:function(){yt["a"].filter("editorLink",this.filter())}},{key:"filter",value:function(){var e=this;return function(t,s){var a={atom:function(e,t){return"atom://open?url=file://".concat(e,"&line=").concat(t)},phpstorm:function(e,t){return"phpstorm://open?file=".concat(e,"&line=").concat(t)},sublime:function(e,t){return"subl://open?url=file://".concat(e,"&line=").concat(t)},textmate:function(e,t){return"txmt://open?url=file://".concat(e,"&line=").concat(t)},"vs-code":function(e,t){return"vscode://file/".concat(e,":").concat(t)}},i=e.settings.global.editor;if(i&&a[i])return t&&e.settings.site.localPathMap.real&&(t=t.replace(e.settings.site.localPathMap.real,e.settings.site.localPathMap.local)),a[i](t,s)}}}]),e}(),qn=function(){function e(){Object(tt["a"])(this,e),this.backend=null,this.isLocalStorageAvailable()?this.backend="local-storage":this.isBrowserStorageAvailable()&&(this.backend="browser-storage"),this.persistent=!!this.backend,this.data=null,this.load()}return Object(st["a"])(e,[{key:"get",value:function(){var e=Object(te["a"])(regeneratorRuntime.mark((function e(t,s){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.load();case 2:if(void 0!=this.data[t]){e.next=5;break}return e.next=5,this.set(t,s);case 5:return e.abrupt("return",this.data[t]);case 6:case"end":return e.stop()}}),e,this)})));function t(t,s){return e.apply(this,arguments)}return t}()},{key:"set",value:function(){var e=Object(te["a"])(regeneratorRuntime.mark((function e(t,s){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.load();case 2:this.data[t]=s,this.save();case 4:case"end":return e.stop()}}),e,this)})));function t(t,s){return e.apply(this,arguments)}return t}()},{key:"load",value:function(){var e=this;return this.data?Promise.resolve():new Promise((function(t){"local-storage"==e.backend?e.loaded(t,localStorage.getItem("clockwork")):"browser-storage"==e.backend?(window.browser||window.chrome).storage.local.get(["clockwork"],(function(s){e.loaded(t,s.clockwork)})):e.loaded(t)}))}},{key:"loaded",value:function(e,t){try{this.data=JSON.parse(t)}catch(s){}this.data=this.data instanceof Object?this.data:{},e()}},{key:"save",value:function(){if("local-storage"==this.backend)try{localStorage.setItem("clockwork",JSON.stringify(this.data))}catch(e){}else"browser-storage"==this.backend&&(window.browser||window.chrome).storage.local.set({clockwork:JSON.stringify(this.data)})}},{key:"isLocalStorageAvailable",value:function(){try{localStorage}catch(e){return!1}return!0}},{key:"isBrowserStorageAvailable",value:function(){return window.browser&&browser.storage||window.chrome&&chrome.storage}}]),e}(),$n=function(){function e(t,s){Object(tt["a"])(this,e),this.platform=t,this.settings=s,this.enabled=!1,this.cookieLifetime=60}return Object(st["a"])(e,[{key:"enableProfiling",value:function(){var e=this;this.enabled=!0,this.platform.setCookie("clockwork-profile",this.settings.site.onDemandSecret,this.cookieLifetime),clearTimeout(this.timeout),this.timeout=setTimeout((function(){e.enabled&&e.enableProfiling()}),1e3*this.cookieLifetime)}},{key:"disableProfiling",value:function(){this.enabled=!1,clearTimeout(this.timeout)}}]),e}(),kn=(s("2ca0"),s("cb29"),function(){function e(t,s){Object(tt["a"])(this,e),this.metadata=t,this.functions=s}return Object(st["a"])(e,null,[{key:"parse",value:function(t){return new Promise((function(s,a){s(e.parseSync(t))}))}},{key:"parseSync",value:function(t){var s=this,i={},n=[],r={},o={};t=t.split("\n");var l,c,u,d,h=0;while(void 0!==(l=t[h++]))l.startsWith("fl=")?function(){var e=s.resolveCompressedName(l.match(/fl=(.+)/)[0],r),i=Object(a["a"])(e,2),c=(i[0],i[1]);l=t[h++];var d=s.resolveCompressedName(l.match(/fn=(.+)/)[0],o),v=Object(a["a"])(d,2),f=v[0],m=v[1];l=t[h++];var p=l.split(" ").map((function(e){return parseFloat(e)})),g=p.shift();u=n[f],u||(n[f]=u={name:m,file:c,line:g,invocations:0,self:new Array(p.length).fill(0),inclusive:new Array(p.length).fill(0),callers:[],subCalls:[]}),u.invocations++,u.self=u.self.map((function(e,t){return e+p[t]})),u.inclusive=u.inclusive.map((function(e,t){return e+p[t]}))}():l.startsWith("cfn=")?function(){var e=s.resolveCompressedName(l.match(/cfn=(.+)/)[0],o),i=Object(a["a"])(e,2),r=i[0],c=i[1];l=t[h++],l=t[h++];var v=l.split(" ").map((function(e){return parseFloat(e)})),f=v.shift(),m=n[r];u.inclusive=v.map((function(e,t){return e+(u.inclusive[t]||0)}));var p=m.callers[d];p||(m.callers[d]=p={name:u.name,line:f,calls:0,summed:new Array(v.length).fill(0)}),p.calls++,p.summed=p.summed.map((function(e,t){return e+v[t]}));var g=u.subCalls[r];g||(u.subCalls[r]=g={name:c,line:f,calls:0,summed:new Array(v.length).fill(0)}),g.calls++,g.summed=g.summed.map((function(e,t){return e+v[t]}))}():(c=l.match(/^(.+?): (.+)/))&&(i[c[1]]=c[2]);return new e(i,n.slice(1))}},{key:"resolveCompressedName",value:function(e,t){var s=e.match(/\((\d+)\)(?: (.*))?/),i=Object(a["a"])(s,3),n=(i[0],i[1]),r=i[2];return r&&(t[n]=r),[n,t[n]]}}]),e}()),xn=function(){function e(t,s){var a=this;Object(tt["a"])(this,e),this.requests=t,this.platform=s,this.available=!1,this.loading=!1,this.parsing=!1,this.ready=!1,this.isProfiling=!1,this.metric=0,this.percentual=!1,this.shownFraction=.9,this.request=void 0,this.functions=[],this.platform.getCookie("XDEBUG_PROFILE").then((function(e){return a.isProfiling=e}))}return Object(st["a"])(e,[{key:"enableProfiling",value:function(){var e=this;return this.platform.setCookie("XDEBUG_PROFILE","1",2592e3).then((function(){e.isProfiling=!0}))}},{key:"disableProfiling",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&this.clear(),this.platform.setCookie("XDEBUG_PROFILE","0",0).then((function(){s||(e.isProfiling=!1)}))}},{key:"withoutProfiling",value:function(e){if(!this.isProfiling)return e();this.disableProfiling(!1,!0);var t=e();return this.enableProfiling(),t}},{key:"loadRequest",value:function(e){var t=this;if(!this.request||this.request.id!=e.id){if(!e.xdebug||!e.xdebug.profile)return this.clear();if(this.request=e,this.available=this.loading=this.parsing=this.ready=!1,this.summary=this.metadata=this.functions=[],this.available=!0,e.xdebug.profileData)return this.parseProfile();this.loading=!0,this.requests.loadExtended(e.id,["xdebug"]).then((function(e){t.loading=!1,t.parseProfile()}))}}},{key:"parseProfile",value:function(){var e=this;if(!this.request.xdebug.profileData)return this.available=!1;this.ready=!1,this.parsing=!0,kn.parse(this.request.xdebug.profileData).then((function(t){if(!t.metadata.summary)return e.parsing=e.available=!1;e.metadata=t.metadata,e.summary=e.metadata.summary.split(" ");var s=e.metadata.events.includes("Time_(10ns)");e.functionsAll=t.functions.filter((function(e){return"{main}"!=e.name})).map((function(e){return e.selfAll=e.self,e.inclusiveAll=e.inclusive,e.fullPath="php:internal"==e.file?"internal":"".concat(e.file,":").concat(e.line),e.shortPath="internal"!=e.fullPath?e.fullPath.split(/[\/\\]/).pop():e.fullPath,s&&(e.selfAll[0]=e.selfAll[0]/100,e.inclusiveAll[0]=e.inclusiveAll[0]/100),e})),e.presentProfile(),e.parsing=!1,e.ready=!0}))}},{key:"presentProfile",value:function(){var e=this,t=this.shownFraction*this.summary[this.metric];this.functions=this.functionsAll.filter((function(s){return t-=s.selfAll[e.metric],t>0})).map((function(t){return t.self=e.percentual?t.selfAll[e.metric]/e.summary[e.metric]*100:t.selfAll[e.metric],t.inclusive=e.percentual?t.inclusiveAll[e.metric]/e.summary[e.metric]*100:t.inclusiveAll[e.metric],t}))}},{key:"clear",value:function(){this.available=this.loading=this.parsing=this.ready=!1,this.summary=this.metadata=this.functions=[],this.request=void 0}},{key:"showMetric",value:function(e){this.metric=e,this.presentProfile()}},{key:"showPercentual",value:function(e){this.percentual=!0===e||void 0===e,this.presentProfile()}},{key:"setShownFraction",value:function(e){this.shownFraction=e,this.presentProfile()}},{key:"formatMetric",value:function(e){return this.percentual?Math.round(e)+" %":1==this.metric?Math.round(e/1024)+" kB":Math.round(e/100)/10+" ms"}}]),e}(),Tn=function(){function e(){Object(tt["a"])(this,e),this.settings=null,this.items=[],this.query={},this.exclusive={}}return Object(st["a"])(e,[{key:"all",value:function(){return this.items}},{key:"findId",value:function(e){return this.items.find((function(t){return t.id==e}))}},{key:"loadId",value:function(e,t,s){var a=this,i=this.findId(e);return i?s=i:!1!==s&&(s=s||pn.placeholder(e),this.items.push(s)),s&&!t&&(s.loading=!0),this.withQuery(t?{only:t.join(",")}:{},(function(){return a.load(e,(function(e){return e.then((function(e){return s?s.resolve(e[0],t):a.items.push(e[0]),t||a.sort(),s||e[0]})).catch((function(e){s&&s.resolveWithError(e)}))}))}))}},{key:"loadExtended",value:function(e,t){var s=this,a=this.findId(e);return this.withQuery(t?{only:t.join(",")}:{},(function(){return s.load("".concat(e,"/extended"),(function(e){return e.then((function(e){return a.resolve(e[0],t)})).catch((function(e){}))}))}))}},{key:"loadLatest",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.load("latest",(function(s){return s.then((function(s){return t&&e.merge(s),s[0]}))}),t)}},{key:"returnLatest",value:function(){return this.loadLatest(!1)}},{key:"loadNext",value:function(e,t){var s,a=this,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return t=t||(null===(s=this.last((function(e){return!e.loading})))||void 0===s?void 0:s.id),t?this.load("".concat(t,"/next")+(e?"/".concat(e):""),(function(e){return e.then((function(e){return i&&a.merge(e),e})).catch((function(e){}))}),i):Promise.resolve([])}},{key:"returnNext",value:function(e,t){return this.loadNext(e,t,!1)}},{key:"loadPrevious",value:function(e,t){var s=this,a=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return t||this.items.length?(t=t||this.first((function(e){return!e.loading})).id,this.load("".concat(t,"/previous")+(e?"/".concat(e):""),(function(e){return e.then((function(e){return a&&s.merge(e),e})).catch((function(e){}))}),a)):Promise.resolve([])}},{key:"returnPrevious",value:function(e,t){return this.loadPrevious(e,t,!1)}},{key:"clear",value:function(){this.items.splice(0)}},{key:"merge",value:function(e){var t=this;e=e.filter((function(e){return!t.findId(e.id)})),e.length&&(this.items=this.items.concat(e),this.sort())}},{key:"sort",value:function(){this.items=this.items.sort((function(e,t){return e.time-t.time}))}},{key:"first",value:function(e){return e?this.items.find(e):this.items[0]}},{key:"last",value:function(e){return e?this.items.slice().reverse().find(e):this.items[this.items.length-1]}},{key:"setClient",value:function(e){this.client=e}},{key:"setItems",value:function(e){this.items=e}},{key:"setRemote",value:function(e,t){t=t||{},t.path=t.path||"/__clockwork/",e=new mn.a(e);var s=t.path.split("?"),i=Object(a["a"])(s,2),n=i[0],r=i[1];e.pathname(n||""),e.query(r||""),e.hash(""),this.remoteUrl=e.toString(),this.remoteHeaders=t.headers||{}}},{key:"setAuthenticationToken",value:function(e){this.settings.site.authToken=e,this.settings.save()}},{key:"setQuery",value:function(e){this.query=e}},{key:"withQuery",value:function(e,t){var s=this.query;this.query=e;var a=t();return this.query=s,a}},{key:"load",value:function(e,t){var s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(s)return this.loadExclusive(e,t);var a=mn()("".concat(this.remoteUrl).concat(e)).addQuery(this.query).toString(),i=Object.assign({},this.remoteHeaders,{"X-Clockwork-Auth":this.settings.site.authToken});return t(this.client("GET",a,{},i).then((function(e){return e?(e instanceof Array?e:[e]).map((function(e){return new pn(e)})):[]})))}},{key:"loadExclusive",value:function(e,t){var s=this;return this.exclusive[e]?this.exclusive[e]:this.exclusive[e]=this.load(e,t).finally((function(){return s.exclusive[e]=null}))}}]),e}(),Sn=s("5530"),jn=(s("841c"),function(){function e(t){var s=this;Object(tt["a"])(this,e),this.requests=t,this.tags=[{tag:"controller"},{tag:"method",validate:function(e){return["get","post","put","patch","delete","head"].includes(e)}},{tag:"status",validate:function(e){return e>=100&&e<600}},{tag:"time"},{tag:"received",validate:function(e){return new Date(e)}},{tag:"type",validate:function(e){return["command","queue-job","request"].includes(e)}}],this.shown=!1,this.input="",this.searchDebounced=Ie()((function(){return s.search()}),500)}return Object(st["a"])(e,[{key:"toggle",value:function(){this.shown=!this.shown,this.shown?yt["a"].nextTick((function(){return document.querySelector(".requests-search input").focus()})):(this.input="",this.search())}},{key:"search",value:function(){var e=this,t=this.tokenize(this.input),s=t.terms,i=t.tags;i=Object.entries(i).filter((function(t){var s=Object(a["a"])(t,2),i=s[0],n=s[1];return i=e.tags.find((function(e){return e.tag==i})),i&&(!i.validate||n.every((function(e){return i.validate(e)})))})).reduce((function(e,t){var s=Object(a["a"])(t,2),i=s[0],n=s[1];return e["".concat(i,"[]")]=n,e}),{}),this.requests.setQuery(s.length||Object.keys(i).length?Object(Sn["a"])({"uri[]":s,"name[]":s},i):{}),this.requests.returnLatest().then((function(t){e.requests.returnPrevious(9,t.id).then((function(s){e.requests.setItems(s?[].concat(Object(ts["a"])(s),[t]):[t])}))})).catch((function(){e.requests.clear()}))}},{key:"tokenize",value:function(e){var t,s=[],a={},i=/(\w+:)?("[^"]*"|[^\s]+)/g;while(t=i.exec(e)){var n=t[1]?t[1].substr(0,t[1].length-1):void 0,r=t[2];(t=r.match(/^"(.+?)"$/))&&(r=t[1]),n?(a[n]||(a[n]=[]),a[n].push(r)):s.push(r)}return{terms:s,tags:a}}}]),e}()),On=s("5c9a"),Pn=s.n(On),Dn=function(){function e(t,s,a){Object(tt["a"])(this,e),this.store=t,this.requests=s,this.platform=a,this.requests.settings=this,this.shown=!1,this.loaded=!1,this.settings=this.defaults(),this.defaultAppearance="light",this.load()}return Object(st["a"])(e,[{key:"global",get:function(){return this.settings.global}},{key:"site",get:function(){return this.settings.site[this.requests.remoteUrl]||(this.settings.site[this.requests.remoteUrl]=oa()(!0,{},this.defaults().site)),this.settings.site[this.requests.remoteUrl]}},{key:"persistent",get:function(){return this.store.persistent}},{key:"appearance",get:function(){return"auto"!=this.global.appearance?this.global.appearance:this.defaultAppearance}},{key:"toggle",value:function(){this.shown=!this.shown}},{key:"save",value:function(){this.store.set("settings",this.settings),this.platform.settingsChanged()}},{key:"load",value:function(){var e=Object(te["a"])(regeneratorRuntime.mark((function e(){var t,s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.defaults(),e.next=3,this.store.get("settings",{});case 3:s=e.sent,this.settings={global:oa()(!0,t.global,s.global||{}),site:Pn()(s.site||{},(function(e){return oa()(!0,{},t.site,e||{})}))},this.loaded=!0,this.platform.settingsChanged();case 7:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"defaults",value:function(){return{global:{appearance:"auto",databasePrettified:!1,editor:null,showIncomingRequests:!0,hideCommandTypeRequests:this.platform instanceof gn,hideQueueJobTypeRequests:this.platform instanceof gn,hideTestTypeRequests:this.platform instanceof gn,ignoredUpdateNotifications:{},metadataPath:null,performanceVitalsInfoShown:!0,preserveLog:!0,requestsListCollapsed:!1,requestSidebarCollapsed:!1,requestSidebarCollapsedSections:{headers:!1,data:!1,getData:!1,postData:!1,cookies:!1,middleware:!1,session:!1,arguments:!1,options:!1,payload:!1,queue:!1,connection:!1,asserts:!1},seenReleaseNotesVersion:null,shareTermsAccepted:!1,timelineCondensed:{performance:!0,views:!1},timelineHiddenTags:{}},site:{localPathMap:{real:null,local:null},onDemandSecret:null}}}}]),e}(),Rn=function(){function e(t,s){Object(tt["a"])(this,e),this.$platform=t,this.$settings=s,this.shown=!1,this.shownDelete=!1,this.inProgress=!1}return Object(st["a"])(e,[{key:"termsAccepted",get:function(){return this.$settings.global.shareTermsAccepted}},{key:"acceptTerms",value:function(){this.$settings.global.shareTermsAccepted=!0,this.$settings.save()}},{key:"toggle",value:function(){this.shown=!this.shown}},{key:"toggleDelete",value:function(){this.shownDelete=!this.shownDelete}},{key:"clear",value:function(e){e.shareId=e.shareUrl=e.shareImageUrl=null}},{key:"share",value:function(e,t){var s=this;return e.shareUrl?Promise.resolve():(this.inProgress=!0,this.$platform.fetch("POST","https://clockwork.underground.works/ingest",{data:this.resolveSharedData(e,t)}).then((function(t){var a=t.response,i=t.data;return s.inProgress=!1,500==a.status?{error:"server-error"}:(e.shareId=i.shareId,e.shareUrl=i.shareUrl,e.shareImageUrl=i.shareImageUrl,i)})).catch((function(){return s.inProgress=!1,{error:"server-error"}})))}},{key:"resolveSharedData",value:function(e,t){var s=an()(e.original);return t.log||(s.log=[]),t.events||(s.events=[]),t.models||(s.modelsActions=[],s.modelsRetrieved=s.modelsCreated=s.modelsUpdated=s.modelsDeleted=void 0),t.database||(s.databaseQueries=[],s.databaseQueriesCount=s.databaseSlowQueries=s.databaseSelects=s.databaseInserts=s.databaseUpdates=s.databaseDeletes=s.databaseOthers=void 0),t.cache||(s.cacheQueries=[],s.cacheReads=s.cacheHits=s.cacheWrites=s.cacheDeletes=void 0),t.redis||(s.redisCommands=[]),t.queue||(s.queueJobs=[]),t.views||(s.viewsData=[]),t.notifications||(s.notifications=s.emailsData=[]),t.routes||(s.routes=[]),t.output||(s.commandOutput=void 0),t.userData||(s.userData=[]),JSON.stringify(s)}},{key:"deleteShared",value:function(){mn()(window.location.href).path().split("/").slice(-1)[0];return this.$platform.fetch("POST",window.location,{_method:"delete"})}}]),e}(),En=(s("b680"),function(){function e(){Object(tt["a"])(this,e)}return Object(st["a"])(e,[{key:"register",value:function(){yt["a"].filter("date",this.date),yt["a"].filter("join",this.join),yt["a"].filter("round",this.round),yt["a"].filter("shortClass",this.shortClass),yt["a"].filter("title",this.title)}},{key:"date",value:function(e,t){return Object(bt["a"])(new Date(e),t)}},{key:"join",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:", ";return e instanceof Array?e.join(t):e}},{key:"round",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return parseFloat(parseFloat(e).toFixed(t))}},{key:"shortClass",value:function(e){return e?e.split("\\").pop():""}},{key:"title",value:function(e){return"string"!=typeof e?e:e.replace(/(\w)([A-Z])/,"$1 $2").split(" ").map((function(e){return e[0].toUpperCase()+e.substr(1).toLowerCase()})).join(" ")}}]),e}()),Nn=function(){function e(t){Object(tt["a"])(this,e),this.settings=t,this.serverVersion=null}return Object(st["a"])(e,[{key:"ignoredUpdates",get:function(){return this.settings.global.ignoredUpdateNotifications||{}}},{key:"latest",value:function(){return{version:"4.1.2",url:"https://underground.works/blog/clockwork-4.1-released-with-commands-queue-jobs-tests-profiling-and-more"}}},{key:"show",value:function(e){if(!this.ignoresUpdate(e)&&this.serverVersion)return 1==this.versionCompare(this.latest().version,this.serverVersion)?{version:this.latest().version,url:this.latest().url,currentVersion:this.serverVersion}:void 0}},{key:"ignoresUpdate",value:function(e){var t=this.ignoredUpdates[e];return t&&this.versionCompare(t,this.latest().version)>=0}},{key:"ignoreUpdate",value:function(e){var t=this.ignoredUpdates;t[e]=this.latest().version,this.store.set("update-notification.ignored-updates",t)}},{key:"versionCompare",value:function(e,t){e=e.split(".").map((function(e){return parseInt(e)})),t=t.split(".").map((function(e){return parseInt(e)}));for(var s=0;s<Math.max(e.length,t.length);s++){if(e[s]&&!t[s]||e[s]>t[s])return 1;if(!e[s]&&t[s]||e[s]<t[s])return-1}return 0}}]),e}(),An=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"ui-icon"},[s(e.component,{tag:"component"})],1)},In=[],Mn=s("c84b"),Ln=s.n(Mn),Qn=s("2779"),Un=s.n(Qn),Fn=s("159f"),Jn=s.n(Fn),Hn=s("bcee"),Vn=s.n(Hn),Wn=s("3f61"),Bn=s.n(Wn),zn=s("44f9"),Gn=s.n(zn),Kn=s("9976"),Xn=s.n(Kn),Zn=s("d2c2"),Yn=s.n(Zn),er=s("d1c0"),tr=s.n(er),sr=s("87f4"),ar=s.n(sr),ir=s("a729"),nr=s.n(ir),rr=s("2878"),or=s.n(rr),lr=s("54ed"),cr=s.n(lr),ur=s("e78e"),dr=s.n(ur),hr=s("4492"),vr=s.n(hr),fr=s("c0f2"),mr=s.n(fr),pr=s("cd61"),gr=s.n(pr),br=s("60cf"),wr=s.n(br),_r=s("65f5"),yr=s.n(_r),Cr=s("9a87"),qr=s.n(Cr),$r=s("7a84"),kr=s.n($r),xr=s("2d78"),Tr=s.n(xr),Sr=s("806d"),jr=s.n(Sr),Or=s("47b3"),Pr=s.n(Or),Dr=s("5830"),Rr=s.n(Dr),Er=s("001f"),Nr=s.n(Er),Ar=s("13b3"),Ir=s.n(Ar),Mr=s("d056"),Lr=s.n(Mr),Qr=s("fb9d"),Ur=s.n(Qr),Fr=s("bbc2"),Jr=s.n(Fr),Hr=s("92b2"),Vr=s.n(Hr),Wr=s("81c8"),Br=s.n(Wr),zr=s("1a78"),Gr=s.n(zr),Kr=s("db04"),Xr=s.n(Kr),Zr=s("72a2"),Yr=s.n(Zr),eo=s("c7d2"),to=s.n(eo),so=s("b5ac"),ao=s.n(so),io=s("3c09"),no=s.n(io),ro=s("876f"),oo=s.n(ro),lo=s("6c39"),co=s.n(lo),uo=s("183c"),ho=s.n(uo),vo=s("0070"),fo=s.n(vo),mo=s("87ff"),po=s.n(mo),go=s("8ec9"),bo=s.n(go),wo={name:"Icon",components:{ActivityIcon:Ln.a,AlertCircleIcon:Un.a,AlertTriangleIcon:Jn.a,ArrowDownCircleIcon:Vn.a,CheckCircleIcon:Bn.a,ChevronDownIcon:Gn.a,ChevronLeftIcon:Xn.a,ChevronRightIcon:Yn.a,ChevronUpIcon:tr.a,ClockIcon:ar.a,CpuIcon:nr.a,DatabaseIcon:or.a,DiscIcon:cr.a,Edit2Icon:dr.a,GithubIcon:vr.a,HashIcon:mr.a,HeartIcon:gr.a,HelpCircleIcon:wr.a,ImageIcon:yr.a,InfoIcon:qr.a,LayersIcon:kr.a,LinkIcon:Tr.a,ListIcon:jr.a,LockIcon:Pr.a,MailIcon:Rr.a,MapIcon:Nr.a,MenuIcon:Ir.a,PaperclipIcon:Lr.a,PercentIcon:Ur.a,PieChartIcon:Jr.a,SearchIcon:Vr.a,SettingsIcon:Br.a,ShareIcon:Gr.a,SlashIcon:Xr.a,SmileIcon:Yr.a,StarIcon:to.a,TerminalIcon:ao.a,Trash2Icon:no.a,TwitterIcon:oo.a,UserIcon:co.a,UsersIcon:ho.a,XIcon:fo.a,XCircleIcon:po.a,ZapIcon:bo.a},props:["name"],computed:{component:function(){return this.name.split("-").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join("")+"Icon"}}},_o=wo,yo=(s("1c62"),Object(f["a"])(_o,An,In,!1,null,null,null)),Co=yo.exports;tn="share"==Object({NODE_ENV:"production",VUE_APP_SHARING_URL:"https://clockwork.underground.works/ingest",VUE_APP_VERSION:"5.1.0",VUE_APP_CREDITS_URL:"https://meta.underground.works",BASE_URL:""}).VUE_APP_PLATFORM?new wn:gn.runningAsExtension()?new gn:new bn;var qo=new qn,$o=new Tn,ko=new Dn(qo,$o,tn),xo=new _n($o),To=new yn(tn),So=new Cn(ko),jo=new $n(tn,ko),Oo=new xn($o,tn),Po=new jn($o),Do=new Rn(tn,ko),Ro=new En,Eo=new Nn(ko),No=new Mi(tn,ko),Ao={$requests:$o,$platform:tn,$authentication:xo,$credits:To,$onDemand:jo,$profiler:Oo,$requestsSearch:Po,$settings:ko,$sharing:Do,$store:qo,$updateNotification:Eo,$whatsNew:No,$request:null,activeDetailsTab:"performance",showIncomingRequests:!0};tn.init(Ao),So.register(),jo.enableProfiling(),Ro.register(),yt["a"].mixin({data:function(){return{global:Ao}},computed:Object.entries(Ao).reduce((function(e,t){var s=Object(a["a"])(t,2),i=s[0];s[1];return e[i]=function(){return this.global[i]},e}),{})}),yt["a"].component("icon",Co),new yt["a"]({render:function(e){return e(Wi)}}).$mount("#app")},5773:function(e,t,s){},"5a57":function(e,t,s){"use strict";s("3df1")},"5c0b":function(e,t,s){"use strict";s("9c0c")},"675c":function(e,t,s){},"68f4":function(e,t,s){},"6b0a":function(e,t,s){"use strict";s("72c1")},"6ff6":function(e,t,s){"use strict";s("675c")},"70b4":function(e,t,s){"use strict";s("cd20")},"72c1":function(e,t,s){},7807:function(e,t,s){"use strict";s("7811")},7811:function(e,t,s){},"7e04":function(e,t,s){"use strict";s("fcc2")},8161:function(e,t,s){"use strict";s("0340")},"82a0":function(e,t,s){},8394:function(e,t,s){},"850c":function(e,t,s){},"85ae":function(e,t,s){},"86a4":function(e,t,s){"use strict";s("23b2")},"8e9b":function(e,t,s){"use strict";s("be8b")},"9a64":function(e,t,s){"use strict";s("abfd")},"9b34":function(e,t,s){"use strict";s("850c")},"9c0c":function(e,t,s){},"9cad":function(e,t,s){"use strict";s("a4c6")},"9fca":function(e,t,s){},a4c6:function(e,t,s){},a5d3:function(e,t,s){"use strict";s("334f")},abfd:function(e,t,s){},af36:function(e,t,s){"use strict";s("8394")},b72e:function(e,t,s){"use strict";s("d257")},be8b:function(e,t,s){},c419:function(e,t,s){"use strict";s("39de")},c563:function(e,t,s){"use strict";s("3c48")},c677:function(e,t,s){},cd20:function(e,t,s){},d0e3:function(e,t,s){},d257:function(e,t,s){},d3ec:function(e,t,s){"use strict";s("105c")},d75a:function(e,t,s){},d852:function(e,t,s){"use strict";s("d75a")},d901:function(e,t,s){"use strict";s("09a0")},e01a5:function(e,t,s){},ef62:function(e,t,s){"use strict";s("1bca")},f16e:function(e,t,s){"use strict";s("2fa3")},f407:function(e,t,s){"use strict";s("3f73")},f939:function(e,t,s){},fcc2:function(e,t,s){}});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoianMvYXBwLjFiMTdlNWU2LmpzIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vL2pzL2FwcC4xYjE3ZTVlNi5qcyJdLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VSb290IjoiIn0=