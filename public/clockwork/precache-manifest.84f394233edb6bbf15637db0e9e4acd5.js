self.__precacheManifest = (self.__precacheManifest || []).concat([
  {
    "revision": "8d099129fa5ea5174496",
    "url": "css/app.515e4027.css"
  },
  {
    "revision": "611a8f3ac0aa856f4018f676d719c67f",
    "url": "img/appearance-auto-icon.png"
  },
  {
    "revision": "4a9a5b7dee3fc2c7d2a684e63c447b55",
    "url": "img/appearance-dark-icon.png"
  },
  {
    "revision": "2439692fa652a49e5c3d820fd67cdc27",
    "url": "img/appearance-light-icon.png"
  },
  {
    "revision": "bb5196d7b075e39fd6a661ba44aa6a92",
    "url": "img/whats-new/5.0/client-metrics.png"
  },
  {
    "revision": "d78cafd4b6be96deda3eb2e9954b77ab",
    "url": "img/whats-new/5.0/clockwork-5.png"
  },
  {
    "revision": "81a43ef164ad401de937091824436e75",
    "url": "img/whats-new/5.0/models-tab.png"
  },
  {
    "revision": "8289742997480ce6748e3f819b68ca0e",
    "url": "img/whats-new/5.0/notifications-tab.png"
  },
  {
    "revision": "942ad4031f62aac1061c257428b9ad40",
    "url": "img/whats-new/5.0/timeline.png"
  },
  {
    "revision": "6c9e98b21595855028a29014642fee4c",
    "url": "img/whats-new/5.0/toolbar.png"
  },
  {
    "revision": "a403ddd6fac00289df60a70163f8aca5",
    "url": "img/whats-new/5.1/database-queries.png"
  },
  {
    "revision": "5f78ccbaa09abbd6ce825683b6950605",
    "url": "index.html"
  },
  {
    "revision": "8d099129fa5ea5174496",
    "url": "js/app.1b17e5e6.js"
  },
  {
    "revision": "70804b62af80bfb6fd7b",
    "url": "js/chunk-vendors.a7f629a4.js"
  },
  {
    "revision": "7bcaca7d628e3fabb246a02425e072dd",
    "url": "manifest.json"
  }
]);