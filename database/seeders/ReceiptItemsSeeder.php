<?php

namespace Database\Seeders;

use App\CompanyReceiptItem;
use Illuminate\Database\Seeder;

class ReceiptItemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $receipt_items = CompanyReceiptItem::whereNull('user_id')->with('receipt')->get();

        foreach ($receipt_items as $receipt_item) {
            $receipt_item->update([
                'user_id' => $receipt_item->receipt->user_id
            ]);
        }
    }
}
