<?php

namespace Database\Seeders;

use App\Company;
use Illuminate\Database\Seeder;

class AddContactedToAllCompany extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach (Company::cursor() as $key => $company) {
            $company->lead()->updateOrCreate([
                'company_id' => $company->id,
            ], [
                'contacted' => true,
            ]);
        }
    }
}
