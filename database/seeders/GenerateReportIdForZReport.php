<?php

namespace Database\Seeders;

use App\Company;
use App\CompanyZReport;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GenerateReportIdForZReport extends Seeder
{
    use HasUniqueCode;


    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = Company::select('id', 'viva_account_id', 'viva_merchant_id')
            ->whereNotNull('viva_account_id')->whereNotNull('viva_merchant_id')
            ->lazy();

        foreach ($companies as $company) {
            $z_reports = CompanyZReport::where('report_id', null)
                ->where('company_id', $company->id)->lazy();

            DB::transaction(function () use ($z_reports, $company) {
                $value = $this->getIncrementalValueModel($company->id);

                foreach ($z_reports as $key => $z_report) {
                    $z_report->report_id = $value->report_sequence_number;
                    $z_report->save();

                    $value->report_sequence_number += 1;
                    $value->save();
                }
            });
        }
    }
}
