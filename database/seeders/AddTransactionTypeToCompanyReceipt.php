<?php

namespace Database\Seeders;

use App\CompanyReceipt;
use Illuminate\Database\Seeder;

class AddTransactionTypeToCompanyReceipt extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $company_receipts = CompanyReceipt::where('transaction_id', '!=', null)->where('transaction_type_id', null)->lazy();

        foreach ($company_receipts as $key => $receipt) {
            $transaction = $receipt->company->findTransaction($receipt->transaction_id);

            $receipt->transaction_type_id = $transaction->transactionTypeId;
            $receipt->save();
        }
    }
}
