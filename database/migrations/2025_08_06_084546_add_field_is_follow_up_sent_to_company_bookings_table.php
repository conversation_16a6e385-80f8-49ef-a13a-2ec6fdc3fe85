<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldIsFollowUpSentToCompanyBookingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_bookings', function (Blueprint $table) {
            $table->boolean('is_follow_up_email_sent')->default(false)->nullable();
            $table->boolean('is_follow_up_sms_sent')->default(false)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_bookings', function (Blueprint $table) {
            $table->dropColumn('is_follow_up_email_sent');
            $table->dropColumn('is_follow_up_sms_sent');
        });
    }
}