<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUniqueInSubscriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->unique('stripe_id');
        });
        Schema::table('subscription_items', function (Blueprint $table) {
            $table->unique('stripe_id');

            $table->dropUnique(['subscription_id', 'stripe_plan']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropUnique('stripe_id');
        });

        Schema::table('subscription_items', function (Blueprint $table) {
            $table->dropUnique('stripe_id');

            $table->unique(['subscription_id', 'stripe_plan']);
        });
    }
}
