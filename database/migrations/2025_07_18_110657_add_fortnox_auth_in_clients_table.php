<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFortnoxAuthInClientsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->text('fortnox_auth')->nullable();
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->string('fortnox_customer_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('fortnox_auth');
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->dropColumn('fortnox_customer_id');
        });
    }
}
