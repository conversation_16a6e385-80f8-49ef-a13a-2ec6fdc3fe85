<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMedicalDeviceMaintenancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('medical_device_maintenances', function (Blueprint $table) {
            $table->id();

            $table->foreignId('medical_device_id')->nullable()->constrained('medical_devices')->cascadeOnDelete()->cascadeOnUpdate();
            $table->longText('title')->nullable();
            $table->longText('protocol_path')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('medical_device_maintenances');
    }
}
