<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsAboutCancellationToClientLetterOfConsentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client_letter_of_consents', function (Blueprint $table) {
            $table->boolean('is_cancelled')->nullable()->default(false);
            $table->longText('cancel_note')->nullable();
            $table->foreignId('cancelled_by_id')->nullable()->constrained('users')->nullOnDelete()->cascadeOnUpdate();
            $table->timestamp('cancelled_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client_letter_of_consents', function (Blueprint $table) {
            $table->dropColumn('is_cancelled');
            $table->dropColumn('cancel_note');
            $table->dropForeign(['cancelled_by_id']);
            $table->dropColumn('cancelled_by_id');
            $table->dropColumn('cancelled_at');
        });
    }
}