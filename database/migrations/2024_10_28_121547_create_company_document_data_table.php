<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCompanyDocumentDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_document_data', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('company_document_id');
            $table->longText('pdf');
            $table->longText('response');
            $table->longText('questions');
            $table->string('version');
            $table->longText('sign')->nullable();
            $table->timestamp('signed_at')->nullable();
            $table->unsignedBigInteger('signed_by_id')->nullable();
            $table->string('process');
            $table->timestamps();

            $table->foreign('company_document_id')->references('id')->on('company_documents')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('signed_by_id')->references('id')->on('users')->onDelete('restrict')->onUpdate('cascade');
            $table->foreign('company_id')->references('id')
                ->on('companies')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_document_data');
    }
}
