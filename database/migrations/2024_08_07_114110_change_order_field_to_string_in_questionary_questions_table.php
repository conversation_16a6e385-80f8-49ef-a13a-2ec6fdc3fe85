<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeOrderFieldToStringInQuestionaryQuestionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('questionary_questions', function (Blueprint $table) {
            $table->string('order')->collation('ascii_bin')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('questionary_questions', function (Blueprint $table) {
            $table->unsignedInteger('order')->default(0)->change();
        });
    }
}
