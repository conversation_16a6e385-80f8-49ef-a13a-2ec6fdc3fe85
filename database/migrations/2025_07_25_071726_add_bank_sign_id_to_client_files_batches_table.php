<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBankSignIdToClientFilesBatchesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client_files_batches', function (Blueprint $table) {
            $table->boolean('is_signed_by_bank_id')->default(false)->nullable();
            $table->longText('signed_by_bank_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client_files_batches', function (Blueprint $table) {
            $table->dropColumn('is_signed_by_bank_id');
            $table->dropColumn('signed_by_bank_id');
        });
    }
}
