<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVideoCallMembersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('video_call_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('video_call_id')->constrained('video_calls')->cascadeOnDelete()->cascadeOnUpdate();
            $table->nullableMorphs('memberable');
            $table->timestamp('not_before')->nullable();
            $table->timestamp('expires')->nullable();
            $table->boolean('eject_at_room_exp')->default(false);
            $table->boolean('is_owner')->default(false);
            $table->string('user_name', 1000)->nullable();
            $table->string('user_id')->nullable();
            $table->string('token', 1000);
            $table->string('key', 1000);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('ended_at')->nullable();

            $table->timestamp('notified_at')->nullable();
            $table->unsignedInteger('notify_before_mins')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('video_call_members');
    }
}
