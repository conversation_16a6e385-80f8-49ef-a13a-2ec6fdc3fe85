<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGeneralTemplateQuestionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('general_template_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('general_template_id');
            $table->text('question');
            $table->json('options')->nullable();
            $table->longText('default')->nullable();
            $table->boolean('required');
            $table->string('type');
            $table->string('order')->collation('ascii_bin');
            $table->timestamps();

            $table->foreign('general_template_id')->references('id')
                ->on('general_templates')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('general_template_questions');
    }
}
