<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMedicalDevicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('medical_devices', function (Blueprint $table) {
            $table->id();

            $table->foreignId('company_id')->nullable()->constrained('companies')->cascadeOnDelete()->cascadeOnUpdate();
            $table->longText('product_name')->nullable();
            $table->longText('model')->nullable();
            $table->longText('brand')->nullable();
            $table->longText('serial_number')->nullable();
            $table->longText('supplier')->nullable();
            $table->longText('performed_maintenance')->nullable();
            $table->longText('upcoming_maintenance')->nullable();
            $table->boolean('compliance_declared')->default(false);
            $table->longText('upload_manual')->nullable();
            $table->longText('supplier_agreement')->nullable();
            $table->softDeletes();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('medical_devices');
    }
}
