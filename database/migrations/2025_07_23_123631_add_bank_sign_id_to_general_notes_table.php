<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBankSignIdToGeneralNotesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('general_notes', function (Blueprint $table) {
            $table->boolean('is_signed_by_bank_id')->default(false)->nullable();
            $table->longText('signed_by_bank_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('general_notes', function (Blueprint $table) {
            $table->dropColumn('is_signed_by_bank_id');
            $table->dropColumn('signed_by_bank_id');
        });
    }
}