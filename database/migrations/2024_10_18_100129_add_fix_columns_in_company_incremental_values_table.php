<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFixColumnsInCompanyIncrementalValuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_incremental_values', function (Blueprint $table) {
            $table->renameColumn('max_pos_code', 'item_sequence_number');
            $table->renameColumn('pos_sequence_number', 'receipt_sequence_number');
            $table->string('report_sequence_number')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_incremental_values', function (Blueprint $table) {
            $table->renameColumn('item_sequence_number', 'max_pos_code');
            $table->renameColumn('receipt_sequence_number', 'pos_sequence_number');

            $table->dropColumn('pos_sequence_number');
        });
    }
}
