<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldForFinishedBookingToCompanyServicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_services', function (Blueprint $table) {
            $table->foreignId('finished_email_template_id')->nullable()->constrained('email_templates')->nullOnDelete()->nullOnDelete();
            $table->foreignId('finished_sms_template_id')->nullable()->constrained('sms_templates')->nullOnDelete()->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */

    public function down()
    {
        Schema::table('company_services', function (Blueprint $table) {
            $table->dropForeign('company_services_finished_email_template_id_foreign');
            $table->dropForeign('company_services_finished_sms_template_id_foreign');
            $table->dropColumn('finished_email_template_id');
            $table->dropColumn('finished_sms_template_id');
        });
    }
}
