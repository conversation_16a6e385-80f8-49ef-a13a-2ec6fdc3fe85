<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCompanyExtraFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_extra_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained('companies')->cascadeOnDelete()->cascadeOnUpdate();

            $table->string('place_id')->nullable();
            $table->string('rating')->nullable();
            $table->json('reviews')->nullable();
            $table->string('user_rating_count')->nullable();
            $table->json('google_maps_links')->nullable();

            $table->json('opening_hours')->nullable();
            $table->string('title')->nullable();
            $table->string('sub_text')->nullable();
            $table->string('mode_of_payment', 1000)->nullable();
            $table->longText('about')->nullable();
            $table->json('images')->nullable();
            $table->json('social_links')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_extra_fields');
    }
}
