<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFortnoxInvoiceItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fortnox_invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('fortnox_invoice_id')->constrained('fortnox_invoices')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('user_id')->nullable()->constrained('users')->cascadeOnDelete()->cascadeOnUpdate();
            $table->longText('description');
            $table->longText('price');
            $table->longText('quantity');
            $table->longText('unit');
            $table->longText('vat');
            $table->longText('total');
            $table->longText('discount_type')->nullable();
            $table->longText('discount_value')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fortnox_invoice_items');
    }
}
