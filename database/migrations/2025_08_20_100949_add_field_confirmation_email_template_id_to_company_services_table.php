<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldConfirmationEmailTemplateIdToCompanyServicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_services', function (Blueprint $table) {
            $table->foreignId('confirmation_email_template_id')->nullable()->constrained('email_templates')->nullOnDelete()->nullOnDelete();
            $table->foreignId('confirmation_sms_template_id')->nullable()->constrained('sms_templates')->nullOnDelete()->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_services', function (Blueprint $table) {
            $table->dropForeign('confirmation_email_template_id_foreign');
            $table->dropForeign('confirmation_sms_template_id_foreign');
            $table->dropColumn('confirmation_email_template_id');
            $table->dropColumn('confirmation_sms_template_id');
        });
    }
}