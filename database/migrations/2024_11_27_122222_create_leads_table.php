<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeadsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->string('client_name');
            $table->string('company_name');
            $table->string('email')->nullable();
            $table->string('mobile_number')->nullable();
            $table->string('country_code')->nullable();
            $table->string('type')->nullable();
            $table->string('status')->nullable();
            $table->string('industry')->nullable();
            $table->string('size')->nullable();
            $table->text('about')->nullable();
            $table->string('outcome')->nullable();
            $table->text('outcome_note')->nullable();

            $table->foreignId('company_id')->nullable()->constrained('companies')->nullOnDelete()->nullOnDelete();

            $table->unique(['email', 'mobile_number']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leads');
    }
}
