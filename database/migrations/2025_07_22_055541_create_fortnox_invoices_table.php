<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFortnoxInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fortnox_invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->nullable()->constrained('clients')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('company_id')->nullable()->constrained('companies')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('user_id')->nullable()->constrained('users')->cascadeOnDelete()->cascadeOnUpdate();
            $table->longText('fortnox_document_number')->nullable();
            $table->longText('fortnox_customer_number')->nullable();
            $table->longText('customer_name')->nullable();
            $table->longText('currency');
            $table->longText('total');
            $table->longText('total_vat');
            $table->longText('net');
            $table->longText('gross');
            $table->longText('balance');
            $table->longText('ocr')->nullable();
            $table->longText('url')->nullable();
            $table->longText('discount_type')->nullable();
            $table->longText('discount_value')->nullable();
            $table->longText('note')->nullable();
            $table->timestamp('booked_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fortnox_invoices');
    }
}
