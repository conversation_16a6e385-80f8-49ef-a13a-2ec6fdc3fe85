<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTransactionTypeIdInCompanyReceiptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_receipts', function (Blueprint $table) {
            $table->longText('transaction_type_id')->nullable();
        });
        Schema::table('company_receipt_refunds', function (Blueprint $table) {
            $table->longText('transaction_type_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_receipts', function (Blueprint $table) {
            $table->dropColumn('transaction_type_id');
        });
        Schema::table('company_receipt_refunds', function (Blueprint $table) {
            $table->dropColumn('transaction_type_id');
        });
    }
}
