<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCompanyBookingMetaDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_booking_meta_data', function (Blueprint $table) {
            $table->id();

            $table->foreignId('company_booking_id')->nullable()->constrained('company_bookings')->nullOnDelete()->cascadeOnUpdate();
            $table->longText('key')->nullable();
            $table->longText('value')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_booking_meta_data');
    }
}
