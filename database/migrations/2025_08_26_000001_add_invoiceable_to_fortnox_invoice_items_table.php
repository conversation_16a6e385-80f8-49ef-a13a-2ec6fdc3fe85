<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInvoiceableToFortnoxInvoiceItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fortnox_invoice_items', function (Blueprint $table) {
            $table->nullableMorphs('invoiceable');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fortnox_invoice_items', function (Blueprint $table) {
            $table->dropMorphs('invoiceable');
        });
    }
}
