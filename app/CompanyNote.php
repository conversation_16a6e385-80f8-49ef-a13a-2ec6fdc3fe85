<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyNote extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'company_notes';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'company_id',
        'title',
        'details'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules=[];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}