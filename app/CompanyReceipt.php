<?php

namespace App;

use App\Traits\Encryptable;
use App\CompanyReceiptItem;
use App\CompanyReceiptRefund;
use App\Http\Integrations\POSPayment\Facades\POSPayment;
use App\Services\POS\PaymentService;
use App\Traits\POS\HasCCUControlCode;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphTo;

enum ReceiptExport
{
    case PDF_DOWNLOAD;
    case PDF_STREAM;
    case HTML;
    case MAIL;
    case STRING;
}

enum ReceiptType
{
    case normal;
    case kopia;
}


class CompanyReceipt extends Model
{
    use HasFactory, Encryptable, HasCCUControlCode, HasUniqueCode;

    const
        PENDING = "PENDING",
        PROCESSING = "PROCESSING",
        PAID = "PAID",
        CANCELLED = "CANCELLED",
        REFUNDED = "REFUNDED",
        PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED",
        ABORTED = "ABORTED";

    const DISCOUNT_PERCENTAGE = 'percentage',
        DISCOUNT_VALUE = 'value';

    const PAYMENT_METHOD_VIVA = 'viva',
        PAYMENT_METHOD_VIVA_ONLINE = 'viva_online',
        PAYMENT_METHOD_SWISH = 'swish';

    const TRANSACTION_TYPE_KLARNA = 52;

    //TABLE
    public $table = 'company_receipts';

    //FILLABLES
    protected $fillable = [
        'client_id',
        'company_id',
        'user_id',

        'gift_card_id',
        'gift_card_amount',

        'discount_type',
        'discount_value',

        'sub_total',
        'paid_amount',
        'total_tax_information',
        'refund_amount',

        'status',
        'note',

        'terminal_id',

        'session_id',

        'payment_mode',
        'gift_card_refund_amount',

        'paid_at',

        'downloaded',
        'ctu_id',
        'control_code',
        'user_downloads',
        'viva_receipt_id',

        'payment_method',

        'relatable_id',
        'relatable_type',

        'transaction_id',
        'transaction_type_id',
    ];

    protected $encrypted = [
        'gift_card_amount',

        'discount_type',
        'discount_value',

        'sub_total',
        'paid_amount',
        'total_tax_information',

        'gift_card_refund_amount',
        'refund_amount',
        'note',
        'payment_mode',

        'ctu_id',
        'control_code',

        'transaction_id',
        'transaction_type_id',

        'viva_receipt_id',
    ];

    //HIDDEN
    protected $hidden = [
        'user_downloads'
    ];

    //APPENDS
    protected $appends = [
        'total_formatted',
        'paid_amount_formatted',
        'sub_total_formatted',
        'total_tax_information_formatted',
        'refund_amount_formatted',
        'discount_amount_formatted',
        'gift_card_remaining_amount',
        'remaining_amount',
        'transaction_type',
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $value = self::getIncrementalValueModel($query->company_id);

            $query->viva_receipt_id = $value->receipt_sequence_number;

            $value->receipt_sequence_number += 1;
            $value->save();
        });
    }

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'user_downloads' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS

    public function isOnline()
    {
        return $this->payment_method == self::PAYMENT_METHOD_VIVA_ONLINE;
    }

    public function relatable(): MorphTo
    {
        return $this->morphTo();
    }

    public function items()
    {
        return $this->hasMany(CompanyReceiptItem::class, 'receipt_id');
    }

    public function refunds()
    {
        return $this->hasMany(CompanyReceiptRefund::class, 'receipt_id');
    }

    public function client()
    {
        return $this->belongsTo(ClientFixed::class, 'client_id');
    }

    public function terminal()
    {
        return $this->belongsTo(CompanyTerminal::class, 'terminal_id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function gift_card()
    {
        return $this->belongsTo(CompanyGiftCard::class, 'gift_card_id');
    }

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function paid_for_gift_card()
    {
        return $this->morphOne(CompanyGiftCard::class, 'payable')->latest();
    }

    function shouldDecreaseQuantity()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING, self::PAID]);
    }

    function shouldIncreaseQuantity()
    {
        return in_array($this->status, [self::CANCELLED, self::ABORTED, self::REFUNDED]);
    }

    function shouldIncreaseGiftCard()
    {
        return in_array($this->status, [self::CANCELLED, self::ABORTED]);
    }

    function shouldDecreaseGiftCard()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING, self::PAID]);
    }

    public function allowRefund()
    {
        if ($this->paid_for_gift_card_exists) return false;

        return in_array($this->status, [self::PAID, self::REFUNDED, self::PARTIALLY_REFUNDED]);
    }

    public function allowAbort()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING]);
    }

    public function allowRetry()
    {
        return in_array($this->status, [self::CANCELLED, self::ABORTED]);
    }

    public function allowExport()
    {
        return in_array($this->status, [self::PAID, self::REFUNDED, self::PARTIALLY_REFUNDED]);
    }

    public function allowUpdateStatus()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING]);
    }

    function shouldChangeQuantity($new_status, $old_status)
    {
        if (!$new_status) return false;

        $new = in_array($new_status, [self::PENDING, self::PROCESSING, self::PAID]);
        $old = in_array($old_status, [self::PENDING, self::PROCESSING, self::PAID]);

        return $new != $old;
    }

    function isKlarna()
    {
        return $this->transaction_type_id == self::TRANSACTION_TYPE_KLARNA;
    }

    function getPaymentModeAttribute()
    {
        $paymentMode = "";

        $paymentMode .= $this->gift_card_id ? __('pos_strings.gift_card.gift_card') : "";

        if ($this->paid_amount_formatted > 0 && $this->payment_method == self::PAYMENT_METHOD_VIVA_ONLINE) {
            $paymentMode .= ($this->gift_card_id ? " + " : "") . __('pos_strings.viva.viva_online');
        }

        if ($this->paid_amount_formatted > 0 && $this->payment_method == self::PAYMENT_METHOD_VIVA) {
            $paymentMode .= ($this->gift_card_id ? " + " : "") . __('pos_strings.viva.viva');
        }

        if ($this->paid_amount_formatted > 0 && $this->payment_method == self::PAYMENT_METHOD_SWISH) {
            $paymentMode .= ($this->gift_card_id ? " + " : "") . __('pos_strings.swish.swish');
        }

        return encrypt($paymentMode);
    }

    function getPaymentMethodAttribute($value)
    {
        if (!$value) {
            return self::PAYMENT_METHOD_VIVA;
        }

        try {
            if (strlen($value) > 50) {
                return decrypt($value);
            }
            return $value;
        } catch (\Throwable $th) {
            return $value;
        }
    }

    function getTransactionTypeAttribute()
    {
        if ($this->transaction_type_id) {
            return config("viva_wallet.transaction_type.{$this->transaction_type_id}", "Error");
        }

        return '';
    }

    function getOrderCodeAttribute()
    {
        if ($this->payment_method = self::PAYMENT_METHOD_VIVA_ONLINE) {
            return $this->session_id;
        }

        return null;
    }

    function generateOrderLink(?string $color = '5551CE')
    {
        if ($this->order_code) {
            return POSPayment::account("testMerchantId")->payment_order()->returnOrderUrl($this->order_code, $color);
        }

        return null;
    }

    function getTotalFormattedAttribute()
    {
        return self::roundValue($this->sub_total_formatted + $this->total_tax_information_formatted - $this->discount_amount_formatted);
    }

    function getPaidAmountFormattedAttribute()
    {
        return self::roundValue($this->paid_amount);
    }

    function getDiscountAmountFormattedAttribute()
    {
        if ($this->discount_type == self::DISCOUNT_VALUE) {
            return self::roundValue($this->discount_value);
        }

        if ($this->discount_type == self::DISCOUNT_PERCENTAGE) {
            // TOTAL (subtotal + total_tax) * (discount_value / 100)
            return self::roundValue(($this->sub_total + $this->total_tax_information) * ($this->discount_value / 100));
        }

        return 0;
    }

    function getSubTotalFormattedAttribute()
    {
        return self::roundValue($this->sub_total);
    }

    function getTotalTaxInformationFormattedAttribute()
    {
        return self::roundValue($this->total_tax_information);
    }

    function getDiscountTaxInformationFormattedAttribute()
    {
        $total_tax = $this->total_tax_information;
        $sub_total = $this->sub_total;

        $total = $sub_total + $total_tax;

        $tax_percentage = 0;

        if ($total != 0)
            $tax_percentage = ($total_tax / $total) * 100;

        $discount_vat = $this->discount_amount_formatted * ($tax_percentage / 100);

        return self::roundValue($discount_vat);
    }

    function getRefundAmountFormattedAttribute()
    {
        if (!$this->refund_amount) return null;
        return self::roundValue($this->refund_amount);
    }

    //getRemainingValue
    public function getGiftCardRemainingAmountAttribute()
    {
        return self::roundValue(($this->gift_card_amount ?? 0) - ($this->gift_card_refund_amount ?? 0));
    }

    //getRemainingValue
    public function getRemainingAmountAttribute()
    {
        return self::roundValue(($this->paid_amount ?? 0) - ($this->refund_amount ?? 0));
    }

    //getRemainingValue
    public function getTotalRemainingAmountAttribute()
    {
        return self::roundValue((($this->gift_card_amount ?? 0) - ($this->gift_card_refund_amount ?? 0)) + ($this->paid_amount ?? 0) - ($this->refund_amount ?? 0));
    }

    function getVivaReceiptIdAttribute($value)
    {
        if ($value) {
            try {
                return encrypt(str_pad(decrypt($value), 4, "0", STR_PAD_LEFT));
            } catch (\Throwable $th) {
            }
        }

        return encrypt(str_pad($value ?? $this->id, 4, "0", STR_PAD_LEFT));
    }

    static function roundValue($value)
    {
        return round($value * 100) / 100;
    }


    public function scopePaid($query)
    {
        $query->where('paid_at', '!=', null);
    }

    public function scopePaidAtBetween($query, string $start_date, string $end_date)
    {
        $query->where('paid_at', '>=', $start_date)->where('paid_at', '<', $end_date);
    }

    public function scopeCancelAtBetween($query, string $start_date, string $end_date)
    {
        $query->where('created_at', '>=', $start_date)->where('created_at', '<', $end_date)->where((function ($query) {
            $query->where('status', CompanyReceipt::CANCELLED)
                ->orWhere('status', CompanyReceipt::ABORTED);
        }));
    }

    public function scopeCreatedAtBetween($query, string $start_date, string $end_date)
    {
        $query->where('created_at', '>=', $start_date)->where('created_at', '<', $end_date);
    }

    public function generateTaxLevel()
    {
        $items = $this->items;

        return collect($items->reduce(function ($carry, $item) {

            $selling_price = $item->discounted_price;

            $price = $selling_price / (1 + ($item->tax_information / 100));

            $tax = ($selling_price - $price) * ($item->quantity ?? 1);
            $net = $price * ($item->quantity ?? 1);

            $new_tax = isset($carry["$item->tax_information%"]) ? ($carry["$item->tax_information%"]['tax'] + $tax) : $tax;
            $new_net = isset($carry["$item->tax_information%"]) ? ($carry["$item->tax_information%"]['net'] + $net) : $net;
            $new_gross = isset($carry["$item->tax_information%"]) ? ($carry["$item->tax_information%"]['gross'] + $net + $tax) : $net + $tax;

            return array_merge($carry, [
                "$item->tax_information%" => collect([
                    "percentage" => $item->tax_information,
                    "tax" => CompanyReceipt::roundValue($new_tax),
                    "net" => CompanyReceipt::roundValue($new_net),
                    "gross" => CompanyReceipt::roundValue($new_gross),
                ]),
            ]);
        }, []))->sortByDesc('percentage')->values();
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
