<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserCompany extends Model
{
    use HasFactory;

    const PENDING = "PENDING", ACCEPTED = "ACCEPTED", EXPIRED = "EXPIRED", STOPPED = "STOPPED", DELETED = "DELETED";
    //TABLE
    public $table = 'user_companies';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'company_id',
        'price',
        'invite_status',
        'expire_at'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}

    static function setPrescription(UserCompany $user_company)
    {
        ClientPrescription::where('assign_to_id', $user_company->user_id)->whereHas('client', function ($query) use ($user_company) {
            $query = $query->where('clients.company_id', $user_company->company_id);
        })->whereNull('sign_by_id')->update(['assign_to_id' => null]);
        return true;
    }
}
