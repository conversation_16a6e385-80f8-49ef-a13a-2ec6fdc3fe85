<?php

namespace App;

use App\QuestionaryData;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\Model;

class Covid19 extends Model
{
    use LogsActivity, GetEncryptedFile;

    protected $fillable = [
        'data', 'client_id',
    ];

    public function setDataAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['data'] = $value;
    }

    public function getDataAttribute($value)
    {
        try {
            if ($value != null) {
                return json_decode(Crypt::decrypt($value));
            }
        } catch (\Throwable $th) {
        }
        return $value;
    }

    protected function isAssoc(array $arr)
    {
        if (array() === $arr) return false;
        return array_keys($arr) !== range(0, count($arr) - 1);
    }

    public function getDataNewAttribute($value)
    {
        try {
            if ($value != null) {
                $value = json_decode(Crypt::decrypt($value), true);
            }
            //code...
            if ($this->isAssoc($value)) {
                $value = collect($value)->sortBy(function ($data, $key) {
                    // Being sure the string is actually a number
                    if (is_numeric($key))
                        $number = $key + 0;
                    else // Let the number be 0 if the string is not a number
                        $number = 0;
                    return $number;
                })->values()->toArray();
            }
        } catch (\Throwable $th) {
        }
        return $value;
    }


    public function datas()
    {
        return $this->morphMany(QuestionaryData::class, 'modelable');
    }

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    protected static $logAttributes = [];

    protected static $logName = 'covid 19 questionnaries';

    public function getDescriptionForEvent(string $eventName): string
    {
        $client_user = $this->client()->first();
        return $client_user->first_name . " " . $client_user->last_name . "'s covid 19 questionnaries has been {$eventName} by :causer.first_name :causer.last_name";
    }
}
