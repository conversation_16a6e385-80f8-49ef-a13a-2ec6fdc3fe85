<?php

namespace App;

use App\CompanyReceipt;
use App\Traits\Encryptable;
use App\Traits\POS\HasCCUControlCode;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

enum RefundReceiptExport
{
    case PDF_STREAM;
    case PDF_DOWNLOAD;
    case HTML;
    case MAIL;
    case STRING;
}

class CompanyReceiptRefund extends Model
{
    use HasFactory, Encryptable, HasCCUControlCode, HasUniqueCode;

    //TABLE
    // public $table = '';

    const
        PENDING = "PENDING",
        PROCESSING = "PROCESSING",
        CANCELLED = "CANCELLED",
        REFUNDED = "REFUNDED",
        ABORTED = "ABORTED";

    //FILLABLES
    protected $fillable = [
        'receipt_id',
        'gift_card_id',
        'company_id',
        'user_id',
        'sub_total',
        'amount',
        'gift_card_amount',
        'tax_information',
        'discount',
        'note',
        'status',
        'session_id',

        'transaction_id',
        'transaction_type_id',

        'viva_receipt_id',

        'payment_mode',
        'refunded_at',
        'terminal_id',
        'ctu_id',
        'control_code',
        'downloaded',
        'user_downloads',
    ];


    protected $encrypted = [
        'sub_total',
        'amount',
        'gift_card_amount',
        'tax_information',
        'discount',
        'note',
        'payment_mode',

        'ctu_id',
        'control_code',

        'transaction_id',

        'viva_receipt_id',
        'transaction_type_id',
    ];

    //HIDDEN
    protected $hidden = [
        'user_downloads',
    ];

    //APPENDS
    protected $appends = [
        'amount_formatted',
        'total_amount',
        'total_tax_information',
        'transaction_type',
        'tax_percentage',
    ];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        // 'amount' => Round::class,
        'user_downloads' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $value = self::getIncrementalValueModel($query->company_id);

            $query->viva_receipt_id = $value->receipt_sequence_number;

            $value->receipt_sequence_number += 1;
            $value->save();
        });
    }

    public function receipt()
    {
        return $this->belongsTo(CompanyReceipt::class, 'receipt_id');
    }

    public function terminal()
    {
        return $this->belongsTo(CompanyTerminal::class, 'terminal_id');
    }

    public function gift_card()
    {
        return $this->belongsTo(CompanyGiftCard::class, 'gift_card_id');
    }

    public function items()
    {
        return $this->hasMany(CompanyReceiptRefundItem::class, 'refund_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function paid_for_gift_card()
    {
        return $this->morphOne(CompanyGiftCard::class, 'payable')->latest();
    }

    public function allowUpdateStatus()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING]);
    }

    function shouldDecreaseQuantity()
    {
        return in_array($this->status, [self::CANCELLED, self::ABORTED]);
    }

    function shouldIncreaseQuantity()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING, self::REFUNDED]);
    }

    public function allowAbort()
    {
        return in_array($this->status, [self::PENDING, self::PROCESSING]);
    }

    public function allowRetry()
    {
        return in_array($this->status, [self::CANCELLED, self::ABORTED]);
    }

    public function allowExport()
    {
        return in_array($this->status, [self::REFUNDED]);
    }

    function shouldChangeQuantity($new_status, $old_status)
    {
        if (!$new_status) return false;

        $new = in_array($new_status, [self::PENDING, self::PROCESSING, self::REFUNDED]);
        $old = in_array($old_status, [self::PENDING, self::PROCESSING, self::REFUNDED]);

        return $new != $old;
    }

    public function scopeRefundAtBetween($query, string $start_date, string $end_date)
    {
        $query->where('refunded_at', '>=', $start_date)->where('refunded_at', '<', $end_date);
    }

    public function scopeCreatedAtBetween($query, string $start_date, string $end_date)
    {
        $query->where('created_at', '>=', $start_date)->where('created_at', '<', $end_date);
    }

    public function scopeRefunded($query)
    {
        $query->where('refunded_at', '!=', null);
    }

    public function getAmountFormattedAttribute()
    {
        return CompanyReceipt::roundValue($this->amount);
    }

    public function getTotalAmountAttribute()
    {
        return CompanyReceipt::roundValue($this->amount_formatted + $this->gift_card_amount);
    }

    public function getDiscountPercentageAttribute()
    {
        return 100 - ($this->total_amount / ($this->sub_total + ($this->tax_information ?? 0))) * 100;
    }

    function getVivaReceiptIdAttribute($value)
    {
        if ($value) {
            try {
                return encrypt(str_pad(decrypt($value), 4, "0", STR_PAD_LEFT));
            } catch (\Throwable $th) {
            }
        }

        return encrypt(str_pad($value ?? $this->id, 4, "0", STR_PAD_LEFT));
    }

    function getTransactionTypeAttribute()
    {
        if ($this->transaction_type_id) {
            return config("viva_wallet.transaction_type.{$this->transaction_type_id}", "Error");
        }

        return '';
    }

    public function getTaxPercentageAttribute()
    {
        $percentage = 0;

        if ($this->tax_information != 0)
            $percentage = ($this->tax_information / ($this->sub_total + $this->tax_information)) * 100;

        return $percentage;
    }

    function getTotalTaxInformationAttribute($value)
    {
        $tax_amount = $this->total_amount - ($this->total_amount / (1 + ($this->tax_percentage / 100)));

        return CompanyReceipt::roundValue($tax_amount);
    }

    public function generateTaxLevel()
    {
        $items = $this->items;
        $discount_percentage = $this->discount_percentage;
        // dd($discount_percentage);
        return collect($items->reduce(function ($carry, $item) use ($discount_percentage) {
            $selling_price = $item->selling_price - ($item->selling_price * ($discount_percentage / 100));

            $price = $selling_price / (1 + ($item->tax_information / 100));

            $tax = ($selling_price - $price) * ($item->quantity ?? 1);
            $net = $price * ($item->quantity ?? 1);

            $new_tax = isset($carry["$item->tax_information%"]) ? ($carry["$item->tax_information%"]['tax'] + $tax) : $tax;
            $new_net = isset($carry["$item->tax_information%"]) ? ($carry["$item->tax_information%"]['net'] + $net) : $net;
            $new_gross = isset($carry["$item->tax_information%"]) ? ($carry["$item->tax_information%"]['gross'] + $net + $tax) : $net + $tax;

            return array_merge($carry, [
                "$item->tax_information%" => collect([
                    "percentage" => $item->tax_information,
                    "tax" => CompanyReceipt::roundValue($new_tax),
                    "net" => CompanyReceipt::roundValue($new_net),
                    "gross" => CompanyReceipt::roundValue($new_gross),
                ]),
            ]);
        }, []))->sortByDesc('percentage')->values();
    }
}
