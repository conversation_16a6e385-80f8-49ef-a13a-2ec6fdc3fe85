<?php

namespace App\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\GeneralTemplateQuestion;
use App\User;

class GeneralTemplateQuestionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        if ($user->user_role != User::MASTER_ADMIN) {
            return $this->deny(__('prescription_strings.unable_to_access'));
        }
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\GeneralTemplateQuestion  $generalTemplateQuestion
     * @return mixed
     */
    public function view(User $user, GeneralTemplateQuestion $generalTemplateQuestion)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\GeneralTemplateQuestion  $generalTemplateQuestion
     * @return mixed
     */
    public function update(User $user, GeneralTemplateQuestion $generalTemplateQuestion)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\GeneralTemplateQuestion  $generalTemplateQuestion
     * @return mixed
     */
    public function delete(User $user, GeneralTemplateQuestion $generalTemplateQuestion)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\GeneralTemplateQuestion  $generalTemplateQuestion
     * @return mixed
     */
    public function restore(User $user, GeneralTemplateQuestion $generalTemplateQuestion)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\GeneralTemplateQuestion  $generalTemplateQuestion
     * @return mixed
     */
    public function forceDelete(User $user, GeneralTemplateQuestion $generalTemplateQuestion)
    {
        //
    }
}
