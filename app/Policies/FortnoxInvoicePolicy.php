<?php

namespace App\Policies;

use App\Models\FortnoxInvoice;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class FortnoxInvoicePolicy
{
    use HandlesAuthorization;

    public function before(User $user, $ability)
    {
        if ($user->user_role === User::MASTER_ADMIN) {
            return true;
        }

        if (!$user->company->is_pos_on) {
            return false;
        }
    }

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\FortnoxInvoice  $fortnoxInvoice
     * @return mixed
     */
    public function view(User $user, FortnoxInvoice $fortnoxInvoice)
    {
        if ($user->company_id != $fortnoxInvoice->company_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\FortnoxInvoice  $fortnoxInvoice
     * @return mixed
     */
    public function download(User $user, FortnoxInvoice $fortnoxInvoice)
    {
        if ($user->company_id != $fortnoxInvoice->company_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\FortnoxInvoice  $fortnoxInvoice
     * @return mixed
     */
    public function update(User $user, FortnoxInvoice $fortnoxInvoice)
    {
        if ($user->company_id != $fortnoxInvoice->company_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\FortnoxInvoice  $fortnoxInvoice
     * @return mixed
     */
    public function delete(User $user, FortnoxInvoice $fortnoxInvoice)
    {
        if ($user->company_id != $fortnoxInvoice->company_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\FortnoxInvoice  $fortnoxInvoice
     * @return mixed
     */
    public function restore(User $user, FortnoxInvoice $fortnoxInvoice)
    {
        if ($user->company_id != $fortnoxInvoice->company_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\FortnoxInvoice  $fortnoxInvoice
     * @return mixed
     */
    public function forceDelete(User $user, FortnoxInvoice $fortnoxInvoice)
    {
        if ($user->company_id != $fortnoxInvoice->company_id) {
            return false;
        }

        return true;
    }
}
