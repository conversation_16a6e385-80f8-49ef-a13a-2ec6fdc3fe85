<?php

namespace App\Policies;

use App\User;
use App\Client;
use App\ClientKind;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClientKindPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user, Client $client)
    {
        if ($user->user_role == User::USER) {
            $access = $user->accesses()->where('client_id', $client->id)->count();
            if ($access != 0) {
                return true;
            }
            // if ($access == 0) {
            //     return $this->deny("No access to perform this action.");
            // }
        } else {
            $access = $user->company->clients()->where('id', $client->id)->count();
            if ($access != 0 && $user->user_role == User::ADMIN && $user->company->email == $user->email) {
                return true;
            }
            $access = $user->accesses()->where('client_id', $client->id)->count();
            if ($access != 0 && $user->user_role == User::ADMIN) {
                return true;
            }
        }
        
        return $this->deny("No access to perform this action.");
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\ClientKind  $clientKind
     * @return mixed
     */
    public function view(User $user, ClientKind $clientKind)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user, Client $client)
    {
        if ($user->company->is_read_only) {
            return $this->deny(__('strings.account_read_only_message', ['message' => '<EMAIL>']), 403);
        }
        if ($user->user_role == User::USER) {
            $access = $user->accesses()->where('client_id', $client->id)->count();
            if ($access != 0) {
                return true;
            }
            // if ($access == 0) {
            //     return $this->deny("No access to perform this action.");
            // }
        } else {
            $access = $user->company->clients()->where('id', $client->id)->count();
            if ($access != 0 && $user->user_role == User::ADMIN && $user->company->email == $user->email) {
                return true;
            }
            $access = $user->accesses()->where('client_id', $client->id)->count();
            if ($access != 0 && $user->user_role == User::ADMIN) {
                return true;
            }
        }
        
        return $this->deny("No access to perform this action.");
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\ClientKind  $clientKind
     * @return mixed
     */
    public function update(User $user, Client $client)
    {
        if ($user->company->is_read_only) {
            return $this->deny(__('strings.account_read_only_message', ['message' => '<EMAIL>']), 403);
        }

        if ($user->user_role == User::USER) {
            $access = $user->accesses()->where('client_id', $client->id)->count();
            if ($access != 0) {
                return true;
            }
            // if ($access == 0) {
            //     return $this->deny("No access to perform this action.");
            // }
        } else {
            $access = $user->company->clients()->where('id', $client->id)->count();
            if ($user->user_role == User::ADMIN && $user->company->email == $user->email) {
                return true;
            }
            $access = $user->accesses()->where('client_id', $client->id)->count();
            if ($access != 0 && $user->user_role == User::ADMIN) {
                return true;
            }
        }

        return $this->deny("No access to perform this action.");
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\ClientKind  $clientKind
     * @return mixed
     */
    public function delete(User $user, ClientKind $clientKind)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\ClientKind  $clientKind
     * @return mixed
     */
    public function restore(User $user, ClientKind $clientKind)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\ClientKind  $clientKind
     * @return mixed
     */
    public function forceDelete(User $user, ClientKind $clientKind)
    {
        //
    }
}
