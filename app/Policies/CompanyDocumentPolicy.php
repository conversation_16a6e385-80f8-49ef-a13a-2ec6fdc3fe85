<?php

namespace App\Policies;

use App\Models\CompanyDocument;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CompanyDocumentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function view(User $user, CompanyDocument $companyDocument)
    {
        return $companyDocument->company_id == $user->company_id;
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function update(User $user, CompanyDocument $companyDocument)
    {
        return $companyDocument->company_id == $user->company_id;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function delete(User $user, CompanyDocument $companyDocument)
    {
        return $companyDocument->company_id == $user->company_id;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function restore(User $user, CompanyDocument $companyDocument)
    {
        return $companyDocument->company_id == $user->company_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function forceDelete(User $user, CompanyDocument $companyDocument)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function download(User $user)
    {
        if (!$user->company->is_management_on) {
            return false;
        }

        return $user->email == $user->company->email;
    }
}
