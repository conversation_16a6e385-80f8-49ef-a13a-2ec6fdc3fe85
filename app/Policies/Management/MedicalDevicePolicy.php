<?php

namespace App\Policies\Management;

use Illuminate\Auth\Access\HandlesAuthorization;
use App\Models\MedicalDevice;
use App\User;

class MedicalDevicePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    public function access(User $user, MedicalDevice $medicalDevice)
    {
        if ($user->company_id !== $medicalDevice->company_id) {
            return $this->deny(__('management_strings.unable_to_access'));
        }
        return true;
    }
    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\MedicalDevice  $medicalDevice
     * @return mixed
     */
    public function view(User $user, MedicalDevice $medicalDevice)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\MedicalDevice  $medicalDevice
     * @return mixed
     */
    public function update(User $user, MedicalDevice $medicalDevice) {}

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\MedicalDevice  $medicalDevice
     * @return mixed
     */
    public function delete(User $user, MedicalDevice $medicalDevice) {}

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\MedicalDevice  $medicalDevice
     * @return mixed
     */
    public function restore(User $user, MedicalDevice $medicalDevice)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\MedicalDevice  $medicalDevice
     * @return mixed
     */
    public function forceDelete(User $user, MedicalDevice $medicalDevice)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyDocument  $companyDocument
     * @return mixed
     */
    public function download(User $user)
    {
        if (!$user->company->is_management_on) {
            return false;
        }

        return $user->email == $user->company->email;
    }
}
