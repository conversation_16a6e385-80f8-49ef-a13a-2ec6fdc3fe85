<?php

namespace App\Policies;

use App\Models\CompanyCampaignSMS;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CompanyCampaignSMSPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function listClient(User $user, CompanyCampaignSMS $companyCampaignSMS)
    {
        return $companyCampaignSMS->company_id == $user->company_id;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return mixed
     */
    public function view(User $user, CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return mixed
     */
    public function update(User $user, CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return mixed
     */
    public function delete(User $user, CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return mixed
     */
    public function restore(User $user, CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return mixed
     */
    public function forceDelete(User $user, CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }
}
