<?php

namespace App\Policies;

use App\Models\CompanyExtraField;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CompanyExtraFieldPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyExtraField  $companyExtraField
     * @return mixed
     */
    public function view(User $user, CompanyExtraField $companyExtraField)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyExtraField  $companyExtraField
     * @return mixed
     */
    public function update(User $user, CompanyExtraField $companyExtraField)
    {
        return $companyExtraField->company->email == $user->email;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyExtraField  $companyExtraField
     * @return mixed
     */
    public function delete(User $user, CompanyExtraField $companyExtraField)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyExtraField  $companyExtraField
     * @return mixed
     */
    public function restore(User $user, CompanyExtraField $companyExtraField)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\CompanyExtraField  $companyExtraField
     * @return mixed
     */
    public function forceDelete(User $user, CompanyExtraField $companyExtraField)
    {
        //
    }
}
