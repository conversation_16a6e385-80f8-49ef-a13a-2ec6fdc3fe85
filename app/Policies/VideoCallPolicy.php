<?php

namespace App\Policies;

use App\Models\VideoCall;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class VideoCallPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\VideoCall  $videoCall
     * @return mixed
     */
    public function view(User $user, VideoCall $videoCall)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\VideoCall  $videoCall
     * @return mixed
     */
    public function update(User $user, VideoCall $videoCall)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\VideoCall  $videoCall
     * @return mixed
     */
    public function delete(User $user, VideoCall $videoCall)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\VideoCall  $videoCall
     * @return mixed
     */
    public function restore(User $user, VideoCall $videoCall)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\VideoCall  $videoCall
     * @return mixed
     */
    public function forceDelete(User $user, VideoCall $videoCall)
    {
        //
    }

    /**
     * Determine whether the user can end the video call.
     *
     * @param  \App\User  $user
     * @param  \App\Models\VideoCall  $videoCall
     * @return mixed
     */
    public function end(User $user, VideoCall $videoCall)
    {
        return $user->company_id == $videoCall->company_id;
    }
}
