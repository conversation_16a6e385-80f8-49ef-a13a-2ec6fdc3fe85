<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\Sinch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;

class ClientSMS extends Model
{
    use HasFactory;
    use Encryptable;
    use LogsActivity;
    use Sinch;

    //TABLE
    public $table = 'client_sms';

    //FILLABLES
    protected $fillable = [
        'text',
        'number',
        'type',
        'total_message_count',
        'client_after_care_id',
        'statuses',
        'batch_id',
        'user_id',
        'log_id',
        'client_id',
        'company_id',
        'sendable_id',
        'sendable_type',
    ];

    // ENCRYPTED
    protected $encrypted = [
        'text',
        'number',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'statuses' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    protected static $logAttributes = [];

    protected static $logName = 'client sms';

    public function getDescriptionForEvent(string $eventName): string
    {
        return "sms has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }


    public function client()
    {
        return $this->belongsTo(Client::class)->setEagerLoads([]);
    }

    /**
     * Get the parent commentable model (company_campaign_sms or ).
     */
    public function sendable()
    {
        return $this->morphTo();
    }

    //RELATIONSHIPS
    // public function send()
    // {

    // }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
