<?php

namespace App\Jobs;

use App\Company;
use App\Contracts\Services\ZIP\QMSZipServiceInterface;
use App\Mail\QMSZipMailAdmin;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use App\User;
use App\UserNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Throwable;

class QMSZipDownloadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, SaveFile, GetEncryptedFile;

    public $company;

    public $lang;

    public $cacheKey;

    // number of seconds the job can run before timing out
    public $timeout = 3000; // 30 minutes

    public $failOnTimeout = true;

    public function __construct($company, $lang)
    {
        $this->onQueue('client_zip');
        $this->company = $company;

        $this->lang = $lang;
        $this->cacheKey = 'qms_zip_' . $company->id;
    }

    public function handle(QMSZipServiceInterface $qmsZipService)
    {
        app()->setLocale($this->lang ?? 'en');

        Cache::put($this->cacheKey, 'working', 86400);

        $company = $this->company;
        $user = $this->company->users()->where('email', $company->email)->first();

        $file = $qmsZipService->generateAll($company);

        $download_link = (string) $this->getS3SignedUrl($file->filename, 4320);

        UserNotification::create([
            'user_id' => $user->id,
            'title' => __('zip.zip_qms.notification_title', ['company_name' => $company->company_name], $this->lang ?? 'en'),
            'description' => __('zip.zip_qms.notification_description', ['link' => $download_link], $this->lang ?? 'en'),
            'is_read' => 0
        ]);

        // Mail::to($this->company->email)
        $this->logActivity($user, $company);

        if (!!config('app.qms_data_email')) {
            Mail::to(config('app.qms_data_email'))
                ->locale('en')
                ->send(new QMSZipMailAdmin($company));
        }

        Cache::forget($this->cacheKey);
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Cache::forget($this->cacheKey);
    }

    public function logActivity(User $user, Company $company)
    {
        $msg = "{$user->first_name} {$user->last_name} exported QMS data.";
        activity()->performedOn($company)->by($user)->log($msg);
    }
}
