<?php

namespace App\Jobs;

use App\Company;
use App\Contracts\Services\ZIP\QMSZipServiceInterface;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use App\User;
use App\UserNotification;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Throwable;

class QMSMedicalDeviceZipDownloadJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels, SaveFile, GetEncryptedFile;

    public Company $company;

    public User $user;

    public $lang;

    public $send_log;
    public $send_notification;

    public $cacheKey;

    // number of seconds the job can run before timing out
    public $timeout = 600; // 10 minutes

    public $failOnTimeout = false;

    public function __construct(Company $company, User $user, $lang, $send_notification = true, $send_log = true)
    {
        $this->onQueue('client_zip');

        $this->send_log = $send_log;
        $this->send_notification = $send_notification;
        $this->user = $user;
        $this->company = $company;
        $this->lang = $lang;
        $this->cacheKey = 'qms_medical_device_zip_' . $company->id;
    }

    public function handle(QMSZipServiceInterface $qmsZipService)
    {
        app()->setLocale($this->lang ?? 'en');

        Cache::put($this->cacheKey, 'working', 86400);

        $file = $qmsZipService->generateForMedicalDevices($this->company);

        $download_link = (string) $this->getS3SignedUrl($file->filename, 1440);

        if ($this->send_notification) {
            UserNotification::create([
                'user_id' => $this->user->id,
                'title' => __('zip.zip_qms_medical_device.notification_title', ['company_name' => $this->company->company_name], $this->lang ?? 'en'),
                'description' => __('zip.zip_qms_medical_device.notification_description', ['link' => $download_link], $this->lang ?? 'en'),
                'is_read' => 0
            ]);
        }

        if ($this->send_log) {
            $this->logToActivity($this->user, $this->company);
        }

        Cache::forget($this->cacheKey);
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Cache::forget($this->cacheKey);
    }

    public function logToActivity(User $user, Company $company)
    {
        $msg = "{$user->first_name} {$user->last_name} exported QMS medical devices.";
        activity()->performedOn($company)->by($user)->log($msg);
    }
}
