<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientAfterCare extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'clients_after_cares';

    //FILLABLES
    protected $fillable = [
        'client_id',
        'notes',
        'file_path',
        'is_filled'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function aftercare()
    {
        return $this->hasMany(AfterCareData::class, 'client_after_care_id', 'id');
    }

    public function sms()
    {
        return $this->hasOne(ClientSMS::class, 'client_after_care_id', 'id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
