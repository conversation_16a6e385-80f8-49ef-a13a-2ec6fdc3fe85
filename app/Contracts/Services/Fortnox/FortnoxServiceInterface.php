<?php

declare(strict_types=1);

namespace App\Contracts\Services\Fortnox;

use App\Client;
use App\Company;
use App\Http\Integrations\Fortnox\DataObject\Customer;
use App\Models\FortnoxInvoice;
use Exception;
use Psr\Http\Message\StreamInterface;

interface FortnoxServiceInterface
{
    /**
     * Generate authorization URL for Fortnox OAuth
     *
     * @throws Exception
     */
    public function getAuthorizationUrl(Company $company, array $scopes = []): string;

    /**
     * Handle OAuth callback and store access token
     *
     * @throws Exception
     */
    public function handleOAuthCallback(Company $company, string $authorizationCode, string $currentState): void;

    /**
     * Create a Fortnox customer for the client
     *
     * @throws Exception
     */
    public function createCustomer(Client $client): Customer;

    /**
     * Create a Fortnox invoice
     *
     * @throws Exception
     */
    public function createInvoice(
        Client $client,
        array $data,
    ): FortnoxInvoice;

    /**
     * Refresh the access token for the company
     *
     * @throws Exception
     */
    public function refreshToken(Company $company): void;

    /**
     * Download a Fortnox invoice
     *
     * @throws Exception
     */
    public function downloadInvoice(FortnoxInvoice $fortnoxInvoice): StreamInterface;
}
