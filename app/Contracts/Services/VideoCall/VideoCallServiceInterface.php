<?php

namespace App\Contracts\Services\VideoCall;

use App\Models\VideoCall;
use App\User;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface VideoCallServiceInterface
{
    /**
     * @throws Exception
     */
    public function single(User|string $owner, Model|string $participant, array $properties = []): VideoCall;

    /**
     * @throws Exception
     */
    public function group(User|string $owner, array|Collection $participants,  array $properties = []): VideoCall;

    /**
     * @throws Exception
     */
    public function close(VideoCall $video_call): VideoCall;

    /**
     * @throws Exception
     */
    public function update(VideoCall $video_call, User|string $owner, Model|string $participant, array $properties = []): VideoCall;
}
