<?php

declare(strict_types=1);

namespace App\Contracts\Services\ZIP;

use App\Client;
use App\Company;
use App\File;
use App\User;
use Illuminate\Support\Collection;

interface ClientZipServiceInterface
{
    /**
     * @throws Exception
     */
    public function generate(User $user, Client $client): File;

    /**
     * @throws Exception
     */
    public function generateAll(Company $company): Collection;
}
