<?php

declare(strict_types=1);

namespace App\Contracts\Services\ZIP;

use App\Company;
use App\File;

interface QMSZipServiceInterface
{
  /**
   * @throws Exception
   */
  public function generateAll(Company $company): File;

  /**
   * @throws Exception
   */
  public function generateForDocuments(Company $company): File;

  /**
   * @throws Exception
   */
  public function generateForMedicalDevices(Company $company): File;
}
