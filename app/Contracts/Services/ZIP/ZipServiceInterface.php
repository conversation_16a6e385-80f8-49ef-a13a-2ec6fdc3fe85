<?php

declare(strict_types=1);

namespace App\Contracts\Services\ZIP;

use App\File;
use App\User;
use Exception;
use Illuminate\Support\Collection;

interface ZipServiceInterface
{
    /**
     * @throws Exception
     */
    public function make(Collection $zip_data): self;

    /**
     * @throws Exception
     */
    public function payload(string $fileName): array;

    /**
     * @throws Exception
     */
    public function save(string $fileName): string;

    /**
     * @throws Exception
     */
    public function saveFile(User $user, string $fileName): File;
}
