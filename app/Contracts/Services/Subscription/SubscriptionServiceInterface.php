<?php

declare(strict_types=1);

namespace App\Contracts\Services\Subscription;

use App\Company;
use App\Services\Subscription\BillingType;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Collection;
use Stripe\Coupon;

interface SubscriptionServiceInterface
{
    /**
     * @throws Exception
     */
    public function subscribe(Request $request, Company $company): void;

    /**
     * @throws Exception
     */
    public function plans(?Company $company = null, ?BillingType $type = null, ?string $platform = null, ?bool $depricated = null): Collection;

    /**
     * @throws Exception
     */
    public function validation(Request $request): array;

    /**
     * @throws Exception
     */
    public function subscribeToFutureSubscription(Company $company): void;

    /**
     * @throws Exception
     */
    public function subscribeCompleteFreeTrial(Company $company, $force): void;

    /**
     * @throws Exception
     */
    public function getCoupon(string $coupon): ?Coupon;
}
