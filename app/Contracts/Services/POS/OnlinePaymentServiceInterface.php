<?php

declare(strict_types=1);

namespace App\Contracts\Services\POS;

use App\CompanyReceipt;
use App\User;
use Exception;
use Illuminate\Http\Request;

interface OnlinePaymentServiceInterface
{
    /**
     * @throws Exception
     */
    public function processOrder(Request $request, User $user): CompanyReceipt;

    /**
     * @throws Exception
     */
    public function cancelOrder(CompanyReceipt $receipt): CompanyReceipt;
}
