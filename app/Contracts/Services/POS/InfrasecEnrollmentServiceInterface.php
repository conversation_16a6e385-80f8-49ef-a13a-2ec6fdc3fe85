<?php

declare(strict_types=1);

namespace App\Contracts\Services\POS;

use App\Company;
use Exception;

interface InfrasecEnrollmentServiceInterface
{
    /**
     * @throws Exception
     */
    public function store(Company $company): Company;


    /**
     * @throws Exception
     */
    public function update(Company $company): Company;

    /**
     * @throws Exception
     */
    public function close(Company $company): Company;

    /**
     * @throws Exception
     */
    public function open(Company $company): Company;

    /**
     * @throws Exception
     */
    public function checkStatus(Company $company): array;

}
