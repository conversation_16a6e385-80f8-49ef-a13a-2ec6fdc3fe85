<?php

declare(strict_types=1);

namespace App\Contracts\Services\POS;

use App\CompanyReceipt;
use App\User;
use Exception;
use Illuminate\Http\Request;

interface PaymentServiceInterface
{
    /**
     * @throws Exception
     */
    public function processPayment(Request $request, User $user): CompanyReceipt;

    /**
     * @throws Exception
     */
    public function retryPayment(Request $request, CompanyReceipt $receipt, User $user): CompanyReceipt;

    /**
     * @throws Exception
     */
    public function abortPayment(CompanyReceipt $receipt): CompanyReceipt;
}
