<?php

declare(strict_types=1);

namespace App\Contracts\Services\POS;

use App\CompanyReceipt;
use App\ReceiptExport;
use App\ReceiptType;
use Exception;

interface ReceiptServiceInterface
{
    /**
     * @throws Exception
     */
    public function updatePaymentStatus(CompanyReceipt $receipt, bool $checkUpdateStatus = true, $transactionId = null): CompanyReceipt;

    /**
     * @throws Exception
     */
    public function updateRefundStatus(CompanyReceipt $receipt): CompanyReceipt;

    /**
     * @throws Exception
     */
    public function exportPayment(CompanyReceipt $receipt, ReceiptExport $type , ReceiptType $receipt_type);

}
