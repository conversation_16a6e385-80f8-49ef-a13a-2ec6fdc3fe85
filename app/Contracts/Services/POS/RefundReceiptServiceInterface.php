<?php

declare(strict_types=1);

namespace App\Contracts\Services\POS;

use App\CompanyReceiptRefund;
use App\ReceiptExport;
use App\ReceiptType;
use Exception;
use Symfony\Component\HttpFoundation\Response;

interface RefundReceiptServiceInterface
{
    /**
     * @throws Exception
     */
    public function updateStatus(CompanyReceiptRefund $refund, bool $checkUpdateStatus = true): CompanyReceiptRefund;

    /**
     * @throws Exception
     */
    public function exportRefund(CompanyReceiptRefund $refund, ReceiptExport $type , ReceiptType $receipt_type);
}
