<?php

declare(strict_types=1);

namespace App\Contracts\Services\POS;

use App\CompanyReceipt;
use App\CompanyReceiptRefund;
use App\User;
use Exception;
use Illuminate\Http\Request;

interface RefundServiceInterface
{
    /**
     * @throws Exception
     */
    public function processRefund(Request $request,  User $user, CompanyReceipt $receipt): CompanyReceiptRefund;

    /**
     * @throws Exception
     */
    public function retryRefund(Request $request, User $user,  CompanyReceipt $receipt, CompanyReceiptRefund $refund): CompanyReceiptRefund;

    /**
     * @throws Exception
     */
    public function abortRefund(CompanyReceiptRefund $refund): CompanyReceiptRefund;
}
