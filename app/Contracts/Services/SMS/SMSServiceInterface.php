<?php

declare(strict_types=1);

namespace App\Contracts\Services\SMS;

use App\Client;
use App\User;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\LazyCollection;

interface SMSServiceInterface
{
    /**
     * @throws Exception
     */
    public function sendSMS(Client $client, string $content): bool;

    /**
     * @throws Exception
     */
    public function sendBulkSMSToClient(User $user, string $content, Collection|LazyCollection $datas, ?Model $related = null): object;

    /**
     * @throws Exception
     */
    public function getBulkSMSLength(User $user, string $content, Collection|LazyCollection $datas): int;

    /**
     * @throws Exception
     */
    public function getBulkLength(Collection|LazyCollection $clients, string $content): int;
}
