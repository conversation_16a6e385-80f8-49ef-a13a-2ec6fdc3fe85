<?php

declare(strict_types=1);

namespace App\Contracts\Services\PDF\Company;

use App\Company;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Models\CompanyDocument;
use App\Models\CompanyDocumentData;
use App\Models\MedicalDevice;
use Exception;

interface CompanyPDFServiceInterface
{
    /**
     * @throws Exception
     */
    public function document(
        CompanyDocument $document,
        CompanyDocumentData $documentData,
    ): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function legal_document(
        Company $company,
        $type,
    ): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function medical_device(
        MedicalDevice $medicalDevice,
    ): PDFDataServiceInterface;
}
