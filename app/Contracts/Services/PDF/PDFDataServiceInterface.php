<?php

declare(strict_types=1);

namespace App\Contracts\Services\PDF;

use App\File;
use App\User;
use Illuminate\Http\Response;
use Exception;

interface PDFDataServiceInterface
{
    /**
     * @throws Exception
     */
    public function payload(): array;

    /**
     * @throws Exception
     */
    public function savePayload(string $fileName): array;

    /**
     * @throws Exception
     */
    public function download(): Response;

    /**
     * @throws Exception
     */
    public function stream(): Response;

    /**
     * @throws Exception
     */
    public function save(string $fileName): string;

    /**
     * @throws Exception
     */
    public function saveFile(User $user, string $fileName): File;
}
