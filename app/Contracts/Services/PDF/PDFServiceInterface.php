<?php

declare(strict_types=1);

namespace App\Contracts\Services\PDF;

use App\Client;
use App\Company;
use App\Contracts\Services\PDF\Client\ClientPDFServiceInterface;
use App\Contracts\Services\PDF\Company\CompanyPDFServiceInterface;
use Exception;

interface PDFServiceInterface
{
    /**
     * @throws Exception
     */
    public function client(Client $client): ClientPDFServiceInterface;

    /**
     * @throws Exception
     */
    public function company(Company $company): CompanyPDFServiceInterface;

    /**
     * @throws Exception
     */
    public function pos_licence(Company $company, string $timezone): PDFDataServiceInterface;
}
