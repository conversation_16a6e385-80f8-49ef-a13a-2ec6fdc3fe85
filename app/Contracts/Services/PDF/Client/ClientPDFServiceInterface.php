<?php

declare(strict_types=1);

namespace App\Contracts\Services\PDF\Client;

use App\ClientConsent;
use App\ClientLetterOfConsent;
use App\ClientPrescription;
use App\ClientTreatment;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\Contracts\Services\PDF\Client\Questionary\ClientQuestionaryPDFServiceInterface;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\GeneralNote;
use Exception;
use Illuminate\Support\Collection;

interface ClientPDFServiceInterface
{
    /**
     * @throws Exception
     */
    public function info(): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function booking(CompanyBooking $booking, ?CompanyBookingClient $clientBooking = null): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function generalNote(GeneralNote $note): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function letterOfConsent(ClientLetterOfConsent $letterOfConsent): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function logs(Collection $logs): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function procedure(ClientTreatment $treatment): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function prescription(ClientPrescription $prescription): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function personalDataPolicy(ClientConsent $clientConsent): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function sendInfo(): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function questionary(): ClientQuestionaryPDFServiceInterface;
}
