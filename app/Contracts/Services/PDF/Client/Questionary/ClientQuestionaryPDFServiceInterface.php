<?php

declare(strict_types=1);

namespace App\Contracts\Services\PDF\Client\Questionary;

use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Questionary;

interface ClientQuestionaryPDFServiceInterface
{
    /**
     * @throws Exception
     */
    public function aesthethicInterest(array $data, string $created_at): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function health(array $data, string $created_at): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function covid19(array $data, string $created_at): PDFDataServiceInterface;

    /**
     * @throws Exception
     */
    public function custom(
        Questionary $questionary,
        array $data,
        string $created_at,
        ?array $questions = null,
    ): PDFDataServiceInterface;
}
