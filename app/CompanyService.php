<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyService extends Model
{
    use HasFactory, GetEncryptedFile, Encryptable, SoftDeletes, HasUniqueCode;

    //TABLE
    public $table = 'company_services';

    protected $encrypted = [
        'name',
        'color',
        'price',
        'duration',
        'description',
        'time_margin',
        'tax_information',
    ];

    //FILLABLES
    protected $fillable = [
        'company_id',
        'category_id',
        'product_code',
        'name',
        'color',
        'price',
        'duration',
        'description',
        'is_active',
        'time_margin',
        'parallel_booking_count',
        'group_quantity',
        'tax_information',
        'is_hidden',
        'is_online_payment',
        'is_video_call',
        'confirmation_email_template_id',
        'confirmation_sms_template_id',
        'finished_email_template_id',
        'finished_sms_template_id',
        'cancellation_policy_hours'
    ];

    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $value = self::getIncrementalValueModel($query->company_id);

            $query->product_code = $value->item_sequence_number;

            $value->item_sequence_number += 1;
            $value->save();
        });
    }


    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'is_video_call' => 'boolean',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function category()
    {
        return $this->belongsTo(CompanyCategory::class, 'category_id');
    }

    public function users()
    {
        return $this->hasManyThrough(User::class, UserService::class, 'service_id',  'id', 'id', 'user_id');
    }

    public function letter_of_consents()
    {
        return $this->hasManyThrough(LetterOfConsent::class, CompanyServiceLetterOfConsent::class, 'company_service_id',  'id', 'id', 'letter_of_consent_id');
    }

    public function company_service_questionnaires()
    {
        return $this->hasMany(CompanyServiceQuestionary::class, 'company_service_id', 'id')->has('questionary');
    }

    public function questionnaires()
    {
        return $this->hasManyThrough(Questionary::class, CompanyServiceQuestionary::class, 'company_service_id', 'id', 'id', 'questionary_id');
    }

    public function bookings()
    {
        return $this->hasMany(CompanyBooking::class, 'service_id', 'id')->where('is_verified', 1);
    }

    public function receipt_items(): MorphMany
    {
        return $this->morphMany(CompanyReceiptItem::class, 'receiptable');
    }

    public function invoice_items(): MorphMany
    {
        return $this->morphMany(\App\Models\FortnoxInvoiceItem::class, 'invoiceable');
    }

    public function isVideoCall()
    {
        return $this->is_video_call;
    }

    public function finishedEmailTemplate()
    {
        return $this->belongsTo(EmailTemplate::class, 'finished_email_template_id');
    }

    public function finishedSmsTemplate()
    {
        return $this->belongsTo(SMSTemplate::class, 'finished_sms_template_id');
    }

    // public function refunds(): MorphMany
    // {
    //     return $this->morphMany(CompanyReceiptRefundItem::class, 'refundable');
    // }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
