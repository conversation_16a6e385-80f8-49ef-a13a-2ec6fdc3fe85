<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserNotification extends Model
{
    use HasFactory;
    const NOTIFICATION = "NOTIFICATION", EMAIL = "EMAIL", SMS = "SMS";
    //TABLE
    public $table = 'user_notifications';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'is_read',
        'click_action',
        'external_link',
        'type',
        'is_popup'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}