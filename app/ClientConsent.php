<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClientConsent extends Model
{
    use HasFactory;
    use Encryptable, GetEncryptedFile;

    protected $fillable = [
        'id',
        'fields',
        'message',
        'verified_at',
        'client_id',
    ];

    protected $encrypted = [
        'fields',
        'message',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
}
