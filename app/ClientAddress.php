<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;

class ClientAddress extends Model
{
    use Encryptable, LogsActivity, GetEncryptedFile;

    protected $encrypted = [
        'street_address', 'state', 'zip_code', 'city', 'country',
    ];

    protected $fillable = [
        'client_id', 'street_address', 'state', 'zip_code', 'city', 'country',
    ];

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    protected static $logAttributes = [];

    protected static $logName = 'client_address';

    public function getDescriptionForEvent(string $eventName): string
    {
        $client = $this->client()->first();
        return "{$client->first_name} {$client->last_name} client 's address has been {$eventName} by :causer.first_name :causer.last_name";
    }
}
