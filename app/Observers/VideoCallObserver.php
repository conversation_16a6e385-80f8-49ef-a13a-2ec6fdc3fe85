<?php

namespace App\Observers;

use App\Models\VideoCall;

class VideoCallObserver
{
    /**
     * Handle the VideoCall "created" event.
     *
     * @param  \App\Models\VideoCall  $videoCall
     * @return void
     */
    public function created(VideoCall $videoCall)
    {

        // $activity = activity('video_call')
        //     ->performedOn($videoCall);

        // $activity = $activity->log("Video call created by {$videoCall->owner_member->user_name}");
    }

    /**
     * Handle the VideoCall "updated" event.
     *
     * @param  \App\Models\VideoCall  $videoCall
     * @return void
     */
    public function updated(VideoCall $videoCall)
    {
        //
    }

    /**
     * Handle the VideoCall "deleted" event.
     *
     * @param  \App\Models\VideoCall  $videoCall
     * @return void
     */
    public function deleted(VideoCall $videoCall)
    {
        //
    }

    /**
     * Handle the VideoCall "restored" event.
     *
     * @param  \App\Models\VideoCall  $videoCall
     * @return void
     */
    public function restored(VideoCall $videoCall)
    {
        //
    }

    /**
     * Handle the VideoCall "force deleted" event.
     *
     * @param  \App\Models\VideoCall  $videoCall
     * @return void
     */
    public function forceDeleted(VideoCall $videoCall)
    {
        //
    }
}
