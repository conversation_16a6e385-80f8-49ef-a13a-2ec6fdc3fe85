<?php

namespace App\Observers;

use App\UserTimeSlot;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class UserTimeSlotObserver
{
    /**
     * Handle the UserTimeSlot "created" event.
     *
     * @param  \App\UserTimeSlot  $userTimeSlot
     * @return void
     */
    public function created(UserTimeSlot $userTimeSlot)
    {
        if ($userTimeSlot->type == UserTimeSlot::PRIVATE_SLOT_BLOCKING) {
            Cache::forget('company_' . $userTimeSlot->user->company_id . '_private_slots_' . Carbon::parse($userTimeSlot->start_at)->format('Y-m-d') .  '_data');
        } else {
            Cache::forget('company_' . $userTimeSlot->user->company_id . '_available_slots_' . Carbon::parse($userTimeSlot->start_at)->format('Y-m-d') .  '_data');
        }
    }

    /**
     * Handle the UserTimeSlot "updated" event.
     *
     * @param  \App\UserTimeSlot  $userTimeSlot
     * @return void
     */
    public function updated(UserTimeSlot $userTimeSlot)
    {
        if ($userTimeSlot->type == UserTimeSlot::PRIVATE_SLOT_BLOCKING) {
            Cache::forget('company_' . $userTimeSlot->user->company_id . '_private_slots_' . Carbon::parse($userTimeSlot->start_at)->format('Y-m-d') .  '_data');
        } else {
            Cache::forget('company_' . $userTimeSlot->user->company_id . '_available_slots_' . Carbon::parse($userTimeSlot->start_at)->format('Y-m-d') .  '_data');
        }
    }

    /**
     * Handle the UserTimeSlot "deleted" event.
     *
     * @param  \App\UserTimeSlot  $userTimeSlot
     * @return void
     */
    public function deleted(UserTimeSlot $userTimeSlot)
    {
        if ($userTimeSlot->type == UserTimeSlot::PRIVATE_SLOT_BLOCKING) {
            Cache::forget('company_' . $userTimeSlot->user->company_id . '_private_slots_' . Carbon::parse($userTimeSlot->start_at)->format('Y-m-d') .  '_data');
        } else {
            Cache::forget('company_' . $userTimeSlot->user->company_id . '_available_slots_' . Carbon::parse($userTimeSlot->start_at)->format('Y-m-d') .  '_data');
        }
    }

    /**
     * Handle the UserTimeSlot "restored" event.
     *
     * @param  \App\UserTimeSlot  $userTimeSlot
     * @return void
     */
    public function restored(UserTimeSlot $userTimeSlot)
    {
        //
    }

    /**
     * Handle the UserTimeSlot "force deleted" event.
     *
     * @param  \App\UserTimeSlot  $userTimeSlot
     * @return void
     */
    public function forceDeleted(UserTimeSlot $userTimeSlot)
    {
        //
    }
}
