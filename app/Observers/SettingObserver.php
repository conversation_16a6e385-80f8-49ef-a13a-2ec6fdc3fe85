<?php

namespace App\Observers;

use App\Setting;
use Illuminate\Support\Facades\Cache;

class SettingObserver
{
    /**
     * Handle the Setting "created" event.
     *
     * @param  \App\Models\Setting  $setting
     * @return void
     */
    public function created(Setting $setting)
    {
        Cache::forget("company_settings_$setting->company_id");
    }

    /**
     * Handle the Setting "saved" event.
     *
     * @param  \App\Models\Setting  $setting
     * @return void
     */
    public function saved(Setting $setting)
    {
        Cache::forget("company_settings_$setting->company_id");
    }

    /**
     * Handle the Setting "updated" event.
     *
     * @param  \App\Models\Setting  $setting
     * @return void
     */
    public function updated(Setting $setting)
    {
        Cache::forget("company_settings_$setting->company_id");
    }

    /**
     * Handle the Setting "deleted" event.
     *
     * @param  \App\Models\Setting  $setting
     * @return void
     */
    public function deleted(Setting $setting)
    {
        Cache::forget("company_settings_$setting->company_id");
    }

    /**
     * Handle the Setting "restored" event.
     *
     * @param  \App\Models\Setting  $setting
     * @return void
     */
    public function restored(Setting $setting)
    {
        Cache::forget("company_settings_$setting->company_id");
    }

    /**
     * Handle the Setting "force deleted" event.
     *
     * @param  \App\Models\Setting  $setting
     * @return void
     */
    public function forceDeleted(Setting $setting)
    {
        Cache::forget("company_settings_$setting->company_id");
    }
}
