<?php

namespace App\Observers;

use App\CompanyBooking;
use App\CompanyBookingClient;
use App\Traits\Booking\BookingGoogleCalendarManager;

class CompanyBookingClientObserver
{
    use BookingGoogleCalendarManager;
    /**
     * Handle the CompanyBookingClient "created" event.
     *
     * @param  \App\Models\CompanyBookingClient  $companyBookingClient
     * @return void
     */
    public function created(CompanyBookingClient $companyBookingClient)
    {
        $booking = $companyBookingClient->booking;
        if (!$booking->service?->is_online_payment) {
            $data = CompanyBooking::getBookingMailData($booking, $companyBookingClient);

            $msg = "{$booking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} created with practitioner {$booking->user->fullName()}";
            $activity = activity()
                ->performedOn($companyBookingClient)
                ->by($booking->user)
                ->log($msg);
        }

        BookingGoogleCalendarManager::syncWithBooking($companyBookingClient->booking);
    }

    /**
     * Handle the CompanyBookingClient "updated" event.
     *
     * @param  \App\Models\CompanyBookingClient  $companyBookingClient
     * @return void
     */
    public function updated(CompanyBookingClient $companyBookingClient)
    {


        BookingGoogleCalendarManager::syncWithBooking($companyBookingClient->booking);
    }

    /**
     * Handle the CompanyBookingClient "deleted" event.
     *
     * @param  \App\Models\CompanyBookingClient  $companyBookingClient
     * @return void
     */
    public function deleted(CompanyBookingClient $companyBookingClient)
    {
        //
    }

    /**
     * Handle the CompanyBookingClient "restored" event.
     *
     * @param  \App\Models\CompanyBookingClient  $companyBookingClient
     * @return void
     */
    public function restored(CompanyBookingClient $companyBookingClient)
    {
        //
    }

    /**
     * Handle the CompanyBookingClient "force deleted" event.
     *
     * @param  \App\Models\CompanyBookingClient  $companyBookingClient
     * @return void
     */
    public function forceDeleted(CompanyBookingClient $companyBookingClient)
    {
        //
    }
}