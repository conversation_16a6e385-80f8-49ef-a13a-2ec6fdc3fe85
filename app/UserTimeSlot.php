<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserTimeSlot extends Model
{
    use HasFactory, Encryptable;

    //TABLE
    public $table = 'user_time_slots';

    protected $encrypted = [
        'note',
    ];

    //types
    const AVAILABLE = "AVAILABLE",
        NOT_AVAILABLE = "NOT_AVAILABLE",
        PRIVATE_SLOT_BLOCKING = "PRIVATE_SLOT_BLOCKING",
        PRIVATE_SLOT_NON_BLOCKING = "PRIVATE_SLOT_NON_BLOCKING";

    //toggle actions
    const OPEN = "OPEN", CLOSE = "CLOSE";

    //FILLABLES
    protected $fillable = [
        'user_id',
        'note',
        'week_day',
        'type',
        'start_at',
        'end_at',
        'is_for_all_day',
        'description',
        'color',
        'is_for_all_services'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        // 'start_at' => 'datetime',
        // 'end_at' => 'datetime',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function services()
    {
        return $this->hasManyThrough(CompanyService::class, UserTimeSlotService::class, 'user_time_slot_id', 'id', 'id', 'company_service_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
