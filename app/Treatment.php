<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use VerumConsilium\Browsershot\Facades\PDF;

class Treatment extends Model
{
    use Encryptable, SoftDeletes, LogsActivity, GetEncryptedFile;
    const TYPE_TREATMENT = 'treatment';
    const TYPE_TEXT = 'text';

    protected $encrypted = [
        'name', 'description', 'cost', 'color', 'unit', 'notes', 'type',
    ];

    protected $fillable = [
        'name', 'company_id', 'description', 'cost', 'color', 'unit', 'notes', 'type','is_changed'
    ];

    protected static $logAttributes = [];

    protected static $logName = 'treatment';

    public function getDescriptionForEvent(string $eventName): string
    {
        return ":subject.name treatment has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public function company()
    {
        return $this->belongsTo('App\Company', 'company_id', 'id');
    }
}