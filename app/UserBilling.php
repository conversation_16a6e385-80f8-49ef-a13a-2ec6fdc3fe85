<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserBilling extends Model
{
    use HasFactory;
    const WEEKLY = "WEEKLY", MONTHLY = "MONTHLY", YEARLY = "YEARLY", PAID = "PAID",UNPAID = "UNPAID", UPCOMING = "UPCOMING";
    //TABLE
    public $table = 'user_billings';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'earning',
        'start_date',
        'end_date',
        'payment_status',
        'period_type',
        'user_earning',
        'signed_prescription_count',
        'price',
        'price_id',
        'invoice_vat'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function signed_prescriptions()
    {
        return $this->hasManyThrough(ClientPrescription::class,User::class,'id','sign_by_id','user_id','id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}