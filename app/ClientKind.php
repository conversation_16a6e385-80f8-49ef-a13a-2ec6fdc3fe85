<?php

namespace App;

use App\Client;
use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Model;

class ClientKind extends Model
{
    use Encryptable, GetEncryptedFile;

    protected $fillable = [
        'client_id',
        'name',
        'email',
        'phone',
        'relation',
    ];

    protected $encrypted = [
        'name',
        'email',
        'phone',
        'relation',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
}
