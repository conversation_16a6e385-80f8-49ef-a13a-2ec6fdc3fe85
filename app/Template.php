<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class Template extends Model
{
    use Encryptable, LogsActivity, SoftDeletes, GetEncryptedFile;

    protected $encrypted = [
        'image', 'name'
    ];

    protected $fillable = [
        'company_id', 'image', 'name', 'is_editable', 'created_at'
    ];

    protected $casts = [
        'is_editable' => 'boolean',
    ];

    public function company()
    {
        return $this->belongsTo('App\Company', 'company_id', 'id');
    }

    public function file()
    {
        return $this->morphOne('App\File', 'fileable')->latest();
    }

    public function getImageAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    protected static $logAttributes = [];

    protected static $logName = 'template';

    public function getDescriptionForEvent(string $eventName): string
    {
        return ":subject.name template has been {$eventName} by :causer.first_name :causer.last_name";
    }
}
