<?php

namespace App\Traits;

use App\Http\Integrations\Fortnox\Facades\Fortnox;
use App\Http\Integrations\Fortnox\Facades\FortnoxAuth;
use App\Http\Integrations\Fortnox\Resources\CustomerResource;

trait HasFortnox
{
    public function fortnox()
    {
        $auth = $this->fortnox_auth;

        if (!$auth) {
            throw new \Exception('Please connect to fortnox first');
        }

        if ($auth->hasExpired()) {
            $auth = FortnoxAuth::refreshAccessToken($auth);

            $this->fortnox_auth = $auth;
            $this->save();
        }

        return Fortnox::authenticate($auth);
    }

    public function fortnoxAuth()
    {
        return FortnoxAuth::make();
    }
}
