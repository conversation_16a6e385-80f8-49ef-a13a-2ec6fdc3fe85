<?php

namespace App\Traits;

use <PERSON><PERSON>\Passport\Client;
use Psr\Http\Message\ServerRequestInterface;
use <PERSON><PERSON>\Passport\Http\Controllers\AccessTokenController;

trait Oauth2Helper
{

    private $client;

    public function __construct()
    {
        $this->client = Client::findOrFail(2);
    }

    public function getUserAccessTokens($username, $password, ServerRequestInterface $server_request, AccessTokenController $accessTokenController)
    {
        $params = [
            'grant_type'     => 'password',
            'client_id'      => $this->client->id,
            'client_secret'  => $this->client->secret,
            'username'       => $username,
            'password'       => $password,
        ];

        $server_request = $server_request->withParsedBody($params);

        $responseBody = $accessTokenController->issueToken($server_request);

        return json_decode($responseBody->content(), true);
    }
}
