<?php

namespace App\Traits;

use App\File;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Intervention\Image\Facades\Image;
use App\Exceptions\StorageLimitExceed;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use VerumConsilium\Browsershot\Facades\PDF;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;

trait GetEncryptedFile
{
    protected function getS3SignedUrl($path, $expires = 10)
    {
        if ($path == "") {
            return $path;
        }

        return Storage::temporaryUrl(
            $path,
            now()->addMinutes($expires)
        );


        // $s3Client = new S3Client([
        //     'region' => config('services.ses')['region'],
        //     'version' => 'latest',
        //     'credentials' => [
        //         'key'    => config('services.ses')['key'],
        //         'secret' => config('services.ses')['secret'],
        //     ],
        // ]);
        // $s3Client = Storage::disk('s3')->getDriver()->getAdapter()->getClient();

        // $cmd = $s3Client->getCommand('GetObject', [
        //     'Bucket' => config('filesystems.disks.s3.bucket', 'meridiq-test'),
        //     'Key' => $path,
        // ]);

        // $request    = $s3Client->createPresignedRequest($cmd, $expires);
        // return (string)$request->getUri();
    }
}
