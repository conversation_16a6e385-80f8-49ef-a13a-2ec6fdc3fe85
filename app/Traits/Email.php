<?php

namespace App\Traits;

use App\Client;
use App\Company;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\EmailTemplate;
use App\Setting;
use App\User;
use Carbon\Carbon;

trait Email
{
    public static function REPLACE_KEYS(
        string $message,
        User $user = null,
        Client $client = null,
        CompanyBooking $booking = null
    ) {
        $keys = collect();
        // Company
        $company = $user?->company ?? $client?->company ?? $booking?->company;
        if ($company) {
            $full_address = $company->street_address;
            if ($company->zip_code) {
                if ($full_address && $full_address != '') {
                    $full_address = $full_address . ', ' . $company->zip_code;
                } else {
                    $full_address = $full_address . $company->zip_code;
                }
            }
            if ($company->city) {
                if ($full_address && $full_address != '') {
                    $full_address = $full_address . ', ' . $company->city;
                } else {
                    $full_address = $full_address . $company->city;
                }
            }
            if ($company->state) {
                if ($full_address && $full_address != '') {
                    $full_address = $full_address . ', ' . $company->state;
                } else {
                    $full_address = $full_address . $company->state;
                }
            }
            if ($company->country) {
                if ($full_address && $full_address != '') {
                    $full_address = $full_address . ', ' . $company->country;
                } else {
                    $full_address = $full_address . $company->country;
                }
            }
            $keys->put('clinic_name', $company->company_name);
            $keys->put('clinic_location', $full_address);
            $keys->put('clinic_phone', $company->mobile_number ? Email::FORMAT_NUMBER($company->country_code, $company->mobile_number) : '');
        }

        // Auth user
        if ($user) {
            $keys->put('practitioner_name', $user->first_name . ' ' . $user->last_name);
        }

        // Client
        if ($client) {
            $keys->put('client_first_name', $client->first_name);
            $keys->put('client_last_name', $client->last_name);
            $keys->put('client_cell_phone', $client->phone_number ? Email::FORMAT_NUMBER($client->country_code, $client->phone_number) : '');
            $keys->put('client_email', $client->email);
        }

        // Booking
        if ($booking) {
            $keys->put('booking_date', Setting::formateDate($booking->company, Carbon::parse($booking->start_at)));
            $keys->put('booking_start_time', Setting::formateTime($booking->company, Carbon::parse($booking->start_at)));
            $keys->put('booking_end_time', Setting::formateTime($booking->company, Carbon::parse($booking->end_at)));
            if ($booking && $booking?->service?->category?->group_booking) {
                $company_booking_client = CompanyBookingClient::where(['booking_id' => $booking->id, 'client_id' => $client?->id])->first();
                if ($company_booking_client) {
                    $keys->put('booking_special_request', $company_booking_client->special_request);
                } else {
                    $keys->put('booking_special_request', $booking->special_request);
                }
            } else {
                $keys->put('booking_special_request', $booking->special_request);
            }

            $keys->put('booking_information',  Setting::getSetting($booking->company, Setting::BOOKING_PORTAL_TEXT)->value);

            // Booking service
            $keys->put('service_name', $booking->service->name);
            $keys->put('service_duration', $booking->service->duration . ' min');
            $keys->put('service_price', $booking->company->withCurrency($booking->service->price));
            $keys->put('service_description', $booking->service->description);
        }
        //TODO::FIXED IN OPTIMUM WAY
        foreach ($keys->toArray() as $key => $value) {
            if ($key == 'client_first_name' || $key == 'client_last_name') {
                if ($booking && !$booking?->service?->category?->group_booking) {
                    $message = str_replace('{{' . $key . '}}', $key == 'client_first_name' ? $booking->first_name : $booking->last_name, $message);
                } else {
                    $message = str_replace('{{' . $key . '}}', $value  ?? '', $message);
                }
            } else {
                $message = str_replace('{{' . $key . '}}', $value  ?? '', $message);
            }
        }



        $message = str_replace("<p></p>", "<br/>", $message);

        return $message;
    }

    public static function CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
        Company $company,
        string $on_key,
        string $template_key,
        bool $check_enabled = false,
    ): EmailTemplate | null {
        if ($check_enabled === false) {
            $on_value = Setting::getSetting($company, $on_key);
            if (!$on_value) {
                return null;
            }
            if (!$on_value->value) {
                return null;
            }
        }

        $template_setting = Setting::getSetting($company, $template_key);
        if (!$template_setting) {
            return null;
        }
        $template_id = $template_setting->value;
        if (!$template_id) {
            return null;
        }

        $template = EmailTemplate::query()->where('company_id', $company->id)->find($template_id);

        return $template;
    }

    public static function FORMAT_NUMBER($code, $number, $add_plus = true)
    {
        if ((!$code || $code == "null") && !$number) {
            return '';
        }
        // remove number from start
        $code = str_replace('+', '', $code);
        // Remove leading zeros
        $number = ltrim($number, '0');
        // Remove spaces
        $number = str_replace(' ', '', $number);

        return ($add_plus ? '+' : '') . $code . $number;
    }
}