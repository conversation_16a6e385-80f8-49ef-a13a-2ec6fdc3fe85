<?php

namespace App\Traits;

use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Calendar;

trait GoogleAuth
{

    public static function createOAuthClient($token): Google_Client
    {
        $client = new Google_Client;

        $client->setScopes([
            Google_Service_Calendar::CALENDAR,
        ]);

        $oauth_credentail_file_path = config('google_calendar.oauth_file_path');
        $client->setAuthConfig(storage_path($oauth_credentail_file_path));

        $token = json_encode([
            'access_token' => $token,
        ]);
        $client->setAccessToken($token);

        return $client;
    }

    public static function createForCalendarId(Google_Client $google_Client, string $calendarId): GoogleCalendar
    {

        $service = new Google_Service_Calendar($google_Client);

        return new GoogleCalendar($service, $calendarId);
    }

    public static function createNewCalendar(Google_Client $google_Client, $calendar_name): GoogleCalendar
    {

        $service = new Google_Service_Calendar($google_Client);

        $new_cal = new Google_Service_Calendar_Calendar();

        $new_cal->setSummary($calendar_name);

        $new_cal->setDescription($calendar_name);

        $cal_data = $service->calendars->insert($new_cal);
        return new GoogleCalendar($service, $cal_data->id);
    }
}
