<?php

namespace App\Traits\POS;

use Exception;
use App\Http\Integrations\POSPayment\Facades\POSPayment;
use App\Http\Integrations\VivaWallet\DataObject\Order;
use App\Http\Integrations\VivaWallet\DataObject\RefundTransaction;
use App\Http\Integrations\VivaWallet\DataObject\Transaction;
use App\Http\Integrations\VivaWallet\DataObject\TransactionResponse;
use App\Http\Integrations\VivaWallet\Exceptions\ConflictException;
use App\Http\Integrations\VivaWallet\ValueObject\Customer;
use App\Setting;
use Illuminate\Support\Collection;
use Saloon\Exceptions\Request\Statuses\ForbiddenException;

trait HasOnlineVivaWallet
{
    public function createOnlineVivaPayment(
        int $amount,
        int $isvAmount,
        Customer $customer,
        string $customerTrns,
        string $merchantTrns,
        string $dynamicDescriptor,
        ?array $tags = [],
    ): string {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        $countryDetail = collect(config('stripe.country'))->filter(function ($country) {
            return  in_array($this->country, $country['countries']);
        })->first();

        if (!$countryDetail) {
            $countryDetail = collect(config('stripe.country'))->where('default', true)->first();
        }

        $sourceCode = $this->getOrCreatePaymentSource();
        $orderCode = null;

        while (true) {
            try {
                $orderCode = POSPayment::account($this->viva_merchant_id)
                    ->payment_order()
                    ->create(
                        amount: $amount,
                        customer: $customer,
                        customerTrns: $customerTrns,
                        merchantTrns: $merchantTrns,
                        currencyCode: (string) $countryDetail['code'],
                        isvAmount: $isvAmount,
                        dynamicDescriptor: $dynamicDescriptor,
                        disableWallet: false,
                        sourceCode: $sourceCode,
                        tags: $tags,
                        paymentTimeout: 900,
                    );
                break;
            } catch (ForbiddenException $th) {
                $sourceCode = $this->getOrCreatePaymentSource(true);
                continue;
            } catch (\Throwable $th) {
                throw $th;
            }
        }

        return $orderCode;
    }

    public function getOrCreatePaymentSource($regenerate = false)
    {
        $sourceCode = $this->settings()->where('key', Setting::VIVA_WALLET_SOURCE_CODE)->first();
        $code = $sourceCode?->value;

        if (!$code || $regenerate) {
            $code = 1000;

            while (true) {
                try {
                    POSPayment::account($this->viva_merchant_id)
                        ->sources()->create(
                            domain: parse_url(config('app.frontend_url'), 1),
                            isSecure: true,
                            name: config('app.name'),
                            pathFail: "/payment?status=fail",
                            pathSuccess: "/payment?status=success",
                            sourceCode: (string) $code,
                        );
                    break;
                } catch (ConflictException $th) {
                    $code++;
                    continue;
                } catch (\Throwable $th) {
                    throw $th;
                }
            }

            $this->settings()->updateOrCreate([
                'key' => Setting::VIVA_WALLET_SOURCE_CODE,
                'value' => $code
            ], [
                'company_id' => $this->id,
            ]);
        }

        return $code;
    }

    public function createOnlineVivaRefund(
        string $transactionId,
        int $amount,
    ): RefundTransaction {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        return POSPayment::account($this->viva_merchant_id)
            ->payment_order()
            ->refund(
                amount: $amount,
                transactionId: $transactionId,
            );
    }

    public function findTransaction(
        string $transactionId,
    ): Transaction {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        return POSPayment::account($this->viva_merchant_id)
            ->transactions()
            ->find($transactionId);
    }

    public function findOrder(
        string $orderCode,
    ): Order {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        return POSPayment::account($this->viva_merchant_id)
            ->payment_order()
            ->find($orderCode);
    }

    public function findTransactionsByOrderCode(
        string $orderCode,
    ): Collection {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        $transactionResponse = POSPayment::account($this->viva_merchant_id)
            ->transactions()
            ->findTransactionByOrderCode($orderCode);

        return $transactionResponse?->transactions ?? collect();
    }
}
