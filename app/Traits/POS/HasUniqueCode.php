<?php

namespace App\Traits\POS;

use App\CompanyIncrementalValue;


trait HasUniqueCode
{
    static function getIncrementalValueModel($company_id)
    {
        $companyIncrementalValue = CompanyIncrementalValue::lockForUpdate()->firstOrCreate(
            ['company_id' => $company_id],
            ['company_id' => $company_id]
        );
        if ($companyIncrementalValue->wasRecentlyCreated) {
            return $companyIncrementalValue->refresh();
        }
        return  $companyIncrementalValue;
    }
}
