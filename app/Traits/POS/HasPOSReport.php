<?php

namespace App\Traits\POS;

use App\CompanyReceipt;
use App\CompanyService;
use App\CompanyReceiptRefund;
use App\CompanyZReport;
use App\Setting;
use App\Traits\TimeZoneManager;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

trait HasPOSReport
{
    use TimeZoneManager;

    public function getReportData(CompanyZReport $z_report, $end_datetime = null)
    {
        $company = $z_report->company;

        $company_users = $company->users;

        $start_date = $z_report->start_datetime;
        $end_date = $z_report->end_datetime ?? $end_datetime ?? now();

        $report_data = [];

        $company_receipts = CompanyReceipt::where('company_id', $z_report->company_id)->paidAtBetween($start_date, $end_date)->with(['items', 'gift_card'])->get();
        $company_refund_receipts = CompanyReceiptRefund::where('company_id', $z_report->company_id)->refundAtBetween($start_date, $end_date)->with(['items', 'gift_card'])->get();
        $cancelled_receipts = CompanyReceipt::where('company_id', $z_report->company_id)->cancelAtBetween($start_date, $end_date)->with(['items', 'gift_card'])->get();

        $data = $this->processReceipts($company_receipts, $company_refund_receipts, $cancelled_receipts);

        $receipt_amount = CompanyReceipt::where('company_id', $z_report->company_id)->paid()->where('paid_at', '<=', $end_date)->lazy()->sum('total_formatted');
        $refund_amount = CompanyReceiptRefund::where('company_id', $z_report->company_id)->refunded()->where('refunded_at', '<=', $end_date)->lazy()->sum('total_amount');

        $r_data = $this->generateReportData($data);
        $gr_data = $this->generateGrandReportData((object) [
            "sale" => $receipt_amount,
            "refund" => $refund_amount,
        ]);

        $report_data['total_sale'] = array_merge($r_data, $gr_data);

        $closing_amount = $data->total->in_tax;

        //USER DATA
        foreach ($company_users as $index => $user) {

            $user_receipts = $company_receipts->where('user_id', $user->id);
            $user_refund_receipts = $company_refund_receipts->where('user_id', $user->id);
            $cancelled_receipts = $cancelled_receipts->where('user_id', $user->id);

            $data = $this->processReceipts($user_receipts, $user_refund_receipts, $cancelled_receipts, $user);

            $report_data['users'][] = [
                "name" => $user->fullName(),
                "data" => $this->generateReportData($data, $user),
            ];
        }

        // dd(json_decode(json_encode($report_data['total_sale']), true));

        $receipts = "";
        $first = $company_receipts->sortBy('paid_at')->first();
        if ($first) {
            $receipts = $first->viva_receipt_id;
        }
        $last = $company_receipts->last();
        if ($last && $last->id != $first->id) {
            $receipts .= " - " . $last->viva_receipt_id;
        }

        $dateFormat = Setting::getDateTimeFormat($company);
        $dateTimeFormat = Setting::getDateTimeFormat($company, true);

        $date = Carbon::parse($start_date)->timezone($company->timezone ?? 'Europe/Stockholm')->format($dateFormat);
        $end_date = Carbon::parse($end_date)->setTimezone($company->timezone ?? 'Europe/Stockholm')->format($dateTimeFormat);
        $now = now()->setTimezone($company->timezone)->format($dateTimeFormat);

        return [
            "title" => ($z_report->end_datetime ? __('pos_strings.z_report') : __('pos_strings.x_report')) . " - " . $date,
            'data' => json_decode(json_encode($report_data)),
            "from" => $company->company_name,
            "from_image" => $company->profile_photo ? remoteFileToBase64(Storage::url($company->profile_photo)) : null,
            "org_no" => $company->organization_number,
            "print_date" => $now,
            "ecr_name" => config("infrasec.register_make"),
            "ecr_version" => config("infrasec.register_model"),
            "ecr_id" => $company->ccu_register_id,
            'receipts' => $receipts,
            'carried_out' => $end_date,
            'z_report_id' => $z_report->report_id,
            'closing_amount' => $closing_amount,
            'report' => $z_report->end_datetime ? __('pos_strings.z_report') : __('pos_strings.x_report')
        ];
    }

    private function processReceipts($receipts, $refund_receipts, $cancelled_receipts, ?User $user = null)
    {
        $data = (object) [
            "product" =>  (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "service" =>  (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "products" => [],
            "services" => [],
            "payment_methods" => [],
            "gift_card" => (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "discount" => (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "receipt_copy" => (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "refund" => (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "cancelled" => (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
            "total" => (object) [
                "quantity" => 0,
                "ex_tax" => 0,
                "tax" => 0,
                "in_tax" => 0,
            ],
        ];


        // $refund_receipt_copy = isset($refund_receipt->user_downloads["user_$refund_receipt->user_id"]) ? $refund_receipt->user_downloads["user_$refund_receipt->user_id"] : 0;


        foreach ($receipts as $receipt) {
            $data->gift_card->quantity += $receipt->gift_card_id ? 1 : 0;
            $data->gift_card->tax = 0;
            $data->gift_card->ex_tax = $receipt->gift_card_amount;
            $data->gift_card->in_tax += $receipt->gift_card_amount;

            $data->discount->quantity += $receipt->discount_value ? 1 : 0;
            $data->discount->tax += 0;
            $data->discount->ex_tax += $receipt->discount_amount_formatted;
            $data->discount->in_tax += $receipt->discount_amount_formatted;

            if ($user) {
                $receipt_downloads = isset($receipt->user_downloads["user_$receipt->user_id"]) ? $receipt->user_downloads["user_$receipt->user_id"] : 0;
            } else {
                $receipt_downloads = $receipt->downloaded;
            }

            $receipt_copy = $receipt_downloads > 1 ? $receipt_downloads - 1 : 0;

            $data->receipt_copy->quantity += $receipt_copy;

            foreach ($receipt->items as $item) {
                $tax = $item->tax_information ?? 0;
                $taxKey = "{$tax}%";
                if ($item->receiptable_type != app(CompanyService::class)->getMorphClass()) {
                    if (!isset($data->products[$taxKey])) {
                        $data->products[$taxKey] = [
                            'percentage' => $tax,
                            'quantity' => 0,
                            'ex_tax' => 0,
                            'tax' => 0,
                            'in_tax' => 0,
                        ];
                    }

                    $data->products[$taxKey]['percentage'] = $tax;
                    $data->products[$taxKey]['quantity'] += $item->quantity;
                    $data->products[$taxKey]['ex_tax'] += ($item->discounted_price_with_tax - $item->discounted_tax_amount) * $item->quantity;
                    $data->products[$taxKey]['tax'] += $item->discounted_tax_amount * $item->quantity;
                    $data->products[$taxKey]['in_tax'] += $item->discounted_price_with_tax * $item->quantity;

                    $data->product->quantity += $item->quantity;
                    $data->product->ex_tax += ($item->discounted_price_with_tax - $item->discounted_tax_amount) * $item->quantity;
                    $data->product->tax += $item->discounted_tax_amount * $item->quantity;
                    $data->product->in_tax += $item->discounted_price_with_tax * $item->quantity;
                } else {
                    if (!isset($data->services[$taxKey])) {
                        $data->services[$taxKey] = [
                            'percentage' => $tax,
                            'quantity' => 0,
                            'ex_tax' => 0,
                            'tax' => 0,
                            'in_tax' => 0,
                        ];
                    }

                    $data->services[$taxKey]['percentage'] = $tax;
                    $data->services[$taxKey]['quantity'] += $item->quantity;
                    $data->services[$taxKey]['ex_tax'] += ($item->discounted_price_with_tax - $item->discounted_tax_amount) * $item->quantity;
                    $data->services[$taxKey]['tax'] += $item->discounted_tax_amount * $item->quantity;
                    $data->services[$taxKey]['in_tax'] += $item->discounted_price_with_tax * $item->quantity;

                    $data->service->quantity += $item->quantity;
                    $data->service->ex_tax += ($item->discounted_price_with_tax - $item->discounted_tax_amount) * $item->quantity;
                    $data->service->tax += $item->discounted_tax_amount * $item->quantity;
                    $data->service->in_tax += $item->discounted_price_with_tax * $item->quantity;
                }

                // if (!isset($data->taxes[$taxKey])) {
                //     $data->taxes[$taxKey] = [
                //         'percentage' => $tax,
                //         'quantity' => 0,
                //         'ex_tax' => 0,
                //         'tax' => 0,
                //         'in_tax' => 0,
                //     ];
                // }

                // $data->taxes[$taxKey]['percentage'] = $tax;
                // $data->taxes[$taxKey]['quantity'] += $item->quantity;
                // $data->taxes[$taxKey]['ex_tax'] += $item->discounted_price_with_tax - $item->discounted_tax_amount;
                // $data->taxes[$taxKey]['tax'] += $item->discounted_tax_amount;
                // $data->taxes[$taxKey]['in_tax'] += $item->discounted_price_with_tax;

                $data->total->quantity += $item->quantity;
                $data->total->ex_tax += ($item->discounted_price_with_tax - $item->discounted_tax_amount) * $item->quantity;
                $data->total->tax += $item->discounted_tax_amount * $item->quantity;
                $data->total->in_tax += $item->discounted_price_with_tax * $item->quantity;
            }


            if ($receipt->paid_amount_formatted > 0) {
                $payment_method = $this->getPaymentMethod($receipt);

                if (!isset($data->payment_methods[$payment_method])) {
                    $data->payment_methods[$payment_method] = [
                        'name' => $payment_method,
                        'quantity' => 0,
                        'ex_tax' => 0,
                        'tax' => 0,
                        'in_tax' => 0,
                    ];
                }

                $data->payment_methods[$payment_method]['name'] = $payment_method;
                $data->payment_methods[$payment_method]['quantity'] += 1;
                $data->payment_methods[$payment_method]['ex_tax'] = 0;
                $data->payment_methods[$payment_method]['tax'] = 0;
                $data->payment_methods[$payment_method]['in_tax'] += $receipt->paid_amount_formatted;
            }
        }

        $data->products = collect($data->products)->values()->all();
        $data->services = collect($data->services)->values()->all();
        // $data->taxes = collect($data->taxes)->values()->all();
        $data->payment_methods = collect($data->payment_methods)->values()->all();

        foreach ($refund_receipts as $receipt) {
            $data->refund->quantity += 1;

            $tax_levels = $receipt->generateTaxLevel();

            $data->refund->tax += $tax_levels->sum('tax');
            $data->refund->ex_tax += $tax_levels->sum('net');
            $data->refund->in_tax += $tax_levels->sum('gross');

            $data->receipt_copy->quantity += $receipt->downloaded ?? 0;
        }

        foreach ($cancelled_receipts as $receipt) {
            $data->cancelled->quantity += 1;
            foreach ($receipt->items as $item) {
                $data->cancelled->ex_tax += ($item->discounted_price_with_tax - $item->discounted_tax_amount) * $item->quantity;
                $data->cancelled->tax += $item->discounted_tax_amount * $item->quantity;
                $data->cancelled->in_tax += $item->discounted_price_with_tax * $item->quantity;
            }
        }

        // $data->total->ex_tax = $data->total->ex_tax;
        // $data->total->tax = $data->total->tax;
        // $data->total->in_tax = $data->total->in_tax;

        return json_decode(json_encode($data));
    }

    private function getPaymentMethod($receipt)
    {
        $transaction_type = config("viva_wallet.transaction_type.{$receipt->transaction_type_id}");

        if ($transaction_type) {
            return str_replace([
                "refund",
                "charge",
                "capture",
                "pre-auth",
                "(installments)",
                "void",
                "Original Credit (refund, betting MCC only)",
                "claimed",
                "payout",
                "withdrawal",
            ], "", $transaction_type);
        }

        if ($receipt->payment_method == CompanyReceipt::PAYMENT_METHOD_SWISH) {
            return "Swish";
        }

        return "Other";
    }

    private function generateGrandReportData($data)
    {
        $report_data = [];
        $grand_total = [];

        $grand_total[] = [
            'name' => __('pos_strings.pdf.grand_total'),
            'quantity' =>  "",
            'excluding_vat' => "",
            'tax' => "",
            'including_vat' => __('pos_strings.pdf.amount'),
        ];

        $grand_total[] = [
            'name' => __('pos_strings.pdf.sales_inc_returns'),
            'quantity' =>  "",
            'excluding_vat' => "",
            'tax' => "",
            'including_vat' => CompanyReceipt::roundValue($data->sale),
        ];

        $grand_total[] = [
            'name' => __('pos_strings.pdf.sales_exc_returns'),
            'quantity' =>  "",
            'excluding_vat' => "",
            'tax' => "",
            'including_vat' => CompanyReceipt::roundValue($data->sale - $data->refund),
        ];

        $grand_total[] = [
            'name' => __('pos_strings.pdf.grand_return'),
            'quantity' =>  "",
            'excluding_vat' => "",
            'tax' => "",
            'including_vat' => CompanyReceipt::roundValue($data->refund),
        ];

        $report_data[] = $grand_total;

        return $report_data;
    }

    private function generateReportData($data, ?User $user = null)
    {
        $report_data = [];

        $sales = [];
        $products = [];
        $services = [];
        $taxes = [];
        $payment_methods = [];
        $others = [];

        $common_header = [
            'name' => __('pos_strings.pdf.sale'),
            'quantity' =>  __('pos_strings.pdf.qty'),
            'excluding_vat' => __('pos_strings.pdf.excluding_vat'),
            'tax' => __('pos_strings.pdf.vat'),
            'including_vat' => __('pos_strings.pdf.including_vat'),
        ];

        $sales[] = $common_header;

        $sales[] = [
            'name' => __('pos_strings.products'),
            'quantity' =>  $data->product->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->product->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->product->tax),
            'including_vat' => CompanyReceipt::roundValue($data->product->in_tax),
        ];

        $sales[] = [
            'name' => __('pos_strings.pdf.services'),
            'quantity' =>  $data->service->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->service->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->service->tax),
            'including_vat' => CompanyReceipt::roundValue($data->service->in_tax),
        ];

        $sales[] = [
            'name' => __('pos_strings.pdf.total'),
            'quantity' =>  $data->service->quantity + $data->product->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->service->ex_tax + $data->product->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->service->tax + $data->product->tax),
            'including_vat' => CompanyReceipt::roundValue($data->service->in_tax + $data->product->in_tax),
            'highlight' => true,
        ];

        // if (count($data->taxes ?? [])) {
        //     $taxes[] = array_merge($common_header, [
        //         'name' => __('pos_strings.pdf.tax') . " %"
        //     ]);
        // }

        // foreach ($data->taxes as $key => $item) {
        //     $taxes[] = [
        //         'name' => __('pos_strings.pdf.tax') . " {$item->percentage}%",
        //         'quantity' =>  $item->quantity,
        //         'excluding_vat' => CompanyReceipt::roundValue($item->ex_tax),
        //         'tax' => CompanyReceipt::roundValue($item->tax),
        //         'including_vat' => CompanyReceipt::roundValue($item->in_tax),
        //     ];
        // }

        // if (count($data->taxes ?? [])) {
        //     $taxes[] = [
        //         'name' => __('pos_strings.pdf.total'),
        //         'quantity' =>  collect($data->taxes)->sum('quantity'),
        //         'excluding_vat' => collect($data->taxes)->sum('ex_tax'),
        //         'tax' => collect($data->taxes)->sum('tax'),
        //         'including_vat' => collect($data->taxes)->sum('in_tax'),
        //         'highlight' => true,
        //     ];
        // }

        if (count($data->products ?? [])) {
            $products[] = array_merge($common_header, [
                'name' => __('pos_strings.products'),
            ]);
        }

        foreach (collect($data->products)->sortByDesc('percentage') as $key => $item) {
            $products[] = [
                'name' => __('pos_strings.pdf.tax') . " {$item->percentage}%",
                'quantity' =>  $item->quantity,
                'excluding_vat' => CompanyReceipt::roundValue($item->ex_tax),
                'tax' => CompanyReceipt::roundValue($item->tax),
                'including_vat' => CompanyReceipt::roundValue($item->in_tax),
            ];
        }

        if (count($data->products ?? [])) {
            $products[] = [
                'name' => __('pos_strings.pdf.total'),
                'quantity' =>  collect($data->products)->sum('quantity'),
                'excluding_vat' => CompanyReceipt::roundValue(collect($data->products)->sum('ex_tax')),
                'tax' => CompanyReceipt::roundValue(collect($data->products)->sum('tax')),
                'including_vat' => CompanyReceipt::roundValue(collect($data->products)->sum('in_tax')),
                'highlight' => true,
            ];
        }

        if (count($data->services ?? [])) {
            $services[] = array_merge($common_header, [
                'name' => __('pos_strings.pdf.services'),
            ]);
        }

        foreach (collect($data->services)->sortByDesc('percentage') as $key => $item) {
            $services[] = [
                'name' => __('pos_strings.pdf.tax') . " {$item->percentage}%",
                'quantity' =>  $item->quantity,
                'excluding_vat' => CompanyReceipt::roundValue($item->ex_tax),
                'tax' => CompanyReceipt::roundValue($item->tax),
                'including_vat' => CompanyReceipt::roundValue($item->in_tax),
            ];
        }

        if (count($data->services ?? [])) {
            $services[] = [
                'name' => __('pos_strings.pdf.total'),
                'quantity' =>  collect($data->services)->sum('quantity'),
                'excluding_vat' => CompanyReceipt::roundValue(collect($data->services)->sum('ex_tax')),
                'tax' => CompanyReceipt::roundValue(collect($data->services)->sum('tax')),
                'including_vat' => CompanyReceipt::roundValue(collect($data->services)->sum('in_tax')),
                'highlight' => true,
            ];
        }

        if (count($data?->payment_methods ?? [])) {
            $payment_methods[] = array_merge($common_header, [
                'name' => __('pos_strings.pdf.payment_method'),
            ]);
        }

        foreach ($data->payment_methods as $key => $item) {
            $payment_methods[] = [
                'name' => $item->name,
                'quantity' =>  $item->quantity,
                'excluding_vat' => "",
                'tax' => "",
                'including_vat' => CompanyReceipt::roundValue($item->in_tax),
            ];
        }

        if ($data->gift_card->quantity) {
            $payment_methods[] = [
                'name' => __('pos_strings.pdf.gift_card'),
                'quantity' =>  $data->gift_card->quantity,
                'excluding_vat' => "",
                'tax' => "",
                'including_vat' => CompanyReceipt::roundValue($data->gift_card->in_tax),
            ];
        }

        if (count($payment_methods)) {
            $payment_methods[] = [
                'name' => __('pos_strings.pdf.total'),
                'quantity' =>  collect($data->payment_methods)->sum('quantity') + $data->gift_card->quantity,
                'excluding_vat' => "",
                'tax' => "",
                'including_vat' => CompanyReceipt::roundValue(collect($data->payment_methods)->sum('in_tax') + $data->gift_card->in_tax),
                'highlight' => true,
            ];
        }

        $others[] = array_merge($common_header, [
            'name' => __('pos_strings.pdf.other'),
        ]);

        // $others[] = [
        //     'name' => __('pos_strings.pdf.total'),
        //     'quantity' =>  collect($data->products)->sum('quantity') + collect($data->services)->sum('quantity'),
        //     'excluding_vat' => collect($data->products)->sum('ex_tax') + collect($data->services)->sum('ex_tax'),
        //     'tax' => collect($data->products)->sum('tax') + collect($data->services)->sum('tax'),
        //     'including_vat' => collect($data->products)->sum('in_tax') + collect($data->services)->sum('in_tax'),
        // ];

        $others[] = [
            'name' => __('pos_strings.pdf.returns'),
            'quantity' =>  $data->refund->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->refund->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->refund->tax),
            'including_vat' => CompanyReceipt::roundValue($data->refund->in_tax),
        ];

        $others[] = [
            'name' => __('pos_strings.pdf.discount'),
            'quantity' =>  $data->discount->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->discount->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->discount->tax),
            'including_vat' => CompanyReceipt::roundValue($data->discount->in_tax),
        ];

        $others[] = [
            'name' => __('pos_strings.pdf.cancellation'),
            'quantity' =>  $data->cancelled->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->cancelled->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->cancelled->tax),
            'including_vat' => CompanyReceipt::roundValue($data->cancelled->in_tax),
        ];

        $others[] = [
            'name' => __('pos_strings.pdf.receipt_copy'),
            'quantity' =>  $data->receipt_copy->quantity,
            'excluding_vat' => CompanyReceipt::roundValue($data->receipt_copy->ex_tax),
            'tax' => CompanyReceipt::roundValue($data->receipt_copy->tax),
            'including_vat' => CompanyReceipt::roundValue($data->receipt_copy->in_tax),
        ];

        // $report_data[] = [
        //     'name' => __('pos_strings.pdf.total'),
        //     'quantity' =>  $data->total->quantity,
        //     'excluding_vat' => CompanyReceipt::roundValue($data->total->ex_tax),
        //     'tax' => CompanyReceipt::roundValue($data->total->tax),
        //     'including_vat' => CompanyReceipt::roundValue($data->total->in_tax),
        // ];

        $report_data[] = $sales;
        $report_data[] = $products;
        $report_data[] = $services;
        $report_data[] = $taxes;
        $report_data[] = $payment_methods;
        $report_data[] = $others;

        return $report_data;
    }

    public function getClosingAmount(CompanyZReport $z_report)
    {
        $start_date = $z_report->start_datetime;
        $end_date = $z_report->end_datetime;

        $receipt_amount = CompanyReceipt::where('company_id', $z_report->company_id)->paidAtBetween($start_date, $end_date)->get()->sum('total_formatted');
        $refund_amount = CompanyReceiptRefund::where('company_id', $z_report->company_id)->refundAtBetween($start_date, $end_date)->get()->sum('total_amount');
        return CompanyReceipt::roundValue($receipt_amount - $refund_amount);
    }

    public function hasReportData(CompanyZReport $z_report): bool
    {
        $start_date = $z_report->start_datetime;
        $end_date = $z_report->end_datetime ?? now();

        $has_company_receipts = CompanyReceipt::where('company_id', $z_report->company_id)->createdAtBetween($start_date, $end_date)->exists();
        $has_company_refund_receipts = CompanyReceiptRefund::where('company_id', $z_report->company_id)->refundAtBetween($start_date, $end_date)->exists();

        return $has_company_receipts || $has_company_refund_receipts;
    }
}
