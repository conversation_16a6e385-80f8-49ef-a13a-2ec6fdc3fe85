<?php

namespace App\Traits\Booking;

use App\CompanyBooking;
use App\Models\CompanyBookingMetadata;
use App\Models\UserCalendar;
use App\Models\UserOauthToken;
use App\Traits\OAuth\Google\Event;
use App\Traits\OAuth\Google\GoogleAuth;
use Carbon\Carbon;
use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Calendar;
use PhpParser\Node\Stmt\Foreach_;

trait BookingGoogleCalendarManager
{
    public static function syncWithBooking(CompanyBooking $booking)
    {
        $booking = $booking->refresh();
        $user = $booking->user;
        if (!$user) {
            return;
        }
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        if (!$oauth_token) {
            return;
        }

        if ($booking->is_cancelled) {
            return;
        }

        $calendars = $user->calendars;

        GoogleAuth::refreshTokenForUser($user);
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        foreach ($calendars as $calendar) {
            if ($calendar->type == UserCalendar::PUBLIC) {
                $company_meta_data = CompanyBookingMetadata::where('company_booking_id', $booking->id)->where('key', CompanyBookingMetadata::PUBLIC_CAL_ID)->first();
                $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
                $google_calendar = GoogleAuth::createForCalendarId($google_auth, $calendar->calendar_id);
                if (!$company_meta_data) {
                    $event = Event::create($google_calendar, [
                        'name' => $booking?->service?->name ?? 'no service',
                        'startDateTime' => Carbon::parse($booking->start_at, $booking->company->timezone),
                        'endDateTime' => Carbon::parse($booking->end_at, $booking->company->timezone),
                    ]);
                    if ($event) {
                        CompanyBookingMetadata::create([
                            'company_booking_id' => $booking->id,
                            'key' => CompanyBookingMetadata::PUBLIC_CAL_ID,
                            'value' => $event?->id,
                        ]);
                    }
                } else {

                    try {
                        $event = Event::edit(
                            $google_calendar,
                            $company_meta_data->value,
                            [
                                'name' => $booking?->service?->name ?? 'no service',
                                'startDateTime' => Carbon::parse($booking->start_at, $booking->company->timezone),
                                'endDateTime' => Carbon::parse($booking->end_at, $booking->company->timezone),
                            ]
                        );
                    } catch (\Throwable $th) {
                        $company_meta_data->delete();
                        $event = Event::create($google_calendar, [
                            'name' => $booking?->service?->name ?? 'no service',
                            'startDateTime' => Carbon::parse($booking->start_at, $booking->company->timezone),
                            'endDateTime' => Carbon::parse($booking->end_at, $booking->company->timezone),
                        ]);
                        if ($event) {
                            CompanyBookingMetadata::create([
                                'company_booking_id' => $booking->id,
                                'key' => CompanyBookingMetadata::PUBLIC_CAL_ID,
                                'value' => $event?->id,
                            ]);
                        }
                    }
                }
            } else {
                $company_meta_data = CompanyBookingMetadata::where('company_booking_id', $booking->id)->where('key', CompanyBookingMetadata::PRIVATE_CAL_ID)->first();
                $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
                $google_calendar = GoogleAuth::createForCalendarId($google_auth, $calendar->calendar_id);
                $final_message = null;
                $service_name = $booking?->service?->name ?? 'no service';
                if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
                    $client_count = $booking->active_clients()->count();
                    $final_message = $service_name . ' ( ' . $client_count . ' )';
                } else {
                    $client_name = $booking?->client?->first_name . ' ' . $booking?->client?->last_name;
                    $client_email = $booking?->client?->email;
                    $final_message = $service_name . ' ( ' . $client_name . ' - ' . $client_email . ' )';
                }
                if (!$company_meta_data) {
                    $event = Event::create($google_calendar, [
                        'name' => $final_message,
                        'startDateTime' => Carbon::parse($booking->start_at, $booking->company->timezone),
                        'endDateTime' => Carbon::parse($booking->end_at, $booking->company->timezone),
                    ]);
                    if ($event) {
                        CompanyBookingMetadata::create([
                            'company_booking_id' => $booking->id,
                            'key' => CompanyBookingMetadata::PRIVATE_CAL_ID,
                            'value' => $event?->id,
                        ]);
                    }
                } else {
                    try {
                        $event = Event::edit(
                            $google_calendar,
                            $company_meta_data->value,
                            [
                                'name' => $final_message,
                                'startDateTime' => Carbon::parse($booking->start_at, $booking->company->timezone),
                                'endDateTime' => Carbon::parse($booking->end_at, $booking->company->timezone),
                            ]
                        );
                    } catch (\Throwable $th) {
                        $company_meta_data->delete();
                        $event = Event::create($google_calendar, [
                            'name' => $final_message,
                            'startDateTime' => Carbon::parse($booking->start_at, $booking->company->timezone),
                            'endDateTime' => Carbon::parse($booking->end_at, $booking->company->timezone),
                        ]);
                        if ($event) {
                            CompanyBookingMetadata::create([
                                'company_booking_id' => $booking->id,
                                'key' => CompanyBookingMetadata::PRIVATE_CAL_ID,
                                'value' => $event?->id,
                            ]);
                        }
                    }
                }
            }
        }
    }

    public static function removeBooking(CompanyBooking $booking)
    {
        $booking = $booking->refresh();
        $user = $booking->user;
        if (!$user) {
            return;
        }
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        if (!$oauth_token) {
            return;
        }

        $calendars = $user->calendars;

        GoogleAuth::refreshTokenForUser($user);
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        foreach ($calendars as $calendar) {
            if ($calendar->type == UserCalendar::PUBLIC) {
                $company_meta_data = CompanyBookingMetadata::where('company_booking_id', $booking->id)->where('key', CompanyBookingMetadata::PUBLIC_CAL_ID)->first();
                $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
                $google_calendar = GoogleAuth::createForCalendarId($google_auth, $calendar->calendar_id);
                if ($company_meta_data) {
                    $event = Event::delete(
                        $google_calendar,
                        $company_meta_data->value
                    );
                    $company_meta_data->delete();
                }
            } else {
                $company_meta_data = CompanyBookingMetadata::where('company_booking_id', $booking->id)->where('key', CompanyBookingMetadata::PRIVATE_CAL_ID)->first();
                $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
                $google_calendar = GoogleAuth::createForCalendarId($google_auth, $calendar->calendar_id);
                if ($company_meta_data) {
                    $event = Event::delete(
                        $google_calendar,
                        $company_meta_data->value
                    );
                    $company_meta_data->delete();
                }
            }
        }
    }
}