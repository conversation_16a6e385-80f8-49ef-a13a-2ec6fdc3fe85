<?php

namespace App\Traits;

use App\Client;
use App\CompanyBooking;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

trait HasCampaign
{
    use ApiResponser;

    public function filterClients(array $filters = [], bool $paginate = false, bool $showCount = false)
    {
        $timezone = $this->timezone ?? 'Europe/Stockholm';

        $clients = Client::query()->setEagerLoads([]);

        $clients = $clients->marketingSubscribed();

        $clients = $clients->where('deleted_at', null);

        $clients = $clients->where('company_id', $this->id);

        $filters = collect($filters);

        $from_query_filters = $filters->whereIn('type', [
            'joined',
            'service',
            'booking',
            'letter_of_consent',
            'questionary',
            'has_mobile_number',
        ]);

        $from_collect_filters = $filters->whereIn('type', [
            'upcoming_birthday',
            'age',
            'has_mobile_number',
            'search',
        ]);

        foreach ($from_query_filters as $key => $filter) {
            $type = $filter['type'];
            $key = $filter['key'];
            $value = $filter['value'];

            if ($type == 'has_mobile_number') {
                $clients = $clients->where('phone_number', "!=", null);
                $clients = $clients->where('country_code', "!=", null);
            }

            if ($type == 'joined') {
                $date = now()->subtract($key, $value)->startOfDay()->timezone($timezone);

                $clients = $clients->where('created_at', '>=', $date);
            }

            if (($type == 'service' || $type == 'booking') && !$showCount) {
                $services = isset($value['services']) ? $value['services'] : null;

                $clients = $clients->with([
                    'booking' => function ($query) use ($services) {
                        $query = $query->orderBy('start_at', 'desc');

                        if ($services && count($services)) {
                            $query->whereIn('service_id', $services);
                        }

                        $query->where(fn($q) => $q->where('is_cancelled', null)->orWhere('is_cancelled', false));

                        return $query;
                    },
                    'group_booking' => function ($query) use ($services) {
                        if ($services && count($services)) {
                            return $query->orderByDesc(
                                CompanyBooking::select('start_at')
                                    ->whereColumn('id', 'company_booking_clients.id')
                                    ->where(fn($q) => $q->where('is_cancelled', null)->orWhere('is_cancelled', false))
                                    ->whereIn('service_id', $services)
                                    ->orderByDesc('start_at')
                                    ->limit(1)
                            );
                        }
                        return $query->orderByDesc(
                            CompanyBooking::select('start_at')
                                ->whereColumn('id', 'company_booking_clients.id')
                                ->where(fn($q) => $q->where('is_cancelled', null)->orWhere('is_cancelled', false))
                                ->orderByDesc('start_at')
                                ->limit(1)
                        );
                    },
                ]);
            }

            if ($type == 'service') {
                $clients = $clients->where(function ($query) use ($key, $value) {
                    $query->whereHas('bookings', function ($query) use ($key, $value) {
                        return $query->whereIn('company_bookings.service_id', $value)->where(fn($q) => $q->where('is_cancelled', null)->orWhere('is_cancelled', false));
                    })->orWhereHas('group_bookings.booking', function ($query) use ($key, $value) {
                        return $query->whereIn('company_bookings.service_id', $value)->where(fn($q) => $q->where('is_cancelled', null)->orWhere('is_cancelled', false));
                    });
                });
            }

            if ($type == 'booking') {
                $date = $value['date'];
                $services = isset($value['services']) ? $value['services'] : null;

                $date = now()->subtract($key, $date)->startOfDay()->timezone($timezone);

                $clients = $clients->where(function ($query) use ($services) {
                    $query->selectRaw('start_at')->from('company_bookings');

                    $query->where(function ($query) {
                        $query->whereColumn('company_bookings.client_id', 'clients.id')
                            ->orWhereExists(function ($query) {
                                $query->select(DB::raw(1))
                                    ->from('company_booking_clients')
                                    ->whereColumn('company_booking_clients.booking_id', 'company_bookings.id')
                                    ->whereColumn('company_booking_clients.client_id', 'clients.id');
                            });
                    });

                    if ($services && count($services)) {
                        $query->whereIn('company_bookings.service_id', $services);
                    }

                    $query->where(fn($q) => $q->where('is_cancelled', null)->orWhere('is_cancelled', false));

                    return $query->orderByDesc('company_bookings.start_at')
                        ->limit(1);
                }, '<', $date);
            }

            if ($type == 'letter_of_consent') {
                $date = $value;

                $date = now()->subtract($key, $date)->startOfDay()->timezone($timezone);

                $clients = $clients->where(function ($query) {
                    $query = $query->selectRaw('created_at')->from('client_letter_of_consents')
                        ->whereColumn('client_letter_of_consents.client_id', 'clients.id');

                    return $query->orderByDesc('client_letter_of_consents.created_at')
                        ->limit(1);
                }, '<', $date);
            }

            if ($type == 'questionary') {
                $date = $value;

                $date = now()->subtract($key, $date)->startOfDay()->timezone($timezone);

                $clients = $clients->where(function ($query) use ($date) {
                    return $query->where(function ($query) {

                        $query->select('created_at') // Select the maximum date
                            ->from(function ($unionQuery) {
                                // Get dates from aesthetic_interests
                                $unionQuery->select('created_at')
                                    ->from('aesthetic_interests')
                                    ->whereColumn('aesthetic_interests.client_id', 'clients.id')
                                    ->unionAll(
                                        DB::table("health_questionaries")
                                            ->select('created_at')
                                            ->whereColumn('health_questionaries.client_id', 'clients.id')
                                    )->unionAll(
                                        DB::table("covid19s")
                                            ->select('created_at')
                                            ->whereColumn('covid19s.client_id', 'clients.id')
                                    )->unionAll(
                                        DB::table("questionary_data")
                                            ->select('created_at')
                                            ->whereColumn('questionary_data.client_id', 'clients.id')
                                    );
                            }, 'all_dates')
                            ->orderByDesc('created_at')
                            ->limit(1);
                    }, '<', $date);
                });
            }
        }


        if ($showCount && !count($from_collect_filters)) {
            return $clients->count();
        }

        if ($paginate && !count($from_collect_filters)) {
            return $clients->paginate();
        }

        $clients = $clients->lazy();

        if ($from_collect_filters->count()) {
            $clients = $clients->filter(function ($client) use ($from_collect_filters) {
                $show = true;

                foreach ($from_collect_filters as $key => $filter) {
                    $type = $filter['type'];
                    $key = $filter['key'];
                    $value = $filter['value'];

                    if ($type == 'upcoming_birthday' && $show) {
                        if (!$client->date_of_birth) {
                            $show = false;
                            break;
                        }

                        $start_date = now()->startOfDay();
                        $end_date = now()->add($key, $value)->startOfDay();

                        $show = $this->isBirthdayBetweenDates($client->date_of_birth, $start_date, $end_date);
                    }

                    if ($type == 'age' && $show) {
                        if (!$client->date_of_birth) {
                            $show = false;
                            break;
                        }

                        $age = Carbon::parse($client->date_of_birth)->age;

                        if ($key == 'above')
                            $show = $age > $value;
                        if ($key == 'below')
                            $show = $age < $value;
                        if ($key == 'equal')
                            $show = $age == $value;
                    }

                    if ($type == 'has_mobile_number' && $show) {
                        $show = $client->phone_number && $client->country_code;
                    }

                    if ($type == 'search' && $show && $value) {
                        $search = Str::lower($value);
                        $search = preg_replace('/\s\s+/', ' ', $search);

                        $names = explode(' ', trim($search ?? "")); // Split string by spaces

                        if (count($names) > 1) {
                            $last_name = array_pop($names); // Remove and get the last word
                            $first_name = implode(' ', $names); // Join the remaining words
                        } else {
                            $first_name = $names[0] ?? ''; // Single word as first name
                            $last_name = $first_name ?? ''; // No last name
                        }

                        $show = (Str::contains(Str::lower($client->first_name), $first_name) &&
                            Str::contains(Str::lower($client->last_name), $last_name))
                            ||
                            Str::contains(Str::lower($client->full_name), $search) ||
                            Str::contains(Str::lower($client->email), $search) ||
                            Str::contains(Str::lower($client->phone()), $search);
                    }
                }

                return $show;
            });

            $search_data = $from_collect_filters->where('key', 'search')->first();

            if ($search_data && $search_data['value']) {
                $type = $filter['type'];
                $key = $filter['key'];
                $value = $filter['value'];

                $search = Str::lower($value);
                $search = preg_replace('/\s\s+/', ' ', $search);

                $clients = $clients->sortByDesc(function ($i, $k) use ($search) {
                    $props = [
                        // 'full_name' => 10,
                        'first_name' => 5,
                        'last_name' => 5,
                        'email' => 2,
                        'phone_number' => 1,
                        'country_code' => 1,
                    ];
                    $weight = 0;
                    foreach ($props as $prop => $acquire_weight) {
                        if (strpos(Str::lower($i->{$prop}), $search) !== false) {
                            $weight += $acquire_weight;
                        } // Increase weight if the search term is found
                    }
                    $search_array = explode(' ', $search);
                    if (Str::lower($i->first_name) == Str::lower($search_array[0])) {
                        $weight = $weight + 100;
                    }
                    return $weight;
                });
            }
        }

        if ($showCount) {
            return $clients->count();
        }

        if ($paginate) {
            return $this->paginate($clients);
        }

        return $clients->values();
    }

    private function isBirthdayBetweenDates($birthdate, $startDate, $endDate)
    {
        // Parse the dates using Carbon
        $birthdate = Carbon::parse($birthdate);
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // Extract month and day only (ignore the year)
        $birthMonthDay = $birthdate->format('m-d');
        $startMonthDay = $startDate->format('m-d');
        $endMonthDay = $endDate->format('m-d');

        // Check if the birthday falls between the start and end date (month and day only)
        if ($startMonthDay <= $endMonthDay) {
            // When the date range is within the same year
            return ($birthMonthDay >= $startMonthDay && $birthMonthDay <= $endMonthDay);
        } else {
            // When the date range spans across December to January (end of year)
            return ($birthMonthDay >= $startMonthDay || $birthMonthDay <= $endMonthDay);
        }
    }
}
