<?php

namespace App\Traits\OAuth\Google;

use Carbon\Carbon;
use DateTime;
use Google\Service\Calendar\EventDateTime;
use Google_Service_Calendar_ConferenceData;
use Google_Service_Calendar_ConferenceSolutionKey;
use Google_Service_Calendar_CreateConferenceRequest;
use Google_Service_Calendar_Event;
use Google_Service_Calendar_EventAttendee;
use Google_Service_Calendar_EventDateTime;
use Google_Service_Calendar_EventSource;
use Illuminate\Support\Collection;
use Carbon\CarbonInterface;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class Event
{
    /** @var \Google_Service_Calendar_Event */
    public $googleEvent;
    public $id;
    public EventDateTime $start_datetime;
    public EventDateTime $end_datetime;
    public $summary;

    public function __construct()
    {
        $this->googleEvent = new Google_Service_Calendar_Event;
    }
    public static function createFromGoogleCalendarEvent(Google_Service_Calendar_Event $googleEvent)
    {
        $event = new static;

        $event->id = $googleEvent->id;
        $event->start_datetime = $googleEvent->start;
        $event->end_datetime = $googleEvent->end;
        $event->summary = $googleEvent->summary;
        $event->googleEvent = $googleEvent;

        return $event;
    }


    public static function get(GoogleCalendar $googleCalendar): Collection
    {

        $googleEvents = $googleCalendar->listEvents();

        $googleEventsList = $googleEvents->getItems();

        while ($googleEvents->getNextPageToken()) {
            $queryParameters['pageToken'] = $googleEvents->getNextPageToken();

            $googleEvents = $googleCalendar->listEvents();

            $googleEventsList = array_merge($googleEventsList, $googleEvents->getItems());
        }

        $useUserOrder = isset($queryParameters['orderBy']);

        return collect($googleEventsList)
            ->map(function (Google_Service_Calendar_Event $event) {
                return static::createFromGoogleCalendarEvent($event);
            })
            // ->sortBy(function (self $event, $index) use ($useUserOrder) {
            //     if ($useUserOrder) {
            //         return $index;
            //     }
            //     return $event->sortDate;
            // })
            ->values();
    }

    public static function create(GoogleCalendar $googleCalendar, array $properties)
    {
        $event = new static;

        foreach ($properties as $name => $value) {
            $event->$name = $value;
        }

        return $event->save($googleCalendar);
    }

    public static function edit(GoogleCalendar $googleCalendar, $event_id, array $properties)
    {
        $event = new static;

        $event->id = $event_id;
        foreach ($properties as $name => $value) {
            $event->$name = $value;
        }

        return $event->update($googleCalendar);
    }

    public static function delete(GoogleCalendar $googleCalendar, $event_id, array $properties = [])
    {
        $event = new static;
        $event->id = $event_id;
        foreach ($properties as $name => $value) {
            $event->$name = $value;
        }
        return $googleCalendar->deleteEvent($event, $properties);
    }

    public function save(GoogleCalendar $googleCalendar)
    {
        return $googleCalendar->insertEvent($this);
    }

    public function update(GoogleCalendar $googleCalendar)
    {
        return $googleCalendar->updateEvent($this);
    }

    public function __get($name)
    {
        $name = $this->getFieldName($name);

        if ($name === 'sortDate') {
            return $this->getSortDate();
        }

        if ($name === 'source') {
            return [
                'title' => $this->googleEvent->getSource()->title,
                'url' => $this->googleEvent->getSource()->url,
            ];
        }

        $value = Arr::get($this->googleEvent, $name);

        if (in_array($name, ['start.date', 'end.date']) && $value) {
            $value = Carbon::createFromFormat('Y-m-d', $value)->startOfDay();
        }

        if (in_array($name, ['start.dateTime', 'end.dateTime']) && $value) {
            $value = Carbon::createFromFormat(DateTime::RFC3339, $value);
        }

        return $value;
    }

    public function __set($name, $value)
    {
        $name = $this->getFieldName($name);

        if (in_array($name, ['start.date', 'end.date', 'start.dateTime', 'end.dateTime'])) {
            $this->setDateProperty($name, $value);

            return;
        }

        if ($name == 'source') {
            $this->setSourceProperty($value);

            return;
        }

        Arr::set($this->googleEvent, $name, $value);
    }

    protected function setSourceProperty(array $value)
    {
        $source = new Google_Service_Calendar_EventSource([
            'title' => $value['title'],
            'url' => $value['url'],
        ]);

        $this->googleEvent->setSource($source);
    }
    public function getSortDate(): string
    {
        if ($this->startDate) {
            return $this->startDate;
        }

        if ($this->startDateTime) {
            return $this->startDateTime;
        }

        return '';
    }

    protected function setDateProperty(string $name, CarbonInterface $date)
    {
        $eventDateTime = new Google_Service_Calendar_EventDateTime;

        if (in_array($name, ['start.date', 'end.date'])) {
            $eventDateTime->setDate($date->format('Y-m-d'));
            $eventDateTime->setTimezone((string) $date->getTimezone());
        }

        if (in_array($name, ['start.dateTime', 'end.dateTime'])) {
            $eventDateTime->setDateTime($date->format(DateTime::RFC3339));
            $eventDateTime->setTimezone((string) $date->getTimezone());
        }

        if (Str::startsWith($name, 'start')) {
            $this->googleEvent->setStart($eventDateTime);
        }

        if (Str::startsWith($name, 'end')) {
            $this->googleEvent->setEnd($eventDateTime);
        }
    }

    protected function getFieldName(string $name): string
    {
        return [
            'name' => 'summary',
            'startDate' => 'start.date',
            'endDate' => 'end.date',
            'startDateTime' => 'start.dateTime',
            'endDateTime' => 'end.dateTime',
        ][$name] ?? $name;
    }
}
