<?php

namespace App\Traits\OAuth\Google;

use App\Models\UserOauthToken;
use App\User;
use Carbon\Carbon;
use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Calendar;
use <PERSON><PERSON>\Socialite\Facades\Socialite;

class GoogleAuth
{

    public static function createOAuthClient($token): Google_Client
    {
        $client = new Google_Client;

        $client->setScopes([
            Google_Service_Calendar::CALENDAR,
        ]);

        $client->setAuthConfig(storage_path('app/google-calendar/oauth-credentials.json'));

        $token = json_encode([
            'access_token' => $token,
        ]);
        $client->setAccessToken($token);

        return $client;
    }

    public static function createForCalendarId(Google_Client $google_Client, string $calendarId): GoogleCalendar
    {

        $service = new Google_Service_Calendar($google_Client);

        return new GoogleCalendar($service, $calendarId);
    }

    public static function createNewCalendar(Google_Client $google_Client, $calendar_name): GoogleCalendar
    {

        $service = new Google_Service_Calendar($google_Client);

        $new_cal = new Google_Service_Calendar_Calendar();

        $new_cal->setSummary($calendar_name);

        $new_cal->setDescription($calendar_name);

        $cal_data = $service->calendars->insert($new_cal);
        return new GoogleCalendar($service, $cal_data->id);
    }

    public static function deleteCalendarById(Google_Client $google_Client, string $calendarId)
    {
        $service = new Google_Service_Calendar($google_Client);

        $service->calendars->delete($calendarId);
        return true;
    }

    public static function refreshTokenForUser(User $user): UserOauthToken
    {
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        $social_user = Socialite::driver('google')->refreshToken($oauth_token->refresh_token);
        $user_data = Socialite::driver('google')->userFromToken($social_user->token);
        $oauth_token = UserOauthToken::updateOrCreate([
            'user_id' => $user->id,
            'provider' => UserOauthToken::GOOGLE,
            'type' => UserOauthToken::CALENDAR,
        ], [
            'email' => $user_data->email,
            'scopes' => $social_user->approvedScopes,
            'access_token' => $social_user->token,
            'refresh_token' => $social_user->refreshToken,
            'token_expires_at' => Carbon::now()->addMinutes(58),
        ]);

        return $oauth_token;
    }
}