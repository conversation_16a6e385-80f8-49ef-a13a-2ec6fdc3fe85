<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserCompanyBilling extends Model
{
    use HasFactory;

    const WEEKLY = "WEEKLY", MONTHLY = "MONTHLY", YEARLY = "YEARLY", PAID = "PAID", UPCOMING = "UPCOMING",PENDING = "PENDING";
    //TABLE
    public $table = 'user_company_billings';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'user_id',
        'earning',
        'signed_prescription_count',
        'start_date',
        'end_date',
        'payment_status',
        'period_type',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}