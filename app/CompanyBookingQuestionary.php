<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyBookingQuestionary extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'company_booking_questionaries';

    //FILLABLES
    protected $fillable = [
        'company_booking_id',
        'modelable_type',
        'modelable_id',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function modelable()
    {
        return $this->morphTo();
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
