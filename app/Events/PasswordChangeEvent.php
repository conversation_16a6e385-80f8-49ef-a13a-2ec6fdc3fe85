<?php

namespace App\Events;

use App\Support;
use App\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PasswordChangeEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user, $old_password_hash;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $user, $old_password_hash)
    {
        $this->user = $user;
        $this->old_password_hash = $old_password_hash;
    }
}
