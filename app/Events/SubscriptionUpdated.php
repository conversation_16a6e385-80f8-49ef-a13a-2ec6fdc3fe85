<?php

namespace App\Events;

use App\Company;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SubscriptionUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $company;
    public $previous_subscription;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Company $company, $previous_subscription)
    {
        $this->company = $company;
        $this->previous_subscription = $previous_subscription;
    }
}
