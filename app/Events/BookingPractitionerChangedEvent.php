<?php

namespace App\Events;

use App\CompanyBooking;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BookingPractitionerChangedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $company_booking;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(CompanyBooking $company_booking)
    {
        $this->company_booking = $company_booking;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
