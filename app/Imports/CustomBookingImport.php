<?php

namespace App\Imports;

use App\Client;
use App\Company;
use App\CompanyBooking;
use App\CompanyService;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class CustomBookingImport implements WithStartRow, ToCollection, WithChunkReading, ShouldQueue
{
    public array $data;
    public Company $company;
    public User $user;
    public array $output;
    public $should_show_summery;

    public function __construct(
        $data,
        Company $company,
        User $user,
        $should_show_summery = 0
    ) {
        $this->data = $data;
        $this->company = $company;
        $this->user = $user;
        $this->output = [];
        $this->should_show_summery = $should_show_summery;
    }

    public function startRow(): int
    {
        return 2;
    }
    public function chunkSize(): int
    {
        return 200;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        $interval_index = $this->data['interval_index'];
        $date_index = $this->data['date_index'];
        // $status_index = $this->data['status_index'];
        $special_request_index = null;

        // checking for email format and to be unique
        $email_pattern = "/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i";

        if (isset($this->data['special_request_index'])) {
            $special_request_index = $this->data['special_request_index'];
        }

        $client_first_name_index = $this->data['client_first_name_index'];

        $client_last_name_index = null;
        if (isset($this->data['client_last_name_index'])) {
            $client_last_name_index = $this->data['client_last_name_index'];
        }

        $client_email_index = null;
        if (isset($this->data['client_email_index'])) {
            $client_email_index = $this->data['client_email_index'];
        }

        $client_personal_number_index = null;
        if (isset($this->data['client_personal_number_index'])) {
            $client_personal_number_index = $this->data['client_personal_number_index'];
        }

        $client_phone_number_index = null;
        if (isset($this->data['client_phone_number_index'])) {
            $client_phone_number_index = $this->data['client_phone_number_index'];
        }

        $practitioner_name_index = null;
        if (isset($this->data['practitioner_name_index'])) {
            $practitioner_name_index = $this->data['practitioner_name_index'];
        }
        $practitioner_email_index = null;
        if (isset($this->data['practitioner_email_index'])) {
            $practitioner_email_index = $this->data['practitioner_email_index'];
        }
        $service_name_index = $this->data['service_name_index'];
        $price_index = null;
        if (isset($this->data['price_index'])) {
            $price_index = $this->data['price_index'];
        }

        $duration_index = $this->data['duration_index'];

        $should_find_by_email = false;
        if ($practitioner_email_index) {
            $should_find_by_email = true;
        }

        $already_added_clients_emails = $this->company->clients;

        //BOOKINGS
        $imported_booking_data = collect([]);
        $not_imported_booking_data = collect([]);

        //SERVICES
        $imported_service_names = collect([]);
        $not_imported_service_names = collect([]);

        //CLIENTS
        $imported_client_emails = collect([]);
        $not_imported_client_emails = collect([]);

        foreach ($collection as $row) {
            //BOOKING DATA VALIDATION
            $start_at = null;
            $end_at = null;
            $status = 'BOOKED';
            if ($status != 'BOOKED') {
                $not_imported_booking_data->push([
                    'date' => $row[$date_index],
                    'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
                    'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
                    'interval' => $row[$interval_index],
                    'client_email' => isset($row[$client_email_index]) ? $row[$client_email_index] : null,
                    'reason' => __('strings.status_not_booked'),
                ]);
                continue;
            }

            if ($special_request_index != null) {
                $special_request = $row[$special_request_index];
            }

            //getting the date of the booking
            $booking_date = $row[$date_index];

            try {
                $booking_date = Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($booking_date))->format('Y-m-d');
            } catch (\Throwable $th) {
                try {
                    $booking_date = Carbon::parse($booking_date)->format('Y-m-d');
                } catch (\Throwable $th) {
                    $not_imported_booking_data->push([
                        'date' => $row[$date_index],
                        'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
                        'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
                        'interval' => $row[$interval_index],
                        'client_email' => isset($row[$client_email_index]) ? $row[$client_email_index] : null,
                        'reason' => __('strings.wrong_date_format'),
                    ]);
                    continue;
                }
            }
            // if (Carbon::parse($booking_date)->lessThan(now()->format('Y-m-d'))) {
            //     $not_imported_booking_data->push([
            //         'date' => $booking_date,
            //         'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
            //         'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
            //         'interval' => $row[$interval_index],
            //         'client_email' => $row[$client_email_index],
            //         'reason' => __('strings.previous_date'),
            //     ]);
            //     continue;
            // }

            //getting intervals
            $temp_start_time = null;
            $temp_end_time = null;
            if (str_contains($row[$interval_index], '-')) {
                $temp_array = explode('-', $row[$interval_index]);
                try {
                    $temp_start_time = Carbon::parse($temp_array[0])->format('H:i:s');
                    $temp_end_time = Carbon::parse($temp_array[1])->format('H:i:s');
                } catch (\Throwable $th) {
                    $not_imported_booking_data->push([
                        'date' => $booking_date,
                        'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
                        'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
                        'interval' => $row[$interval_index],
                        'client_email' => isset($row[$client_email_index]) ? $row[$client_email_index] : null,
                        'reason' => __('strings.wrong_interval_format'),
                    ]);
                    continue;
                }
            } else {
                $not_imported_booking_data->push([
                    'date' => $booking_date,
                    'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
                    'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
                    'interval' => $row[$interval_index],
                    'client_email' => isset($row[$client_email_index]) ? $row[$client_email_index] : null,
                    'reason' => __('strings.wrong_interval_format'),
                ]);
                continue;
            }

            //PRACTITIONER DATA VALIDATION
            $practitioner_id = null;
            if ($should_find_by_email) {
                $pattern = "/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i";
                if (preg_match($pattern, $row[$practitioner_email_index])) {
                    $practitioner = User::where('company_id', $this->company->id)->where('email', $row[$practitioner_email_index])->verifiedEmail()->first();
                    if ($practitioner) {
                        $practitioner_id = $practitioner->id;
                    }
                }
            } else {
                $practitioners = User::where('company_id', $this->company->id)->verifiedEmail()->get();
                $practitioner_name = null;
                if ($practitioner_name_index) {
                    $practitioner_name = $row[$practitioner_name_index];
                }
                if ($practitioner_name) {
                    $name_array = explode(' ', $practitioner_name);

                    foreach ($practitioners as $practitioner) {
                        $temp_name = $practitioner->first_name . ' ' . $practitioner->last_name;
                        if (trim($temp_name) == trim($practitioner_name)) {
                            $practitioner_id = $practitioner->id;
                        }
                    }
                    if ($practitioner_id != null) {
                        foreach ($practitioners as $practitioner) {
                            if ($practitioner_id != null) {
                                continue;
                            }
                            foreach ($name_array as $element) {
                                if (str_contains($practitioner->first_name, $element)) {
                                    $practitioner_id = $practitioner->id;
                                }
                                if (str_contains($practitioner->last_name, $element)) {
                                    $practitioner_id = $practitioner->id;
                                }
                            }
                        }
                    }
                }
            }

            if ($practitioner_id == null) {
                $practitioner_id = $this->user->id;
            }

            // if ($practitioner_id == null) {
            //     $not_imported_booking_data->push([
            //         'date' => $booking_date,
            //         'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
            //         'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
            //         'interval' => $row[$interval_index],
            //         'client_email' => $row[$client_email_index],
            //     ]);
            //     continue;
            // }

            $start_at = Carbon::parse($booking_date . ' ' . $temp_start_time)->format('Y-m-d H:i:s');
            $end_at = Carbon::parse($booking_date . ' ' . $temp_end_time)->format('Y-m-d H:i:s');
            //CLIENT DATA VALIDATION
            $personal_number = null;
            if ($client_personal_number_index != null) {
                $personal_number = (string) $row[$client_personal_number_index];
            }
            // if ($personal_number) {
            //     $birth_date = null;
            //     $code = null;
            //     if (str_contains($personal_number, '-')) {
            //         $birth_date = explode('-', $personal_number)[0];
            //         $code = explode('-', $personal_number)[1];
            //     } else {
            //         if (strlen($personal_number) == 12) {
            //             $birth_date = substr($personal_number, 0, 8);
            //             $code = substr($personal_number, 8, 4);
            //         }
            //     }
            //     if ($birth_date && $code) {
            //         try {
            //             Carbon::createFromFormat('Ymd', $birth_date)->format('Y-m-d');
            //             if (strlen($code) == 4) {
            //                 $personal_number = $birth_date . '-' . $code;
            //             } else {
            //                 $personal_number = null;
            //             }
            //         } catch (\Throwable $th) {
            //             $personal_number = null;
            //         }
            //     } else {
            //         $personal_number = null;
            //     }
            // }

            $client_id = null;
            // if (! $row[$client_email_index]) {
            //     $not_imported_booking_data->push([
            //         'date' => $booking_date,
            //         'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
            //         'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
            //         'interval' => $row[$interval_index],
            //         'client_email' => $row[$client_email_index],
            //         'reason' => __('strings.client_email_not_given'),
            //     ]);
            //     continue;
            // }

            // if (isset($row[$client_email_index])) {
            //     if (! preg_match($email_pattern, $row[$client_email_index])) {
            //         $not_imported_booking_data->push([
            //             'date' => $booking_date,
            //             'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
            //             'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
            //             'interval' => $row[$interval_index],
            //             'client_email' => $row[$client_email_index],
            //             'reason' => __('strings.client_email_not_not_valid'),
            //         ]);
            //         continue;
            //     }
            // }

            // if ($client_email_index != null) {
            //     $clients = $already_added_clients_emails->filter(function ($client) use ($row, $client_email_index) {
            //         return strtolower($client->email) === strtolower($row[$client_email_index]);
            //     });

            //     if (count($clients) > 0) {
            //         foreach ($clients as $client) {
            //             $client_id = $client->id;
            //         }
            //     } else {
            //         $client_first_name = $client_first_name_index ? $row[$client_first_name_index] : null;
            //         $client_last_name = $client_last_name_index ? $row[$client_last_name_index] : null;

            //         $phone_number = null;
            //         $country_code = '46';
            //         if ($client_phone_number_index != null) {
            //             $phone_number = $row[$client_phone_number_index];

            //             // //separating the phone number and country code in 1 way(if given)
            //             // if ($phone_number && str_contains($phone_number, '(') && str_contains($phone_number, ')')) {
            //             //     $start = strpos($phone_number, '(') + 1;
            //             //     $end = strpos($phone_number, ')');
            //             //     $length = $end - $start;
            //             //     $country_code = substr($phone_number, $start, $length);
            //             //     $phone_number = explode(')', $phone_number)[1];
            //             // }

            //             // //separating the phone number and country code in other way (if given)
            //             // if ($phone_number && str_contains($phone_number, '+')) {
            //             //     if ($phone_number[0] != '+') {
            //             //         $phone_number = null;
            //             //     } else {
            //             //         $phone_number = substr($phone_number, 1);
            //             //     }
            //             //     if ($phone_number != null) {
            //             //         if (str_contains($phone_number, ' ')) {
            //             //             $country_code = explode(' ', $phone_number, 2)[0];
            //             //             $phone_number = str_replace(' ', '', explode(' ', $phone_number, 2)[1]);
            //             //         }
            //             //     }
            //             // }
            //         }

            //         $client = Client::create([
            //             'user_id' => $this->user->id,
            //             'company_id' => $this->company->id,
            //             'profile_picture' => '',
            //             'first_name' => $client_first_name ?? '',
            //             'last_name' => $client_last_name ?? '',
            //             'personal_id' => $personal_number ?? '',
            //             'email' => $row[$client_email_index] ?? '',
            //             'phone_number' => $phone_number ?? '',
            //             'country_code' => $country_code ?? '',
            //             'social_security_number' => '',
            //         ]);
            //         foreach ($this->company->users as $index => $user_in) {
            //             $user_in->accesses()->toggle([$client->id]);
            //         }
            //         $client_id = $client->id;

            //         $imported_client_emails->push($row[$client_email_index] . " - " . $personal_number);
            //     }
            //     if ($client_id) {
            //         $client = Client::where('id', $client_id)->first();
            //         // if ($client->personal_id) {
            //         $client->personal_id = $personal_number;
            //         $client->save();
            //         // }
            //     }
            // }
            $client_id = null;
            if ($client_id == null) {
                $clients = $already_added_clients_emails->filter(function ($client) use ($row, $personal_number) {
                    return strtolower($client->personal_id) === strtolower($personal_number);
                });
                if (count($clients) > 0) {
                    foreach ($clients as $client) {
                        $client_id = $client->id;
                    }
                } else {
                    $client_first_name = $client_first_name_index ? $row[$client_first_name_index] : null;
                    $client_last_name = $client_last_name_index ? $row[$client_last_name_index] : null;

                    $phone_number = null;
                    $country_code = '46';
                    if ($client_phone_number_index != null) {
                        $phone_number = $row[$client_phone_number_index];

                        // //separating the phone number and country code in 1 way(if given)
                        // if ($phone_number && str_contains($phone_number, '(') && str_contains($phone_number, ')')) {
                        //     $start = strpos($phone_number, '(') + 1;
                        //     $end = strpos($phone_number, ')');
                        //     $length = $end - $start;
                        //     $country_code = substr($phone_number, $start, $length);
                        //     $phone_number = explode(')', $phone_number)[1];
                        // }

                        // //separating the phone number and country code in other way (if given)
                        // if ($phone_number && str_contains($phone_number, '+')) {
                        //     if ($phone_number[0] != '+') {
                        //         $phone_number = null;
                        //     } else {
                        //         $phone_number = substr($phone_number, 1);
                        //     }
                        //     if ($phone_number != null) {
                        //         if (str_contains($phone_number, ' ')) {
                        //             $country_code = explode(' ', $phone_number, 2)[0];
                        //             $phone_number = str_replace(' ', '', explode(' ', $phone_number, 2)[1]);
                        //         }
                        //     }
                        // }
                    }

                    $client = Client::create([
                        'user_id' => $this->user->id,
                        'company_id' => $this->company->id,
                        'profile_picture' => '',
                        'first_name' => $client_first_name ?? '',
                        'last_name' => $client_last_name ?? '',
                        'personal_id' => $personal_number ?? '',
                        'email' => $row[$client_email_index] ?? '',
                        'phone_number' => $phone_number ?? '',
                        'country_code' => $country_code ?? '',
                        'social_security_number' => '',
                    ]);
                    foreach ($this->company->users as $index => $user_in) {
                        $user_in->accesses()->toggle([$client->id]);
                    }
                    $client_id = $client->id;

                    if (isset($row[$client_email_index])) {
                        $imported_client_emails->push($row[$client_email_index] . " - " . $personal_number);
                    } else {
                        $imported_client_emails->push($personal_number);
                    }
                }
            }


            //SERVICE DATA VALIDATION
            $service_name = $row[$service_name_index];
            if (! isset($service_name) || empty($service_name)) {
                $not_imported_booking_data->push([
                    'date' => $booking_date,
                    'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
                    'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
                    'interval' => $row[$interval_index],
                    'client_email' => $row[$client_email_index],
                    'reason' => __('strings.wrong_service_name'),
                ]);
                continue;
            }
            $service_id = null;
            $services = CompanyService::where('company_id', $this->company->id)->get();
            $service = $services->where('name', $row[$service_name_index])->first();
            if ($service) {
                $service_id = $service->id;
            } else {
                // if (! $this->should_show_summery) {
                $price = 0;
                if (isset($row[$price_index]) && is_numeric($row[$price_index])) {
                    $price = $row[$price_index];
                }
                $duration = 0;
                if (is_numeric($row[$duration_index])) {
                    $duration = $row[$duration_index];
                }
                $service = CompanyService::create([
                    'company_id' => $this->company->id,
                    'category_id' => null,
                    'name' => $row[$service_name_index],
                    'color' => null,
                    'price' => $price ?? 0,
                    'duration' => $duration ?? 0,
                    'description' => null,
                    'is_active' => 1,
                    'time_margin' => null,
                    'parallel_booking_count' => null,
                ]);
                $service_id = $service->id;
                // }

                $imported_service_names->push($row[$service_name_index]);
            }

            if ($client_id && $service_id) {
                $client = Client::where('id', $client_id)->first();
                $service = CompanyService::where('id', $service_id)->first();
                $practitioner = null;
                if ($practitioner_id) {
                    $practitioner = User::where('id', $practitioner_id)->verifiedEmail()->first();
                }
                $address = null;
                if (count($client->addresses) > 0) {
                    $address = $client->addresses[0];
                }
                if (!CompanyBooking::where('company_id', $this->company->id)->where('client_id', $client_id)->where('service_id', $service_id)->where('start_at', Carbon::parse($start_at))->where('end_at', Carbon::parse($end_at))->exists()) {
                    $booking = CompanyBooking::create([
                        'company_id' => $this->company->id,
                        // 'full_name' => $client->first_name . ' ' . $client->last_name,
                        'email' => $client->email,
                        'country_code' => null,
                        'phone_number' => $client->phone_number ?? '',
                        'address' => $address ? $address->street_address : null,
                        'city' => $address ? $address->city : null,
                        'zipcode' => $address ? $address->zipcode : null,
                        'country' => $address ? $address->country : null,
                        'state' => $address ? $address->state : null,
                        'special_request' => $special_request ?? '',
                        'start_at' => Carbon::parse($start_at),
                        'end_at' => Carbon::parse($end_at),
                        'price' => $service->price ?? 0,
                        'is_cancelled' => 0,
                        'is_verified' => 1,
                        'service_id' => $service->id,
                        'user_id' => $practitioner_id,
                        'client_id' => $client->id,
                        'first_name' => $client->first_name ?? null,
                        'last_name' => $client->last_name ?? null,
                        'time_margin' => $service->time_margin ?? 0,
                        'should_make_slot_unavailable' => 0,
                    ]);
                }
            }

            $imported_booking_data->push([
                'date' => $booking_date,
                'practitioner_name' => $practitioner_name_index ? $row[$practitioner_name_index] : null,
                'practitioner_email' => $practitioner_email_index ? $row[$practitioner_email_index] : null,
                'interval' => $row[$interval_index],
                'client_email' => isset($row[$client_email_index]) ? $row[$client_email_index] : null,
            ]);
        }

        $imported_booking_count = $imported_booking_data->count();
        $not_imported_booking_count = $not_imported_booking_data->count();
        $imported_service_count = $imported_service_names->count();
        $not_imported_service_count = $not_imported_service_names->count();
        $imported_client_count = $imported_client_emails->count();
        $not_imported_client_count = $not_imported_client_emails->count();

        $this->output = [
            'imported_booking_count' => $imported_booking_count,
            'imported_booking_data' => $imported_booking_data,
            'not_imported_booking_count' => $not_imported_booking_count,
            'not_imported_booking_data' => $not_imported_booking_data,

            'imported_service_count' => $imported_service_count,
            'imported_service_names' => $imported_service_names,
            'not_imported_service_count' => $not_imported_service_count,
            'not_imported_service_names' => $not_imported_service_names,

            'imported_client_count' => $imported_client_count,
            'imported_client_emails' => $imported_client_emails,
            'not_imported_client_count' => $not_imported_client_count,
            'not_imported_client_emails' => $not_imported_client_emails,
        ];
    }
}
