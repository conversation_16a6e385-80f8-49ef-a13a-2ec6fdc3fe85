<?php

namespace App\Imports;

use App\Client;
use App\Company;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class CustomClientImport implements WithStartRow, ToCollection
{
    public array $data;
    public Company $company;
    public User $user;
    public array $output;

    public function __construct(
        $data,
        Company $company,
        User $user
    ) {
        $this->data = $data;
        $this->company = $company;
        $this->user = $user;
        $this->output = [];
    }
    public function startRow(): int
    {
        return 2;
    }
    public function chunkSize(): int
    {
        return 500;
    }
    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        $first_name_index = $this->data['first_name_index'];
        $last_name_index = null;
        $email_index = null;
        $phone_number_index = null;
        $personal_number_index = null;
        $danish_id_index = null;

        if (isset($this->data['last_name_index'])) {
            $last_name_index = $this->data['last_name_index'];
        }

        if (isset($this->data['email_index'])) {
            $email_index = $this->data['email_index'];
        }

        if (isset($this->data['phone_number_index'])) {
            $phone_number_index = $this->data['phone_number_index'];
        }

        if (isset($this->data['personal_number_index'])) {
            $personal_number_index = $this->data['personal_number_index'];
        }

        if (isset($this->data['danish_id_index'])) {
            $danish_id_index = $this->data['danish_id_index'];
        }


        $client_to_import = collect([]);
        $already_added_clients_emails = $this->company->clients->pluck('email');
        $already_added_clients_personal_ids = $this->company->clients->pluck('personal_id');
        $already_added_clients_cpr_ids = $this->company->clients->pluck('cpr_id');

        $imported_client_emails = collect([]);
        $not_imported_client_emails = collect([]);
        foreach ($collection as $row) {


            $first_name = null;
            $last_name = null;
            $email = null;
            $phone_number = null;
            $personal_number = null;
            $danish_id = null;


            $first_name = $row[$first_name_index];
            if (! isset($first_name)) {
                // $not_imported_client_emails->push('No First name');
                continue;
            }

            $last_name = null;
            if ($last_name_index != null && isset($row[$last_name_index])) {
                $last_name = (string) $row[$last_name_index];
            }
            if (strpos($first_name, ' ') !== false) {
                $names = explode(' ', $first_name);
                $last_name = array_pop($names);
                $first_name = implode(' ', $names);
            }

            if ($email_index != null && isset($row[$email_index])) {
                $email = (string) $row[$email_index];

                // checking for email format and to be unique
                $pattern = "/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i";
                if (! preg_match($pattern, $email)) {
                    $not_imported_client_emails->push($email . ' - Invalid email');
                    continue;
                }

                // Check: Already existed client(email)
                $count = $already_added_clients_emails->contains(function ($client_email) use ($email) {
                    return strtolower($client_email) === strtolower($email);
                });

                if ($count) {
                    $not_imported_client_emails->push($email . ' - Already exists');
                    continue;
                }
            }

            if ($personal_number_index != null && isset($row[$personal_number_index])) {
                $personal_number = (string) $row[$personal_number_index];

                //checking for personal number format
                if ($personal_number) {
                    $birth_date = null;
                    $code = null;
                    if (str_contains($personal_number, '-')) {
                        $birth_date = explode('-', $personal_number)[0];
                        $code = explode('-', $personal_number)[1];
                    } else {
                        if (strlen($personal_number) == 12) {
                            $birth_date = substr($personal_number, 0, 8);
                            $code = substr($personal_number, 8, 4);
                        }
                    }
                    if ($birth_date && $code) {
                        try {
                            Carbon::createFromFormat('Ymd', $birth_date)->format('Y-m-d');
                            if (strlen($code) == 4) {
                                $personal_number = $birth_date . '-' . $code;
                            } else {
                                $personal_number = null;
                            }
                        } catch (\Throwable $th) {
                            $personal_number = null;
                        }
                    } else {
                        $personal_number = null;
                    }
                }


                // Check: Already existed client(personal_id)
                $count = $already_added_clients_personal_ids->contains(function ($client_personal_id) use ($personal_number) {
                    return strtolower($client_personal_id) === strtolower($personal_number);
                });

                if ($count) {
                    $not_imported_client_emails->push($personal_number . ' - Already exists');
                    continue;
                }
            }

            if ($danish_id_index != null && isset($row[$danish_id_index])) {
                $danish_id = (string) $row[$danish_id_index];

                // Check: Already existed client(cpr)
                $count = $already_added_clients_cpr_ids->contains(function ($client_cpr) use ($danish_id) {
                    return strtolower($client_cpr) === strtolower($danish_id);
                });

                if ($count) {
                    $not_imported_client_emails->push($danish_id . ' - Already exists');
                    continue;
                }
            }


            $phone_number = null;
            if ($phone_number_index != null) {
                $phone_number = (string) $row[$phone_number_index];
            }
            $country_code = '46';
            $personal_number = null;
            if ($personal_number_index != null) {
                $personal_number = (string) $row[$personal_number_index];
            }

            //separating the phone number and country code (if given)
            if ($phone_number && str_contains($phone_number, '(') && str_contains($phone_number, ')')) {
                $start = strpos($phone_number, '(') + 1;
                $end = strpos($phone_number, ')');
                $length = $end - $start;
                $country_code = substr($phone_number, $start, $length);
                $phone_number = explode(')', $phone_number)[1];
            }

            //separating the phone number and country code in other way (if given)
            if ($phone_number && str_contains($phone_number, '+')) {
                if ($phone_number[0] != '+') {
                    $phone_number = null;
                } else {
                    $phone_number = substr($phone_number, 1);
                }
                if ($phone_number != null) {
                    if (str_contains($phone_number, ' ')) {
                        $country_code = explode(' ', $phone_number, 2)[0];
                        $phone_number = str_replace(' ', '', explode(' ', $phone_number, 2)[1]);
                    }
                }
            }

            $already_existing_client = null;
            if ($email) {
                $already_existing_client = $client_to_import->where('email', $email)->first();
            }
            if (!$already_existing_client && $personal_number) {
                $already_existing_client = $client_to_import->where('personal_id', $personal_number)->first();
            }
            if (!$already_existing_client && $danish_id) {
                $already_existing_client = $client_to_import->where('cpr_id', $danish_id)->first();
            }
            if (!$already_existing_client) {

                if ($personal_number == 0) {
                    $personal_number = null;
                }
                if ($email != null || $personal_number != null) {
                    $client_to_import->push([
                        'user_id' => $this->user->id,
                        'company_id' => $this->company->id,
                        'profile_picture' => '',
                        'first_name' => $first_name ?? '',
                        'last_name' => $last_name ?? '',
                        'personal_id' => $personal_number ?? '',
                        'cpr_id' => $danish_id ?? '',
                        'email' => $email,
                        'phone_number' => $phone_number ?? '',
                        'country_code' => $country_code ?? '',
                        'social_security_number' => '',
                    ]);
                } else {
                    $not_imported_client_emails->push("$first_name - $last_name - $email - $personal_number - $danish_id" . ' - NO data');
                }


                $imported_client_emails->push("$email - $personal_number - $danish_id");
            } else {

                if (!$already_existing_client['personal_id'] && $personal_number != 0) {
                    $already_existing_client['personal_id'] = $personal_number;
                }


                $not_imported_client_emails->push("$email - $personal_number - $danish_id" . ' - Already exists');
            }
        }

        $created_clients = $this->company->clients()->createMany($client_to_import->toArray());
        $created_clients_ids = $created_clients->pluck('id')->toArray();

        foreach ($this->company->users as $user_in) {
            $user_in->accesses()->toggle($created_clients_ids);
        }

        $imported_client_count = $imported_client_emails->count();
        $not_imported_client_count = $not_imported_client_emails->count();

        $this->output = [
            'imported_client_count' => $imported_client_count,
            'imported_client_emails' => $imported_client_emails,
            'not_imported_client_count' => $not_imported_client_count,
            'not_imported_client_emails' => $not_imported_client_emails,
        ];
    }
}
