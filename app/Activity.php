<?php

namespace App;

use App\Models\VideoCall;
use App\Traits\Encryptable;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Activitylog\Contracts\Activity as ActivityContract;

class Activity extends Model implements ActivityContract
{
    // use Encryptable;

    public $guarded = [];

    // protected $encrypted = [
    //     'description', 'properties'
    // ];

    // protected $casts = [
    //     'properties' => 'collection',
    // ];

    public function setDescriptionAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['description'] = $value;
    }

    public function getDescriptionAttribute($value)
    {
        if ($value != null) {
            return Crypt::decrypt($value);
        }
        return $value;
    }

    public function setPropertiesAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['properties'] = $value;
    }

    public function getPropertiesAttribute($value)
    {
        try {
            if ($value != null && $value != "[]") {
                return collect(json_decode(Crypt::decrypt($value)));
            }
        } catch (\Throwable $th) {
        }
        return $value;
    }

    public function __construct(array $attributes = [])
    {
        if (!isset($this->connection)) {
            $this->setConnection(config('activitylog.database_connection'));
        }

        if (!isset($this->table)) {
            $this->setTable(config('activitylog.table_name'));
        }

        parent::__construct($attributes);
    }

    public function subject(): MorphTo
    {
        if (config('activitylog.subject_returns_soft_deleted_models')) {
            return $this->morphTo()->withTrashed();
        }

        return $this->morphTo();
    }

    public function causer(): MorphTo
    {
        return $this->morphTo();
    }

    public function getExtraProperty(string $propertyName)
    {
        return Arr::get($this->properties->toArray(), $propertyName);
    }

    public function changes(): Collection
    {
        if (!$this->properties instanceof Collection) {
            return new Collection();
        }

        return $this->properties->only(['attributes', 'old']);
    }

    public function getChangesAttribute(): Collection
    {
        return $this->changes();
    }

    public function scopeInLog(Builder $query, ...$logNames): Builder
    {
        if (is_array($logNames[0])) {
            $logNames = $logNames[0];
        }

        return $query->whereIn('log_name', $logNames);
    }

    public function scopeCausedBy(Builder $query, Model $causer): Builder
    {
        return $query
            ->where('causer_type', $causer->getMorphClass())
            ->where('causer_id', $causer->getKey());
    }

    public function scopeForSubject(Builder $query, Model $subject): Builder
    {
        return $query
            ->where('subject_type', $subject->getMorphClass())
            ->where('subject_id', $subject->getKey());
    }

    public function user()
    {
        return $this->belongsTo(User::class, "causer_id", "id");
    }

    public function sms()
    {
        return $this->hasOne(ClientSMS::class, "log_id", "id");
    }

    public function video_call()
    {
        return $this->morphOne(VideoCall::class, 'callable');
    }
}
