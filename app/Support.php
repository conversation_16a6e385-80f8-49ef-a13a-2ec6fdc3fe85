<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class Support extends Model
{
    use Encryptable, LogsActivity, GetEncryptedFile;

    protected $encrypted = [
        'email', 'subject', 'message', 'file'
    ];

    protected $fillable = [
        'email', 'subject', 'message', 'file'
    ];

    public function user()
    {
        return $this->belongsTo('App\User');
    }

    public function file()
    {
        return $this->morphOne('App\File', 'fileable')->latest();
    }

    public function getFileAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    protected static $logAttributes = [];

    protected static $logName = 'support';

    public function getDescriptionForEvent(string $eventName): string
    {
        return "support has been sent by :causer.first_name :causer.last_name";
    }
}
