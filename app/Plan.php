<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use Encryptable, GetEncryptedFile;

    protected $encrypted = [
        'name',
        'subscription_id',
        'plan_id',
        'cost',
        'description',
        'users',
        'client',
        'storage',
    ];

    protected $fillable = [
        'name',
        'subscription_id',
        'plan_id',
        'cost',
        'description',
        'users',
        'client',
        'storage',
        'cost_value',
        'is_free',
        'currency',
        'is_2022',
        'is_yearly'
    ];

    protected $appends = [
        'focused',
    ];

    protected $casts = [
        'is_free' => 'boolean',
    ];

    public function getFocusedAttribute()
    {
        return $this->name == "Medium";
    }

    public function isLicensed()
    {
        return $this->name == "Licensed";
    }

    public function isFree()
    {
        return $this->is_free;
    }

    public function isOld()
    {
        return $this->name != "Licensed";
    }
}
