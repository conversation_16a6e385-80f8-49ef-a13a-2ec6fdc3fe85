<?php

namespace App;

use App\Company;
use App\QuestionaryQuestion;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Questionary extends Model
{
    use SoftDeletes, GetEncryptedFile;

    protected $fillable = [
        'company_id', 'title', 'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function questions()
    {
        if (request()->has('orderBy')) {
            return $this->hasMany(QuestionaryQuestion::class);
        }
        return $this->hasMany(QuestionaryQuestion::class)->orderBy('order');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function datas()
    {
        return $this->morphMany(QuestionaryData::class, 'modelable');
    }

    public function data()
    {
        return $this->morphOne(QuestionaryData::class, 'modelable');
    }
}
