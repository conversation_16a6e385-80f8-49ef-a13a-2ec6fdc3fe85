<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyIncrementalValue extends Model
{
    use HasFactory;

    //TABLE
    // public $table = '';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'max_gift_code',
        'item_sequence_number',
        'viva_receipt_id',
        'fortnox_invoice_id',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
