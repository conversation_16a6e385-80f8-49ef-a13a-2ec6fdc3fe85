<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;

class SubscriptionRequireActionException extends Exception
{
    public $data = null;

    public function __construct($data = null, $message = null, Exception $previous = null)
    {
        parent::__construct(!!$message ? $message : __('strings.payment_failure_require_action_with_stripe'), 500, $previous);

        $this->data = $data;
    }

    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::critical($this);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @return \Illuminate\Http\Response
     */
    public function render()
    {
        return response()->json([
            'data' => $this->data,
            'message' => $this->getMessage(),
            'status' => '3'
        ]);
    }
}
