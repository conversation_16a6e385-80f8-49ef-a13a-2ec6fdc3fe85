<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;

class SubscriptionPaymentIncompleteException extends Exception
{
    public $data = null;

    public function __construct($data = null, Exception $previous = null)
    {
        parent::__construct(__('strings.payment_failure_with_stripe'), 500, $previous);

        $this->data = $data;
    }

    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::critical($this);
    }

    /**
     * Ren<PERSON> an exception into an HTTP response.
     *
     * @return \Illuminate\Http\Response
     */
    public function render()
    {
        return response()->json([
            'data' => $this->data,
            'message' => __('strings.payment_failure_with_stripe'),
            'status' => '2'
        ]);
    }
}
