<?php

namespace App\Exceptions;

use Exception;

class SMSFailedException extends Exception
{
    public $data = null;

    public function __construct($code, $message,  $data = null, Exception $previous = NULL)
    {
        $this->message = $message;
        $this->code = $code;
        $this->data = $data;
    }


    public function render()
    {
        return response()->json([
            'data' => $this->data,
            'message' => $this->message,
            'status' => '0',
        ], $this->code);
    }
}
