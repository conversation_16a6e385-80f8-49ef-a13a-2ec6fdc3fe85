<?php

declare(strict_types=1);

namespace App\Exceptions\Fortnox;

use Exception;
use Illuminate\Support\Facades\Log;

class FortnoxCustomerAlreadyExistsException extends Exception
{
    public function __construct(string $message = 'Client already has a Fortnox customer', int $code = 409, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::info($this);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @return \Illuminate\Http\Response
     */
    public function render()
    {
        return response()->json([
            'message' => $this->getMessage(),
            'status' => '0'
        ], $this->getCode());
    }
}
