<?php

declare(strict_types=1);

namespace App\Exceptions\Fortnox;

use Exception;
use Illuminate\Support\Facades\Log;

class FortnoxNotAuthenticatedException extends Exception
{
    public function __construct(string $message = 'Client is not authenticated with Fortnox', int $code = 401, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::warning($this);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @return \Illuminate\Http\Response
     */
    public function render()
    {
        return response()->json([
            'message' => $this->getMessage(),
            'status' => '0'
        ], $this->getCode());
    }
}
