<?php

declare(strict_types=1);

namespace App\Exceptions\Fortnox;

use Exception;
use Illuminate\Support\Facades\Log;

class FortnoxInvalidStateException extends Exception
{
    public function __construct(string $message = 'Invalid OAuth state parameter', int $code = 400, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::warning($this);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @return \Illuminate\Http\Response
     */
    public function render()
    {
        return response()->json([
            'message' => $this->getMessage(),
            'status' => '0'
        ], $this->getCode());
    }
}
