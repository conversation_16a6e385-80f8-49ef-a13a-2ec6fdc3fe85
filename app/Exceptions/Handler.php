<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;
use Illuminate\Database\QueryException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Foundation\Http\Exceptions\MaintenanceModeException;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use League\OAuth2\Server\Exception\OAuthServerException;
use Laravel\Passport\Passport;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Sentry\Laravel\Integration;

class Handler extends ExceptionHandler
{
    use ApiResponser;
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        OAuthServerException::class,
        NoValidClientSMSException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];


    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            Integration::captureUnhandledException($e);
        });
    }

    /**
     * Report or log an exception.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function report(Throwable $exception)
    {
        // You can specify which exceptions not to report here
        if ($this->shouldntReport($exception)) {
            return;
        }

        // We are getting the directory so we can filter out any vendor code,
        // along with the directory, so it looks better for the developer.
        $dir = substr(__DIR__, 0, -14);
        $backtrace =  $exception->getTraceAsString();
        $backtrace = str_replace([$dir], "", $backtrace);
        $backtrace = preg_replace('^(.*vendor.*)\n^', '', $backtrace);

        // And finally, we log the exception!
        Log::channel('slack')->critical("@here" . PHP_EOL . " " . PHP_EOL . "**Env:** " . App::environment() . PHP_EOL . "**Error** " . $exception->getMessage() . PHP_EOL . '**Line:** ' . $exception->getLine() . PHP_EOL . '**User:** ' . auth()->id() . PHP_EOL . '**File:** ' . $exception->getFile() . PHP_EOL . '**Trace:**' . PHP_EOL . $backtrace);

        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {
        if (request()->is('api/*')) {

            if ($exception instanceof InvalidSignatureException) {
                if ($this->hasUrlExpired($request) && $request->has('platform')) {
                    return $this->errorResponse(__('validation.link_has_expired_login_again'), 403);
                }
                return $this->errorResponse(__('validation.invalid_link'), 403);
            }

            if ($exception instanceof QueryException) {
                if (env("APP_ENV") == "production") {
                    return $this->errorResponse(__('validation.something_went_wrong'), 0);
                } else {
                    $errorCode = $exception->errorInfo[1];
                    if ($errorCode == 1451) {
                        return $this->errorResponse(__('validation.cannot_remove_resource'), 409);
                    }
                }
            }

            if ($exception instanceof ValidationException) {
                return $this->validationResponse($exception, $request);
            }

            if ($exception instanceof ModelNotFoundException) {
                $modelName = strtolower(trim(preg_replace('/(?<!\ )[A-Z]/', ' $0', class_basename($exception->getModel()))));

                return $this->errorResponse(__('validation.no_modal_found', ['modelName' => $modelName]), 404);
            }

            if ($exception instanceof AuthenticationException) {
                return $this->unauthenticatedResponse($request, $exception);
            }

            if ($exception instanceof AuthorizationException) {
                // return response()->json($exception);
                try {
                    $status_code = $exception->getCode();
                } catch (\Throwable $th) {
                    // throw $th;
                    $status_code = 403;
                }
                return $this->errorResponse($exception->getMessage(), $status_code);
            }

            if ($exception instanceof MethodNotAllowedHttpException) {
                return $this->errorResponse(__('validation.method_invalid'), 405);
            }

            if ($exception instanceof ThrottleRequestsException) {
                return $this->errorResponse(__('validation.too_many_attempts_try_again_later'), 405);
            }

            if ($exception instanceof NotFoundHttpException) {
                return $this->errorResponse(__('validation.url_invalid'), 403);
            }

            if ($exception instanceof HttpException) {
                return $this->errorResponse($exception->getMessage(), $exception->getStatusCode());
            }


            if ($exception instanceof BadRequestException) {
                return $this->errorResponse($exception->getMessage(), $exception->getCode());
            }

            if ($exception instanceof MaintenanceModeException) {
                return $this->errorResponse(__('validation.maintenance_work'), $exception->getStatusCode());
            }

            if ($exception instanceof SubscriptionRequirePaymentMethodException) {
                return parent::render($request, $exception);
            }

            if ($exception instanceof SubscriptionPaymentIncompleteException) {
                return parent::render($request, $exception);
            }

            if ($exception instanceof SubscriptionRequireActionException) {
                return parent::render($request, $exception);
            }

            if (config('app.debug')) {
                return parent::render($request, $exception);
                // return $this->errorResponse($exception->getMessage(), 500);
            }

            if (Str::contains($exception->getMessage(), ["file_put_contents(/var/www/html/meridiq-api/storage/framework/cache", "file_put_contents(/var/www/html/meridiq-api-test/storage/framework/cache"])) {
                return $this->errorResponse(__('validation.maintenance_work'), 500);
            }

            return $this->errorResponse($exception->getMessage(), 500);
        } else {
            return parent::render($request, $exception);
        }
    }

    protected function validationResponse(ValidationException $e, $request)
    {
        $errors = $e->validator->errors()->getMessages();
        foreach ($errors as $field => $messages) {
            $errors = $messages[0];
        }
        return $this->errorResponse($errors, 200);
    }

    protected function unauthenticatedResponse($request, AuthenticationException $exception)
    {
        $token_name = Passport::cookie();
        if (isset($request->cookie()[$token_name])) {
            if ($request->cookie()[$token_name] != 'logout') {
                return response()->json([
                    'message' => __('validation.unauthenticated'),
                    'status' => '5',
                ], 410);
            }
        }
        return $this->errorResponse(__('validation.unauthenticated'), 401);
    }

    protected function hasUrlExpired($request)
    {
        $expires = $request->query('expires');

        return $expires && now()->getTimestamp() > $expires;
    }
}
