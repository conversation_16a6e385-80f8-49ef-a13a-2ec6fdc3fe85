<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;

class StorageLimitExceed extends Exception
{
    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::debug('StorageLimitExceed.');
    }

     /**
     * Render an exception into an HTTP response.
     *
    * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Exception $exception)
    {
        return response()->json([
            'message' => 'Maximum storage limit has reached, please upgrade your plan',
            'status' => '0'
        ]);
    }
}
