<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;

class NoSubscriptionException extends Exception
{
    /**
     * Report or log an exception.
     *
     * @return void
     */
    public function report()
    {
        Log::error($this);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function render($request)
    {
        return response()->json([
            'message' => __('strings.please_subscribe'),
            'status' => '2'
        ]);
    }
}
