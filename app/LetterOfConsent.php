<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LetterOfConsent extends Model
{
    use Encryptable, SoftDeletes, LogsActivity, GetEncryptedFile;

    protected $encrypted = [
        'consent_title', 'letter', 'version'
    ];

    protected $fillable = [
        'consent_title', 'company_id', 'letter', 'is_publish_before_after_pictures', 'version', 'letter_json', 'letter_html'
    ];

    protected $casts = [
        'letter_json' => 'object',
    ];

    protected static $logAttributes = [];

    protected static $logName = 'letter of consent';

    public function getDescriptionForEvent(string $eventName): string
    {
        return ":subject.consent_title letter of consent has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public function company()
    {
        return $this->belongsTo('App\Company', 'company_id', 'id');
    }
}
