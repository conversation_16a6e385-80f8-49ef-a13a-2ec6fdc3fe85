<?php

namespace App;

use App\Casts\EncryptedOAuthAuthenticatorCast;
use App\Models\Cashier\SubscriptionBuilder;
use App\EmailTemplate;
use App\Models\CompanyExtraField;
use App\Models\VideoCall;
use App\Models\CompanyCampaignSMS;
use App\Models\CompanyLegalDocument;
use App\Models\CompanyDocument;
use App\Models\MedicalDevice;
use App\Traits\Encryptable;
use App\Traits\FreeTrailManager;
use App\Traits\GetEncryptedFile;
use App\Traits\HasFortnox;
use App\Traits\HasSubscription;
use App\Traits\LogsActivity;
use App\Traits\SaveFile;
use App\Traits\TimeZoneManager;
use Carbon\Carbon;
use Exception;
use Laravel\Cashier\Billable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

// POS
use App\Traits\HasBatch;
use App\Traits\HasCampaign;
use App\Traits\POS\HasPOSReport;
use App\Traits\POS\HasVivaWallet;
use Cknow\Money\Money;
use App\Traits\POS\HasCCU;
use Laravel\Scout\Searchable;
use Stripe\Customer as StripeCustomer;
use Laravel\Cashier\PaymentMethod;

class Company extends Model
{
    use Encryptable;
    use Billable;
    use LogsActivity;
    use HasFactory;
    use GetEncryptedFile;
    use HasVivaWallet;
    use TimeZoneManager;
    use HasPOSReport;
    use HasCCU;
    use SaveFile;
    use HasBatch;
    use HasSubscription;
    use HasCampaign;
    use HasFortnox;
    use Searchable;

    public $asYouType = true;

    const COMPANY_VERIFIED = 'verified';
    const COMPANY_REJECTED = 'rejected';

    protected $encrypted = [
        'company_name',
        'first_name',
        'last_name',
        'profile_photo',
        'mobile_number',
        'street_address',
        'zip_code',
        'city',
        'state',
        'country',
        'unit',
        'theme',
        'cover_image',
        'country_code',
        'organization_number',
        'ccu_register_id',
        'viva_account_id',
        'viva_merchant_id',
        'isv_percentage',
    ];

    protected $fillable = [
        'company_name',
        'first_name',
        'last_name',
        'date_of_birth',
        'profile_photo',
        'language_id',
        'email',
        'mobile_number',
        'street_address',
        'zip_code',
        'city',
        'state',
        'country',
        'email_verified_at',
        'password',
        'unit',
        'is_blocked',
        'payment_failed_attempts',
        'is_read_only',
        'theme',
        'cover_image',
        'country_code',
        'is_black_text',
        'is_booking_on',
        'is_record_on',
        'is_pos_on',
        'timezone',
        'is_free_trail_finished',
        'free_trail_start_date',
        'free_trail_end_date',
        'viva_account_id',
        'viva_merchant_id',
        'user_count',
        'is_free_trail_cancelled',
        'free_trail_coupon',
        'free_trail_plan_id',
        'free_trail_quantity',
        'free_trail_type',
        'pos_counter',
        'organization_number',
        'ccu_enrollment_status',
        'ccu_register_id',
        'verification',
        'verified_for_sms',
        'is_management_on',
        'is_free_trail_booking_finished',
        'fortnox_auth',
    ];

    protected $hidden = [
        'password',
        'subscriptions',
        'fortnox_auth',
    ];

    protected $appends = [
        'encrypted_id',
        'storage_usage',
        'connected_to_fortnox',
        // 'is_subscribed',
        // 'is_cancelled',
        // 'active_subscription',
        // 'record_plan',
        // 'pos_plan',
        // 'has_system',

        // 'last_login_at'
        // 'space_occupied'
    ];

    protected $casts = [
        'is_free_trail_cancelled' => 'boolean',
        'is_record_on' => 'boolean',
        'is_booking_on' => 'boolean',
        'is_pos_on' => 'boolean',
        'is_management_on' => 'boolean',
        'is_blocked' => 'boolean',
        'is_read_only' => 'boolean',
        'ccu_enrollment_status' => 'boolean',
        'fortnox_auth' => EncryptedOAuthAuthenticatorCast::class,
    ];

    protected static $logAttributes = [];

    protected static $logName = 'company';

    public function getDescriptionForEvent(string $eventName): string
    {
        return ":subject.company_name company has been {$eventName} by :causer.first_name :causer.last_name";
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $doctors = $this->doctors()->where('user_companies.invite_status', UserCompany::ACCEPTED)->get();

        return [
            'id' => $this->id,
            'company_name' => $this->company_name,
            'email' => $this->email,
            'mobile_number' => $this->mobile_number,
            'user_emails' => $this->all_users->pluck('email')->values()->join(' '),
            'user_names' => $this->all_users->map->fullName()->values()->join(' '),
            'doctors_names' => $doctors->map->fullName()->values()->join(' '),
            'doctors_emails' => $doctors->map->email->values()->join(' '),
        ];
    }

    public function users()
    {
        return $this->hasMany('App\User')->where('is_active', 1);
    }

    public function super_user()
    {
        return $this->hasOne('App\User')->where('is_active', 1)->whereColumn('email', 'users.email');
    }

    public function extra_field()
    {
        return $this->hasOne(CompanyExtraField::class);
    }

    public function all_users()
    {
        return $this->hasMany('App\User');
    }

    public function campaign_sms()
    {
        return $this->hasMany(CompanyCampaignSMS::class);
    }

    public function legal_documents()
    {
        return $this->hasMany(CompanyLegalDocument::class);
    }

    public function doctors()
    {
        //TODO::add scope to only select user's main column(plucked)
        return $this->belongsToMany(User::class, UserCompany::class)
            ->withPivot('price', 'invite_status');
    }

    public function platforms()
    {
        return $this->hasMany(CompanyPlatform::class);
    }

    public function hasPrescriptionPlatform()
    {
        return $this->platforms()->where('platform', CompanyPlatform::PRESCRIPTION)->exists();
    }

    public function upcoming_platform_fees()
    {
        return $this->hasMany(UpcomingPlatformFees::class);
    }

    public function medical_devices()
    {
        return $this->hasMany(MedicalDevice::class);
    }

    public function documents()
    {
        return $this->hasMany(CompanyDocument::class);
    }

    public function taxRates()
    {
        return [];
    }

    public function unitCurrency()
    {
        return Cache::remember("country-{$this->id}-{$this->unit}", 600, function () {
            if ($this->unit == 'usd') {
                return '$';
            }
            if ($this->unit == 'eur') {
                return '€';
            }
            if ($this->unit == 'sek') {
                return 'kr';
            }
            if ($this->unit == 'gbp') {
                return '£';
            }
            return $this->unit;
        });
    }

    public function withCurrency($value, $unit = null)
    {
        return (new Money($value, $unit ?? $this->unit, true))->format(app()->getLocale());
    }

    public function verified()
    {
        if ($this->verification == self::COMPANY_VERIFIED) {
            return true;
        }

        return false;
    }

    public function isFreeTrailActive()
    {
        return !$this->is_free_trail_finished;
    }

    public function isFreeTrailUsed()
    {
        if ($this->is_free_trail_finished) return true;

        if (!$this->free_trail_end_date) return false;

        return now()->format('Y-m-d') > $this->free_trail_end_date;
    }

    public function isFreeTrailExpired()
    {
        if (!$this->free_trail_end_date) return false;

        return now()->format('Y-m-d') > $this->free_trail_end_date;
    }

    public function getIsFreeTrailFinishedAttribute($value)
    {
        if ($value) return $value;

        if ($this->isFreeTrailExpired()) return true;

        return false;
    }

    public function getIsvPercentageAttribute($value)
    {
        if ($value) return $value;

        return encrypt(config('viva_wallet.isv_percentage') * 100);
    }

    public function isvPercentageToViva()
    {
        return $this->isv_percentage / 100;
    }

    // public function taxPercentage()
    // {
    //     return 25;
    // }

    public function products()
    {
        return $this->hasMany(CompanyProduct::class);
    }

    public function product_categories()
    {
        return $this->hasMany(CompanyProductCategory::class);
    }

    public function receipts()
    {
        return $this->hasMany(CompanyReceipt::class);
    }

    public function receipt_items()
    {
        return $this->hasManyThrough(CompanyReceiptItem::class, CompanyReceipt::class, 'company_id', 'receipt_id');
    }

    public function gift_cards()
    {
        return $this->hasMany(CompanyGiftCard::class);
    }

    public function terminals()
    {
        return $this->hasMany(CompanyTerminal::class);
    }

    public function treatments()
    {
        return $this->hasMany('App\Treatment');
    }

    public function coupons()
    {
        return $this->hasMany(CompanyCoupon::class);
    }

    public function letterOfConsents()
    {
        return $this->hasMany('App\LetterOfConsent');
    }

    public function clients()
    {
        return $this->hasMany('App\Client');
    }
    public function company_services()
    {
        return $this->hasMany(CompanyService::class);
    }
    public function company_categories()
    {
        return $this->hasMany(CompanyCategory::class);
    }

    public function procedures()
    {
        return $this->hasManyThrough(ClientTreatment::class, Client::class);
    }
    public function prescriptions()
    {
        return $this->hasManyThrough(ClientPrescription::class, Client::class);
    }
    public function companyBills()
    {
        return $this->hasMany(UserCompanyBilling::class);
    }

    public function video_calls()
    {
        return $this->hasMany(VideoCall::class);
    }

    public function devices()
    {
        return $this->hasManyThrough(UserDevice::class, User::class);
    }

    public function file()
    {
        return $this->morphOne('App\File', 'fileable')->latest();
    }

    function incremental_value()
    {
        return $this->hasOne(CompanyIncrementalValue::class);
    }

    function z_report()
    {
        return $this->hasOne(CompanyZReport::class);
    }

    public function getConnectedToFortnoxAttribute()
    {
        return !!$this->fortnox_auth;
    }

    public function getProfilePhotoAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    public function getCoverImageAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    public function files()
    {
        return $this->hasMany('App\File');
        // return $this->hasManyThrough('App\File','App\User');
    }

    // public function getSpaceOccupiedAttribute()
    // {
    //     // return false;
    //     return ($this->files()->sum('size') / 1000000000);
    // }

    public function bookings()
    {
        return $this->hasMany(CompanyBooking::class)->orderBy('created_at', 'desc');
    }

    public function templates()
    {
        return $this->hasMany('App\Template');
    }

    public function sms_templates()
    {
        return $this->hasMany(SMSTemplate::class);
    }
    public function email_templates()
    {
        return $this->hasMany(EmailTemplate::class);
    }

    /**
     * Create an invoice view Response.
     *
     * @param  string  $id
     * @param  array  $data
     * @param  string  $filename
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function viewInvoice($id, array $data)
    {
        $invoice = $this->findInvoiceOrFail($id);

        return $invoice->view($data);
    }

    public function settings()
    {
        return $this->hasMany('App\Setting');
    }

    public function client_sms()
    {
        return $this->hasMany(ClientSMS::class, 'company_id')->whereNotNull('statuses');
    }

    public function lead()
    {
        return $this->hasOne(CompanyLead::class);
    }

    public function questionaries()
    {
        if (request()->has('orderBy')) {
            return $this->hasMany(Questionary::class);
        }
        return $this->hasMany(Questionary::class)->latest();
    }

    public function client_extra_fields()
    {
        return $this->hasMany(CompanyClientExtraField::class);
    }

    public function getEncryptedIdAttribute()
    {
        return \base64_encode($this->id);
    }

    public function getStorageUsageAttribute()
    {
        $value = Cache::rememberForever("storage-usage-{$this->id}", function () {
            $value = round($this->files()->sum('size') / 1000000);

            if ($value > 1024) {
                $value = round($value / 1000, 1) . ' GB';
            } else {
                $value = round($value, 1) . ' MB';
            }

            return $value;
        });

        return $value;
    }

    public function getCompanyDeleteDateAttribute()
    {
        $start_date = Carbon::parse(env("COMPANY_DELETE_START_DATE"));

        if (Carbon::parse($this->created_at)->lte($start_date)) {
            return $start_date->addDays(env("COMPANY_DELETE_DAYS"))->format('Y-m-d');
        }

        return $this->created_at->addDays(env("COMPANY_DELETE_DAYS"))->format('Y-m-d');
    }

    public static function decryptId($base64)
    {
        try {
            if (base64_encode(base64_decode($base64, true)) === $base64) {
                return base64_decode($base64, true);
            }
        } catch (\Throwable $th) {
            throw new \Exception(__('strings.Please_provide_valid_url'));
        }
    }

    public function invoicePay($id)
    {
        $invoice = $this->findInvoiceOrFail($id);

        return $invoice->pay();
    }

    public static function setUpSettings(Company $company)
    {
        foreach (Setting::BOOKING_SETTINGS as $key) {
            Setting::getSetting($company, $key);
        }
    }

    public static function updateFreeTrail(Company $company)
    {
        if (!$company->is_free_trail_finished) {
            if ($company->free_trail_start_date && $company->free_trail_end_date) {
                if (
                    Carbon::parse($company->free_trail_end_date)->lessThan(Carbon::now())
                    &&
                    $company->is_record_on
                ) {
                    $company->is_record_on = 0;
                    $company->save();
                }
            }
        }

        return true;
    }

    public static function canBookForThisTime(Company $company, Carbon $time, $time_zone = null, $is_manual = false)
    {
        if (!$time_zone) {
            $time_zone = $company->timezone;
        }
        $now = TimeZoneManager::getTimeZoneCarbonNow($time_zone);
        if ($now->greaterThan($time)) {
            throw new Exception(__('strings.can_not_book_for_past'));
        }
        $minimum_lead_time_setting = Setting::getSetting($company, Setting::MINIMUM_LEAD_TIME);
        $maximum_lead_time_setting = Setting::getSetting($company, Setting::MAXIMUM_LEAD_TIME);
        $minimum_lead_time = $minimum_lead_time_setting->value;
        $maximum_lead_time = $maximum_lead_time_setting->value;

        $diff_in_minutes = $now->diffInMinutes($time);

        if ($diff_in_minutes > $maximum_lead_time) {
            if (!$is_manual) {
                throw new Exception(__('strings.can_not_book_for_this_time'));
            }
        }
        if ($diff_in_minutes < $minimum_lead_time) {
            if (!$is_manual) {
                throw new Exception(__('strings.can_not_book_for_this_time'));
            }
        }

        return true;
    }

    function address()
    {
        return collect([$this->street_address, $this->city, $this->state, $this->zip_code, $this->country])->filter()->join(', ');
    }

    public function phone()
    {
        if (!$this->mobile_number) return null;

        return "+{$this->country_code}{$this->mobile_number}";
    }

    public function sms_credits()
    {
        return $this->hasOne(CompanySMSCredits::class)->withDefault([
            "credits" => 0,
            "auto_pay" => 0,
            "auto_pay_sms_counts" => 0,
        ]);
    }

    public function getTaxRates(): array
    {
        $country = $this->country;

        return config("countries.$country.vat.percentage", [0]);
    }

    /**
     * Begin creating a new subscription.
     *
     * @param  string  $name
     * @param  string|string[]  $plans
     * @return \Laravel\Cashier\SubscriptionBuilder
     */
    public function newSubscription($name, $plans)
    {
        return new SubscriptionBuilder($this, $name, $plans);
    }

    /**
     * Get the default payment method for the customer.
     *
     * @return \Laravel\Cashier\PaymentMethod|\Stripe\Card|\Stripe\BankAccount|null
     */
    public function defaultPaymentMethod()
    {
        if (! $this->hasStripeId()) {
            return;
        }

        $customer = StripeCustomer::retrieve([
            'id' => $this->stripe_id,
            'expand' => [
                'invoice_settings.default_payment_method',
                'default_source',
            ],
        ], $this->stripeOptions());

        if ($customer->invoice_settings?->default_payment_method) {
            return new PaymentMethod($this, $customer->invoice_settings->default_payment_method);
        }

        // If we can't find a payment method, try to return a legacy source...
        return $customer->default_source;
    }
}
