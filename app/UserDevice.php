<?php

namespace App;

use App\Traits\GetEncryptedFile;
use App\User;
use Illuminate\Database\Eloquent\Model;

class UserDevice extends Model
{
    use GetEncryptedFile;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'token',
        'expire_at',
        'otp',
        'verified',
        'otp_expire_at',
        'ip',
        'user_agent',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'otp_expire_at' => 'datetime',
    ];

    public function isVerified()
    {
        return !!$this->verified;
    }

    public function setVerified($bool = true)
    {
        $this->verified = true;
        return true;
    }

    public function isOtpValid($otp)
    {
        return now() < $this->otp_expire_at && $this->otp == $otp;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
