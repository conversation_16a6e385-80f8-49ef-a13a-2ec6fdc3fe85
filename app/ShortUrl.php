<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShortUrl extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'short_urls';

    //FILLABLES
    protected $fillable = [
        'key',
        'original_url',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public static function shorten($originalUrl)
    {
        do {
            $key = ShortUrl::generateKey();
        } while (ShortUrl::query()->where('key', $key)->first() !== null);

        $url = ShortUrl::firstOrCreate([
            'original_url' => $originalUrl,
        ], [
            'key' => $key,
        ]);

        $key = $url->key;
        $front_url = config('sms.SHORT_URL_PREFIX');

        return $front_url . $url->key;
    }

    public static function sample_short_url()
    {
        $front_url = config('sms.SHORT_URL_PREFIX');

        return $front_url . ShortUrl::generateKey();
    }

    public static function generateKey($length = 6)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $shortUrl = '';

        for ($i = 0; $i < $length; $i++) {
            $shortUrl .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $shortUrl;
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
