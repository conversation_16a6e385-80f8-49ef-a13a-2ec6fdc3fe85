<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyServiceQuestionary extends Model
{
    use HasFactory;

    const CUSTOM = "CUSTOM", HEALTH_QUESTIONNAIRES = "HEALTH_QUESTIONNAIRES", AESTHETIC_INTEREST = "AESTHETIC_INTEREST", COVID_19 = "COVID_19";

    //TABLE
    public $table = 'company_service_questionaries';

    //FILLABLES
    protected $fillable = [
        'company_service_id',
        'questionary_id',
        'type',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [
        'questionary'
    ];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function questionary()
    {
        return $this->belongsTo(Questionary::class, 'questionary_id', 'id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
