<?php

namespace App\Manager;

use App\Company;
use App\CompanyPlatform;
use App\User;
use Carbon\Carbon;
use Laravel\Cashier\Subscription;
use Stripe\Exception\ApiErrorException;

class SubscriptionManager
{
    protected Company $company;

    protected ?Subscription $existingMonthlySubscription = null;
    protected ?Subscription $existingYearlySubscription = null;

    // eg: ['platform' => 'POS_SYSTEM', 'price' => 'price_1J4J9vLbJ9J9J9J9', 'quantity' => '1']
    protected array $existingMonthlyPlans = [];
    protected array $existingYearlyPlans = [];

    protected bool $existingPOSPlan = false;

    // eg: ['platform' => 'POS_SYSTEM', 'price' => 'price_1J4J9vLbJ9J9J9J9', 'quantity' => '1']
    protected array $monthlyPlans = [];
    protected array $yearlyPlans = [];

    protected array $monthlyRemovePlans = [];
    protected array $yearlyRemovePlans = [];

    protected array $monthly_invoice_items = [];
    protected array $yearly_invoice_items = [];

    protected $coupon = null;

    /**
     * Create a new SubscriptionManager instance.
     *
     * @param Company $company The company context for subscription management
     */
    public function __construct(Company $company)
    {
        $this->company = $company;

        $this->existingMonthlySubscription = $this->company->activeSubscription(false);
        $this->existingYearlySubscription = $this->company->activeSubscription(true);

        $this->existingMonthlyPlans = $this->existingMonthlySubscription?->items->map(fn($item) => ([
            'platform' => $item->plan->platform,
            'stripe_price' => $item->stripe_plan,
            'quantity' => $item->quantity,
        ]))->values()->toArray() ?? [];
        $this->monthlyRemovePlans = $this->existingMonthlyPlans;

        $this->existingYearlyPlans = $this->existingYearlySubscription?->items->map(fn($item) => ([
            'platform' => $item->plan->platform,
            'stripe_price' => $item->stripe_plan,
            'quantity' => $item->quantity,
        ]))->values()->toArray() ?? [];
        $this->yearlyRemovePlans = $this->existingYearlyPlans;

        $this->existingPOSPlan = $this->existingMonthlySubscription?->items->where('platform', CompanyPlatform::POS_SYSTEM)->first()
            || $this->existingYearlySubscription?->items->where('platform', CompanyPlatform::POS_SYSTEM)->first();
    }

    /**
     * Add a subscription plan.
     *
     * @param object $planData
     * @param int $quantity
     * @return $this
     */
    public function addPlan($planData, $quantity = 1)
    {
        // if plan platform already exists in yearly, remove it and add the new one
        $this->yearlyPlans = array_filter($this->yearlyPlans, fn($plan) => $plan['platform'] !== $planData->platform);

        // if plan platform already exists in monthly, remove it
        $this->monthlyPlans = array_filter($this->monthlyPlans, fn($plan) => $plan['platform'] !== $planData->platform);

        // if yearly plan's platform cancelled is remove from it
        $this->yearlyRemovePlans = array_filter($this->yearlyRemovePlans, fn($plan) => $plan['platform'] !== $planData->platform);

        // if monthly plan's platform cancelled is remove from it
        $this->monthlyRemovePlans = array_filter($this->monthlyRemovePlans, fn($plan) => $plan['platform'] !== $planData->platform);

        $item = null;

        if ($planData->is_yearly) {
            // add the new yearly plan
            $this->yearlyPlans[] = [
                'platform' => $planData->platform,
                'stripe_price' => $planData->stripe_id,
                'quantity' => $quantity,
            ];

            $item = $this?->existingYearlySubscription?->items->where('plan.platform', $planData->platform)->first();
        } else {
            // add the new monthly plan
            $this->monthlyPlans[] = [
                'platform' => $planData->platform,
                'stripe_price' => $planData->stripe_id,
                'quantity' => $quantity,
            ];

            $item = $this?->existingMonthlySubscription?->items->where('plan.platform', $planData->platform)->first();
        }

        if ($item) {
            $item->cancel_at = null;
            $item->save();
        }

        return $this;
    }

    // add invoice items
    public function addInvoiceItems($invoice_items, $is_yearly = false)
    {
        if ($is_yearly) {
            $this->yearly_invoice_items[] = $invoice_items;
        } else {
            $this->monthly_invoice_items[] = $invoice_items;
        }

        return $this;
    }

    // add coupon
    public function addCoupon($coupon)
    {
        $this->coupon = $coupon;

        return $this;
    }

    /**
     * @return object {subscriptions: Subscription[], cancelledItems: SubscriptionItem[]}
     * @throws ApiErrorException
     */
    public function subscribe($metadata = [])
    {
        $subscriptions = [];
        $cancelledItems = [];

        // print_r($this->monthlyRemovePlans);
        // print_r($this->yearlyRemovePlans);

        // print_r($this->monthlyPlans);
        // print_r($this->yearlyPlans);
        // return;

        if (count($this->monthlyRemovePlans)) {
            $cancelledItems = array_merge($cancelledItems, $this->cancelPlan($this->existingMonthlySubscription, $this->monthlyRemovePlans));
        }

        if (count($this->yearlyRemovePlans)) {
            $cancelledItems = array_merge($cancelledItems, $this->cancelPlan($this->existingYearlySubscription, $this->yearlyRemovePlans));
        }

        if (count($this->existingMonthlyPlans) && !(count($this->monthlyPlans) + count($this->monthlyRemovePlans))) {
            $subscriptions[] = $this->deleteSubscription($this->existingMonthlySubscription);
            $this->existingMonthlySubscription = null;
        }

        if (count($this->existingYearlyPlans) && !(count($this->yearlyPlans) + count($this->yearlyRemovePlans))) {
            $subscriptions[] = $this->deleteSubscription($this->existingYearlySubscription);
            $this->existingYearlySubscription = null;
        }

        if ($this->existingMonthlySubscription && (count($this->monthlyPlans) || count($this->monthlyRemovePlans))) {
            $subscriptions[] = $this->updateSubscription($this->existingMonthlySubscription, array_merge($this->monthlyRemovePlans, $this->monthlyPlans), $metadata, $this->monthly_invoice_items);
        }

        if ($this->existingYearlySubscription && (count($this->yearlyPlans) || count($this->yearlyRemovePlans))) {
            $subscriptions[] = $this->updateSubscription($this->existingYearlySubscription, array_merge($this->yearlyRemovePlans, $this->yearlyPlans), $metadata, $this->yearly_invoice_items);
        }

        if (!$this->existingMonthlySubscription && (count($this->monthlyPlans) || count($this->monthlyRemovePlans))) {
            $subscriptions[] = $this->createSubscription("Monthly Subscription", array_merge($this->monthlyRemovePlans, $this->monthlyPlans), $metadata, $this->monthly_invoice_items);
        }

        if (!$this->existingYearlySubscription && (count($this->yearlyPlans) || count($this->yearlyRemovePlans))) {
            $subscriptions[] = $this->createSubscription("Yearly Subscription", array_merge($this->yearlyRemovePlans, $this->yearlyPlans), $metadata, $this->yearly_invoice_items);
        }

        return (object) [
            'subscriptions' => $subscriptions,
            'cancelledItems' => $cancelledItems,
        ];
    }

    private function deleteSubscription(Subscription $subscription)
    {
        try {
            return $subscription->cancelNow();
        } catch (\Throwable $th) {
            $subscription->markAsCancelled();
            report($th);
            //throw $th;
        }
    }

    private function cancelPlan(Subscription $subscription, $plans)
    {
        $items = $subscription->items;

        $stripeSubscription = $subscription->asStripeSubscription();

        $cancelledItems = [];

        foreach ($items as $key => $item) {
            if (in_array($item->plan->platform, array_column($plans, 'platform'))) {
                $item->cancel_at = Carbon::createFromTimestamp($stripeSubscription->current_period_end)->subMinutes(10);

                if ($item->plan->platform == CompanyPlatform::POS_SYSTEM) {
                    $item->cancel_at = Carbon::createFromTimestamp($stripeSubscription->billing_cycle_anchor)->addYear()->subMinutes(10);
                }

                $cancelledItems[] = $item;
            } else {
                $item->cancel_at = null;
            }

            if ($item->isDirty()) {
                $item->save();
            }
        }

        return $cancelledItems;
    }

    private function createSubscription($name, $plans = [], $metadata = [], $invoice_items = [])
    {
        $prices = array_map(fn($plan) => $plan['stripe_price'], $plans);
        $subscriptionBuilder = $this->company->newSubscription($name, $prices);

        foreach ($plans as $key => $plan) {
            $subscriptionBuilder = $subscriptionBuilder->quantity($plan['quantity'], $plan['stripe_price']);
        }

        if ($this->coupon) {
            $subscriptionBuilder = $subscriptionBuilder->withCoupon($this->coupon);
        }

        return $subscriptionBuilder->create(null, [], [
            'metadata' => $metadata,
            'default_tax_rates' => $this->company->subscriptionTaxRates(),
            "add_invoice_items" => $invoice_items,
        ]);
    }

    private function updateSubscription(Subscription $subscription, $plans = [], $metadata = [], $invoice_items = [])
    {
        $subscriptionBuilder = $subscription->alwaysInvoice();

        // if plans has POS_SYSTEM platform, anchor billing cycle on
        if (in_array(CompanyPlatform::POS_SYSTEM, array_column($plans, 'platform')) && !$this->existingPOSPlan) {
            $subscriptionBuilder = $subscriptionBuilder->anchorBillingCycleOn();
        }

        $subscriptionBuilder = $subscriptionBuilder;

        $prices = [];

        foreach ($plans as $key => $plan) {
            $prices[$plan['stripe_price']] = ['quantity' => $plan['quantity']];
        }

        return $subscriptionBuilder->swap($prices, [
            'coupon' => $this->coupon,
            "add_invoice_items" => $invoice_items,
        ]);
    }
}
