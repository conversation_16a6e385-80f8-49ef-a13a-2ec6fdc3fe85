<?php

namespace App\Models;

use App\Company;
use App\Contracts\Services\PDF\Company\CompanyPDFServiceInterface;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class CompanyLegalDocument extends Model
{
    use HasFactory, GetEncryptedFile;
    use SaveFile;

    //TABLE
    public $table = 'company_legal_documents';

    const DPA = "DPA";
    const PUBLIC = "PUBLIC";
    const SUPPLIER = "SUPPLIER";

    //FILLABLES
    protected $fillable = [
        'company_id',
        'type',
        'language',
        'signed_at',
        'title',
        'description',
        'file_path',
        'created_at'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [
        'url'
    ];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    //ATTRIBUTES
    public function getUrlAttribute($value)
    {
        if ($this->file_path) {
            return $this->getS3SignedUrl($this->file_path);
        }
        return $this->file_path;
    }

    // public function getCreatedAtAttribute()
    // {
    //     return $this->company->created_at;
    // }

    public function generateLegalDocument(Company $company)
    {
        $pdfService = app(CompanyPDFServiceInterface::class);

        $user = $company->users->first();
        $language_to_use = $company->country == 'Sweden' ? 'sv' : 'en';


        $filename = $this->generateFilePath('companies/' . md5($company->id) . '/legal_documents', $user, null, 'pdf');
        $file = $pdfService->legal_document($company, CompanyLegalDocument::PUBLIC)->saveFile($company->users->first(), $filename);
        $legalDocument = CompanyLegalDocument::updateOrCreate([
            'company_id' => $company->id,
            'type' => CompanyLegalDocument::PUBLIC,
        ], [
            'title' => $language_to_use == 'en' ? "PUBLIC AGREEMENT" : "PUB-AVTAL",
            'language' => $language_to_use,
            'file_path' => $file->filename,
            'created_at' => $company->created_at,
        ]);
        $legalDocument->created_at = $legalDocument->company->created_at;
        $legalDocument->save();


        $filename = $this->generateFilePath('companies/' . md5($company->id) . '/legal_documents', $user, null, 'pdf');
        $file = $pdfService->legal_document($company, CompanyLegalDocument::DPA)->saveFile($company->users->first(), $filename);
        $legalDocument = CompanyLegalDocument::updateOrCreate([
            'company_id' => $company->id,
            'type' => CompanyLegalDocument::DPA,
        ], [
            'title' => $language_to_use == 'en' ? "DPA AGREEMENT" : "DPA-AVTAL",
            'language' => $language_to_use,
            'file_path' => $file->filename,
            'created_at' => $company->created_at,
        ]);
        $legalDocument->created_at = $legalDocument->company->created_at;
        $legalDocument->save();

        $filename = $this->generateFilePath('companies/' . md5($company->id) . '/legal_documents', $user, null, 'pdf');
        $file = $pdfService->legal_document($company, CompanyLegalDocument::SUPPLIER)->saveFile($company->users->first(), $filename);
        $legalDocument = CompanyLegalDocument::updateOrCreate([
            'company_id' => $company->id,
            'type' => CompanyLegalDocument::SUPPLIER,
        ], [
            'title' => $language_to_use == 'en' ? "SUPPLIER AGREEMENT" : "LEVERANTÖRSAVTAL",
            'language' => $language_to_use,
            'file_path' => $file->filename,
            'created_at' => $company->created_at,
        ]);
        $legalDocument->created_at = $legalDocument->company->created_at;
        $legalDocument->save();

        return $legalDocument->refresh();
    }
}