<?php

namespace App\Models;

use App\Company;
use App\Http\Integrations\GooglePlaces\PlacesNew\GooglePlaces;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class CompanyExtraField extends Model
{
    use HasFactory;


    //FILLABLES
    protected $fillable = [
        "company_id",

        "place_id",
        "rating",
        "reviews",
        "user_rating_count",
        "reviews",
        "google_maps_links",

        "opening_hours",
        "title",
        "sub_text",
        "mode_of_payment",
        "about",
        "images",
        "social_links",
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'reviews' => 'array',
        'opening_hours' => 'array',
        'google_maps_links' => 'array',
        'social_links' => 'array',
        'images' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}

    function updateGoogleRating($session = null)
    {
        $session = $session ?? Str::uuid()->toString();

        $data = Cache::remember("google_places_" . $this->place_id, 86400, function () use ($session) {
            $response = GooglePlaces::make()->placeDetails($this->place_id, [
                "name",
                "id",
                "rating",
                "reviews",
                "userRatingCount",
                "googleMapsLinks",
            ], [
                'sessionToken' => $session,
            ]);

            return $response->json();
        });

        $this->place_id = isset($data['id']) ? $data['id'] : null;
        $this->rating =  isset($data['rating']) ? $data['rating'] : null;
        $this->reviews =  isset($data['reviews']) ? $data['reviews'] : null;
        $this->user_rating_count = isset($data['userRatingCount']) ? $data['userRatingCount'] : null;
        $this->google_maps_links = isset($data['googleMapsLinks']) ? $data['googleMapsLinks'] : null;

        $this->save();
    }
}
