<?php

namespace App\Models;

use App\Traits\Encryptable;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class FortnoxInvoiceItem extends Model
{
    use HasFactory, Encryptable;

    protected $fillable = [
        'fortnox_invoice_id',
        'description',
        'price',
        'quantity',
        'unit',
        'vat',
        'total',
        'user_id',
        'discount_type',
        'discount_value',
        'invoiceable_type',
        'invoiceable_id',
    ];

    protected $encrypted = [
        'description',
        'price',
        'quantity',
        'unit',
        'vat',
        'total',
        'discount_type',
        'discount_value',
    ];

    protected $casts = [];

    /**
     * Get the invoice that owns the item
     */
    public function fortnoxInvoice(): BelongsTo
    {
        return $this->belongsTo(FortnoxInvoice::class);
    }

    /**
     * Get the user that created the item
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the invoiceable model (product or service)
     */
    public function invoiceable(): MorphTo
    {
        return $this->morphTo();
    }
}
