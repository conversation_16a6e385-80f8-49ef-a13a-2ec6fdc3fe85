<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoCallMember extends Model
{
    use HasFactory;

    //FILLABLES
    protected $fillable = [
        'video_call_id',
        'memberable_type',
        'memberable_id',
        'role',
        'not_before',
        'expires',
        'eject_at_room_exp',
        'is_owner',
        'user_name',
        'user_id',
        'key',
        'token',
        'started_at',
        'ended_at',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [
        'url'
    ];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'is_owner' => 'boolean',
        'eject_at_room_exp' => 'boolean',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function video_call()
    {
        return $this->belongsTo(VideoCall::class);
    }

    public function memberable()
    {
        return $this->morphTo();
    }

    //ATTRIBUTES
    public function getUrlAttribute()
    {
        return config('dailyco.domain') . "/$this->key" . "?t=$this->token";
    }
}
