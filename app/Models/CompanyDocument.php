<?php

namespace App\Models;

use App\Company;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyDocument extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'modelable_type',
        'modelable_id',
        'title',
        'process',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function versions()
    {
        return $this->hasMany(CompanyDocumentData::class);
    }

    public function version()
    {
        return $this->hasOne(CompanyDocumentData::class)->latest();
    }

    public function signed_by()
    {
        return $this->hasOneThrough(
            User::class,
            CompanyDocumentData::class,
            'company_document_id',
            'id',
            'id',
            'signed_by_id'
        )->orderByDesc('company_document_data.version')->without('company')->select('users.id', 'users.first_name', 'users.last_name', 'users.email', 'users.created_at');
    }

    public function modelable()
    {
        return $this->morphTo();
    }

    function scopeAddVersion($query)
    {
        return $query->addSelect([
            'version_count' => CompanyDocumentData::select('version')
                ->whereColumn('company_document_id', 'company_documents.id')
                ->orderByDesc('version')
                ->limit(1)
        ]);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
