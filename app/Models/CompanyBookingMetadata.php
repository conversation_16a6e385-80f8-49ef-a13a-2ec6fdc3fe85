<?php

namespace App\Models;

use App\CompanyBooking;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyBookingMetadata extends Model
{
    use HasFactory;

    const PUBLIC_CAL_ID = "PUBLIC_CAL_ID", PRIVATE_CAL_ID = "PRIVATE_CAL_ID";

    //TABLE
    public $table = 'company_booking_meta_data';

    //FILLABLES
    protected $fillable = [
        'company_booking_id',
        'key',
        'value',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function booking()
    {
        return $this->belongsTo(CompanyBooking::class, 'company_booking_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}