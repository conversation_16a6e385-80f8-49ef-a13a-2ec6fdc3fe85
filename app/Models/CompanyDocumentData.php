<?php

namespace App\Models;

use App\Company;
use App\File;
use App\Traits\GetEncryptedFile;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CompanyDocumentData extends Model
{
    use HasFactory, GetEncryptedFile;

    //FILLABLES
    protected $fillable = [
        "company_id",
        "company_document_id",
        "pdf",
        "response",
        "questions",
        "version",
        "sign",
        "signed_at",
        "signed_by_id",
        "process"
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [
        'formatted_response',
        'should_regen_pdf',
    ];

    //CASTS
    protected $casts = [
        'questions' => 'object',
    ];

    //WITH
    protected $with = [];

    //RULES
    public static $getListRules = [];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $current_version = CompanyDocumentData::where('company_document_id', $query->company_document_id)
                ->orderByDesc('version')->first();

            if (!$current_version) {
                $query->version = 1;
            } else {
                $query->version = $current_version->version + 1;
            }
        });
    }


    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function company_document()
    {
        return $this->belongsTo(CompanyDocument::class);
    }

    // protected $casts = [
    //     'response' => 'collection',
    // ];

    public function getFormattedResponseAttribute()
    {
        return collect(json_decode($this->response ?? '', true));
    }

    public function getShouldRegenPdfAttribute()
    {
        return $this->updated_at < Carbon::parse(config('app.regen_file_before', now()));
    }

    public function signed_by()
    {
        return $this->belongsTo(User::class, 'signed_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function files()
    {
        return $this->morphMany(File::class, 'fileable')->latest();
    }

    public function getPdfAttribute($value)
    {
        if ($value) {
            return str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }

    public function getSignAttribute($value)
    {
        if ($value) {
            return str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
