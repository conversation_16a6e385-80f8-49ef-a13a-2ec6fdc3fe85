<?php

namespace App\Models;

use App\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lead extends Model
{
    use HasFactory;

    //FILLABLES
    protected $fillable = [
        'company_name',
        'client_name',
        'email',
        'mobile_number',
        'country_code',
        'company_id',
        'type',
        'status',
        'industry',
        'size',
        'about',
        'outcome',
        'outcome_note',
    ];

    const TYPE_COLD = 'cold';
    const TYPE_WARM = 'warm';

    const STATUS_NEW = 'new';
    const STATUS_FOLLOW_UP = 'follow_up';
    const STATUS_ON_GOING = 'on_going';
    const STATUS_CLOSE = 'close';
    const STATUS_WON = 'won';
    const STATUS_LOST = 'lost';

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function notes()
    {
        return $this->hasMany(LeadNote::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
