<?php

namespace App\Models;

use App\Company;
use App\Contracts\Services\VideoCall\VideoCallServiceInterface;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoCall extends Model
{
    use HasFactory;

    //FILLABLES
    protected $fillable = [
        'company_id',
        'callable_type',
        'callable_id',
        'privacy',
        'not_before',
        'expires',
        'eject_at_room_exp',
        'max_participants',
        'started_at',
        'ended_at',
        'credit_used',
        'key',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'eject_at_room_exp' => 'boolean',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function members()
    {
        return $this->hasMany(VideoCallMember::class);
    }

    public function owner_member()
    {
        return $this->hasOne(VideoCallMember::class)->where('is_owner', true);
    }

    public function other_members()
    {
        return $this->hasMany(VideoCallMember::class)->where('is_owner', "!=", true);
    }

    public function other_member()
    {
        return $this->hasOne(VideoCallMember::class)->where('is_owner', "!=", true);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function callable()
    {
        return $this->morphTo();
    }

    public function end()
    {
        $activity = activity('video_call')
            ->performedOn($this);

        $duration = Carbon::parse($this->started_at)->diffInMinutes($this->ended_at);
        $activity_message = "Video call with ID {$this->key} ended in {$duration} mins. Credits used: {$this->credit_used}.";

        $activity = $activity->log($activity_message);
        return app(VideoCallServiceInterface::class)->close($this);
    }

    public function updateVideoCall($properties = null)
    {
        $videoCallService = app(VideoCallServiceInterface::class);
        $videoCallService->update($this, $this->owner_member, $this->other_member, $properties);

        return $this;
    }
}