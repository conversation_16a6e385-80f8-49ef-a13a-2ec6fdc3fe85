<?php

namespace App\Models;

use App\Company;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class MedicalDevice extends Model
{
    use HasFactory,GetEncryptedFile, SoftDeletes;

    //TABLE
    public $table = 'medical_devices';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'product_name',
        'model',
        'brand',
        'serial_number',
        'supplier', 
        'performed_maintenance',
        'upcoming_maintenance',
        'compliance_declared',
        'upload_manual',
        'supplier_agreement',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules=[];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function maintenances()
    {
        return $this->hasMany(MedicalDeviceMaintenance::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}

    public function getUploadManualAttribute($value)
    {
        if ($value) {
            return $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }
    public function getSupplierAgreementAttribute($value)
    {
        if ($value) {
            return $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }


}
