<?php

namespace App\Models;

use App\ClientSMS;
use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyCampaignSMS extends Model
{
    use HasFactory, Encryptable;

    //TABLE
    public $table = 'company_campaign_sms';

    //FILLABLES
    protected $fillable = [
        "name",
        "content",
        "credit_used",
        "client_count",
        "client_filter",
        "summary",
        "created_at",
        "updated_at",
        'company_id',
    ];

    protected $encrypted = [
        'content'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'client_filter' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function sendables()
    {
        return $this->morphMany(ClientSMS::class, 'sendable');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
