<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserOauthToken extends Model
{
    use HasFactory;
    //PROVIDER
    const GOOGLE = "GOOGLE";

    //TYPE
    const CALENDAR = "CALENDAR";

    //TABLE
    public $table = 'user_oauth_tokens';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'type',
        'provider',
        'scopes',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'email',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'scopes' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}