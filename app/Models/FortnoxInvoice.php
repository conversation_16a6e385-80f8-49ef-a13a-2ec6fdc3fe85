<?php

namespace App\Models;

use App\Client;
use App\Company;
use App\Traits\Encryptable;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FortnoxInvoice extends Model
{
    use HasFactory, Encryptable;

    protected $fillable = [
        'client_id',
        'company_id',
        'fortnox_document_number',
        'fortnox_customer_number',
        'customer_name',
        'currency',
        'total',
        'total_vat',
        'balance',
        'ocr',
        'url',
        'booked_at',
        'sent_at',
        'cancelled_at',
        'user_id',
        'discount_type',
        'discount_value',
        'note',
        'net',
        'gross',
    ];

    protected $encrypted = [
        'fortnox_document_number',
        'fortnox_customer_number',
        'customer_name',
        'currency',
        'total',
        'total_vat',
        'balance',
        'ocr',
        'url',
        'discount_type',
        'discount_value',
        'note',
        'net',
        'gross',
    ];

    protected $casts = [];

    /**
     * Get the client that owns the invoice
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the company that owns the invoice
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the invoice items
     */
    public function items(): HasMany
    {
        return $this->hasMany(FortnoxInvoiceItem::class);
    }

    /**
     * Get the user that created the invoice
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
