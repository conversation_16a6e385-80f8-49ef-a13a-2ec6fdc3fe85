<?php

namespace App\Models;

use <PERSON><PERSON><PERSON><PERSON>\Sortable\SortableTrait;
use App\GeneralTemplate;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GeneralTemplateQuestion extends Model
{
    use HasFactory, SortableTrait;

    protected static $sortableGroupField = 'general_template_id';

    protected static $sortableField = 'order';

    const
        YES_NO = "yes_no",
        YES_NO_TEXTBOX = "yes_no_textbox",
        TEXTBOX = "textbox",
        SELECT = 'select',
        MULTI_SELECT = 'multi_select',
        HTML_EDITOR = 'html_editor',
        IMAGE = 'image',
        FILE_UPLOAD  = 'file_upload';


    //FILLABLES
    protected $fillable = [
        'general_template_id',
        'question',
        'options',
        'required',
        'type',
        'order',
        'default',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'required' => 'boolean',
        'options' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function general_template()
    {
        return $this->belongsTo(GeneralTemplate::class);
    }

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
