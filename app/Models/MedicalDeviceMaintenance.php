<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Traits\GetEncryptedFile;

class MedicalDeviceMaintenance extends Model
{
    use HasFactory, GetEncryptedFile;

    //TABLE
    public $table = 'medical_device_maintenances';

    //FILLABLES
    protected $fillable = [
        'medical_device_id',
        'title',
        'protocol_path',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules=[];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    public function getProtocolPathAttribute($value)
    {
        if ($value) {
            return $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }

    public function medical_device()
    {
        return $this->belongsTo(MedicalDevice::class);
    }
    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
