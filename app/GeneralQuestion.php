<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class GeneralQuestion extends Model
{
    use GetEncryptedFile;
    protected $fillable = [
        'data', 'client_id'
    ];

    public function setDataAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['data'] = $value;
    }

    public function getDataAttribute($value)
    {
        try {
            if ($value != null) {
                return json_decode(Crypt::decrypt($value));
            }
        } catch (\Throwable $th) {
        }
        return $value;
    }

    public function client()
    {
        return $this->belongsTo('App\Client');
    }
}
