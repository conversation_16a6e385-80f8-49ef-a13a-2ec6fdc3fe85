<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GeneratePDF;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class ClientTreatment extends Model
{
    use Encryptable, LogsActivity, GetEncryptedFile, GeneratePDF, SoftDeletes;

    protected $encrypted = [
        'name',
        'description',
        'cost',
        'color',
        'notes',
        'unit',
        'treatment_cost',
        'treatment_id',
        'sign',
        'cancel_note',
        'notes_html',
        'signed_by_bank_id',
    ];

    protected $fillable = [
        'client_id',
        'name',
        'description',
        'cost',
        'color',
        'date',
        'notes',
        'notes_html',
        'images',
        'unit',
        'treatment_cost',
        'treatment_id',
        'sign',
        'user_id',
        'signed_at',
        'signed_by_id',
        'nrs_rating',
        'is_cancelled',
        'cancel_note',
        'cancelled_by_id',
        'cancelled_at',
        'is_signed_by_bank_id',
        'signed_by_bank_id',
    ];

    protected $with = ['files'];

    public function setImagesAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['images'] = $value;
    }

    public function getImagesAttribute($value)
    {
        try {
            if ($value != null) {
                $data = collect(json_decode(Crypt::decrypt($value)));
                $signed_data = [];
                foreach ($data as $d) {
                    array_push($signed_data, str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($d)));
                }
                return $signed_data;
            }
        } catch (\Throwable $th) {
        }

        return $value;
    }

    public function getCostAttribute($value)
    {
        return Crypt::encrypt((string)Crypt::decrypt($value));
    }

    public function getTreatmentCostAttribute($value)
    {
        return Crypt::encrypt((string)Crypt::decrypt($value));
    }

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function details()
    {
        return $this->hasMany(ClientTreatmentDetail::class);
    }

    public function files()
    {
        return $this->morphMany('App\File', 'fileable');
    }

    public function file_batches()
    {
        return $this->morphMany(ClientFileBatch::class, 'signable');
    }

    public function prescriptions()
    {
        return $this->morphToMany(
            ClientPrescription::class,
            'prescriptionable',
            'prescriptionables',
            'prescriptionable_id',
            'prescription_id',
            'id',
        );
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function signed_by()
    {
        return $this->belongsTo(User::class, 'signed_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function cancelled_by()
    {
        return $this->belongsTo(User::class, 'cancelled_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function getSignAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    protected static $logAttributes = [
        'name',
        'description',
        'cost',
        'color',
        'date',
        'notes',
        'notes_html',
        'images',
        'unit',
        'treatment_cost',
        'treatment_id',
    ];

    protected static $logName = 'client treatment';

    public function getDescriptionForEvent(string $eventName): string
    {
        $client_user = $this->client;

        return $client_user->first_name . ' ' . $client_user->last_name . "'s procedure :subject.name has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public static function downloadPDF(ClientTreatment $treatment, $is_twelve_hours)
    {
        return self::downloadFromView('exports.client.procedure', [
            'client' => $treatment->client,
            'treatment' => $treatment,
            'is_twelve_hours' => $is_twelve_hours,
            'company' => $treatment->client->company
        ]);
    }
}
