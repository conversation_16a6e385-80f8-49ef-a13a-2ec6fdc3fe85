<?php

declare(strict_types=1);

namespace App\Services\PDF\Client;

use App\Client;
use App\ClientConsent;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Services\PDF\PDFCommon;
use App\Setting;
use App\Traits\TimeZoneManager;
use Illuminate\Support\Facades\Cache;

class ClientPersonalDataPolicyPDFService extends PDFCommon implements PDFDataServiceInterface
{
    use TimeZoneManager;

    function __construct(
        readonly private Client $client,
        readonly private ClientConsent $clientConsent,
    ) {}

    /**
     * @throws Exception
     */
    public function payload(): array
    {
        $consent = $this->clientConsent;

        $portalViews = Cache::remember("portal-views-{$this->client->company->id}", 600, function () {
            return $this->client->company->settings()->whereIn('key', Setting::PORTAL_VIEW_SETTINGS)->get();
        });

        $personalIdView = !!$portalViews->where('key', Setting::PORTAL_VIEW_PERSONAL_ID)->first()?->value;
        $dateOfBirthView = !!$portalViews->where('key', Setting::PORTAL_VIEW_DATE_OF_BIRTH)->first()?->value;
        $phoneView = !!$portalViews->where('key', Setting::PORTAL_VIEW_PHONE)->first()?->value;
        $addressView = !!$portalViews->where('key', Setting::PORTAL_VIEW_STREET_ADDRESS)->first()?->value;
        $cityView = !!$portalViews->where('key', Setting::PORTAL_VIEW_CITY)->first()?->value;
        $zipCodeView = !!$portalViews->where('key', Setting::PORTAL_VIEW_ZIPCODE)->first()?->value;
        $stateView = !!$portalViews->where('key', Setting::PORTAL_VIEW_STATE)->first()?->value;
        $countryView = !!$portalViews->where('key', Setting::PORTAL_VIEW_COUNTRY)->first()?->value;

        $showAddress = $addressView || $cityView || $zipCodeView || $stateView || $countryView;

        $message = $consent->message ?? null;

        return [
            "view" => "client/personal_data_policy",
            "lang" => app()->getLocale(),
            "payload" => [
                "client" => [
                    "first_name" => $this->client->first_name,
                    "last_name" => $this->client->last_name,
                    "email" => $this->client->email,
                    "date_of_birth" => $dateOfBirthView ? $this->client->dateOfBirthFormatted($this->client->company) ?? "" : null,
                    "personal_id" => $personalIdView ? $this->client->personal_id ?? "" : null,
                    "phone_number" => $phoneView ? $this->client->phone() ?? "" : null,
                    "pretty_address" => $showAddress ? $this->client->pretty_address ?? "" : null,
                    "country" => $countryView ? $this->client->country ?? "" : null,
                    "message" => $message
                ]
            ],
        ];
    }
}
