<?php

declare(strict_types=1);

namespace App\Services\PDF\Company;

use App\Company;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Models\MedicalDevice;
use App\Services\PDF\PDFCommon;
use App\Setting;
use App\Traits\TimeZoneManager;
use Carbon\Carbon;

class CompanyMedicalDevicePDFService extends PDFCommon implements PDFDataServiceInterface
{
    use TimeZoneManager;

    function __construct(
        readonly private Company $company,
        readonly private MedicalDevice $medicalDevice,
    ) {}

    /**
     * @throws Exception
     */
    public function payload(): array
    {
        $company = $this->company;
        $timezone = $this->getCarbonApprovedTimeZone($company->timezone);
        $format = Setting::getDateTimeFormat($company, false, false);

        return [
            "view" => "company/medical_device",
            "lang" => app()->getLocale(),
            "footer" => '<div style="font-size:10px; width:100%; text-align: right; padding-right: 8px; color:grey;">
                <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
            </div>',
            "payload" => [
                'product_name' => $this->medicalDevice->product_name,
                'model' => $this->medicalDevice->model,
                'brand' => $this->medicalDevice->brand,
                'serial_number' => $this->medicalDevice->serial_number,
                'supplier' => $this->medicalDevice->supplier,
                'compliance_declared' => $this->medicalDevice->compliance_declared,
                'created_at' => $this->medicalDevice->created_at ? Carbon::parse($this->medicalDevice->created_at)->setTimezone($timezone)->format($format) : null,
                "watermark" => $this->company->company_name,
            ],
        ];
    }
}
