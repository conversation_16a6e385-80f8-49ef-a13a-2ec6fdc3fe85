<?php

declare(strict_types=1);

namespace App\Services\PDF\Company;

use App\Company;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Models\CompanyDocument;
use App\Models\CompanyDocumentData;
use App\Models\CompanyLegalDocument;
use App\Services\PDF\PDFCommon;
use App\Setting;
use App\Traits\TimeZoneManager;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class CompanyLegalDocumentPDFService extends PDFCommon implements PDFDataServiceInterface
{
    use TimeZoneManager;

    function __construct(
        readonly private Company $company,
        readonly private string $type,
    ) {}

    /**
     * @throws Exception
     */
    public function payload(): array
    {
        $company = $this->company;
        $type = $this->type;
        $super_user_name = $company->super_user->fullName();

        $language_to_use = $company->country == 'Sweden' ? 'sv' : 'en';
        $view_name = match ($type) {
            CompanyLegalDocument::PUBLIC => 'company/public_doc_' . $language_to_use,
            CompanyLegalDocument::DPA => 'company/dpa_doc_' . $language_to_use,
            CompanyLegalDocument::SUPPLIER => 'company/supplier_doc_' . $language_to_use,
            default => 'company/public_doc_' . $language_to_use,
        };

        return [
            "view" => $view_name,
            "lang" => $language_to_use,
            "footer" => '<div style="font-size:10px; width:100%; text-align: right; padding-right: 8px; color:grey;">
                <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
            </div>',
            "payload" => [
                "data" => [
                    "company_name" => $company->company_name,
                    "company_address" => $company->address(),
                    "company_email" => $company->email,
                    "date_signed" => Carbon::parse($company->created_at)->format('Y-m-d'),
                    "representative_name" => $company->company_name,
                    "platform_description" => $language_to_use == 'en' ? "manage data, analyze information, administer processes" : "hantera data, analysera information, administrera processer",
                    "superuser_name" => $super_user_name,
                ]
            ],
        ];
    }
}