<?php

declare(strict_types=1);

namespace App\Services\PDF\Company;

use App\Company;
use App\Contracts\Services\PDF\Company\CompanyPDFServiceInterface;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Models\CompanyDocument;
use App\Models\CompanyDocumentData;
use App\Models\MedicalDevice;

class CompanyPDFService implements CompanyPDFServiceInterface
{
    function __construct(readonly private Company $company) {}

    /**
     * @throws Exception
     */
    public function document(
        CompanyDocument $document,
        CompanyDocumentData $documentData,
    ): PDFDataServiceInterface {
        return new CompanyDocumentPDFService($this->company, $document, $documentData);
    }

    public function legal_document(
        Company $company,
        $type,
    ): PDFDataServiceInterface {
        return new CompanyLegalDocumentPDFService($company, $type);
    }

    /**
     * @throws Exception
     */
    public function medical_device(
        MedicalDevice $medicalDevice,
    ): PDFDataServiceInterface {
        return new CompanyMedicalDevicePDFService($this->company, $medicalDevice);
    }
}
