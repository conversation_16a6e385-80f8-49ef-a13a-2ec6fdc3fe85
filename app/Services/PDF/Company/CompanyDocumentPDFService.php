<?php

declare(strict_types=1);

namespace App\Services\PDF\Company;

use App\Company;
use App\Contracts\Services\PDF\PDFDataServiceInterface;
use App\Models\CompanyDocument;
use App\Models\CompanyDocumentData;
use App\Services\PDF\PDFCommon;
use App\Setting;
use App\Traits\TimeZoneManager;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class CompanyDocumentPDFService extends PDFCommon implements PDFDataServiceInterface
{
    use TimeZoneManager;

    function __construct(
        readonly private Company $company,
        readonly private CompanyDocument $document,
        readonly private CompanyDocumentData $documentData,
    ) {}

    /**
     * @throws Exception
     */
    public function payload(): array
    {
        $company = $this->company;
        $timezone = $this->getCarbonApprovedTimeZone($company->timezone);
        $format = Setting::getDateTimeFormat($company, false, false);

        return [
            "view" => "company/document",
            "lang" => app()->getLocale(),
            "footer" => '<div style="font-size:10px; width:100%; text-align: right; padding-right: 8px; color:grey;">
                <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
            </div>',
            "payload" => [
                "title" => $this->document->title,
                "version" => $this->documentData->version,
                "created_at" => Carbon::parse($this->documentData->created_at)->setTimezone($timezone)->format($format),
                "questions" => $this->documentData?->questions ?? [],
                "process_name" => $this->documentData?->process,
                "datas" => $this->documentData?->formatted_response?->toArray() ?? [],
                "sign" => $this->documentData->sign ? urldecode(Storage::url($this->documentData->sign)) : null,
                "signed_by" => $this->documentData->signed_by ? [
                    "first_name" => $this->documentData->signed_by->first_name ?? "",
                    "last_name" => $this->documentData->signed_by->last_name ?? "",
                ] : null,
                "signed_at" => $this->documentData->signed_at ? Carbon::parse($this->documentData->signed_at)->setTimezone($timezone)->format($format) : null,
                "watermark" => $this->company->company_name,
            ],
        ];
    }
}
