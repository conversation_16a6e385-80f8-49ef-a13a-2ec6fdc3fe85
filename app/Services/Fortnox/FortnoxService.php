<?php

declare(strict_types=1);

namespace App\Services\Fortnox;

use App\Client;
use App\Company;
use App\CompanyProduct;
use App\CompanyService;
use App\Contracts\Services\Fortnox\FortnoxServiceInterface;
use App\Exceptions\Fortnox\FortnoxCustomerAlreadyExistsException;
use App\Exceptions\Fortnox\FortnoxInvalidStateException;
use App\Exceptions\Fortnox\FortnoxNotAuthenticatedException;
use App\Http\Integrations\Fortnox\DataObject\Customer;
use App\Http\Integrations\Fortnox\DataObject\Invoice;
use App\Http\Integrations\Fortnox\Facades\FortnoxAuth;
use App\Http\Integrations\Fortnox\FortnoxConnector;
use App\Models\FortnoxInvoice;
use App\Models\FortnoxInvoiceItem;
use App\Traits\SMS;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Psr\Http\Message\StreamInterface;

class FortnoxService implements FortnoxServiceInterface
{
    private const CACHE_PREFIX = 'fortnox_oauth_state_';
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Generate authorization URL for Fortnox OAuth
     *
     * @throws Exception
     */
    public function getAuthorizationUrl(Company $company, array $scopes = []): string
    {
        // Generate a random state and store it in cache with company ID
        $state = Str::random(32);
        $cacheKey = self::CACHE_PREFIX . $company->id;

        // Store state in cache, replacing any existing state for this client
        Cache::put($cacheKey, $state, self::CACHE_TTL);

        // Default scopes for Fortnox
        $defaultScopes = ['customer', 'invoice'];
        $allScopes = array_unique(array_merge($defaultScopes, $scopes));

        return FortnoxAuth::getAuthorizationUrl($allScopes, $state, ' ', []);
    }

    /**
     * Handle OAuth callback and store access token
     *
     * @throws Exception
     */
    public function handleOAuthCallback(Company $company, string $authorizationCode, string $currentState): void
    {
        $cacheKey = self::CACHE_PREFIX . $company->id;
        $expectedState = Cache::get($cacheKey);

        if (!$expectedState) {
            throw new FortnoxInvalidStateException(__('strings.fortnox_oauth_state_not_found'));
        }

        if (!$currentState || $currentState !== $expectedState) {
            throw new FortnoxInvalidStateException(__('strings.fortnox_oauth_state_mismatch'));
        }

        // Clear the state from cache
        Cache::forget($cacheKey);

        // Get access token from Fortnox
        $authenticator = FortnoxAuth::getAccessToken($authorizationCode, $currentState, $expectedState);

        // Store the authenticator in the company
        DB::transaction(function () use ($company, $authenticator) {
            $company->fortnox_auth = $authenticator;
            $company->save();
        });
    }

    /**
     * Create a Fortnox customer for the client
     *
     * @throws Exception
     */
    public function createCustomer(Client $client): Customer
    {
        // Get the company from the client
        $company = $client->company;

        // Validate company is authenticated with Fortnox
        $this->validateAuthentication($company);

        // Check if client already has a Fortnox customer
        if (!empty($client->fortnox_customer_id)) {
            throw new FortnoxCustomerAlreadyExistsException(
                __('strings.fortnox_customer_already_exists') . ": {$client->fortnox_customer_id}"
            );
        }

        // Validate required client data
        $this->validateClientDataForCustomer($client);

        // Get client's primary address
        $address = $client->addresses->first();

        // Create customer using the company's fortnox connection
        $fortnox = $company->fortnox();
        /** @var Customer $customer */
        $customer = $fortnox->customer(null)->create(
            name: $client->full_name,
            email: $client->email,
            email_invoice: $client->email,
            currency: 'SEK', // Default to SEK, can be made configurable
            invoice_default_delivery_type: 'EMAIL',
            address1: $address?->address ?? null,
            address2: $address?->address2 ?? null,
            organisation_number: $client->personal_id ?? null,
            phone: SMS::FORMAT_NUMBER($client?->country_code ?? null, $client->phone_number ?? null),
            zip_code: $address?->zip_code ?? null,
            city: $address?->city ?? null,
            country_code: $address?->country ?? null,
        );

        // Store customer ID in client
        DB::transaction(function () use ($client, $customer) {
            $client->fortnox_customer_id = $customer->id;
            $client->save();
        });

        return $customer;
    }

    /**
     * Create a Fortnox invoice
     *
     * @throws Exception
     */
    public function createInvoice(
        Client $client,
        array $data,
    ): FortnoxInvoice {
        // Get the company from the client
        $company = $client->company;

        // Validate company is authenticated with Fortnox
        $this->validateAuthentication($company);

        // Validate client has a Fortnox customer
        if (empty($client->fortnox_customer_id)) {
            throw new FortnoxNotAuthenticatedException(__('strings.fortnox_client_must_have_customer'));
        }

        // Process items from products, services, or legacy items
        $processedItems = $this->processInvoiceItems($data);

        // Format items for Fortnox API (without user_id and morph data)
        $fortnoxApiItems = $this->formatItemsForFortnoxApi($processedItems, $data);

        // Create invoice using the company's fortnox connection
        /** @var FortnoxConnector */
        $fortnox = $company->fortnox();
        /** @var Invoice $fortnoxApiInvoice */
        $fortnoxApiInvoice = $fortnox->customer($client->fortnox_customer_id)->invoices()->create(
            items: $fortnoxApiItems,
            remarks: isset($data['note']) ? $data['note'] : null,
        );

        // Store invoice in database with inventory management
        return DB::transaction(function () use ($client, $company, $fortnoxApiInvoice, $processedItems, $data) {
            // Create the invoice record
            $fortnoxInvoice = FortnoxInvoice::create([
                'client_id' => $client->id,
                'company_id' => $company->id,
                'fortnox_document_number' => $fortnoxApiInvoice->document_number,
                'fortnox_customer_number' => $fortnoxApiInvoice->customer_number,
                'customer_name' => $fortnoxApiInvoice->customer_name,
                'invoice_date' => $fortnoxApiInvoice->invoice_date,
                'currency' => $fortnoxApiInvoice->currency,
                'total' => $fortnoxApiInvoice->total,
                'total_vat' => $fortnoxApiInvoice->total_vat,
                'balance' => $fortnoxApiInvoice->balance,
                'ocr' => $fortnoxApiInvoice->ocr,
                'url' => $fortnoxApiInvoice->url,
                'booked_at' => $fortnoxApiInvoice->booked ? now() : null,
                'sent_at' => $fortnoxApiInvoice->sent ? now() : null,
                'cancelled_at' => $fortnoxApiInvoice->cancelled ? now() : null,
                'user_id' => auth()->id(),
                'discount_type' => $data['discount_type'] ?? null,
                'discount_value' => $data['discount_value'] ?? null,
                'note' => $data['note'] ?? null,
                'net' => $fortnoxApiInvoice->net,
                'gross' => $fortnoxApiInvoice->gross,
            ]);

            // Create invoice items with user_id and morph relationships
            foreach ($processedItems as $item) {
                $originalPrice = $this->getOriginalPrice($item['price'], $item['vat'] ?? 25, 'PERCENT', 0);

                $invoiceItem = FortnoxInvoiceItem::create([
                    'fortnox_invoice_id' => $fortnoxInvoice->id,
                    'description' => $item['description'],
                    'price' => $originalPrice,
                    'quantity' => $item['quantity'],
                    'unit' => $item['unit'] ?? 'st',
                    'vat' => $item['vat'] ?? 25,
                    'total' => ($originalPrice * $item['quantity']),
                    'user_id' => $item['user_id'] ?? auth()->id(),
                    'invoiceable_type' => $item['invoiceable_type'] ?? null,
                    'invoiceable_id' => $item['invoiceable_id'] ?? null,
                    'discount_type' => $data['discount_type'] ?? null,
                    'discount_value' => $data['discount_value'] ?? null,
                ]);

                // Associate with product or service if available
                if (isset($item['invoiceable_type']) && isset($item['invoiceable_id'])) {
                    // Decrease product stock for inventory management
                    if ($item['invoiceable_type'] === CompanyProduct::class) {
                        $product = CompanyProduct::find($item['invoiceable_id']);
                        if ($product) {
                            $product->stock -= $item['quantity'];
                            $product->save();
                        }
                    }
                }
            }

            return $fortnoxInvoice->load('items');
        });
    }

    /**
     * Validate that the company is authenticated with Fortnox
     *
     * @throws FortnoxNotAuthenticatedException
     */
    private function validateAuthentication(Company $company): void
    {
        if (!$company->fortnox_auth) {
            throw new FortnoxNotAuthenticatedException(__('strings.fortnox_company_not_authenticated'));
        }

        // Check if token has expired and needs refresh
        if ($company->fortnox_auth->hasExpired()) {
            $this->refreshToken($company);
        }
    }

    public function refreshToken(Company $company): void
    {
        if (!$company->fortnox_auth) {
            throw new FortnoxNotAuthenticatedException(__('strings.fortnox_company_not_authenticated'));
        }

        try {
            $newAuthenticator = FortnoxAuth::refreshAccessToken($company->fortnox_auth);

            DB::transaction(function () use ($company, $newAuthenticator) {
                $company->fortnox_auth = $newAuthenticator;
                $company->save();
            });
        } catch (Exception $e) {
            throw new FortnoxNotAuthenticatedException(__('strings.fortnox_token_refresh_failed_service') . ': ' . $e->getMessage());
        }
    }



    /**
     * Validate client data required for creating a customer
     *
     * @throws Exception
     */
    private function validateClientDataForCustomer(Client $client): void
    {
        if (empty($client->first_name) && empty($client->last_name)) {
            throw new Exception(__('strings.fortnox_client_must_have_name'));
        }

        if (empty($client->email)) {
            throw new Exception(__('strings.fortnox_client_must_have_email'));
        }
    }

    /**
     * Process invoice items from products, services, or legacy items
     *
     * @throws Exception
     */
    private function processInvoiceItems(array $data): array
    {
        $processedItems = [];

        // Process products
        if (isset($data['products']) && is_array($data['products'])) {
            foreach ($data['products'] as $productData) {
                $product = CompanyProduct::findOrFail($productData['id']);

                $processedItems[] = [
                    'description' => $product->name,
                    'price' => $productData['amount'] ?? $product->selling_price,
                    'quantity' => $productData['quantity'],
                    'unit' => 'st',
                    'vat' => $product->tax_information ?? 25,
                    'user_id' => $productData['user_id'] ?? null,
                    'invoiceable_type' => CompanyProduct::class,
                    'invoiceable_id' => $product->id,
                ];
            }
        }

        // Process services
        if (isset($data['services']) && is_array($data['services'])) {
            foreach ($data['services'] as $serviceData) {
                $service = CompanyService::findOrFail($serviceData['id']);

                $processedItems[] = [
                    'description' => $service->name,
                    'price' => $serviceData['amount'] ?? $service->price,
                    'quantity' => $serviceData['quantity'],
                    'unit' => 'st',
                    'vat' => $service->tax_information ?? 25,
                    'user_id' => $serviceData['user_id'] ?? null,
                    'invoiceable_type' => CompanyService::class,
                    'invoiceable_id' => $service->id,
                ];
            }
        }



        if (empty($processedItems)) {
            throw new Exception(__('strings.fortnox_invoice_must_have_items'));
        }

        return $processedItems;
    }



    /**
     * Format validated items for Fortnox API with global discount calculations
     *
     * @param array $validatedItems
     * @param array $data
     * @return array
     */
    private function formatItemsForFortnoxApi(array $validatedItems, array $data = []): array
    {
        $fortnoxApiItems = [];

        // Calculate global discount percentage if applied
        $globalDiscountPercentage = $this->calculateGlobalDiscountPercentage($validatedItems, $data);

        foreach ($validatedItems as $item) {
            $originalPrice = $this->getOriginalPrice($item['price'], $item['vat'], 'PERCENT', 0);

            // Apply global discount to the price
            $discountedPrice = $originalPrice * (1 - $globalDiscountPercentage);

            $fortnoxApiItems[] = [
                'Description' => $item['description'],
                'Price' => round($discountedPrice, 2),
                'DeliveredQuantity' => $item['quantity'],
                'Unit' => $item['unit'],
                'VAT' => $item['vat'],
            ];
        }

        return $fortnoxApiItems;
    }

    /**
     * Calculate global discount percentage to apply to each item
     *
     * @param array $validatedItems
     * @param array $data
     * @return float
     */
    private function calculateGlobalDiscountPercentage(array $validatedItems, array $data): float
    {
        // If no discount is applied, return 0
        if (!isset($data['discount_type']) || !isset($data['discount_value'])) {
            return 0.0;
        }

        $discountType = $data['discount_type'];
        $discountValue = (float) $data['discount_value'];

        // For percentage discount, it's straightforward
        if ($discountType === 'percentage') {
            return $discountValue / 100;
        }

        // For value discount, calculate the percentage based on subtotal
        if ($discountType === 'value') {
            $subTotal = $this->calculateSubTotal($validatedItems);

            if ($subTotal > 0) {
                return min($discountValue / $subTotal, 1.0); // Cap at 100% discount
            }
        }

        return 0.0;
    }

    /**
     * Calculate subtotal of all items (price including VAT * quantity)
     *
     * @param array $validatedItems
     * @return float
     */
    private function calculateSubTotal(array $validatedItems): float
    {
        $subTotal = 0.0;

        foreach ($validatedItems as $item) {
            $subTotal += $item['price'] * $item['quantity'];
        }

        return $subTotal;
    }

    /**
     * Calculate original price excluding VAT, accounting for discounts
     * 
     * @param float $priceWithVat The price including VAT * quantity
     * @param float $vat VAT percentage
     * @param string $discountType 'PERCENT' or 'AMOUNT'
     * @param float $discountValue Discount value
     * @return float Original price excluding VAT
     */
    private function getOriginalPrice($priceWithVat, $vat, $discountType = 'PERCENT', $discountValue = 0)
    {
        $vatMultiplier = 1 + ($vat / 100);

        if ($discountType === 'AMOUNT' && $discountValue > 0) {
            // For AMOUNT discount, we need to work backwards from the desired total
            // Total with VAT = (Price excluding VAT - Discount) * (1 + VAT%)
            // Solving for Price excluding VAT:
            // Price excluding VAT = (Total with VAT / (1 + VAT%) + Discount)

            $totalWithoutVat = ($priceWithVat - $discountValue) / $vatMultiplier;
            $priceExcludingVat = ($totalWithoutVat + $discountValue);

            return round($priceExcludingVat, 2);
        } else {
            // For PERCENT discount or no discount, standard calculation
            return round($priceWithVat / $vatMultiplier, 2);
        }
    }

    public function downloadInvoice(FortnoxInvoice $fortnoxInvoice): StreamInterface
    {
        $company = $fortnoxInvoice->company;

        $this->validateAuthentication($company);

        $fortnox = $company->fortnox();

        return $fortnox->customer($fortnoxInvoice->fortnox_customer_number)->invoices()->download($fortnoxInvoice->fortnox_document_number);
    }
}
