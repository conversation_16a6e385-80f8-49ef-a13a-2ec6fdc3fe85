<?php

declare(strict_types=1);

namespace App\Services\SMS;

use App\Client;
use App\ClientSMS;
use App\Company;
use App\CompanySMSCredits;
use App\Contracts\Services\SMS\SMSServiceInterface;
use App\Exceptions\NoValidClientSMSException;
use App\Exceptions\SMSFailedException;
use App\Mail\SMSUnsendMail;
use App\Setting;
use Illuminate\Support\Collection;
use App\Traits\SMS;
use App\Traits\Sinch;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\LazyCollection;

class SMSService implements SMSServiceInterface
{
    /**
     * @throws Exception
     */
    public function sendSMS(Client $client, string $content): bool
    {
        // TODO: Implement sendSMS() method.
        return true;
    }

    /**
     * $data = [
     *      'client' => $client,
     *      'booking' => $booking,
     * ]
     * @throws Exception
     */
    public function sendBulkSMSToClient(User $user, string $content, Collection|LazyCollection $datas, ?Model $related = null): object
    {
        $company = $user->company;

        $sender_id = Setting::getSetting($company, Setting::SMS_SENDER_ID)?->value ?? null;

        $data = $this->processDataForClientSMS($user, $content, $datas, $sender_id);

        $errors = $this->sendErrors($user, $data->errors);

        $total_length = $this->checkValidateClientSMSCredits($user, $data);

        $send_sms = null;
        $send_sms_with_sender_id = null;

        if (count($data->data->numbers ?? [])) {
            $send_sms = $this->sendMessages($data->content, $data->data->numbers, $data->data->parameters);
        }
        if (count($data->data_with_sender_id->numbers ?? [])) {
            $send_sms_with_sender_id = $this->sendMessages($data->content, $data->data_with_sender_id->numbers, $data->data_with_sender_id->parameters, $sender_id);
        }

        $sms_credits = $user->company->sms_credits;

        $per_user_credit = round($total_length / (count($data->data->numbers ?? []) + count($data->data_with_sender_id->numbers ?? [])), 2);

        if ($send_sms) {
            $this->postProcessSMSReponse($user, $data->data->processed_data, $send_sms, $per_user_credit, $related);
        }

        if ($send_sms_with_sender_id) {
            $this->postProcessSMSReponse($user, $data->data_with_sender_id->processed_data, $send_sms_with_sender_id, $per_user_credit, $related);
        }

        $sms_credits->credits = $sms_credits->credits - $total_length;
        $sms_credits->save();

        return (object) [
            "sms_credits" => $sms_credits,
            "credit_used" => $total_length,
            "errors" => $errors,
        ];
    }

    /**
     * Processes the data collection to generate numbers and logs for SMS sending.
     *
     * @param User $user
     * @param string $content
     * @param Collection|LazyCollection $datas
     * @return object
     */
    private function processDataForClientSMS(User $user, string $content, Collection|LazyCollection $datas, ?string $sender_id = null): object
    {
        $keys = SMS::GET_KEYS_IN_MESSAGE($content);

        $errors = collect();

        $processed_data = collect();
        $processed_data_with_sender_id = collect();

        $parameters = SMS::GENERATE_DEFAULT_KEYS();
        $parameters_with_sender_id = SMS::GENERATE_DEFAULT_KEYS();

        foreach ($datas as $data) {
            $client = $data['client'];
            $booking = isset($data['booking']) ? $data['booking'] : null;
            // Validate number and collect errors
            $error = $this->validateNumber($client->country_code, $client->phone_number);
            if ($error) {
                $errors->push(['client' => $client, 'reason' => $error]);
                continue;
            }

            $is_alpha_sender_id_supported = $sender_id && SMS::IS_ALPHA_NUMBERIC_SENDER_ID_SUPPORTED($client->country_code);

            // Format number based on whether sender ID is supported
            $formatted_number = SMS::FORMAT_NUMBER($client->country_code, $client->phone_number, true);

            // Generate parameters based on sender ID support
            if ($is_alpha_sender_id_supported) {
                $parameters_with_sender_id = SMS::GENERATE_PARAMETERS($keys, $parameters_with_sender_id, $formatted_number, $client, $booking ? $booking->user : $user, $booking);

                // Store processed data
                $processed_data_with_sender_id->push((object) [
                    'number' => SMS::FORMAT_NUMBER($client->country_code, $client->phone_number),
                    'client' => $client,
                    'use_sender_id' => $is_alpha_sender_id_supported,
                    "message" => SMS::REPLACE_KEYS($content, $booking ? $booking->user : $user, $client, $booking),
                ]);
            } else {
                $parameters = SMS::GENERATE_PARAMETERS($keys, $parameters, $formatted_number, $client, $booking ? $booking->user : $user, $booking);

                // Store processed data
                $processed_data->push((object) [
                    'number' => SMS::FORMAT_NUMBER($client->country_code, $client->phone_number),
                    'client' => $client,
                    'use_sender_id' => $is_alpha_sender_id_supported,
                    "message" => SMS::REPLACE_KEYS($content, $booking ? $booking->user : $user, $client, $booking),
                ]);
            }
        }

        return (object) [
            'content' => $content,
            'errors' => $errors->toArray(),
            'data' => (object) [
                "parameters" => $parameters->toArray(),
                "numbers" => $processed_data->pluck('number')->values()->toArray(),
                "processed_data" => $processed_data,
            ],
            'data_with_sender_id' => (object) [
                "parameters" => $parameters_with_sender_id->toArray(),
                "numbers" => $processed_data_with_sender_id->pluck('number')->values()->toArray(),
                "processed_data" => $processed_data_with_sender_id,
            ],
        ];
    }

    /**
     * Checks if the user has sufficient SMS credits to send the messages.
     *
     * @param User $user
     * @param object $data
     * @throws SMSFailedException
     * @return int
     */
    private function checkValidateClientSMSCredits(User $user, object $data): int
    {
        $total_length = $this->checkSMSCreditsRequired($user, $data);

        $sms_credits = $user->company->sms_credits;

        // MAIN LOGIC
        if ($sms_credits->credits < $total_length) {
            throw new SMSFailedException(200, __('sms.insufficient_sms_credits_required_n_credits', ['length' => $total_length]));
        }

        return $total_length;
    }

    /**
     * Checks how many credits are needed to send the messages.
     *
     * @param User $user
     * @param object $data
     * @return int
     */
    private function checkSMSCreditsRequired(User $user, object $data): int
    {
        $content = $data->content;
        $numbers = $data?->data?->numbers ?? [];
        $numbers_with_sender_id = $data?->data_with_sender_id?->numbers ?? [];

        if (!count($numbers) && !count($numbers_with_sender_id)) {
            throw new NoValidClientSMSException(500, __('sms.no_valid_clients_found'));
        }

        $message = SMS::BULK_GENERATE_MESSAGE_KEYS($content);

        $parameters = $data?->data?->parameters;
        $parameters_with_sender_id = $data?->data_with_sender_id?->parameters;

        $length = count($parameters ?? []) && count($numbers) ? Sinch::bulkGetPartLength($message, $numbers, $parameters) : 0;
        $length_with_sender_id = count($parameters_with_sender_id ?? []) && count($numbers_with_sender_id) ? Sinch::bulkGetPartLength($message, $numbers_with_sender_id, $parameters_with_sender_id) : 0;

        return $length + $length_with_sender_id;
    }


    /**
     * Sends error to email using the appropriate parameters.
     *
     * @param string $content
     * @param array $numbers
     * @param string|null $sender_id
     * @return bool
     */
    private function sendErrors(User $user, ?array $errors = []): bool
    {
        if (count($errors ?? [])) {
            $language = Setting::getSetting($user->company, Setting::CUSTOMER_LANGUAGE)?->value;
            Mail::to($user->email)->locale($language ?? app()->getLocale())->send(new SMSUnsendMail($user->company, collect($errors)));

            return true;
        }

        return false;
    }


    /**
     * Sends the SMS messages using the appropriate parameters.
     *
     * @param string $content
     * @param array $numbers
     * @param string|null $sender_id
     * @return array
     */
    private function sendMessages(string $content, array $numbers, array $parameters, ?string $sender_id = null): array
    {
        $message = SMS::BULK_GENERATE_MESSAGE_KEYS($content);

        return Sinch::bulkSendSMS($message, $numbers, $parameters, $sender_id, 'per_recipient');
    }

    /**
     * Post Process the response sms api
     *
     * @param User $user
     * @param array|Collection|LazyCollection $processed_data
     * @param array|Collection|LazyCollection $response
     * @param int $total_length
     * @return void
     */
    private function postProcessSMSReponse(User $user, array|Collection|LazyCollection $processed_data, array|Collection|LazyCollection $response, int|float $total_length, ?Model $related = null): void
    {
        $company = $user->company;

        foreach ($processed_data as $index => $data) {
            $activity = activity('sms_send')->performedOn($data->client);
            $activity = $activity->by($user);
            $activity = $activity->log("SMS has been sent to $data->number from marketing");

            ClientSMS::create([
                'text' => $data->message,
                'number' => $data->number,
                'type' => 'marketing',
                'total_message_count' => $total_length,
                'client_after_care_id' => null,
                'log_id' => $activity->id,
                'company_id' => $company->id,
                'batch_id' => $response['id'],
                'client_id' => $data->client->id,
                'user_id' => $user->id,
                'sendable_id' => $related?->id,
                'sendable_type' => $related?->getMorphClass(),
            ]);
        }
    }

    private function tryAutoPay(Company $company): CompanySMSCredits
    {
        $sms_credits = $company->sms_credits;

        $auto_pay_success = SMS::tryAutoPay($company);
        if ($auto_pay_success) {
            $sms_credits->refresh();
        }

        return $sms_credits;
    }


    private function validateNumber(?string $country_code, $phone_number): string | bool
    {
        $supported_country_codes = SMS::GET_COUNTRY_CODES();


        if (!$phone_number || !$country_code) {
            return __('sms.client_doesnt_have_a_valid_mobile_number');
        }

        // Not supported country code
        if (!in_array($country_code, $supported_country_codes)) {
            return __('sms.country_code_not_supported');
        }

        return false;
    }

    /**
     * @throws Exception
     */
    public function getBulkSMSLength(User $user, string $content, Collection|LazyCollection $datas): int
    {
        $company = $user->company;

        $sender_id = Setting::getSetting($company, Setting::SMS_SENDER_ID)?->value ?? null;

        $data = $this->processDataForClientSMS($user, $content, $datas, $sender_id);

        $total_length = $this->checkSMSCreditsRequired($user, $data);

        return $total_length;
    }

    /**
     * @throws Exception
     */
    public function getBulkLength(Collection|LazyCollection $clients, string $content): int
    {
        return 0;
    }
}
