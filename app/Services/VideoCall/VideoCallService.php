<?php

declare(strict_types=1);

namespace App\Services\VideoCall;

use App\Company;
use App\Contracts\Services\VideoCall\VideoCallServiceInterface;
use App\Http\Integrations\DailyCo\Facades\DailyCo;
use App\Models\VideoCall;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class VideoCallService implements VideoCallServiceInterface
{
    /**
     * @throws Exception
     */
    public function single(User|string $owner, Model|string $participant, array $properties = []): VideoCall
    {
        $room = null;

        try {
            return DB::transaction(function () use ($room, $owner, $participant, $properties) {
                // https://docs.daily.co/reference/rest-api/rooms/create-room#properties
                $properties = array_merge([
                    "max_participants" => 2,
                    "exp" => Carbon::now()->addMinutes(120)->timestamp,
                    "eject_at_room_exp" => true,
                    "privacy" => 'private',
                ], $properties);

                $privacy = isset($properties['privacy']) ? $properties['privacy'] : 'public';

                unset($properties['privacy']);

                $room = DailyCo::room()->create(
                    privacy: $privacy,
                    properties: $properties
                );

                $notBefore = isset($properties['nbf']) ? Carbon::createFromTimestamp($properties['nbf']) : null;
                $expires = isset($properties['exp']) ? Carbon::createFromTimestamp($properties['exp']) : null;

                /**
                 *
                 * @var Company $company
                 */
                $company = $owner->company;

                $videoCall = $company->video_calls()->create([
                    'privacy' => $privacy,
                    'not_before' => $notBefore,
                    'expires' => $expires,
                    'eject_at_room_exp' => isset($properties['eject_at_room_exp']) ? $properties['eject_at_room_exp'] : false,
                    'max_participants' => isset($properties['max_participants']) ? $properties['max_participants'] : 200,
                    'key' => $room->name,
                ]);

                // Owner Token
                $token = DailyCo::room($room->name)->meetingToken()->create(
                    user_name: $owner->fullName(),
                    user_id: $owner->id,
                    is_owner: true,
                );

                $member = $videoCall->members()->create([
                    'user_id' => $owner->id,
                    'not_before' => $notBefore,
                    'expires' => $expires,
                    'eject_at_room_exp' => isset($properties['eject_at_room_exp']) ? $properties['eject_at_room_exp'] : false,
                    'is_owner' => true,
                    'user_name' => $owner->fullName(),
                    'user_id' => $owner->id,
                    'token' => $token,
                    'key' => $room->name,
                ]);

                $member->memberable()->associate($owner);
                $member->save();

                // Participant Token
                $token = DailyCo::room($room->name)->meetingToken()->create(
                    user_name: $participant->fullName(),
                    user_id: $participant->id,
                    is_owner: false,
                );

                $member = $videoCall->members()->create([
                    'user_id' => $participant->id,
                    'not_before' => $notBefore,
                    'expires' => $expires,
                    'eject_at_room_exp' => isset($properties['eject_at_room_exp']) ? $properties['eject_at_room_exp'] : false,
                    'is_owner' => false,
                    'user_name' => $participant->fullName(),
                    'user_id' => $participant->id,
                    'token' => $token,
                    'key' => $room->name,
                ]);

                $member->memberable()->associate($participant);
                $member->save();

                if ($videoCall->owner_member) {
                    $activity = activity('video_call')
                        ->performedOn($videoCall);

                    $activity_message = "Video call created by {$videoCall->owner_member->user_name}.";
                    if ($videoCall->other_member) {
                        $activity_message = $activity_message . " Participant: {$videoCall->other_member->user_name}.";
                    }

                    $activity_message = $activity_message . " ID: {$videoCall->key}";

                    $activity = $activity->log($activity_message);
                }

                return $videoCall;
            });
        } catch (\Throwable $th) {
            report($th);

            if ($room) {
                try {
                    DailyCo::room($room->name)->delete();
                } catch (\Throwable $th) {
                    report($th);
                }
            }

            throw $th;
        }
    }

    /**
     * @throws Exception
     */
    public function group(User|string $owner, array|Collection $participants,  array $properties = []): VideoCall
    {
        return new VideoCall();
    }


    /**
     * @throws Exception
     */
    public function close(VideoCall $video_call): VideoCall
    {
        try {
            $response = DailyCo::room($video_call->key)->delete();
        } catch (\Throwable $th) {
            report($th);
        }

        $video_call->expires = now();

        if (!$video_call->ended_at) {
            $video_call->ended_at = now();
        }

        $video_call->save();

        return $video_call;
    }

    /**
     * @throws Exception
     */
    public function update(VideoCall $video_call, User|string $owner, Model|string $participant, array $properties = []): VideoCall
    {
        $room = null;
        // https://docs.daily.co/reference/rest-api/rooms/create-room#properties
        $properties = array_merge([
            "privacy" => 'private',
        ], $properties);

        $privacy = isset($properties['privacy']) ? $properties['privacy'] : 'public';

        unset($properties['privacy']);

        try {
            $room = DailyCo::room($video_call->key)->update($privacy, $properties);
        } catch (\Throwable $th) {
            report($th);
        }

        $notBefore = isset($room?->config?->nbf) ? Carbon::createFromTimestamp($room?->config?->nbf) : null;
        $expires = isset($room?->config?->exp) ? Carbon::createFromTimestamp($room?->config?->exp) : null;

        if ($expires) {
            $video_call->expires = $expires;
        }

        if ($notBefore) {
            $video_call->not_before = $notBefore;
        }

        $video_call->save();

        return $video_call;
    }
}