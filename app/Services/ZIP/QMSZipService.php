<?php

declare(strict_types=1);

namespace App\Services\ZIP;

use App\Company;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\ZIP\QMSZipServiceInterface;
use App\Contracts\Services\ZIP\ZipServiceInterface;
use App\File;
use App\Sidecar\GenerateHtmlToPDF;
use App\Traits\SaveFile;
use App\Traits\TimeZoneManager;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class QMSZipService implements QMSZipServiceInterface
{
    use SaveFile, TimeZoneManager;

    public function __construct(private PDFServiceInterface $pdfService, private ZipServiceInterface $zipService) {}

    public function generateAll(Company $company): File
    {
        $user = $company->users()->where('email', $company->email)->firstOrFail();

        $timezone = $this->getCarbonApprovedTimeZone($company->timezone);

        activity()->disableLogging();

        $zip_data = collect([]);

        $zip_data = $this->getDataForDocuments($company, $timezone, $zip_data);

        $zip_data = $this->getDataForMedicalDevices($company, $timezone, $user, $zip_data);

        $file = $this->generateZip($company, $user, $zip_data, 'qms_zip');

        activity()->enableLogging();
        $activity = activity()->performedOn($company);
        $activity = $activity->by($user);
        $activity->log($company->first_name . " " . $company->last_name . "'s QMS zip has been created for :causer.first_name :causer.last_name");
        activity()->disableLogging();

        return $file;
    }

    public function generateForDocuments(Company $company): File
    {
        $user = $company->users()->where('email', $company->email)->firstOrFail();

        $timezone = $this->getCarbonApprovedTimeZone($company->timezone);

        activity()->disableLogging();

        $zip_data = collect([]);

        $zip_data = $this->getDataForDocuments($company, $timezone, $zip_data);

        $file = $this->generateZip($company, $user, $zip_data, 'qms_documents_zip');

        activity()->enableLogging();
        $activity = activity()->performedOn($company);
        $activity = $activity->by($user);
        $activity->log($company->first_name . " " . $company->last_name . "'s QMS Documents zip has been created for :causer.first_name :causer.last_name");
        activity()->disableLogging();

        return $file;
    }


    public function generateForMedicalDevices(Company $company): File
    {
        $user = $company->users()->where('email', $company->email)->firstOrFail();

        $timezone = $this->getCarbonApprovedTimeZone($company->timezone);

        activity()->disableLogging();

        $zip_data = collect([]);

        $zip_data = $this->getDataForMedicalDevices($company, $timezone, $user, $zip_data);

        $file = $this->generateZip($company, $user, $zip_data, 'qms_medical_devices_zip');

        activity()->enableLogging();
        $activity = activity()->performedOn($company);
        $activity = $activity->by($user);
        $activity->log($company->first_name . " " . $company->last_name . "'s QMS Documents zip has been created for :causer.first_name :causer.last_name");
        activity()->disableLogging();

        return $file;
    }

    public function generateZip(Company $company, User $user, Collection $zip_data, $type): File
    {
        $already_generated_pdfs = $zip_data->where('payload', null);

        $instances = (new GenerateHtmlToPDF())->warmingConfig()->instances;

        // Generate PDF in lambda
        $generated_pdfs = $zip_data->where('payload', "!=", null)->chunk($instances)->flatMap(function ($items) {
            $data = $items->map(function ($item) {
                return $item['payload'];
            });

            $responses = GenerateHtmlToPDF::executeMany($data->toArray());

            return $items->map(function ($item, $index) use ($responses) {
                try {
                    $data =  array_merge($item, ['path' => $responses[$index]->data()['data']]);

                    return $data;
                } catch (\Throwable $th) {
                    Log::channel('slack')->critical("Payload",  array_merge($item['payload'], ['S3' => null, 's3' => null]));
                    Log::channel('slack')->critical($responses[$index]->errorAsString(), $responses[$index]->logs());

                    throw $th;
                }
            });
        });

        $zip_data = collect()->merge($generated_pdfs)->merge($already_generated_pdfs);

        // delete old zip files
        File::where('fileable_type', $type)->where('fileable_id', $company->id)->get()->each->delete();

        $zip_path = $this->generateFilePath($type, $user, filename_sanitizer($company->company_name), "zip");

        // Generate and store zip
        $file = $this->zipService->make($zip_data)->saveFile($user, $zip_path);

        $file->fileable_id = $company->id;
        $file->fileable_type = $type;
        $file->save();

        return $file;
    }

    private function getDataForMedicalDevices(Company $company, $timezone, User $user, Collection $zip_data)
    {
        $medical_devices = $company->medical_devices()->withTrashed()->get();

        foreach ($medical_devices as $medical_device) {
            $date = Carbon::parse($medical_device->created_at)->timezone($timezone)->format('Y-m-d G-i-s');
            $deviceName = filename_sanitizer("{$medical_device->product_name} {$date}");

            $name = filename_sanitizer("{$medical_device->product_name} {$date}");

            $path = $this->generateFilePath("company_medical_device", $user, null, "pdf");

            $zip_data->push([
                'payload' => $this->pdfService->company($company)->medical_device($medical_device)->savePayload($path),
                "delete" => true,
                'zip_path' => "Medical Devices/$deviceName/$name.pdf",
                "path" => $path,
            ]);

            if ($medical_device->upload_manual) {
                $ext = pathinfo($medical_device->getRawOriginal('upload_manual'), PATHINFO_EXTENSION);
                $name = filename_sanitizer("Upload Manual");

                $zip_data->push([
                    "delete" => false,
                    'zip_path' => "Medical Devices/$deviceName/$name.$ext",
                    "path" => $medical_device->getRawOriginal('upload_manual'),
                ]);
            }

            if ($medical_device->supplier_agreement) {
                $ext = pathinfo($medical_device->getRawOriginal('supplier_agreement'), PATHINFO_EXTENSION);
                $name = filename_sanitizer("Supplier Agreement");

                $zip_data->push([
                    "delete" => false,
                    'zip_path' => "Medical Devices/$deviceName/$name.$ext",
                    "path" => $medical_device->getRawOriginal('supplier_agreement'),
                ]);
            }

            foreach ($medical_device->maintenances as $index => $maintenance) {
                if ($maintenance->protocol_path) {
                    $date = Carbon::parse($maintenance->created_at)->timezone($timezone)->format('Y-m-d G-i-s');
                    $name = filename_sanitizer("{$maintenance->title} {$date}");
                    $ext = pathinfo($maintenance->getRawOriginal('protocol_path'), PATHINFO_EXTENSION);

                    $zip_data->push([
                        "delete" => false,
                        'zip_path' => "Medical Devices/$deviceName/Maintenance/{$name}.$ext",
                        "path" => $maintenance->getRawOriginal('protocol_path'),
                    ]);
                }
            }
        }

        return $zip_data;
    }

    private function getDataForDocuments(Company $company, $timezone, Collection $zip_data)
    {
        $documents = $company->documents()->withTrashed()->get();

        foreach ($documents as $document) {
            $date = Carbon::parse($document->created_at)->timezone($timezone)->format('Y-m-d G-i-s');
            $docName = filename_sanitizer("{$document->title} {$date}");

            foreach ($document->versions as $version) {
                $date = Carbon::parse($version->created_at)->timezone($timezone)->format('Y-m-d G-i-s');
                $name = filename_sanitizer("{$document->title} v{$version->version} {$date}");
                $ext = pathinfo($version->getRawOriginal('pdf'), PATHINFO_EXTENSION);

                $zip_data->push([
                    "delete" => false,
                    'zip_path' => "Documents/$docName/{$name}.$ext",
                    "path" => $version->getRawOriginal('pdf'),
                ]);
            }
        }

        return $zip_data;
    }
}
