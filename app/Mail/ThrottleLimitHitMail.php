<?php

namespace App\Mail;

use Illuminate\Http\Request;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ThrottleLimitHitMail extends Mailable
{
    use SerializesModels;

    public $request;

    /**
     * Create a new message instance.
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Rate Limit Reached Alert')
            ->markdown('emails.throttle');
    }
}
