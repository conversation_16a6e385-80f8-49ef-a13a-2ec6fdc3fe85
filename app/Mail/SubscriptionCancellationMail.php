<?php

namespace App\Mail;

use App\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SubscriptionCancellationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $company, $cancellations;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Company $company, $cancellations)
    {
        $this->company = $company;
        $this->cancellations = $cancellations;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $mail_data = $this
            ->from('<EMAIL>', 'MERIDIQ')
            ->subject("Subscription Cancel");

        return $mail_data->view('mails.subscription.cancel', [
            'company' => $this->company,
            'cancellations' => $this->cancellations,
        ]);
    }
}
