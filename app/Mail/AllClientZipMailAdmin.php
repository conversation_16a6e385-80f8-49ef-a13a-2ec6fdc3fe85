<?php

namespace App\Mail;

use App\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AllClientZipMailAdmin extends Mailable implements ShouldQueue
{
    //checked
    use Queueable;
    use SerializesModels;

    public $company = null;
    public $totalFiles = null;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Company $company, $totalFiles)
    {
        $this->company = $company;
        $this->totalFiles = $totalFiles;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = __('zip.all_clients.mail_title', ['company_name' => $this->company->company_name]);

        return $this->from('<EMAIL>', config('app.name'))
            ->subject($subject)
            ->view('mails.all_client_zip_admin')
            ->with([
                'name' => $this->company->company_name,
                'totalFiles' => $this->totalFiles,
            ]);
    }
}
