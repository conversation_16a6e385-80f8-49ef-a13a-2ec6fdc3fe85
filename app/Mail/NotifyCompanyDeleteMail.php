<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NotifyCompanyDeleteMail extends Mailable
{
    use Queueable, SerializesModels;
    protected $company, $user, $left_days;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($company, $user, $left_days)
    {
        $this->company = $company;
        $this->user = $user;
        $this->left_days = $left_days;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'MERIDIQ')
            ->subject(__("strings.company_delete_email.mail_subject"))
            ->view(
                "mails.notify_company_delete_mail",
                [
                    'company' => $this->company,
                    'user' => $this->user,
                    'left_days' => $this->left_days
                ]
            );
    }
}
