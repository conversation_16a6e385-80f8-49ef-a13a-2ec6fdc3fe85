<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CompanyDeleteMail extends Mailable
{
    use Queueable, SerializesModels;
    protected $company, $user;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($company, $user)
    {
        $this->company = $company;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'MERIDIQ')
            ->subject(__("strings.company_delete_email.mail_subject_1"))
            ->view(
                "mails.company_delete_mail",
                [
                    'company' => $this->company,
                    'user' => $this->user
                ]
            );
    }
}
