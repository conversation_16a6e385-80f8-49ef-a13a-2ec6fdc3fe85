<?php

namespace App\Mail;

use App\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class QMSZipMailAdmin extends Mailable implements ShouldQueue
{
    //checked
    use Queueable;
    use SerializesModels;

    public $company = null;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = __('zip.zip_qms.mail_title', ['company_name' => $this->company->company_name]);

        return $this->from('<EMAIL>', config('app.name'))
            ->subject($subject)
            ->view('mails.qms_zip_admin')
            ->with([
                'name' => $this->company->company_name,
            ]);
    }
}
