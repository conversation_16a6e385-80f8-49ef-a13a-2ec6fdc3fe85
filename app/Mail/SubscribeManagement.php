<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SubscribeManagement extends Mailable
{
    use Queueable, SerializesModels;

    public $company, $user;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($company, $user)
    {
        $this->company = $company;
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $mail_data = $this
            ->from('<EMAIL>', 'MERIDIQ')
            ->subject("Subscription Management");

        return $mail_data->view('mails.subscription.management', [
            'company' => $this->company,
            'user' => $this->user
        ]);
    }
}