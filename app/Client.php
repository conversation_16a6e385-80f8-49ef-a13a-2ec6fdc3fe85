<?php

namespace App;

use Aj<PERSON><PERSON><PERSON>\EagerLoadPivotRelations\EagerLoadPivotTrait;
use App\Traits\Encryptable;
use App\Traits\GeneratePDF;
use App\Traits\GetEncryptedFile;
use App\Traits\HasFortnox;
use App\Traits\LogsActivity;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class Client extends Model
{
    use Encryptable;
    use LogsActivity;
    use GetEncryptedFile;
    use GeneratePDF;
    use EagerLoadPivotTrait;
    use HasFortnox;

    protected $encrypted = [
        'profile_picture',
        'first_name',
        'last_name',
        'social_security_number',
        'email',
        'phone_number',
        'occupation',
        'personal_id',
        'country_code',
        'important_note',
    ];

    protected $fillable = [
        'profile_picture',
        'first_name',
        'last_name',
        'social_security_number',
        'email',
        'user_id',
        'company_id',
        'phone_number',
        'occupation',
        'deleted_at',
        'cpr_id',
        'personal_id',
        'country_code',
        'viewed_at',
        'is_personal_id_verified',
        'important_note',
        'cancel_marketing_at',
        'fortnox_customer_id',
    ];

    protected $with = [
        'addresses',
        // 'general_notes',
        // 'letter_of_consents',
        // 'treatments',
        // 'general_questions',
        // 'health_questionaries',
        // 'aesthetic_insterests',
        // 'covid19s',
        'verification',
    ];

    protected $appends = [];


    const CLIENT_FILTER_TYPES = [
        'joined',
        'service',
        'booking',
        'letter_of_consent',
        'questionary',
        'upcoming_birthday',
        'age',
        'has_mobile_number',
        'search',
    ];

    const CLIENT_FILTER_KEYS = [
        "day",
        "month",
        "year",
        "week",
        "above",
        "below",
        "equal",
        "id",
    ];

    public function company()
    {
        return $this->belongsTo('App\Company', 'company_id', 'id');
    }

    public function addresses()
    {
        return $this->hasMany('App\ClientAddress');
    }

    public function consent()
    {
        return $this->hasOne(ClientConsent::class)->latest();
    }

    public function accesses()
    {
        return $this->belongsToMany('App\User', 'user_client');
    }

    public function media()
    {
        return $this->hasOne(ClientMedia::class);
    }

    public function verification()
    {
        return $this->hasOne(ClientVerification::class);
    }

    public function extra_fields()
    {
        return $this->hasMany(ClientExtraField::class);
    }

    public function kind()
    {
        return $this->hasOne(ClientKind::class);
    }

    public function bookings()
    {
        return $this->hasMany(CompanyBooking::class, 'client_id', 'id')->where('is_verified', 1);
    }

    public function group_bookings()
    {
        return $this->hasMany(CompanyBookingClient::class, 'client_id', 'id')->where('is_verified', 1);
    }

    public function booking()
    {
        return $this->hasOne(CompanyBooking::class, 'client_id', 'id')->where('is_verified', 1);
    }

    public function group_booking()
    {
        return $this->hasOne(CompanyBookingClient::class, 'client_id', 'id')->where('is_verified', 1);
    }

    public function letter_of_consents()
    {
        if (request()->has('orderBy')) {
            return $this->hasMany('App\ClientLetterOfConsent')->with('verified_signed_by');
        }

        return $this->hasMany('App\ClientLetterOfConsent')->latest()->with('verified_signed_by');
    }

    public function treatments()
    {
        return $this->hasMany('App\ClientTreatment')->latest()->with('details', 'signed_by')->with(['user' => function ($query) {
            $query->without(['company']);
        }]);
    }

    public function file()
    {
        return $this->morphOne('App\File', 'fileable')->latest();
    }

    public function zip_file()
    {
        return $this->hasOne(File::class, 'fileable_id')->latest()->where('fileable_type', 'client_zip');
    }

    public function general_questions()
    {
        return $this->hasMany('App\GeneralQuestion');
    }

    public function health_questionaries()
    {
        return $this->hasMany('App\HealthQuestionary');
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function fullName()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getPrettyAddressAttribute()
    {
        // pretty_address
        $address = $this->addresses && isset($this->addresses[0]) ? $this->addresses[0] : null;
        if (!$address) {
            return '';
        }

        $arr = collect([$address->street_address, $address->city, $address->zip_code, $address->state, $address->country]);

        return $arr->filter(fn($val) => $val)->join(', ');
    }

    public function getCountryAttribute()
    {
        // pretty_address
        $address = $this->addresses && isset($this->addresses[0]) ? $this->addresses[0] : null;
        if (!$address) {
            return '';
        }

        return $address->country;
    }

    public function covid19s()
    {
        return $this->hasMany('App\Covid19');
    }

    public function aesthetic_insterests()
    {
        return $this->hasMany('App\AestheticInterest');
    }

    public function questionary_data()
    {
        return $this->hasMany(QuestionaryData::class);
    }

    public function questionaries()
    {
        return $this->hasManyThrough(
            Questionary::class,
            Company::class,
            'id',
            'company_id',
            'company_id',
            'id'
        );
    }

    public function getProfilePictureAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), '', (string) $this->getS3SignedUrl(Crypt::decrypt($value)));

            return Crypt::encrypt($data);
        }

        return $value;
    }

    public function general_notes()
    {
        if (request()->has('orderBy')) {
            return $this->hasMany(GeneralNote::class)->with('signed_by');
        }

        return $this->hasMany(GeneralNote::class)->latest()->with('signed_by');
    }

    public function prescriptions()
    {
        return $this->hasMany(ClientPrescription::class)
            // ->select('*', DB::raw('((signed_at IS NULL)) as is_inactive'))
            // ->havingRaw('is_inactive = 0')
        ;
    }

    public function all_prescriptions()
    {
        return $this->hasMany(ClientPrescription::class);
    }

    public function receipts()
    {
        return $this->hasMany(CompanyReceipt::class, 'client_id');
    }
    public function scopeMarketingSubscribed($query)
    {
        return $query->where('cancel_marketing_at', null);
    }

    protected static $logAttributes = [];

    protected static $logName = 'client';

    public function getDescriptionForEvent(string $eventName): string
    {
        return ":subject.first_name :subject.last_name client has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public static function downloadPDF(Client $client, $is_twelve_hours)
    {
        $client = $client->append(['pretty_address', 'full_name'])->loadMissing(['kind']);

        return self::downloadFromView('exports.client.client_info', [
            'client' => $client,
            'is_twelve_hours' => $is_twelve_hours,
            'company' => $client->company,
            'city' => $client->country_code ? config('phone_country')[$client->country_code] : '',
        ]);
    }

    public function storeZIP()
    {
        $file_name = '';
    }

    public function zip() {}

    public function receipts_used_gift_card()
    {
        return $this->hasManyThrough(CompanyReceipt::class, CompanyGiftCard::class, 'id', 'client_id', 'id', 'id');
    }

    function getReceiptsUsedGiftCardAmountAttribute()
    {
        return $this->receipts_used_gift_card()->get()->sum('gift_card_amount');
    }

    public function phone()
    {
        if (!$this->phone_number) return null;

        return "+{$this->country_code}{$this->phone_number}";
    }

    function dateOfBirthFormatted($company)
    {
        try {
            $format = Setting::getDateTimeFormat($company, false, false);

            $date_of_birth = null;
            if (!$date_of_birth && !!$this->personal_id && $this->personal_id != "null") {
                $personal_id = explode('-', $this->personal_id)[0];
                $date_of_birth = Carbon::createFromFormat('Ymd', $personal_id);
            }
            if (!$date_of_birth && !!$this->cpr_id && $this->cpr_id != "null") {
                $today = Carbon::today();

                $parts = explode('-', $this->cpr_id);
                $date = Carbon::createFromFormat('dmy', $parts[0]);

                if ($date->year > $today->year) {
                    $date->subYears(100);
                }

                $date_of_birth = $date->format('Y-m-d');
            }

            if (!$date_of_birth && !!$this->social_security_number && $this->social_security_number != "null") {
                $date_of_birth = $this->social_security_number;
            }

            if (!$date_of_birth) {
                return "";
            }

            return Carbon::parse($date_of_birth)->format($format);
        } catch (\Throwable $th) {
            return '';
        }
    }

    function getDateOfBirthAttribute()
    {
        try {
            if ($this->personal_id) {
                // Extract the YYYYMMDD part
                $dobPart = substr($this->personal_id, 0, 8);

                // Use Carbon to format it as YYYY-MM-DD
                return Carbon::createFromFormat('Ymd', $dobPart)->format('Y-m-d');
            }

            if ($this->cpr_id) {
                // Extract the DDMMYY part
                $dobPart = substr($this->cpr_id, 0, 6);

                // Split into day, month, and year
                $day = substr($dobPart, 0, 2);
                $month = substr($dobPart, 2, 2);
                $year = substr($dobPart, 4, 2);

                // Determine century (Assuming people born after 1900 and before 2100)
                $currentYear = now()->format('Y');
                $currentCentury = substr($currentYear, 0, 2);

                // Convert to full year (e.g. '99' becomes '1999', '01' becomes '2001')
                $fullYear = $year >= substr($currentYear, 2, 2) ? $currentCentury - 1 . $year : $currentCentury . $year;

                // Construct the date of birth
                return Carbon::createFromFormat('Y-m-d', "$fullYear-$month-$day")->format('Y-m-d');
            }


            if ($this->social_security_number) {
                return Carbon::parse($this->social_security_number)->format('Y-m-d');
            }

            return '';
        } catch (\Throwable $th) {
            return '';
        }
    }

    public function unsubscribeMarketing()
    {
        $this->update(['cancel_marketing_at' => now()]);
    }

    public function unsubscribeMarketingUrl()
    {
        $url = URL::signedRoute('marketing.unsubscribe', ['id' => $this->id], null, false);

        $url = str_replace("api/", "", $url);

        $baseUrl = config('app.frontend_url');

        $url = $baseUrl . $url;

        return $url;
    }
}
