<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AfterCareData extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'clients_after_cares_data';

    //FILLABLES
    protected $fillable = [
        'client_after_care_id',
        'aftercarable_id',
        'aftercarable_type',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function client_after_care()
    {
        return $this->belongsTo('App\ClientAfterCare');
    }

    // public function letter_of_consents()
    // {
    //     return $this->morphTo('aftercarable');
    // }

    public function aftercarable()
    {
        return $this->morphTo();
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
