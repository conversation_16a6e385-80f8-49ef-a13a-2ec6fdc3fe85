<?php

namespace App\Console\Commands\Subscription;

use App\Models\Cashier\SubscriptionItem;
use Illuminate\Console\Command;

class HandleCancelSubscription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle:cancel:subscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel subscription at scheduled time';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start = now()->subMinutes(5);
        $end = now();

        $subscription_items = SubscriptionItem::where('cancel_at', '>=', $start)->where('cancel_at', '<=', $end)->cursor();

        foreach ($subscription_items as $key => $item) {
            $subscription = $item->subscription;

            if ($subscription->valid()) {
                $items = $subscription->items;

                if (count($items) == 1) {
                    $subscription->cancel();

                    continue;
                }

                $item->asStripeSubscriptionItem()->delete();

                $item->delete();
            }
        }
    }
}
