<?php

namespace App\Console\Commands;

use App\Plan;
use Illuminate\Console\Command;

class CreateYearlyPlans extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:plan:yearly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to create a yearly plan';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $testing_plan_keys = [
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MtmcvCBmBWVCuBdiBiUtfhv',
                'cost' => '230€ / per year',
                'description' => 'Yearly plan for €',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '230.00',
                'is_free' => 0,
                'currency' => '€',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MtmW2CBmBWVCuBd6TMu6VjN',
                'cost' => '2390SEK / per year',
                'description' => 'Yearly plan for SEK',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '2390.00',
                'is_free' => 0,
                'currency' => 'kr',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1MtmYZCBmBWVCuBdVY7mX1LR',
                'cost' => '211£ / per year',
                'description' => 'Yearly plan for £',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '211.00',
                'is_free' => 0,
                'currency' => '£',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1Mtma4CBmBWVCuBdtnwqH9NF',
                'cost' => '250$ / per year',
                'description' => 'Yearly plan for $',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '250.00',
                'is_free' => 0,
                'currency' => '$',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
        ];
        $live_plan_keys = [
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1N5SVoCBmBWVCuBd3bQAhxRZ',
                'cost' => '230€ / per year',
                'description' => 'Yearly plan for €',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '230.00',
                'is_free' => 0,
                'currency' => '€',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1N5STuCBmBWVCuBdjsDVdbqD',
                'cost' => '2390SEK / per year',
                'description' => 'Yearly plan for SEK',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '2390.00',
                'is_free' => 0,
                'currency' => 'kr',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1N5SUGCBmBWVCuBd01qyCBh8',
                'cost' => '211£ / per year',
                'description' => 'Yearly plan for £',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '211.00',
                'is_free' => 0,
                'currency' => '£',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
            [
                'name' => 'Licensed',
                'subscription_id' => 'Subscription',
                'plan_id' => 'price_1N5SUfCBmBWVCuBdFBAKevfy',
                'cost' => '250$ / per year',
                'description' => 'Yearly plan for $',
                'users' => '1',
                'client' => '100000',
                'storage' => '1',
                'cost_value' => '250.00',
                'is_free' => 0,
                'currency' => '$',
                'is_2022' => 1,
                'is_yearly' => 1,
            ],
        ];

        $plans_to_create = [];
        if (env('APP_ENV') == 'production') {
            $plans_to_create = $live_plan_keys;
        } else {
            $plans_to_create = $testing_plan_keys;
        }

        foreach ($plans_to_create as $plan) {
            Plan::create([
                'name' => $plan['name'],
                'subscription_id' => $plan['subscription_id'],
                'plan_id' => $plan['plan_id'],
                'cost' => $plan['cost'],
                'description' => $plan['description'],
                'users' => $plan['users'],
                'client' => $plan['client'],
                'storage' => $plan['storage'],
                'cost_value' => $plan['cost_value'],
                'is_free' => $plan['is_free'],
                'currency' => $plan['currency'],
                'is_2022' => $plan['is_2022'],
                'is_yearly' => $plan['is_yearly']
            ]);
        }
    }
}
