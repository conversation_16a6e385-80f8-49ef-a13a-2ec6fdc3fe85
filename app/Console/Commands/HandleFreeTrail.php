<?php

namespace App\Console\Commands;

use App\Company;
use App\Contracts\Services\Subscription\SubscriptionServiceInterface;
use Illuminate\Console\Command;

class HandleFreeTrail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle:free:trail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to handle free trail';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $subscriptionService = app(SubscriptionServiceInterface::class);

        $companies_to_finish = Company::where(function ($query) {
            return $query->where('is_free_trail_finished', false)
                ->where('free_trail_end_date', '<=', now());
        })->orWhere(function ($query) {
            return $query->whereHas('future_subscriptions', function ($query) {
                return $query->where('start_datetime', '<=', now());
            });
        })->cursor();


        $this->info('Companies to finish free trial: ' . count($companies_to_finish));

        foreach ($companies_to_finish as $company) {
            $this->info("Company: {$company->id}");
            $subscriptionService->subscribeCompleteFreeTrial($company, false);
        }
    }
}
