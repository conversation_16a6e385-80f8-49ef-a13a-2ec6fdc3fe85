<?php

namespace App\Console\Commands;

use App\Company;
use App\Setting;
use Illuminate\Console\Command;

class SetShowLOCIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'set:loc:ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companies = Company::whereHas('settings', function ($query) {
            return $query->where('key', Setting::SHOW_LETTER_OF_CONSENT)->where('value', true);
        })->cursor();

        foreach ($companies as $company) {
            Setting::updateOrCreate([
                'company_id' => $company->id,
                'key' => Setting::SHOW_LETTER_OF_CONSENT_IDS,
            ], [
                'value' => json_encode($company->letterOfConsents()->pluck('id')->map(fn($a) => (string) $a)->values()->toArray()),
            ]);
        }
    }
}
