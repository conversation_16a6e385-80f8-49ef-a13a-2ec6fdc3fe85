<?php

namespace App\Console\Commands;

use App\Company;
use App\Models\CompanyLegalDocument;
use Illuminate\Console\Command;

class AddLegalDocuments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:legal:doc';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'command to make legal documents';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companies = Company::doesntHave('legal_documents')->limit(1000)->cursor();

        foreach ($companies as $company) {
            try {
                $legal_document = app(CompanyLegalDocument::class);
                $legal_document->generateLegalDocument($company);
            } catch (\Throwable $th) {
                //throw $th;
            }
        }
    }
}
