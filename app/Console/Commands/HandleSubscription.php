<?php

namespace App\Console\Commands;

use App\SubscriptionCancellationData;
use Carbon\Carbon;
use Illuminate\Console\Command;

class HandleSubscription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle:subscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle subscriptions';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $subscription_cancellation_data = SubscriptionCancellationData::where('should_delete_data', 1)
            ->where('is_data_deleted', 0)
            ->where('data_deletion_date', Carbon::now()->format('Y-m-d'))
            ->cursor();
        foreach ($subscription_cancellation_data as $subscription_cancellation) {
            $company = $subscription_cancellation->company;
            if (!$subscription_cancellation->is_from_free_trail) {
                if ($subscription_cancellation->subscription) {
                    $company = $subscription_cancellation->subscription->company;
                }
                $active_subscription =  $company->activeSubscription();
                if ($active_subscription) {
                    if ($active_subscription->id != $subscription_cancellation->subscription_id) {
                        $subscription_cancellation->should_delete_data = 0;
                        $subscription_cancellation->save();
                        return;
                    }
                }
            }
            $subscription_cancellation->is_data_deleted = 1;
            $subscription_cancellation->save();
            $company->devices()->delete();
            $company->delete();
        }
    }
}
