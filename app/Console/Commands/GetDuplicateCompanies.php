<?php

namespace App\Console\Commands;

use App\Company;
use Illuminate\Console\Command;

class GetDuplicateCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:duplicate:company {start_id} {end_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to get duplicate companies';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companies_to_watch = [];
        $companies = Company::where('id', '>=', $this->argument('start_id'))->where('id', '<=', $this->argument('end_id'))->cursor();

        $clients = [];
        foreach ($companies as $company) {
            $clients = $company->clients()->get()->groupBy('personal_id')->filter(function ($groups) {
                return $groups->count() > 1;
            });
            if (count($clients) > 1) {
                array_push($companies_to_watch, $company->email);
            }
        }
        dd($companies_to_watch);
    }
}
