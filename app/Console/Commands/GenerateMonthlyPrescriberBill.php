<?php

namespace App\Console\Commands;

use App\CompanyPaymentMethod;
use App\Traits\CustomInvoiceManager;
use App\User;
use App\UserBilling;
use App\UserCompanyBilling;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Laravel\Cashier\Exceptions\PaymentActionRequired;
use Stripe\Exception\CardException;

class GenerateMonthlyPrescriberBill extends Command
{
    use CustomInvoiceManager;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:prescriber:bill:monthly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to Payment of Prescriber Monthly';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start_date = Carbon::now()->subMonth()->startOfMonth();
        $end_date = Carbon::now()->subMonth()->endOfMonth();
        $users = User::withSum(['bills as revenue' => function ($query) use ($start_date, $end_date) {
            $query->where('payment_status', UserBilling::UPCOMING)
                ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')]);
        }], 'earning')->get();
        if (count($users) > 0) {
            foreach ($users as $user) {
                if ($user->revenue) {
                    // $fees = $user->revenue;
                    $company = $user->company;
                    if ($company->hasDefaultPaymentMethod()) {
                        $company->createOrGetStripeCustomer();
                        // $paymentMethod = $company->defaultPaymentMethod();
                        // $paymentMethod = CompanyPaymentMethod::getPaymentMethodByLabel($company,CompanyPaymentMethod::MAL);
                        try {
                            $user_signed_prescription = UserCompanyBilling::where('user_id', $user->id)->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])->sum('signed_prescription_count');
                            $price_id = config('prescription.price_id');

                            $user_billing = UserBilling::where('user_id', $user->id)
                                ->where('payment_status', UserBilling::UPCOMING)
                                ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])
                                ->first();
                            if ($user_billing && $user_billing->price_id) {
                                $price_id = $user_billing->price_id;
                            }
                            $invoice = null;
                            $default_taxes = $company->subscriptionTaxRates();
                            $invoice = CustomInvoiceManager::generateInvoice($company->stripe_id, $price_id, $default_taxes, $user_signed_prescription);
                            if ($invoice) {
                                $paymentMethod = CompanyPaymentMethod::getPaymentMethodByLabel($company, CompanyPaymentMethod::MAL);
                                $invoice = CustomInvoiceManager::payInvoice($invoice->id, $paymentMethod->payment_method_id);
                            }
                            if ($invoice) {
                                $tax = $invoice->tax * 0.01;
                                UserBilling::where('user_id', $user->id)
                                    ->where('payment_status', UserBilling::UPCOMING)
                                    ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])
                                    ->update([
                                        'payment_status' => UserBilling::PAID,
                                        'stripe_payment_id' => $invoice->id,
                                        'invoice_vat' => $tax
                                    ]);
                            }
                        } catch (CardException $e) {
                            // Handle card exception (e.g., insufficient funds, card declined)
                            $paymentIntentId = $e->getError()->payment_intent;
                            UserBilling::where('user_id', $user->id)
                                ->where('payment_status', UserBilling::UPCOMING)
                                ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])
                                ->update([
                                    'payment_status' => UserBilling::UNPAID,
                                    'stripe_payment_id' => $paymentIntentId->id
                                ]);
                        } catch (\Exception $e) {
                            UserBilling::where('user_id', $user->id)
                                ->where('payment_status', UserBilling::UPCOMING)
                                ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])
                                ->update([
                                    'payment_status' => UserBilling::UNPAID,
                                ]);
                        }
                    } else {
                        UserBilling::where('user_id', $user->id)
                            ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])
                            ->update([
                                'payment_status' => UserBilling::UNPAID,
                            ]);
                    }
                }
            }
        }
    }
}
