<?php

namespace App\Console\Commands;

use App\File;
use Illuminate\Console\Command;

class ClientZipDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'client_zip:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete\'s client\'s generated ZIP files, if 48 hours passed.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $expire_date = now()->subHours(48);
        $zip_files = File::where('fileable_type', 'client_zip')->whereDate('created_at', '<', $expire_date);

        $zip_files->delete();

        return 1;
    }
}
