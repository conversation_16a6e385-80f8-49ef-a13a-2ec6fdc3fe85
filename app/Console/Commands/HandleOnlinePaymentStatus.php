<?php

namespace App\Console\Commands;

use App\CompanyReceipt;
use App\Contracts\Services\POS\PaymentServiceInterface;
use App\Contracts\Services\POS\ReceiptServiceInterface;
use Illuminate\Console\Command;

class HandleOnlinePaymentStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle:online:payment:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify online payment status of receipt.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $receipts = CompanyReceipt::whereIn('status', [CompanyReceipt::PENDING, CompanyReceipt::PROCESSING])
            ->where('payment_method', CompanyReceipt::PAYMENT_METHOD_VIVA_ONLINE)
            ->lazy();

        $receiptService = app(ReceiptServiceInterface::class);

        foreach ($receipts as $key => $receipt) {
            $receiptService->updatePaymentStatus($receipt);
        }
    }
}
