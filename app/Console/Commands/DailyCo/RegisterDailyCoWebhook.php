<?php

namespace App\Console\Commands\DailyCo;

use App\Http\Integrations\DailyCo\Enums\WebhookEventType;
use App\Http\Integrations\DailyCo\Facades\DailyCo;
use Illuminate\Console\Command;

class RegisterDailyCoWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dailyco:webhook {--url=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'register webhook with dailyco';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $url = $this->option('url');

        $this->info($url);

        $data = DailyCo::webhook()->list();

        foreach ($data as $webhook) {
            DailyCo::webhook()->delete($webhook->uuid);
        }

        $res = DailyCo::webhook()->create($url, [
            WebhookEventType::MeetingStarted,
            WebhookEventType::MeetingEnded,
            WebhookEventType::ParticipantJoined,
            WebhookEventType::ParticipantLeft,
        ]);

        $this->info('Webhook registered');
    }
}
