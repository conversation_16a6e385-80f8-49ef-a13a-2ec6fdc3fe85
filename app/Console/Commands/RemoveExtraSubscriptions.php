<?php

namespace App\Console\Commands;

use App\Models\Cashier\Subscription;
use App\Models\Cashier\SubscriptionItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveExtraSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'remove:extra-subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove extra subscriptions from the database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $stripe_id_data = Subscription::select('stripe_id', DB::raw('COUNT(*) as `count`'))->groupBy('stripe_id')->havingRaw('COUNT(*) > 1')->get();

        foreach ($stripe_id_data as $data) {
            $subscriptions = Subscription::where('stripe_id', $data->stripe_id)->get();

            $sub = $subscriptions->where('name', 'default')->first() ?? $subscriptions->first();

            if ($sub) {
                $sub->delete();
            }
        }

        $items = SubscriptionItem::doesntHave('subscription')->cursor();

        $items->each->delete();

        $this->line('Extra subscriptions removed successfully!');

        return 0;
    }
}
