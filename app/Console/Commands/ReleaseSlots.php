<?php

namespace App\Console\Commands;

use App\CompanyBooking;
use App\CompanyBookingClient;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ReleaseSlots extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'release:slots';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to release the slots';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //TODO::check for booking that's been paid for(don't delete those)
        CompanyBooking::where('slot_released_at', '<=', Carbon::now())->doesntHave('receipt')->lazy()->each->delete();
        $bookingClients = CompanyBookingClient::where('slot_released_at', '<=', Carbon::now())->doesntHave('receipt')->lazy();

        foreach ($bookingClients as $key => $bookingClient) {
            if ($bookingClient->booking->clients()->count() <= 1) {
                $bookingClient->delete();
                $bookingClient->booking->delete();
            } else {
                $bookingClient->delete();
            }
        }
    }
}