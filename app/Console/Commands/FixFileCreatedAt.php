<?php

namespace App\Console\Commands;

use App\File;
use Illuminate\Console\Command;

class FixFileCreatedAt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:file:created_at';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix file created at';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $files = File::where('created_at', null)->lazy();

        foreach ($files as $file) {
            $file->created_at = $file->updated_at ?? now();
            $file->save();
        }

        return 0;
    }
}
