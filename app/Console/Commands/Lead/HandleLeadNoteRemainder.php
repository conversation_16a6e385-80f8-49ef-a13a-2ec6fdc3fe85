<?php

namespace App\Console\Commands\Lead;


use App\Models\LeadNote;
use App\User;
use App\UserNotification;
use Illuminate\Console\Command;

class HandleLeadNoteRemainder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leadnote:remainder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'lead note remainder';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $current_date = now('Europe/Stockholm')->startOfMinute();

        $lead_notes = LeadNote::where('remind_at', '<=', $current_date)->where('remind_at', '>=', $current_date->subMinute())->cursor();

        $user = User::where('email', '<EMAIL>')->first();

        foreach ($lead_notes as $key => $note) {
            if ($user) {
                UserNotification::create([
                    'user_id' => $user->id,
                    'title' => $note->title,
                    'description' => $note->note,
                    'is_read' => 0
                ]);
            }
        }
    }
}
