<?php

namespace App\Console\Commands;

use App\ClientPrescription;
use App\Mail\CompanyReportMail;
use App\Setting;
use App\UserCompanyBilling;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class GenerateMonthlyCompanyReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:company:report:monthly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to Generate And Send Report of Company Monthly';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start_date = Carbon::now()->subMonth()->startOfMonth();
        $end_date = Carbon::now()->subMonth()->endOfMonth();
        $user_company_billings = UserCompanyBilling::with(['user' => function ($query) {
            $query->setEagerLoads([]);
        }])
            ->where(['start_date' => $start_date->format('Y-m-d'), 'end_date' => $end_date->format('Y-m-d')])
            ->get();

        foreach ($user_company_billings as $user_company_billing) {
            $prescriptions = ClientPrescription::where(['sign_by_id' => $user_company_billing->user->id])
                ->whereHas('client', function ($query) use ($user_company_billing) {
                    $query = $query->where('clients.company_id', $user_company_billing->company_id);
                })
                ->with(['client' => function ($query) {
                    $query->setEagerLoads([]);
                }])
                ->whereBetween('signed_at', [Carbon::parse($user_company_billing->start_date)->startOfDay(), Carbon::parse($user_company_billing->end_date)->endOfDay()])
                ->get();

            $from_photo = $user_company_billing->company->profile_photo ? substr(Storage::url('/'), 0, -1) . $user_company_billing->company->profile_photo : '';
            $pdf = Pdf::loadView('pdf.prescription_report', [
                "from" => $user_company_billing->company,
                "from_sign" => $user_company_billing->company->mobile_number ? '+' : '',
                "from_photo" => $from_photo,
                "to" => $user_company_billing->user->company,
                "to_sign" => $user_company_billing->user->company->mobile_number ? '+' : '',
                "prescriptions" => $prescriptions,
                "currency" => "kr",
                "payment_cycle" => Carbon::parse($user_company_billing->start_date)->format('F Y'),
                "total" => $user_company_billing->earning,
            ]);
            $receive_email_reports_on = Setting::getSetting($user_company_billing->user->company, Setting::RECEIVE_EMAIL_REPORTS_ON);
            if ($receive_email_reports_on->value) {
                $language = Setting::getSetting($user_company_billing->company, Setting::CUSTOMER_LANGUAGE)?->value;
                Mail::to($receive_email_reports_on->value)->locale($language ?? app()->getLocale())->send(new CompanyReportMail($pdf, $user_company_billing->company, Carbon::parse($user_company_billing->start_date)->format('F Y')));
            }
        }
    }
}
