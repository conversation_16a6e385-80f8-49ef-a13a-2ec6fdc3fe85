<?php

namespace App\Console\Commands;

use App\CompanyBooking;
use App\Setting;
use Carbon\Carbon;
use App\Traits\TimeZoneManager;
use Illuminate\Console\Command;

class SendExtraStepMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:extra:mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to send extra steps mail';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $now = Carbon::now();
        $bookings_to_check = CompanyBooking::where('is_extra_step_reminded', 0)
            ->where('is_cancelled', 0)
            ->where('is_verified', 1)
            ->where('is_extra_steps_needed', 1)
            ->where('is_extra_step_done', 0)
            ->whereDate('start_at', '>=', $now)
            ->whereDate('start_at', '<=', Carbon::parse($now)->addDays(2))
            ->cursor();
        foreach ($bookings_to_check as $booking_to_check) {
            $company = $booking_to_check->company;
            $now = TimeZoneManager::getTimeZoneCarbonNow($company->timezone);
            $BOOKING_EXTRAS_STEPS_REMINDER = Setting::getSetting($company, Setting::BOOKING_EXTRAS_STEPS_REMINDER)->value;
            if ($booking_to_check->start_at <= Carbon::parse($now)->addMinutes($BOOKING_EXTRAS_STEPS_REMINDER)) {
                CompanyBooking::remindExtraSteps($booking_to_check);
            }
        }

        $now = Carbon::now();
        $bookings_to_check = CompanyBooking::where('is_extra_step_reminded_sms', 0)
            ->where('is_cancelled', 0)
            ->where('is_verified', 1)
            ->where('is_extra_steps_needed', 1)
            ->where('is_extra_step_done', 0)
            ->whereDate('start_at', '>=', $now)
            ->whereDate('start_at', '<=', Carbon::parse($now)->addDays(2))
            ->cursor();
        foreach ($bookings_to_check as $booking_to_check) {
            $company = $booking_to_check->company;
            $now = TimeZoneManager::getTimeZoneCarbonNow($company->timezone);
            $BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME = Setting::getSetting($company, Setting::BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME)->value;
            if ($booking_to_check->start_at <= Carbon::parse($now)->addHours($BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME)) {
                CompanyBooking::remindExtraStepsSMS($booking_to_check);
            }
        }
    }
}
