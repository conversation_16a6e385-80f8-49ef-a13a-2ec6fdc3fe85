<?php

namespace App\Console\Commands;

use App\Company;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\File;
use App\Setting;
use App\Traits\SaveFile;
use Illuminate\Console\Command;

class RegenPOSLicence extends Command
{
    use SaveFile;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'regen:pos_licence';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate POS Licence for all companies';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $companies = Company::where('viva_account_id', '!=', null)->get();

        $pdfService = app(PDFServiceInterface::class);

        foreach ($companies as $company) {
            $user = $company->users()->where('email', $company->email)->firstOrFail();

            $filename = $this->generateFilePath('companies/' . md5($company->id) . '/pos_licence', $user, null, 'pdf');

            $setting = Setting::where('key', Setting::POS_LICENSE)->where('company_id', $company->id)->first();

            $file = $pdfService->pos_licence($company, $company->timezone ?? "Europe/Stockholm", $setting?->created_at)->saveFile($user, $filename);

            Setting::updateOrCreate([
                'key' => Setting::POS_LICENSE,
                'company_id' => $company->id
            ], [
                'value' => $file->filename,
            ]);
        }

        $this->line('POS Licence regenerated successfully');

        return 0;
    }
}
