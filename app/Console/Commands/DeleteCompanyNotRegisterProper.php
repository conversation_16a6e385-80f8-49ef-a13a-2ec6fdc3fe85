<?php

namespace App\Console\Commands;

use App\Company;
use App\CompanyPlatform;
use App\Mail\CompanyDeleteMail;
use App\Mail\NotifyCompanyDeleteMail;
use App\Setting;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class DeleteCompanyNotRegisterProper extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:company:not:register:proper {--reminder_days=* : Array of days before to send reminder emails} 
                          {--start_date= : Start date for processing (Y-m-d format)} 
                          {--delete_after= : Number of days after which to delete users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'this command will remove Users/Company who has not subscribe to any plan nd  not added card information';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $reminderDays = $this->option('reminder_days'); // 30 , 53
        $startDate = $this->option('start_date');
        $deleteAfter = (int) $this->option('delete_after'); //60

        if (empty($reminderDays)) {
            $this->error('Please provide at least one reminder day');
        }

        if (!$startDate) {
            $this->error('Please provide the start date');
        }

        if (!$deleteAfter) {
            $this->error('Please provide the number of days after which to delete users');
        }

        $startDate = Carbon::parse($this->option('start_date'))->startOfDay();
        $now = Carbon::now();
        foreach ($reminderDays as $reminderDay) {
            $beforeDate = Carbon::now()->subDays($reminderDay);

            $companies = Company::has('clients', '<=', 1)
                ->has('subscriptions', '<=', 0)
                ->has('bookings', '<=', 0)
                ->whereHas('platforms', function ($query) {
                    $query->where('platform',  CompanyPlatform::PRESCRIPTION);
                }, '<=', 0)
                ->where('email', '!=', '<EMAIL>')
                ->whereNull('stripe_id')
                ->whereDate('created_at', '<=', $beforeDate->format('Y-m-d'))
                ->cursor();
            if ($beforeDate->gte($startDate)) {
                foreach ($companies as $company) {
                    if ($company->created_at <= $startDate) {
                        $company->created_at = $startDate;
                    }
                    if ($company->created_at->diffInDays($now) == $reminderDay) {
                        $user = $company->super_user;
                        $left_days = $deleteAfter - $reminderDay;
                        Mail::to($company->email)->locale($language ?? app()->getLocale())->send(new NotifyCompanyDeleteMail($company, $user, $left_days));
                    }
                }
            }
        }

        $beforeDate = Carbon::now()->subDays($deleteAfter);

        $companies = Company::has('clients', '<=', 1)
            ->has('subscriptions', '<=', 0)
            ->has('bookings', '<=', 0)
            ->whereHas('platforms', function ($query) {
                $query->where('platform',  CompanyPlatform::PRESCRIPTION);
            }, '<=', 0)
            ->where('email', '!=', '<EMAIL>')
            ->whereNull('stripe_id')
            ->whereDate('created_at', '<=', $beforeDate->format('Y-m-d'))
            ->cursor();
        foreach ($companies as $company) {
            if ($company->created_at <= $startDate) {
                $company->created_at = $startDate;
            }
            if ($company->created_at->diffInDays($now) >= $deleteAfter) {
                $delete_company = Setting::getSetting($company, Setting::DELETE_COMPANY)->value;
                if ($delete_company) {
                    $user = $company->super_user;
                    Mail::to($company->email)->locale($language ?? app()->getLocale())->send(new CompanyDeleteMail($company, $user));
                    try {
                        $company->devices()->delete();
                    } catch (\Throwable $th) {
                        report($th);
                    }

                    try {
                        $company->delete();
                    } catch (\Throwable $th) {
                        report($th);
                    }
                }
            }
        }
    }
}
