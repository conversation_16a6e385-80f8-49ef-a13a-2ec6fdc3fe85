<?php

namespace App\Console\Commands\GooglePlaces;

use App\Models\CompanyExtraField;
use Illuminate\Console\Command;

class HandleUpdateGoogleRating extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:google-rating';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Google Rating';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Updating Google Rating');

        $companyExtraFields = CompanyExtraField::whereNotNull('place_id')->cursor();

        foreach ($companyExtraFields as $companyExtraField) {
            try {
                $companyExtraField->updateGoogleRating();
            } catch (\Throwable $th) {
                report($th);
            }
        }

        $this->info('Google Rating Updated');

        return 0;
    }
}
