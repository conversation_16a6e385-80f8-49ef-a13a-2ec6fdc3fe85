<?php

namespace App\Console\Commands\Booking;

use App\Company;
use App\CompanyBooking;
use App\Setting;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendFollowUpMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'booking:send:followup:mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'command to send follow up mail';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $now = Carbon::now();


        $companies = Company::query();

        $companies = $companies->whereHas('bookings', function ($query) use ($now) {
            $query->where('is_follow_up_email_sent', 0)
                ->where('is_cancelled', 0)
                ->where('is_verified', 1)
                ->whereHas('service', function ($query) {
                    $query->whereNotNull('finished_email_template_id');
                })
                ->whereDate('end_at', '<=', $now);
        })->cursor();

        foreach ($companies as $company) {
            $days_to_wait =  Setting::getSetting($company, Setting::FOLLOW_UP_REMINDER_EMAIL_DAYS)->value;

            if ($days_to_wait == null) {
                continue;
            }
            $start_time_to_check_with =  Carbon::now($company->timezone)->subDays($days_to_wait)->subHours(2);
            $end_time_to_check_with =  Carbon::now($company->timezone)->subDays($days_to_wait)->addHours(2);

            $company_bookings =  CompanyBooking::where('is_follow_up_email_sent', 0)
                ->where('company_id', $company->id)
                ->where('is_cancelled', 0)
                ->where('is_verified', 1)
                ->whereHas('service', function ($query) {
                    $query->whereNotNull('finished_email_template_id');
                })
                ->where('end_at', '>=', $start_time_to_check_with)
                ->where('end_at', '<=', $end_time_to_check_with)
                ->get();

            foreach ($company_bookings as $booking) {
                CompanyBooking::completeBookingEmail($booking);
            }
        }
    }
}
