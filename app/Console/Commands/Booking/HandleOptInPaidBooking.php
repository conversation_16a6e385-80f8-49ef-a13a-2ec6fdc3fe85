<?php

namespace App\Console\Commands\Booking;

use App\Company;
use App\CompanyPlatform;
use App\Contracts\Services\Subscription\SubscriptionServiceInterface;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class HandleOptInPaidBooking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'optin:paid:booking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Booking handle opt in paid booking subscription';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(SubscriptionServiceInterface $subscriptionService)
    {
        $companies = Company::whereHas('settings', function ($query) {
            $query->where('key', 'BOOKING_PLAN')->where('value', 'OPT_IN');
        })->get();

        foreach ($companies as $company) {

            try {
                $booking_users = $company->users()->forBooking()->count();

                if ($booking_users > 0 && !$company->is_booking_on) {
                    $company_pos_plan = $company->pos_plan ?? null;
                    $company_record_plan = $company->record_plan ?? null;
                    $company_management_plan = $company->management_plan ?? null;

                    $plan_cycle = $company_pos_plan?->is_yearly || $company_record_plan?->is_yearly || $company_management_plan?->is_yearly ? 'yearly' : 'monthly';
                    $booking_plan = $company->plans($plan_cycle == 'yearly' ? true : false)->where('platform', CompanyPlatform::BOOKING_SYSTEM)->first();

                    $plans = [];

                    if ($company_pos_plan) {
                        array_push($plans, $company_pos_plan->stripe_id);
                    }

                    if ($company_record_plan) {
                        array_push($plans, $company_record_plan->stripe_id);
                    }

                    if ($company_management_plan) {
                        array_push($plans, $company_management_plan->stripe_id);
                    }

                    if ($booking_plan) {
                        array_push($plans, $booking_plan->stripe_id);
                    }

                    $request = (new Request())->merge([
                        "plans" => $plans,
                        "users" => $company?->record_plan?->users ?? 1,
                        "type" => $plan_cycle,
                        "booking_users" => $booking_users ?? 1
                    ]);


                    $subscriptionService->subscribe($request, $company);

                    $company->settings()->where('key', 'BOOKING_PLAN')->where('value', 'OPT_IN')->delete();
                }
            } catch (\Throwable $th) {
                //throw $th;
            }
        }
    }
}
