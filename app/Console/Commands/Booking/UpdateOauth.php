<?php

namespace App\Console\Commands\Booking;

use App\Models\UserOauthToken;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateOauth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'oauth:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'update the oath token before it expires';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $user_tokens = UserOauthToken::whereDate('updated_at', '<', Carbon::now())->get();
    }
}
