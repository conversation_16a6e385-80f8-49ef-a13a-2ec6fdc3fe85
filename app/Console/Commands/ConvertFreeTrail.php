<?php

namespace App\Console\Commands;

use App\Company;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ConvertFreeTrail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'convert:free-trail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $effected_company_data = [];
        $uneffected_company_data = [];

        // if ($request->has('company_ids')) {
        // $companies = Company::with(['subscriptions'])->whereIn('id', $request->company_ids)->cursor();
        $companies = Company::with(['subscriptions'])->whereIn('email', [

            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            // "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ])->cursor();
        foreach ($companies as $company) {
            $subscription_to_cancel = $company->activeSubscription();

            if (!$subscription_to_cancel) {
                array_push(
                    $uneffected_company_data,
                    [
                        'id' => $company->id,
                        'email' => $company->email,
                        'reason' => "no active subscription"
                    ]
                );
                continue;
            }

            if (!in_array($subscription_to_cancel->stripe_plan, config('stripe.free_plans'))) {
                array_push(
                    $uneffected_company_data,
                    [
                        'id' => $company->id,
                        'email' => $company->email,
                        'reason' => "no free plan"
                    ]
                );
                continue;
            }

            // if (!$company->getAttribute('is_record_on')) {
            //     array_push(
            //         $uneffected_company_data,
            //         [
            //             'id' => $company->id,
            //             'email' => $company->email,
            //             'reason' => "record off"
            //         ]
            //     );
            //     continue;
            // }
            $currency = $company->createOrGetStripeCustomer()->currency;

            if (!$currency) {
                if ($subscription_to_cancel->stripe_plan == "price_1MErGpCBmBWVCuBd8eSwFFV1") {
                    $currency = "eur";
                }
                if ($subscription_to_cancel->stripe_plan == "price_1MErGDCBmBWVCuBdH7JFHdmm") {
                    $currency = "usd";
                }
                if ($subscription_to_cancel->stripe_plan == "price_1MErFlCBmBWVCuBdAxbSqw6r") {
                    $currency = "gbp";
                }
                if ($subscription_to_cancel->stripe_plan == "price_1MErEzCBmBWVCuBdbSu1griI") {
                    $currency = "sek";
                }
            }

            if ($currency && $company->free_trail_plan_id) {
                // foreach (config('stripe.paid_plans') as $plan) {
                //     if ($plan['currency'] == $currency) {
                //         $company->free_trail_plan_id = $plan['stripe_id'];
                //     }
                // }
                $company->free_trail_start_date = Carbon::now();
                $company->free_trail_end_date = Carbon::now()->addDay(env('FREE_TRAIL_DAYS', 14))->format('Y-m-d');
                $company->is_free_trail_finished = 0;

                $company->free_trail_quantity = 1;
                $company->save();
                $subscription_to_cancel->cancelNow();
                array_push($effected_company_data, ['id' => $company->id, 'email' => $company->email]);
            } else {
                array_push(
                    $uneffected_company_data,
                    [
                        'id' => $company->id,
                        'email' => $company->email,
                        'reason' => "no currency"
                    ]
                );
                continue;
            }
        }
        // }
        dd([
            'effected_company_data' => $effected_company_data,
            'uneffected_company_data' => $uneffected_company_data,
        ]);
    }
}