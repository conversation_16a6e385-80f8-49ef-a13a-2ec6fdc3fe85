<?php

namespace App\Console\Commands;

use App\CompanyPlatform;
use App\UpcomingPlatformFees;
use Carbon\Carbon;
use Illuminate\Console\Command;

class HandlePlatformFees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle:platform:fees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to handle upcoming platform fees';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start_date = Carbon::now()->startOfMonth();

        $platform_fees = UpcomingPlatformFees::where('start_date',$start_date)->get();
        foreach ($platform_fees as $platform_fee) {
            $company_platform = CompanyPlatform::where(['company_id'=>$platform_fee->company_id,'platform'=>CompanyPlatform::PRESCRIPTION])->first();
            if ($company_platform) {
                $company_platform->price = $platform_fee->price;
                $company_platform->price_id = $platform_fee->price_id;
                $company_platform->save();
                UpcomingPlatformFees::find($platform_fee->id)->delete();
            } 
        }
    }
}