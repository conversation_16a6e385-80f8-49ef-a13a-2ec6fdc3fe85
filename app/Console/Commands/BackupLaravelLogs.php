<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BackupLaravelLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create backup of laravel.log file from local to s3';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pathPrefixForEnv = App::isProduction() ? 'logs' : 'logs-test';
        $localDisk = Storage::disk('logs');
        $localFiles = $localDisk->allFiles('/');
        $cloudDisk = Storage::disk('s3');
        $pathPrefix = $pathPrefixForEnv . DIRECTORY_SEPARATOR . Carbon::now()->format("Y-m-d-H-i-s") . DIRECTORY_SEPARATOR;
        foreach ($localFiles as $file) {
            if (!Str::endsWith($file, '.log')) {
                continue;
            }
            $this->info($file);
            $contents = $localDisk->get($file);
            $cloudLocation = $pathPrefix . $file;
            $cloudDisk->put($cloudLocation, $contents);
            // $localDisk->delete($file);
            $localDisk->put($file, "");
        }
    }
}