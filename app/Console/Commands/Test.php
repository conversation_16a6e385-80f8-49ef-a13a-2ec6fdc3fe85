<?php

namespace App\Console\Commands;

use App\Client;
use App\Company;
use Illuminate\Console\Command;

class Test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:run';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $clients = Client::whereDoesntHave('accesses')
            ->whereDate('created_at', '>', '2023-01-10')
            ->cursor();

        foreach ($clients as $client) {
            // $company = $client->company;
            $this->info("Client with id " . $client->id . " and email " . $client->email . " needs to be checked");
            // foreach ($company->users as $index => $user_in) {
            //     $user_in->accesses()->toggle([$client->id]);
            // }
        }
    }
}