<?php

namespace App\Console\Commands\VideoCall;

use App\Models\VideoCall;
use App\Services\VideoCall\VideoCallService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class HandleVideoCallCredits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle:video-call-credits';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'handle video call credits';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get the current time rounded to the nearest minute
        $currentTimeStart = now()->startOfMinute();

        $video_calls = VideoCall::where('ended_at', null)
            ->where('started_at', '!=', null)
            ->where('started_at', "<", $currentTimeStart)
            ->whereRaw("TIMESTAMPDIFF(MINUTE, DATE_FORMAT(started_at, '%Y-%m-%d %H:%i:00'), ?) % 5 = 0", [$currentTimeStart])->cursor();

        foreach ($video_calls as $video_call) {
            try {
                DB::transaction(function () use ($video_call) {
                    $sms_credits = $video_call->company->sms_credits;

                    if ($sms_credits->credits <= 0) {
                        $videoCallService = app(VideoCallService::class);
                        $videoCallService->close($video_call);
                        return;
                    }

                    $sms_credits->decrement('credits', 1);
                    $sms_credits->save();

                    $video_call->increment('credit_used', 1);
                    $video_call->save();
                });
            } catch (\Throwable $th) {
                report($th);
            }
        }

        return Command::SUCCESS;
    }
}
