<?php

namespace App\Console\Commands;

use App\CompanyBooking;
use App\Setting;
use Illuminate\Console\Command;
use App\Traits\TimeZoneManager;
use Carbon\Carbon;

class RemindClient extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'client:remind';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command that will send the reminder mail to client hows booking is about to happen';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $now = Carbon::now();
        $bookings_to_check = CompanyBooking::where('is_reminded', 0)
            ->where('is_cancelled', 0)
            ->where('is_verified', 1)
            ->whereDate('start_at', '>=', $now)
            ->whereDate('start_at', '<=', Carbon::parse($now)->addDays(2))
            ->where('slot_released_at', null)
            ->cursor();

        foreach ($bookings_to_check as $booking_to_check) {

            $company = $booking_to_check->company;
            $now = TimeZoneManager::getTimeZoneCarbonNow($company->timezone);
            $CLIENT_EMAIL_REMINDER = Setting::getSetting($company, Setting::CLIENT_EMAIL_REMINDER)->value;
            if ($booking_to_check->start_at <= Carbon::parse($now)->addMinutes($CLIENT_EMAIL_REMINDER)) {
                CompanyBooking::remindBooking($booking_to_check, true);
            }
        }

        $now = Carbon::now();
        $bookings_to_check = CompanyBooking::where('is_reminded_sms', 0)
            ->where('is_cancelled', 0)
            ->where('is_verified', 1)
            ->whereDate('start_at', '>=', $now)
            ->whereDate('start_at', '<=', Carbon::parse($now)->addDays(2))
            ->cursor();

        foreach ($bookings_to_check as $booking_to_check) {
            $company = $booking_to_check->company;
            $now = TimeZoneManager::getTimeZoneCarbonNow($company->timezone);

            $SMS_CLIENT_BOOKING_REMINDER_TIME = Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_REMINDER_TIME)->value;
            if ($booking_to_check->start_at <= Carbon::parse($now)->addHours($SMS_CLIENT_BOOKING_REMINDER_TIME)) {
                CompanyBooking::remindBookingSMS($booking_to_check);
            }
        }
    }
}