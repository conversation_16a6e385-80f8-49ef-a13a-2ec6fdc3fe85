<?php

namespace App\Console\Commands;

use App\Models\Cashier\Subscription;
use App\Models\Cashier\SubscriptionItem;
use Illuminate\Console\Command;

class RegenSubItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'regen:sub-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate subscription items';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $items = SubscriptionItem::where(function ($query) {
            return $query->where('stripe_plan', null)->orWhere('stripe_plan', '');
        })->cursor();

        foreach ($items as $item) {
            try {
                $stripeItem = $item->asStripeSubscriptionItem();
                $subscription = Subscription::where('stripe_id', $stripeItem->subscription)->first();

                $item->subscription_id = $subscription->id;
                $item->stripe_plan = $stripeItem->plan->id;

                $item->save();
            } catch (\Throwable $th) {
                //throw $th;
            }
        }

        return 0;
    }
}
