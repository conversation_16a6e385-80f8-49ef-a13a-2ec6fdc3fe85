<?php

namespace App\Console\Commands\POS;

use App\CompanyReceiptRefund;
use App\Contracts\Services\POS\RefundReceiptServiceInterface;
use Illuminate\Console\Command;

class SendRefundSessionRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:refunds {timezone} {--default=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to send request to check refund status for a given timezone';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(private readonly RefundReceiptServiceInterface $refundReceiptService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $timezone = $this->argument('timezone');
        $default = $this->option('default');

        // TODO: need more time to think what to do
        $refunds = CompanyReceiptRefund::whereIn('status', [CompanyReceiptRefund::PENDING, CompanyReceiptRefund::PROCESSING])
            ->whereHas('company', function ($query) use ($timezone, $default) {
                $query->when($timezone == $default, function ($query) use ($timezone) {
                    $query->where(function ($query) use ($timezone) {
                        $query->where('timezone', $timezone)->orWhereNull('timezone');
                    });
                }, function ($query) use ($timezone) {
                    $query->where('timezone', $timezone);
                });
            })
            ->where('created_at', '>', now()->subDay(10))
            ->cursor();

        $count = count($refunds);

        foreach ($refunds as $refund) {
            try {
                $this->refundReceiptService->updateStatus($refund);
            } catch (\Throwable $th) {
                report($th);
            }
        }

        $this->info("$count Refund Receipts checked");
    }
}
