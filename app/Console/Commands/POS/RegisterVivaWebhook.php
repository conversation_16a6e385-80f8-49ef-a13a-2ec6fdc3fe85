<?php

namespace App\Console\Commands\POS;

use App\Http\Integrations\POSPayment\Facades\POSPayment;
use App\Http\Integrations\VivaWallet\Enums\WebhookEventType;
use Illuminate\Console\Command;

class RegisterVivaWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'viva:webhook {--url=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'register webhook with viva wallet';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $url = $this->option('url');

        $this->info($url);

        $res = POSPayment::webhook()->create($url, WebhookEventType::TransactionPaymentCreated);
        $this->info($res);

        $res = POSPayment::webhook()->create($url, WebhookEventType::TransactionReversalCreated);
        $this->info($res);

        $res = POSPayment::webhook()->create($url, WebhookEventType::TransactionFailed);
        $this->info($res);

        $res = POSPayment::webhook()->create($url, WebhookEventType::TransactionPriceCalculated);
        $this->info($res);

        $res = POSPayment::webhook()->create($url, WebhookEventType::AccountConnected);
        $this->info($res);

        $res = POSPayment::webhook()->create($url, WebhookEventType::CommandBankTransferCreated);
        $this->info($res);

        $this->info('Webhook registered');
    }
}
