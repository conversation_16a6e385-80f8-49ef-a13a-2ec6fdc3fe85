<?php

namespace App\Console\Commands\POS;

use App\CompanyReceipt;
use App\Contracts\Services\POS\ReceiptServiceInterface;
use Illuminate\Console\Command;

class SendSessionRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:receipts {timezone} {--default=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to send request to check payment status for a given timezone';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(private readonly ReceiptServiceInterface $receiptService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $timezone = $this->argument('timezone');
        $default = $this->option('default');

        $receipts = CompanyReceipt::whereIn('status', [CompanyReceipt::PENDING, CompanyReceipt::PROCESSING])
            ->whereHas('company', function ($query) use ($timezone, $default) {
                $query->when($timezone == $default, function ($query) use ($timezone) {
                    $query->where(function ($query) use ($timezone) {
                        $query->where('timezone', $timezone)->orWhereNull('timezone');
                    });
                }, function ($query) use ($timezone) {
                    $query->where('timezone', $timezone);
                });
            })
            ->where('created_at', '>', now()->subDay(10))
            ->cursor();

        $count = count($receipts);

        foreach ($receipts as $receipt) {
            try {
                $this->receiptService->updatePaymentStatus($receipt);
            } catch (\Throwable $th) {
                report($th);
            }
        }

        $this->info("$count Receipts checked");
    }
}
