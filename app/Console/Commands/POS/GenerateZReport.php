<?php

namespace App\Console\Commands\POS;

use App\Company;
use Illuminate\Console\Command;

class GenerateZReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:zreports {timezone} {--default=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Z-report for companies in specified timezone';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $timezone = $this->argument('timezone');
        $default = $this->option('default');

        $companies = Company::when($timezone == $default, function ($query) use ($timezone) {
            $query->where(function ($query) use ($timezone) {
                $query->where('timezone', $timezone)->orWhereNull('timezone');
            });
        }, function ($query) use ($timezone) {
            $query->where('timezone', $timezone);
        })->whereNotNull('viva_account_id')->whereNotNull('viva_merchant_id')->cursor();

        $companies = $companies->where('is_pos_on', true);

        foreach ($companies as $company) {
            try {
                $company->closeBatch();
            } catch (\Throwable $caught) {
                report($caught); // ignored
            }
            try {
                $company->getCurrentOrCreateBatch();
            } catch (\Throwable $caught) {
                report($caught); // ignored
            }
        }

        $this->info('Z reports created for ' . count($companies) . ' companies');
    }
}
