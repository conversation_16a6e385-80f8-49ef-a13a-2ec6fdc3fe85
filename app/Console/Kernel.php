<?php

namespace App\Console;

use App\Console\Commands\DailyCo\RegisterDailyCoWebhook;
use App\Console\Commands\FixFileCreatedAt;
use App\Console\Commands\GooglePlaces\HandleUpdateGoogleRating;
use App\Console\Commands\HandleOnlinePaymentStatus;
use App\Console\Commands\Lead\HandleLeadNoteRemainder;
use App\Console\Commands\POS\GenerateZReport;
use App\Console\Commands\POS\RegisterVivaWebhook;
use App\Console\Commands\POS\SendRefundSessionRequest;
use App\Console\Commands\POS\SendSessionRequest;
use App\Console\Commands\RegenSubItems;
use App\Console\Commands\RemoveExtraSubscriptions;
use App\Console\Commands\RegenPOSLicence;
use App\Console\Commands\Subscription\HandleCancelSubscription;
use App\Console\Commands\VideoCall\HandleVideoCallCredits;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        SendSessionRequest::class,
        RegisterVivaWebhook::class,
        SendRefundSessionRequest::class,
        GenerateZReport::class,
        HandleCancelSubscription::class,
        HandleOnlinePaymentStatus::class,
        HandleLeadNoteRemainder::class,
        RemoveExtraSubscriptions::class,
        RegenSubItems::class,
        HandleUpdateGoogleRating::class,
        RegisterDailyCoWebhook::class,
        HandleVideoCallCredits::class,
        RegenPOSLicence::class,
        FixFileCreatedAt::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        //record system
        $schedule->command('handle:subscription')->dailyAt('04:00');
        $schedule->command('handle:free:trail')->dailyAt('23:00');

        //booking system
        $schedule->command('client:remind')->everyTenMinutes();
        $schedule->command('send:extra:mail')->everyFifteenMinutes();
        $schedule->command('release:slots')->everyMinute();
        $schedule->command('booking:send:followup:mail')->everyTwoHours();
        $schedule->command('booking:send:followup:sms')->everyTwoHours();

        //prescription
        $schedule->command('generate:prescriber:bill:monthly')->monthlyOn(2);
        $schedule->command('generate:company:report:monthly')->monthly();
        $schedule->command('handle:platform:fees')->monthlyOn(3);

        // client
        $schedule->command('client_zip:clean')->hourly();

        // backup logs
        $schedule->command('backup:logs')->dailyAt('00:00');

        //Delete/Reminder company not register properly
        $start_date = env('COMPANY_DELETE_START_DATE');
        $reminder_days = array_map(function ($item) {
            return  "--reminder_days=" . $item;
        }, explode(',', env('COMPANY_DELETE_REMINDER_DAYS')));
        $reminder_days = implode(' ', $reminder_days);
        $delete_after = env('COMPANY_DELETE_DAYS');

        $schedule->command("delete:company:not:register:proper $reminder_days --start_date=$start_date --delete_after=$delete_after")->dailyAt('00:00');

        // Lambda warm function every 5 minutes
        $schedule->command('sidecar:warm')->everyFiveMinutes();

        // Pos schedule
        $this->posSchedule($schedule);

        // Lead schedule
        $this->leadSchedule($schedule);

        // Video Call schedule
        $this->videoCallSchedule($schedule);

        // online payment
        $schedule->command('handle:online:payment:status')->everyFiveMinutes();


        // subscription
        $schedule->command('handle:cancel:subscription')->everyFiveMinutes();


        // google rating update
        $schedule->command('update:google-rating')->timezone("Europe/Stockholm")->monthly()->at("00:00");
    }

    private function posSchedule(Schedule $schedule)
    {
        foreach (config('timezones') as $key => $timezone) {
            // POS
            $schedule->command("generate:zreports $timezone --default=Europe/Stockholm")->timezone($timezone)->at("00:00");

            $schedule->command("check:receipts $timezone --default=Europe/Stockholm")->timezone($timezone)->at("23:30");
            $schedule->command("check:refunds $timezone --default=Europe/Stockholm")->timezone($timezone)->at("23:30");
        }
    }

    private function leadSchedule(Schedule $schedule)
    {
        $schedule->command("leadnote:remainder")->everyMinute();
    }

    private function videoCallSchedule(Schedule $schedule)
    {
        $schedule->command("handle:video-call-credits")->everyMinute();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
