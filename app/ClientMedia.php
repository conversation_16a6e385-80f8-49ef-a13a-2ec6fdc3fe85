<?php

namespace App;

use App\File;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClientMedia extends Model
{
    use HasFactory, GetEncryptedFile;

    protected $fillable = [
        'client_id',
    ];


    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function files()
    {
        return $this->morphMany(File::class, 'fileable')->latest();
    }
}
