<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\POS\HasPOSReport;
use App\Traits\POS\HasUniqueCode;
use App\Traits\SaveFile;
use Barryvdh\DomPDF\Facade\Pdf;
use Barryvdh\DomPDF\PDF as DomPDFPDF;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CompanyZReport extends Model
{
    use HasFactory, GetEncryptedFile, HasPOSReport, SaveFile, Encryptable, HasUniqueCode;

    //FILLABLES
    protected $fillable = [
        'company_id',
        'start_datetime',
        'end_datetime',
        'closing_amount',
        'pdf',
    ];

    //ENCRYPTED
    protected $encrypted = [
        'closing_amount',
        'pdf',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    protected $dates = [
        'start_datetime',
        'end_datetime',
    ];

    //RULES
    public static $getListRules = [];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $value = self::getIncrementalValueModel($query->company_id);

            $query->report_id = $value->report_sequence_number;

            $value->report_sequence_number += 1;
            $value->save();
        });
    }

    // RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getPdfAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    function generateAndStoreReport(): Self
    {
        return DB::transaction(function () {
            if (!$this->start_datetime || !$this->end_datetime) {
                throw new HttpException(400, __('pos_strings.start_date_and_end_date_required_for_generating_report'));
            }

            $company = $this->company()->firstOrFail();

            $data = $this->getReportData($this);

            $folder = "z_reports";

            $file = $this->generateStoreQuestionary($data, 'pdf.pos_report', $folder, auth()->user() ?? $company->users->first());

            $this->closing_amount = $data['closing_amount'];
            $this->pdf = $file->filename;
            $this->save();

            return $this;
        });
    }

    function generateReport($end_datetime = null): DomPDFPDF
    {
        return DB::transaction(function () use ($end_datetime) {
            if (!$this->start_datetime || !(!!$this->end_datetime || !!$end_datetime)) {
                throw new HttpException(400, __('pos_strings.start_date_and_end_date_required_for_generating_report'));
            }

            $data = $this->getReportData($this);

            return Pdf::loadView('pdf.pos_report', $data);
        });
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
