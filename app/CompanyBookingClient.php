<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\Pivot;

class CompanyBookingClient extends Pivot
{
    use HasFactory;

    public $incrementing = true;

    //TABLE
    public $table = 'company_booking_clients';

    //FILLABLES
    protected $fillable = [
        'booking_id',
        'client_id',
        'is_cancelled',
        'is_verified',
        'otp',
        'is_extra_step_done',
        'is_bankid_verified',
        'special_request',
        'is_reminded',
        'is_reminded_sms',
        'show_comment',
        'slot_released_at',
        'is_shown',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function receipt(): MorphOne
    {
        return $this->morphOne(CompanyReceipt::class, 'relatable');
    }

    public function booking()
    {
        return $this->belongsTo(CompanyBooking::class, 'booking_id', 'id');
    }
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id', 'id')->setEagerLoads([]);
    }

    public function shouldRemind()
    {
        return !$this->receipt || ($this->receipt && $this->receipt->isOnline() && $this->receipt->status == CompanyReceipt::PAID);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
