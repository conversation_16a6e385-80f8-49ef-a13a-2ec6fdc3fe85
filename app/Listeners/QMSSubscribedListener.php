<?php

namespace App\Listeners;

use App\Company;
use App\CompanyPlatform;
use App\Mail\SubscribeManagement;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class QMSSubscribedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct() {}

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle($event)
    {
        $company = $event->company;

        if ($company->is_management_on && !$company->platforms()->where('platform', CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM)->exists()) {
            $company->platforms()->updateOrCreate([
                'platform' => CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
            ], [
            ]);

            $user = $company->users()->where('email', $company->email)->firstOrFail();

            Mail::to(env('SUPPORT_EMAIL', "<EMAIL>"))
                ->locale($language ?? app()->getLocale())
                ->send(new SubscribeManagement($company, $user));

            $email = env('NERALITY_SUPPORT_EMAIL');

            if ($email) {
                Mail::to($email)
                    ->locale($language ?? app()->getLocale())
                    ->send(new SubscribeManagement($company, $user));
            }
        }
    }
}
