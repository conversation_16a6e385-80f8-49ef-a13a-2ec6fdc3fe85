<?php

namespace App\Listeners\VideoCalling;

use App\Http\Integrations\DailyCo\Events\ParticipantLeft;
use App\Models\VideoCall;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ParticipantLeftListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param ParticipantLeft $event
     * @return void
     */
    public function handle(ParticipantLeft $event)
    {
        $video_call = VideoCall::where('key', $event->room)->first();
        if (!$video_call) return;

        $video_call_member = $video_call->members()->where('is_owner', $event->owner)->first();

        if ($video_call_member) {
            $date_time = $event->joined_at->addSeconds($event->duration);
            $video_call_member->ended_at = $date_time;
            $video_call_member->save();
        }
    }
}
