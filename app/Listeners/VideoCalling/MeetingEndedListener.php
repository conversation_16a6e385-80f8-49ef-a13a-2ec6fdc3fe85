<?php

namespace App\Listeners\VideoCalling;

use App\CompanyBooking;
use App\Http\Integrations\DailyCo\Events\MeetingEnded;
use App\Models\VideoCall;
use App\Services\VideoCall\VideoCallService;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class MeetingEndedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MeetingEnded  $event
     * @return void
     */
    public function handle(MeetingEnded $event)
    {
        $video_call = VideoCall::where('key', $event->room)->first();

        if ($video_call) {
            $video_call->ended_at = $event->end_at;
            $video_call->save();

            $allMembersNotJoined = $video_call->members()->where('started_at', null)->exists();

            if (!$allMembersNotJoined && !($video_call->callable instanceof CompanyBooking)) {
                $videoCallService = app(VideoCallService::class);
                $videoCallService->close($video_call);
            }

            $activity = activity('video_call')
                ->performedOn($video_call);

            $duration = Carbon::parse($video_call->started_at)->diffInMinutes($video_call->ended_at);
            $activity_message = "Video call with ID {$video_call->key} ended in {$duration} mins. Credits used: {$video_call->credit_used}.";

            $activity = $activity->log($activity_message);
        }
    }
}