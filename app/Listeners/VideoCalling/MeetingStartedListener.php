<?php

namespace App\Listeners\VideoCalling;

use App\CompanySMSCredits;
use App\Http\Integrations\DailyCo\Events\MeetingStarted;
use App\Models\VideoCall;
use App\Services\VideoCall\VideoCallService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class MeetingStartedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MeetingStarted  $event
     * @return void
     */
    public function handle(MeetingStarted $event)
    {
        try {
            DB::transaction(function () use ($event) {
                $video_call = VideoCall::where('key', $event->room)->first();

                if ($video_call) {
                    /**
                     * @var CompanySMSCredits
                     */
                    $sms_credits = $video_call->company->sms_credits;
                    $video_call->started_at = $event->start_at;

                    if ($sms_credits->credits <= 0) {
                        $videoCallService = app(VideoCallService::class);
                        $videoCallService->close($video_call);
                        return;
                    }

                    $sms_credits->decrement('credits', 1);
                    $sms_credits->save();

                    $video_call->ended_at = null;
                    $video_call->increment('credit_used', 1);
                    $video_call->save();
                }
            });
        } catch (\Throwable $th) {
            report($th);
        }
    }
}
