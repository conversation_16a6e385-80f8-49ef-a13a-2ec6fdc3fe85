<?php

namespace App\Listeners\VideoCalling;

use App\Http\Integrations\DailyCo\Events\ParticipantJoined;
use App\Models\VideoCall;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ParticipantJoinedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ParticipantJoined  $event
     * @return void
     */
    public function handle(ParticipantJoined $event)
    {
        $video_call = VideoCall::where('key', $event->room)->first();
        if (!$video_call) return;

        $video_call_member = $video_call->members()->where('is_owner', $event->owner)->first();

        if ($video_call_member) {

            $activity = activity('video_call')
                ->performedOn($video_call);

            $activity_message = "{$video_call->owner_member->user_name} joined the call.";

            $activity_message = $activity_message . " ID: {$video_call->key}";

            $activity = $activity->log($activity_message);



            $video_call_member->started_at = $event->joined_at;
            $video_call_member->ended_at = null;
            $video_call_member->save();
        }
    }
}
