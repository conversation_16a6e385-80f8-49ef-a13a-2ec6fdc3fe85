<?php

namespace App\Listeners;

use App\CompanyBooking;
use App\Events\BookingCreatedEvent;
use App\Traits\Booking\BookingGoogleCalendarManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class BookingCreatedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(BookingCreatedEvent $event)
    {
        activity()->enableLogging();
        $companyBooking = $event->company_booking;
        if ($companyBooking->start_at && $companyBooking->end_at && $companyBooking->user_id) {
            if (!$companyBooking->service?->is_online_payment) {
                if (!($companyBooking->service->category && $companyBooking->service->category?->group_booking)) {
                    $data = CompanyBooking::getBookingMailData($companyBooking);
                    $msg = "{$companyBooking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} created with practitioner {$companyBooking->user->fullName()}";
                    $activity = activity()
                        ->performedOn($companyBooking)
                        ->by($companyBooking->user)
                        ->log($msg);
                }
            }
        }

        BookingGoogleCalendarManager::syncWithBooking($event->company_booking);
    }
}