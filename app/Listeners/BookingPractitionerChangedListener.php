<?php

namespace App\Listeners;

use App\Events\BookingPractitionerChangedEvent;
use App\Traits\Booking\BookingGoogleCalendarManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class BookingPractitionerChangedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(BookingPractitionerChangedEvent $event)
    {
        BookingGoogleCalendarManager::removeBooking($event->company_booking);
    }
}
