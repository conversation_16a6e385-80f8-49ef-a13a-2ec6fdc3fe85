<?php

namespace App\Listeners;

use App\Company;
use App\CompanyPlatform;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON>vel\Cashier\Events\WebhookReceived;

class SmsPaidListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(WebhookReceived $event)
    {
        $payload = json_decode(json_encode((object) $event->payload), FALSE);

        if ($payload->type === 'invoice.payment_succeeded') {
            $data = $payload->data->object;

            $company = Company::where('stripe_id', $data->customer)->firstOrFail();
            $price_ids = collect($data->lines->data)->map(function ($line_data) {
                return $line_data->price->id;
            });

            $sms_prices = collect(config('stripe.prices'))->filter(function ($price) {
                return $price['platform'] == CompanyPlatform::SMS;
            })->reduce(function ($prev, $next) {
                $next['prices'] = collect($next['prices'])->map(function ($price) use ($next) {
                    return [
                        ...$price,
                        'quantity' => $next['quantity'],
                    ];
                });
                return $prev->merge($next['prices']);
            }, collect([]))->values();


            $sms_credit = $company->sms_credits()->firstOrCreate([], ['credits' => 0]);
            foreach ($price_ids as $price_id) {
                $sms_price =  $sms_prices->where('stripe_id', $price_id)->first();
                if ($sms_price) {
                    $sms_credit->credits = $sms_credit->credits + $sms_price['quantity'];
                    $sms_credit->save();
                }
            }
        }
    }
}