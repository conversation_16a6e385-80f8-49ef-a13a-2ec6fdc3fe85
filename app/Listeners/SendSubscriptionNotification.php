<?php

namespace App\Listeners;

use App\Events\SubscriptionUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\SMS;

class SendSubscriptionNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\SubscriptionUpdated  $event
     * @return void
     */
    public function handle(SubscriptionUpdated $event)
    {
        $company = $event->company;
        $current_subscription = $company->getCurrentSubscriptions();
        $previous_subscription = $event->previous_subscription;

        $user = $company->users()->first();

        // Determine the type of subscription change and send appropriate Slack notification
        $this->processSubscriptionChange($company, $user, $current_subscription, $previous_subscription);
    }

    /**
     * Process subscription changes and send appropriate Slack notifications
     */
    private function processSubscriptionChange($company, $user, $current_subscription, $previous_subscription)
    {
        // Check if this is a new user subscription (no previous subscription)
        if ($this->isNewUserSubscription($previous_subscription)) {
            $this->sendNewUserSubscriptionNotification($company, $user, $current_subscription);
            return;
        }

        // Compare current and previous subscriptions to detect changes
        $changes = $this->detectSubscriptionChanges($current_subscription, $previous_subscription);

        if (!empty($changes['added'])) {
            $this->sendSubscriptionAddedNotification($company, $user, $changes['added']);
        }

        if (!empty($changes['updated'])) {
            $this->sendSubscriptionUpdatedNotification($company, $user, $changes['updated']);
        }

        if (!empty($changes['cancelled'])) {
            $this->sendSubscriptionCancelledNotification($company, $user, $changes['cancelled']);
        }

        if (!empty($changes['restored'])) {
            $this->sendSubscriptionRestoredNotification($company, $user, $changes['restored']);
        }
    }

    /**
     * Check if this is a new user subscription
     */
    private function isNewUserSubscription($previous_subscription)
    {
        if (!$previous_subscription) {
            return true;
        }

        // Check if previous subscription had any active plans
        $hadAnyPlan = $previous_subscription->record_plan ||
            $previous_subscription->booking_plan ||
            $previous_subscription->pos_plan ||
            $previous_subscription->management_plan;

        return !$hadAnyPlan;
    }

    /**
     * Detect changes between current and previous subscriptions
     */
    private function detectSubscriptionChanges($current, $previous)
    {
        $changes = [
            'added' => [],
            'updated' => [],
            'cancelled' => [],
            'restored' => [],
        ];

        $plans = ['record_plan', 'booking_plan', 'pos_plan', 'management_plan'];

        foreach ($plans as $planType) {
            $currentPlan = $current->$planType;
            $previousPlan = $previous->$planType ?? null;

            if ($currentPlan && !isset($currentPlan->cancel_at)) {
                $currentPlan->cancel_at = null;
            }
            if ($previousPlan && !isset($previousPlan->cancel_at)) {
                $previousPlan->cancel_at = null;
            }

            if ($currentPlan && !$previousPlan) {
                // Plan was added
                $changes['added'][$planType] = $currentPlan;
            } elseif ((!$currentPlan && $previousPlan) || ($currentPlan && $previousPlan && $currentPlan->cancel_at && !$previousPlan->cancel_at)) {
                // Plan was cancelled
                $changes['cancelled'][$planType] = $previousPlan;
            } elseif ($currentPlan && $previousPlan && !$currentPlan->cancel_at && $previousPlan->cancel_at) {
                // Plan was resubscribed
                $changes['restored'][$planType] = $currentPlan;
            } elseif ($currentPlan && $previousPlan) {
                // Check if plan was updated
                if ($this->isPlanUpdated($currentPlan, $previousPlan)) {
                    $changes['updated'][$planType] = [
                        'current' => $currentPlan,
                        'previous' => $previousPlan
                    ];
                }
            }
        }

        return $changes;
    }

    /**
     * Check if a plan has been updated
     */
    private function isPlanUpdated($currentPlan, $previousPlan)
    {
        // Compare key properties that indicate an update
        $currentUsers = $currentPlan->users ?? 1;
        $previousUsers = $previousPlan->users ?? 1;

        $currentPrice = $currentPlan->price ?? 0;
        $previousPrice = $previousPlan->price ?? 0;

        return $currentUsers !== $previousUsers ||
            $currentPrice !== $previousPrice ||
            ($currentPlan->stripe_id ?? '') !== ($previousPlan->stripe_id ?? '');
    }

    /**
     * Send notification for new user subscription
     */
    private function sendNewUserSubscriptionNotification($company, $user, $current_subscription)
    {
        $data = $this->buildSubscriptionData($current_subscription);

        if (!count($data)) return;

        Log::channel('slack_sales')->info("🎉 A new user has just subscribed!", array_merge([
            'name' => $user->fullName(),
            'company_name' => $company->company_name,
            'email' => $user->email,
            'phone' => SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number),
        ], $data));
    }

    /**
     * Send notification for subscription additions
     */
    private function sendSubscriptionAddedNotification($company, $user, $addedPlans)
    {
        $planNames = [];
        $data = [];

        foreach ($addedPlans as $planType => $plan) {
            $systemName = $this->getSystemName($planType);
            $planNames[] = $systemName;
            $data[$systemName] = $this->formatPlanDetails($plan);
        }

        if (!count($data)) return;

        $message = "📈 Subscription added: " . implode(', ', $planNames);

        Log::channel('slack_sales')->info($message, array_merge([
            'name' => $user->fullName(),
            'company_name' => $company->company_name,
            'email' => $user->email,
            'phone' => SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number),
        ], $data));
    }

    /**
     * Send notification for subscription updates
     */
    private function sendSubscriptionUpdatedNotification($company, $user, $updatedPlans)
    {
        $planNames = [];
        $data = [];

        foreach ($updatedPlans as $planType => $planData) {
            $systemName = $this->getSystemName($planType);
            $planNames[] = $systemName;

            $data[$systemName . ' (Previous)'] = $this->formatPlanDetails($planData['previous']);
            $data[$systemName . ' (Current)'] = $this->formatPlanDetails($planData['current']);
        }

        if (!count($data)) return;

        $message = "🔄 Subscription updated: " . implode(', ', $planNames);

        Log::channel('slack_sales')->info($message, array_merge([
            'name' => $user->fullName(),
            'company_name' => $company->company_name,
            'email' => $user->email,
            'phone' => SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number),
        ], $data));
    }

    /**
     * Send notification for subscription cancellations
     */
    private function sendSubscriptionCancelledNotification($company, $user, $cancelledPlans)
    {
        $planNames = [];
        $data = [];

        foreach ($cancelledPlans as $planType => $plan) {
            $systemName = $this->getSystemName($planType);
            $planNames[] = $systemName;
            $data[$systemName . ' (Cancel Requested)'] = $this->formatPlanDetails($plan);
        }

        if (!count($data)) return;

        $message = "❌ Subscription cancellation requested: " . implode(', ', $planNames);

        Log::channel('slack_sales')->info($message, array_merge([
            'name' => $user->fullName(),
            'company_name' => $company->company_name,
            'email' => $user->email,
            'phone' => SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number),
        ], $data));
    }


    /**
     * Send notification for subscription restores
     */
    private function sendSubscriptionRestoredNotification($company, $user, $cancelledPlans)
    {
        $planNames = [];
        $data = [];

        foreach ($cancelledPlans as $planType => $plan) {
            $systemName = $this->getSystemName($planType);
            $planNames[] = $systemName;
            $data[$systemName . ' (Resubscribed)'] = $this->formatPlanDetails($plan);
        }

        if (!count($data)) return;

        $message = "🔄 Subscription restored: " . implode(', ', $planNames);

        Log::channel('slack_sales')->info($message, array_merge([
            'name' => $user->fullName(),
            'company_name' => $company->company_name,
            'email' => $user->email,
            'phone' => SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number),
        ], $data));
    }

    /**
     * Build subscription data for all current plans
     */
    private function buildSubscriptionData($current_subscription)
    {
        $data = [];

        if ($current_subscription->record_plan) {
            $data['Record System'] = $this->formatPlanDetails($current_subscription->record_plan);
        }

        if ($current_subscription->booking_plan) {
            $data['Booking System'] = $this->formatPlanDetails($current_subscription->booking_plan);
        }

        if ($current_subscription->pos_plan) {
            $data['POS System'] = $this->formatPlanDetails($current_subscription->pos_plan);
        }

        if ($current_subscription->management_plan) {
            $data['Management System'] = $this->formatPlanDetails($current_subscription->management_plan);
        }

        return $data;
    }

    /**
     * Format plan details for display
     */
    private function formatPlanDetails($plan)
    {
        if (!$plan) {
            return 'N/A';
        }

        $price = $plan->price ?? 0;
        $currency = $plan->currency ?? 'USD';
        $users = $plan->users ?? 1;

        // For plans that have user-based pricing
        if (isset($plan->users) && $plan->users > 1) {
            $amount = $price * $users;
            return "{$amount} {$currency} for {$users} users";
        }

        // For fixed-price plans
        return "{$price} {$currency}";
    }

    /**
     * Get human-readable system name from plan type
     */
    private function getSystemName($planType)
    {
        $systemNames = [
            'record_plan' => 'Record System',
            'booking_plan' => 'Booking System',
            'pos_plan' => 'POS System',
            'management_plan' => 'Management System'
        ];

        return $systemNames[$planType] ?? ucfirst(str_replace('_plan', '', $planType));
    }
}
