<?php

namespace App\Listeners;

use App\Company;
use App\CompanyPlatform;
use App\Contracts\Services\POS\InfrasecEnrollmentServiceInterface;
use App\Events\QMSSubscribed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON><PERSON>\Cashier\Events\WebhookHandled;

class WebhookHandledListener implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  WebhookHandled  $event
     * @return void
     */
    public function handle(WebhookHandled $event)
    {
        $payload = json_decode(json_encode((object) $event->payload), FALSE);

        if (!isset($payload->data->object->customer)) return;

        if (!in_array($payload->type, [
            'customer.subscription.created',
            'customer.subscription.updated',
            'customer.subscription.deleted',
        ])) return;

        $company = Company::where('stripe_id', $payload->data->object->customer)->first();

        if (!$company) return;

        if ($company && $company->is_management_on && !$company->platforms()->where('platform', CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM)->exists()) {
            event(new QMSSubscribed($company));
        }

        $company->is_booking_on = $company->is_booking_on;
        $company->is_record_on = $company->is_record_on;
        $company->is_pos_on = $company->is_pos_on;
        $company->is_management_on = $company->is_management_on;

        if ($company->is_booking_on && !$company->platforms()->where('platform', CompanyPlatform::BOOKING_SYSTEM)->exists()) {
            $company->platforms()->create([
                'platform' => CompanyPlatform::BOOKING_SYSTEM,
            ]);
        }

        try {
            if ($company->is_pos_on && $company->ccu_register_id) {
                $infrasecEnrollService = app(InfrasecEnrollmentServiceInterface::class);
                $infrasecEnrollService->open($company);
            } else if (!$company->is_pos_on && $company->ccu_register_id) {
                $infrasecEnrollService = app(InfrasecEnrollmentServiceInterface::class);
                $infrasecEnrollService->close($company);
            }
        } catch (\Throwable $th) {
            report($th);
        }

        /**
         * @var User
         */
        $user = $company->users()->where('email', $company->email)->first();

        if (!$user) return;

        // TODO: need to fix, to not raise bugs
        if ($company->is_booking_on && $company->canAddUserInBookingSystem()) {
            $user->is_booking_on = true;
        } else {
            $user->is_booking_on = $company->is_booking_on ? $user->is_booking_on : $company->is_booking_on;
        }

        if ($company->is_record_on && $company->canAddUserInRecordSystem()) {
            $user->is_record_on = true;
        } else {
            $user->is_record_on = $company->is_record_on ? $user->is_record_on : $company->is_record_on;
        }
        $user->is_pos_on = $company->is_pos_on;
        $user->is_management_on = $company->is_management_on;

        if ($company->isDirty()) {
            $company->save();
        }

        if ($user->isDirty()) {
            $user->save();
        }
    }
}
