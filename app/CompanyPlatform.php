<?php

namespace App;

use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CompanyPlatform extends Model
{
    use HasFactory;
    use GetEncryptedFile;

    const SMS = "SMS", PRESCRIPTION = "PRESCRIPTION",
        RECORD_SYSTEM = "RECORD_SYSTEM", POS_SYSTEM = "POS_SYSTEM", BOOKING_SYSTEM = "BOOKING_SYSTEM",
        QUALITY_MANAGEMENT_SYSTEM = "QUALITY_MANAGEMENT_SYSTEM", QUALITY_MANAGEMENT_ACTIVATION = "QUALITY_MANAGEMENT_ACTIVATION";

    const ALL_PLATFORMS = [
        CompanyPlatform::SMS,
        CompanyPlatform::PRESCRIPTION,
        CompanyPlatform::RECORD_SYSTEM,
        CompanyPlatform::POS_SYSTEM,
        CompanyPlatform::QUALITY_MANAGEMENT_SYSTEM,
        CompanyPlatform::BOOKING_SYSTEM
    ];

    //TABLE
    public $table = 'company_platforms';

    //FILLABLES
    protected $fillable = [
        'platform',
        'company_id',
        'license_agreement',
        'price',
        'price_id',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    public function getLicenseAgreementAttribute($value)
    {
        try {
            if ($this->platform == CompanyPlatform::POS_SYSTEM && $value) {
                return str_replace(substr(Storage::url('/'), 0, -1), '', (string) $this->getS3SignedUrl($value));
            }

            return $value;
        } catch (\Throwable $th) {
            throw $value;
        }
    }
}