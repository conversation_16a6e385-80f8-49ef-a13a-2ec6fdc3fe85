<?php

namespace App;

use App\Contracts\Services\VideoCall\VideoCallServiceInterface;
use App\Exceptions\PractitionerNotAvailableException;
use App\Mail\BookingExtraStepMail;
use App\Mail\GenericBookingMail;
use App\Mail\OtpMail;
use App\Models\CompanyBookingMetadata;
use App\Models\VideoCall;
use App\Services\VideoCall\VideoCallService;
use App\Traits\BookingManager;
use App\Traits\Encryptable;
use App\Traits\GeneratePDF;
use App\Traits\Sinch;
use App\Traits\SMS;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\Mail;
use App\Traits\PortalManager;

class CompanyBooking extends Model
{
    use HasFactory, Encryptable, BookingManager, GeneratePDF, Sinch, SMS;

    //ACTIONS
    const
        BOOKED = "BOOKED",
        CANCELED = "CANCELED",
        RESCHEDULED = "RESCHEDULED",
        REMINDER = "REMINDER",
        UPDATED = "UPDATED",
        PRACTITIONER_CHANGED = "PRACTITIONER_CHANGED",
        BOOK_SLOT = "BOOK_SLOT",
        RELEASE_SLOT = "RELEASE_SLOT",
        COMPLETED = "COMPLETED",
        PAY_FOR_SLOT = "PAY_FOR_SLOT";

    //TABLE
    public $table = 'company_bookings';

    protected $encrypted = [
        'full_name',
        'email',
        'country_code',
        'phone_number',
        'address',
        'city',
        'zipcode',
        'country',
        'state',
        'special_request'
    ];

    //FILLABLES
    protected $fillable = [
        'company_id',
        'full_name',
        'email',
        'country_code',
        'phone_number',
        'address',
        'city',
        'zipcode',
        'country',
        'state',
        'special_request',
        'start_at',
        'end_at',
        'price',
        'otp',
        'is_cancelled',
        'is_verified',
        'service_id',
        'user_id',
        'client_id',
        'is_rescheduled',
        'is_practitioner_changed',
        'is_reminded',
        'is_reminded_sms',
        'first_name',
        'last_name',
        'is_shown',
        'show_comment',
        'time_margin',
        'should_make_slot_unavailable',
        'is_extra_steps_needed',
        'is_extra_step_done',
        'is_extra_step_reminded',
        'is_bankid_verified',
        'slot_released_at',
        'is_follow_up_email_sent',
        'is_follow_up_sms_sent',
    ];

    //HIDDEN
    protected $hidden = [
        'otp',
    ];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        // 'start_at' => 'datetime',
        // 'end_at' => 'datetime',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function receipt(): MorphOne
    {
        return $this->morphOne(CompanyReceipt::class, 'relatable');
    }

    public function video_call()
    {
        return $this->morphOne(VideoCall::class, 'callable')->latestOfMany();
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->setEagerLoads([]);
    }
    public function client()
    {
        return $this->belongsTo(Client::class)->setEagerLoads([]);
    }

    public function clients()
    {
        return $this->belongsToMany(Client::class, CompanyBookingClient::class, 'booking_id', 'client_id', 'id', 'id')
            ->setEagerLoads([])
            ->withPivot([
                'is_cancelled',
                'is_verified',
                'is_shown',
                'is_extra_step_done',
                'is_bankid_verified',
                'special_request',
                'is_reminded',
                'show_comment',
                'created_at',
                'id',
                'slot_released_at'
            ]);
    }

    public function notes()
    {
        return $this->hasMany(BookingNote::class, 'booking_id');
    }
    public function latest_note()
    {
        return $this->hasOne(BookingNote::class, 'booking_id')->latest();
    }
    public function meta_data()
    {
        return $this->hasOne(CompanyBookingMetadata::class, 'company_booking_id');
    }

    public function shouldRemind()
    {
        return !$this->receipt || ($this->receipt && $this->receipt->isOnline() && $this->receipt->status == CompanyReceipt::PAID);
    }

    public function active_clients()
    {
        return $this->belongsToMany(Client::class, CompanyBookingClient::class, 'booking_id', 'client_id', 'id', 'id')
            ->withPivot([
                'is_cancelled',
                'is_verified',
                'is_shown',
                'is_extra_step_done',
                'is_bankid_verified',
                'special_request',
                'is_reminded',
                'show_comment',
                'created_at',
                'id',
                'slot_released_at'
            ])
            ->wherePivot('is_verified', 1)
            ->wherePivot('is_cancelled', 0);
    }

    public function service()
    {
        return $this->belongsTo(CompanyService::class)->withTrashed();
    }
    public function client_letter_of_consents()
    {
        return $this->hasManyThrough(ClientLetterOfConsent::class, CompanyBookingLetterOfConsent::class, 'company_booking_id', 'id', 'id', 'client_letter_of_consent_id');
    }
    public function questionaries()
    {
        return $this->hasMany(CompanyBookingQuestionary::class)->with(['modelable' => function ($query) {
            // $query = $query->with(['datas' => function ($q) {
            // }]);
        }]);
    }


    function autoUpdateVideoCall()
    {
        if ($this->video_call) {
            $this->video_call->end();
        }

        return $this->generateVideoCall(true);

        return null;
    }

    function generateVideoCall($force = false)
    {
        $service = $this->service;

        if ($service->isVideoCall() && (!$this?->video_call || $force) && $this->isValidForVideoCall()) {
            $videoCallService = app(VideoCallServiceInterface::class);
            $timezone = $this?->company?->timezone ?? "Europe/Stockholm";

            $start_time = Carbon::parse($this->start_at, $timezone)->setTimezone('UTC');
            $end_time = Carbon::parse($this->end_at, $timezone)->setTimezone('UTC');

            $end_timestamp = $start_time->copy()->addHours(2)->timestamp > $end_time->timestamp ? $start_time->copy()->addHours(2)->timestamp : $end_time->timestamp;

            $videoCall = $videoCallService->single(
                $this->user,
                $this->client,
                [
                    'nbf' => $start_time->timestamp,
                    'exp' => $end_timestamp,
                    'eject_at_room_exp' => true,
                ]
            );

            $videoCall->callable()->associate($this);
            $videoCall->save();

            return $videoCall;
        }

        return null;
    }

    function isValidForVideoCall()
    {
        return $this->service->isVideoCall() &&
            $this->company_id &&
            !$this->is_cancelled &&
            $this->is_verified &&
            $this->service_id &&
            $this->user_id &&
            $this->client_id &&
            $this->start_at &&
            $this->end_at &&
            $this->slot_released_at == null;
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}


    static function isBookingAvailable(User $user, Carbon $start_at, Carbon $end_at, CompanyService $service, CompanyBooking $booking_to_ignore = null, $is_manual = false)
    {
        //LOGIC TO FIND OUT IS THERE ANY BOOKING FOR PARTICULAR PRACTITIONER FOR PARTICULAR TIME PERIOD
        $company = $user->company;

        //FIRST CHECKING IS BOOKING CAN BE MADE FOR THIS TIME
        Company::canBookForThisTime($company, $start_at, null, $is_manual);

        //CHECKING IF USER IS AVAILABLE FOR BOOKING OR NOT
        if (!$user->is_booking_on) {
            throw new Exception(__('strings.practitioner_is_not_available_for_booking'));
        }

        //CHECK FOR SERVICE CONCURRENT BOOKING
        if ($service && $service->parallel_booking_count > 0) {
            $concurrent_bookings = CompanyBooking::where('company_id', $company->id)
                ->where('is_verified', 1)
                ->where('is_cancelled', 0)
                ->where(function ($q) use ($start_at, $end_at) {
                    $q = $q->where(function ($q) use ($start_at, $end_at) {
                        $q = $q->where('start_at', '>=', Carbon::parse($start_at))->where('start_at', '<', Carbon::parse($end_at));
                    })->orWhere(function ($q) use ($start_at, $end_at) {
                        $q = $q->where('end_at', '>', Carbon::parse($start_at))->where('end_at', '<=', Carbon::parse($end_at));
                    })->orWhere(function ($q) use ($start_at, $end_at) {
                        $q = $q->where('start_at', '<', Carbon::parse($start_at))->where('end_at', '>=', Carbon::parse($end_at));
                    });
                })
                ->where('service_id', $service->id)
                ->orderBy('start_at', 'asc');
            if ($booking_to_ignore) {
                $concurrent_bookings = $concurrent_bookings->where('id', '!=', $booking_to_ignore->id);
            }
            $concurrent_bookings = $concurrent_bookings->get();
            if ($concurrent_bookings->count() >= $service->parallel_booking_count) {
                throw new Exception(__('strings.can_not_book_more_booking_for_this_service', ['parallel_booking_count' => $service->parallel_booking_count]));
            }
        }

        //CHECKING FOR PRIVATE TIME SLOT
        $user_private_time_slot = UserTimeSlot::where('type', UserTimeSlot::PRIVATE_SLOT_BLOCKING)
            ->whereIn('user_id', [$user->id])
            ->where(function ($q) use ($start_at, $end_at) {
                $q = $q->where(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
                });
            })
            ->orderBy('start_at', 'asc')
            ->first();

        if ($user_private_time_slot) {
            throw new Exception(__('strings.practitioner_has_private_time'));
        }



        $user_available_time_slot = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
            ->whereIn('user_id', [$user->id])
            ->where(function ($q) use ($start_at, $end_at) {
                $q = $q->where(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
                });
            })
            ->orderBy('start_at', 'asc')
            ->first();

        if ($user_available_time_slot) {
            // if (!($user_available_time_slot->start_at <= $start_at && $user_available_time_slot->end_at >= $end_at)) {
            //     throw new PractitionerNotAvailableException(__('strings.practitioner_is_not_available_for_selected_time'));
            // }
        } else {
            throw new PractitionerNotAvailableException(__('strings.practitioner_is_not_available_for_selected_time'));
        }
        //FIRST ONLY CHECKING ON THE BOOKING THAT'S VERIFIED
        $already_booked_bookings = CompanyBooking::where('is_verified', 1);

        //NOW REMOVING ALL THE CANCELED BOOKINGS
        $already_booked_bookings = $already_booked_bookings->where('is_cancelled', 0);

        //LOOKING FOR ONLY GIVEN USER
        $already_booked_bookings = $already_booked_bookings->where('user_id', $user->id);

        //NOW CHECKING FOR GIVEN TIME PERIOD
        $already_booked_bookings = $already_booked_bookings->where(function ($q) use ($start_at, $end_at) {
            $q = $q->where(function ($q) use ($start_at, $end_at) {
                $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
            })->orWhere(function ($q) use ($start_at, $end_at) {
                $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
            })->orWhere(function ($q) use ($start_at, $end_at) {
                $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
            });
        });

        //IF THERE IS ANY BOOKING THAT WE NEED TO IGNORE THEN ADDING THAT LOGIC
        if ($booking_to_ignore) {
            $already_booked_bookings = $already_booked_bookings->where('id', '!=', $booking_to_ignore->id);
        }

        if ($service->category && $service->category->group_booking && $service->group_quantity) {
            $already_booked_bookings = $already_booked_bookings->withCount('active_clients')->having('active_clients_count', '>=', $service->group_quantity);
        }

        //NOW RETURNING THE BOOLEAN SHOWING ANY BOOKING EXISTS OR NOT
        if ($already_booked_bookings->exists()) {
            throw new Exception(__('strings.practitioner_is_busy_for_selected_time'));
        }




        return true;
    }


    static function checkBookingForDeletion(CompanyBooking $booking)
    {
        if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
            if (count($booking->clients) <= 0) {
                $booking->is_cancelled = 1;
                $booking->save();
            } else {
                if (count($booking->active_clients) <= 0) {
                    $booking->is_cancelled = 1;
                    $booking->save();
                }
            }
        } else {
        }
    }

    static function checkForBooking(User $user, Carbon $start_at, Carbon $end_at, CompanyBooking $booking_to_ignore = null)
    {
        //FIRST ONLY CHECKING ON THE BOOKING THAT'S VERIFIED
        $already_booked_bookings = CompanyBooking::where('is_verified', 1);

        //NOW REMOVING ALL THE CANCELED BOOKINGS
        $already_booked_bookings = $already_booked_bookings->where('is_cancelled', 0);

        //LOOKING FOR ONLY GIVEN USER
        $already_booked_bookings = $already_booked_bookings->where('user_id', $user->id);

        //NOW CHECKING FOR GIVEN TIME PERIOD
        $already_booked_bookings = $already_booked_bookings->where(function ($q) use ($start_at, $end_at) {
            $q = $q->where(function ($q) use ($start_at, $end_at) {
                $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
            })->orWhere(function ($q) use ($start_at, $end_at) {
                $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
            })->orWhere(function ($q) use ($start_at, $end_at) {
                $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
            });
        });


        //IF THERE IS ANY BOOKING THAT WE NEED TO IGNORE THEN ADDING THAT LOGIC
        if ($booking_to_ignore) {
            $already_booked_bookings = $already_booked_bookings->where('id', '!=', $booking_to_ignore->id);
        }


        //NOW RETURNING THE BOOLEAN SHOWING ANY BOOKING EXISTS OR NOT
        if ($already_booked_bookings->exists()) {
            throw new Exception(__('strings.practitioner_is_busy_for_selected_time'));
        }

        return true;
    }

    static function checkForPrivateSlot(User $user, Carbon $start_at, Carbon $end_at, UserTimeSlot $private_slot_to_ignore = null)
    {
        //FIRST ONLY CHECKING ON THE BLOCKING PRIVATE SLOTS
        $existing_private_slots = UserTimeSlot::where('type', UserTimeSlot::PRIVATE_SLOT_BLOCKING);

        //LOOKING FOR ONLY GIVEN USER
        $existing_private_slots = $existing_private_slots->where('user_id', $user->id);

        //NOW CHECKING FOR GIVEN TIME PERIOD
        $existing_private_slots = $existing_private_slots->where(function ($q) use ($start_at, $end_at) {
            $q = $q->where(function ($q) use ($start_at, $end_at) {
                $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
            })->orWhere(function ($q) use ($start_at, $end_at) {
                $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
            })->orWhere(function ($q) use ($start_at, $end_at) {
                $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
            });
        });


        //IF THERE IS ANY PRIVATE SLOT THAT WE NEED TO IGNORE THEN ADDING THAT LOGIC
        if ($private_slot_to_ignore) {
            $existing_private_slots = $existing_private_slots->where('id', '!=', $private_slot_to_ignore->id);
        }


        //NOW RETURNING THE BOOLEAN SHOWING ANY PRIVATE SLOT EXISTS OR NOT
        if ($existing_private_slots->exists()) {
            throw new Exception(__('strings.private_time_already_added'));
        }

        return true;
    }


    static function sendOtp(CompanyBooking $booking, CompanyBookingClient $company_booking_client = null)
    {
        if (!$company_booking_client) {
            $generatedOtp = null;
            do {
                $generatedOtp = mt_rand(1000, 9999);
            } while (CompanyBooking::where('otp', $generatedOtp)->first());

            $booking->otp = $generatedOtp;
            $booking->save();
            try {
                $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
                if ($booking->client) {
                    Mail::to($booking->client->email)->locale($language ?? app()->getLocale())->send(new OtpMail($booking->otp, $booking->company->company_name, $language));
                } else {
                    Mail::to($booking->email)->locale($language ?? app()->getLocale())->send(new OtpMail($booking->otp, $booking->company->company_name, $language));
                }
            } catch (\Throwable $th) {
                //TODO::handle this
                // dd($th);
            }
        } else {
            $generatedOtp = null;
            do {
                $generatedOtp = mt_rand(1000, 9999);
            } while (CompanyBookingClient::where('otp', $generatedOtp)->first());
            $company_booking_client->otp = $generatedOtp;
            $company_booking_client->save();
            try {
                $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
                if ($company_booking_client->client) {
                    Mail::to($company_booking_client->client->email)->locale($language ?? app()->getLocale())->send(new OtpMail($company_booking_client->otp, $booking->company->company_name, $language));
                } else {
                    Mail::to($company_booking_client->email)->locale($language ?? app()->getLocale())->send(new OtpMail($company_booking_client->otp, $booking->company->company_name, $language));
                }
            } catch (\Throwable $th) {
                //TODO::handle this
                // dd($th);
            }
        }

        return true;
    }

    static function sendMail(CompanyBooking $booking, $only_to_customer = false, $should_use_lang = false, CompanyBookingClient $company_booking_client = null, $send_mail = true)
    {
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        $client_email_on = Setting::getSetting($booking->company, Setting::EMAIL_CLIENT_BOOKING_CONFIRMATION_ON)?->value;
        try {
            if ($send_mail && $client_email_on) {

                if ($booking->service->category && $booking->service->category?->group_booking) {
                    if ($company_booking_client) {
                        Mail::to($company_booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::BOOKED, true, $should_use_lang, $company_booking_client, $language));;
                    } else {
                        $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();
                        foreach ($company_booking_clients as $booking_client) {
                            Mail::to($booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::BOOKED, true, $should_use_lang, $booking_client, $language));;
                        }
                    }
                } else {
                    if ($booking->client) {
                        Mail::to($booking->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::BOOKED, true, $should_use_lang, $company_booking_client, $language));
                    } else {
                        Mail::to($booking->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::BOOKED, true, $should_use_lang, $company_booking_client, $language));
                    }
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
        }

        if (!$only_to_customer) {
            $INTERNAL_BOOKING_CONFIRMATION = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CONFIRMATION)->value;
            $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
            if ($INTERNAL_BOOKING_CONFIRMATION) {
                try {
                    Mail::to($INTERNAL_BOOKING_CONFIRMATION)->locale($LANGUAGE ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::BOOKED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                } catch (\Throwable $th) {
                    //TODO::handle this
                }
            }

            $INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER)->value;
            if ($INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER) {
                try {
                    if ($booking->user->email) {
                        Mail::to($booking->user->email)->locale($LANGUAGE ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::BOOKED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                    }
                } catch (\Throwable $th) {
                    //TODO::handle this
                }
            }
        }

        return true;
    }

    static function sendSMS(CompanyBooking $booking,  CompanyBookingClient $company_booking_client = null, bool $only_to_customer = true, bool $only_to_practitioner = false, bool $force_enabled = false)
    {
        try {
            $company = $booking->company;

            if ($only_to_practitioner === false) {
                $template = SMS::CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
                    $company,
                    Setting::SMS_CLIENT_BOOKING_CONFIRMATION_ON,
                    Setting::SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID,
                    $force_enabled
                );
                if ($booking->service->confirmation_sms_template_id) {
                    $on_value = Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_CONFIRMATION_ON);
                    if ($on_value && $on_value->value) {
                        $template = SMSTemplate::query()->where('company_id', $booking->company_id)->find($booking->service->confirmation_sms_template_id);
                    }
                }
            }



            $message = $template?->description ?? null;

            $is_group_booking = $booking->service->category && $booking->service->category?->group_booking;
            $internal_template = null;
            $internal_message = null;

            if ($only_to_customer === false || $only_to_practitioner === true) {
                $internal_template = SMS::CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
                    $company,
                    Setting::SMS_INTERNAL_BOOKING_CONFIRMATION_ON,
                    Setting::SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID,
                    $force_enabled
                );
                $internal_message = $internal_template?->description;
            }

            $video_call = $booking->video_call()?->first();

            if ($message && $video_call?->other_member?->url) {
                $message .= "\n" . __('strings.to_join_video_call');
                $message .= "\n" . ShortUrl::shorten($video_call?->other_member?->url);
            }

            if ($internal_message && $video_call?->owner_member?->url) {
                $internal_message .= "\n" . __('strings.to_join_video_call');
                $internal_message .= "\n" . ShortUrl::shorten($video_call?->owner_member?->url);
            }



            if ($is_group_booking) {
                if ($company_booking_client?->client) {
                    if ($internal_message) {
                        SMS::SEND_BOOKING_SMS($internal_message, $booking, $company_booking_client->client, $company, "booking_confirm_portal_sms", true);
                    }
                    if ($message) {
                        SMS::SEND_BOOKING_SMS($message, $booking, $company_booking_client->client, $company, "booking_create_client_sms");
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
                    foreach ($company_booking_clients as $index => $booking_client) {
                        if ($internal_message) {
                            SMS::SEND_BOOKING_SMS($internal_message, $booking, $booking_client->client, $company, "booking_confirm_portal_sms", true);
                        }
                        if ($message && $booking_client->client) {
                            SMS::SEND_BOOKING_SMS($message, $booking, $booking_client->client, $company, "booking_create_client_sms");
                        }
                    }
                }
            } else {
                if ($booking->client) {
                    if ($internal_message) {
                        SMS::SEND_BOOKING_SMS($internal_message, $booking, $booking->client, $company, "booking_confirm_portal_sms", true);
                    }
                    if ($message) {
                        SMS::SEND_BOOKING_SMS($message, $booking, $booking->client, $company, "booking_create_client_sms");
                    }
                } else {
                }
            }
        } catch (\Throwable $th) {
            report($th);
            //TODO::handle this
        }

        return true;
    }

    static function verifyBooking(CompanyBooking $booking, CompanyBookingClient $company_booking_client = null)
    {
        if (!$company_booking_client) {
            $booking->otp = null;
            $booking->is_verified = 1;
            $booking->save();
        } else {
            $company_booking_client->otp = null;
            $company_booking_client->is_verified = 1;
            $company_booking_client->save();
        }


        // try {
        //     Mail::to($booking->client->email)->locale(app()->getLocale())->send(new OtpMail($booking->otp));
        // } catch (\Throwable $th) {
        //     dd($th);
        // }
        return true;
    }

    static function makeSlotAvailable(
        User $user,
        Carbon $start_at,
        Carbon $end_at,
        CompanyService $service,
        CompanyBooking $booking = null
    ) {

        CompanyBooking::checkForBooking(
            $user,
            $start_at,
            $end_at,
            $booking
        );
        $user_available_time_slot = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
            ->whereIn('user_id', [$user->id])
            ->where(function ($q) use ($start_at, $end_at) {
                $q = $q->where(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
                });
            })
            ->orderBy('start_at', 'asc')
            ->first();
        if ($user_available_time_slot) {
            if ($user_available_time_slot->start_at >= $start_at) {
                $user_available_time_slot->start_at = $start_at;
            }
            if ($user_available_time_slot->end_at <= $end_at) {
                $user_available_time_slot->end_at = $end_at;
            }
            $user_available_time_slot->save();
        } else {
            UserTimeSlot::create([
                'user_id' => $user->id,
                'note' => UserTimeSlot::AVAILABLE,
                'week_day' => BookingManager::getWeekDay($start_at),
                'type' => UserTimeSlot::AVAILABLE,
                'start_at' => Carbon::parse($start_at),
                'end_at' => Carbon::parse($end_at),
                'is_for_all_day' => 0,
            ]);
        }
        return true;
    }

    static function revertAvailableSlotForThisBooking(CompanyBooking $booking)
    {
        $user_available_time_slots = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
            ->whereIn('user_id', [$booking->user_id])
            ->where(function ($q) use ($booking) {
                $q = $q->where(function ($q) use ($booking) {
                    $q = $q->where('start_at', '>=', $booking->start_at)->where('start_at', '<', $booking->end_at);
                })->orWhere(function ($q) use ($booking) {
                    $q = $q->where('end_at', '>', $booking->start_at)->where('end_at', '<=', $booking->end_at);
                })->orWhere(function ($q) use ($booking) {
                    $q = $q->where('start_at', '<', $booking->start_at)->where('end_at', '>=', $booking->end_at);
                });
            })
            ->orderBy('start_at', 'asc')
            ->cursor();

        foreach ($user_available_time_slots as $user_available_time_slot) {
            if ($user_available_time_slot->start_at == $booking->start_at && $user_available_time_slot->end_at == $booking->end_at) {
                $user_available_time_slot->delete();
            } else {
                if ($user_available_time_slot->start_at >= $booking->start_at && $user_available_time_slot->end_at <= $booking->end_at) {
                    $user_available_time_slot->delete();
                } else {
                    if (
                        $user_available_time_slot->start_at <= $booking->start_at
                        && $user_available_time_slot->end_at >= $booking->start_at
                        && $user_available_time_slot->end_at <= $booking->end_at
                    ) {
                        $user_available_time_slot->end_at = $booking->start_at;
                        $user_available_time_slot->save();
                    } else {
                        if (
                            $user_available_time_slot->start_at <= $booking->end_at
                            && $user_available_time_slot->end_at >= $booking->start_at
                            && $user_available_time_slot->end_at >= $booking->end_at
                        ) {
                            $user_available_time_slot->start_at = $booking->end_at;
                            $user_available_time_slot->save();
                        }
                    }
                }
            }
        }
        return true;
    }

    static function cancelBooking(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null)
    {
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        $client_email_cancel_on = Setting::getSetting($booking->company, Setting::EMAIL_CLIENT_BOOKING_CANCELLATION_ON)?->value;
        try {
            if ($booking->service->category && $booking->service->category?->group_booking) {
                if ($company_booking_client) {
                    $company_booking_client->is_cancelled = 1;
                    $company_booking_client->save();

                    $should_add_log = true;
                    if ($company_booking_client->is_cancelled) {
                        $companyBooking = $company_booking_client->booking;
                        if ($companyBooking->service?->is_online_payment) {
                            $should_add_log = false;
                            if ($company_booking_client->receipt && $company_booking_client->receipt?->status == CompanyReceipt::PAID) {
                                $should_add_log = true;
                            }
                        }
                        if ($should_add_log && $companyBooking->start_at && $companyBooking->end_at && $companyBooking->user_id) {
                            $data = CompanyBooking::getBookingMailData($companyBooking);
                            $msg = "{$companyBooking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} cancelled with practitioner {$companyBooking->user->fullName()}";
                            $activity = activity()
                                ->performedOn($company_booking_client)
                                ->by($companyBooking->user)
                                ->log($msg);
                        }
                    }
                    if ($company_booking_client->client && $client_email_cancel_on) {
                        Mail::to($company_booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::CANCELED, true, $should_use_lang, $company_booking_client, $language));
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
                    $data = CompanyBooking::getBookingMailData($booking);
                    foreach ($company_booking_clients as $booking_client) {
                        $booking_client->is_cancelled = 1;
                        $booking_client->save();

                        if ($booking_client->is_cancelled) {
                            $companyBooking = $booking_client->booking;

                            $should_add_log = true;
                            $companyBooking = $booking_client->booking;
                            if ($companyBooking->service?->is_online_payment) {
                                $should_add_log = false;
                                if ($booking_client->receipt && $booking_client->receipt?->status == CompanyReceipt::PAID) {
                                    $should_add_log = true;
                                }
                            }

                            if ($should_add_log && $companyBooking->start_at && $companyBooking->end_at && $companyBooking->user_id) {
                                $msg = "{$companyBooking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} cancelled with practitioner {$companyBooking->user->fullName()}";
                                $activity = activity()
                                    ->performedOn($booking_client)
                                    ->by($companyBooking->user)
                                    ->log($msg);
                            }
                        }
                        if ($booking_client->client && $client_email_cancel_on) {
                            Mail::to($booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::CANCELED, true, $should_use_lang, $booking_client, $language));
                        }
                    }
                }
            } else {
                $booking->is_cancelled = 1;
                $booking->save();
                if ($client_email_cancel_on) {
                    if ($booking->client) {
                        Mail::to($booking->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::CANCELED, true, $should_use_lang, $company_booking_client, $language));
                    } else {
                        Mail::to($booking->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::CANCELED, true, $should_use_lang, $company_booking_client, $language));
                    }
                }
            }
        } catch (\Throwable $th) {
        }

        if ($booking->should_make_slot_unavailable) {
            if ($booking->service->category?->group_booking) {
                if ($booking->active_clients()->count() <= 0) {
                    CompanyBooking::revertAvailableSlotForThisBooking($booking);
                }
            } else {
                CompanyBooking::revertAvailableSlotForThisBooking($booking);
            }
        }

        $INTERNAL_BOOKING_CANCELLATION = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CANCELLATION)->value;
        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        if ($INTERNAL_BOOKING_CANCELLATION) {
            try {
                Mail::to($INTERNAL_BOOKING_CANCELLATION)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::CANCELED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }

        $INTERNAL_BOOKING_CANCELLATION_PRACTITIONER = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CANCELLATION_PRACTITIONER)->value;
        if ($INTERNAL_BOOKING_CANCELLATION_PRACTITIONER) {
            try {
                if ($booking->user->email) {
                    Mail::to($booking->user->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::CANCELED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                }
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }
        CompanyBooking::checkBookingForDeletion($booking);

        if ($booking->is_cancelled && $booking->video_call) {
            $booking->video_call->end();
        }

        $should_add_log = true;
        if ($booking->service?->is_online_payment) {
            $should_add_log = false;
            if ($booking->receipt && $booking->receipt?->status == CompanyReceipt::PAID) {
                $should_add_log = true;
            }
        }
        if (
            $should_add_log &&
            !($booking->service->category && $booking->service->category?->group_booking)
            && $booking->start_at && $booking->end_at && $booking->user_id
        ) {
            $data = CompanyBooking::getBookingMailData($booking);
            $msg = "{$booking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} cancelled with practitioner {$booking->user->fullName()}";
            $activity = activity()
                ->performedOn($booking)
                ->by($booking->user)
                ->log($msg);
        }

        return true;
    }

    static function cancelBookingSMS(CompanyBooking $booking, CompanyBookingClient $company_booking_client = null, $only_to_customer = true)
    {
        $company = $booking->company;

        $template = SMS::CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
            $company,
            Setting::SMS_CLIENT_BOOKING_CANCELLATION_ON,
            Setting::SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID
        );

        $message = $template?->description;
        $internal_template = null;
        $internal_message = null;

        if ($only_to_customer === false) {
            $internal_template = SMS::CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
                $company,
                Setting::SMS_INTERNAL_BOOKING_CANCELLATION_ON,
                Setting::SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID
            );
            $internal_message = $internal_template?->description;
        }


        try {
            if ($booking->service->category && $booking->service->category?->group_booking) {
                if ($company_booking_client) {
                    if ($company_booking_client->client) {
                        if ($internal_template) {
                            SMS::SEND_BOOKING_SMS($internal_message, $booking, $company_booking_client->client, $company, "booking_cancel_user_sms", true);
                        }
                        SMS::SEND_BOOKING_SMS($message, $booking, $company_booking_client->client, $company, "booking_cancel_client_sms");
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
                    foreach ($company_booking_clients as $index => $booking_client) {
                        if ($index === 0 && $internal_template) {
                            SMS::SEND_BOOKING_SMS($internal_message, $booking, $booking_client->client, $company, "booking_cancel_user_sms", true);
                        }
                        if ($booking_client->client) {
                            SMS::SEND_BOOKING_SMS($message, $booking, $booking_client->client, $company, "booking_cancel_client_sms");
                        }
                    }
                }
            } else {
                if ($booking->client) {
                    if ($internal_template) {
                        SMS::SEND_BOOKING_SMS($internal_message, $booking, $booking->client, $company, "booking_cancel_user_sms", true);
                    }
                    SMS::SEND_BOOKING_SMS($message, $booking, $booking->client, $company, "booking_cancel_client_sms");
                } else {
                    if ($internal_template) {
                        SMS::SEND_BOOKING_SMS($internal_message, $booking, $booking->client, $company, "booking_cancel_user_sms", true);
                    }
                }
            }
        } catch (\Throwable $th) {
        }

        return true;
    }

    static function completeBookingEmail(CompanyBooking $booking)
    {
        $booking->is_follow_up_email_sent = 1;
        $booking->save();
        // try {
        if ($booking->service->category && $booking->service->category?->group_booking) {
            $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
            foreach ($company_booking_clients as $booking_client) {
                if ($booking_client->client && $booking_client->client->email) {
                    Mail::to($booking_client->client->email)->send(new GenericBookingMail($booking, CompanyBooking::COMPLETED, true, false, $booking_client));
                }
            }
        } else {
            if ($booking->client && $booking->client->email) {
                Mail::to($booking->client->email)->send(new GenericBookingMail($booking, CompanyBooking::COMPLETED, true));
            } else {
                if ($booking->email) {
                    Mail::to($booking->email)->send(new GenericBookingMail($booking, CompanyBooking::COMPLETED, true));
                }
            }
        }
        // } catch (\Throwable $th) {
        //     //TODO::handle this
        // }
    }

    static function completeBookingSMS(CompanyBooking $booking)
    {
        if (!$booking->service->finished_sms_template_id) {
            return 1;
        }

        $template = SMSTemplate::query()->where('company_id', $booking->company_id)->find($booking->service->finished_sms_template_id);
        $company = $booking->company;

        if (!$template) return 1;

        $message = $template->description;

        $booking->is_follow_up_sms_sent = 1;
        $booking->save();
        try {
            if ($booking->service->category && $booking->service->category?->group_booking) {

                $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
                foreach ($company_booking_clients as $booking_client) {
                    if ($booking_client->client) {
                        SMS::SEND_BOOKING_SMS($message, $booking, $booking_client->client, $company, "booking_followup_client_sms");
                    }
                }
            } else {
                if ($booking->client) {
                    SMS::SEND_BOOKING_SMS($message, $booking, $booking->client, $company, "booking_followup_client_sms");
                } else {
                }
            }
        } catch (\Throwable $th) {
        }
    }

    static function remindBooking(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null)
    {
        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        try {
            if ($booking->service->category && $booking->service->category?->group_booking) {
                if ($company_booking_client) {
                    if (!$company_booking_client->shouldRemind()) {
                        return false;
                    }
                    $company_booking_client->is_reminded = 1;
                    $company_booking_client->save();
                    if ($company_booking_client->client) {
                        Mail::to($company_booking_client->client->email)->locale($language ?? $LANGUAGE)->send(new GenericBookingMail($booking, CompanyBooking::REMINDER, true, $should_use_lang, $company_booking_client, $language));
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
                    foreach ($company_booking_clients as $booking_client) {
                        if (!$booking_client->shouldRemind()) {
                            continue;
                        }

                        $booking_client->is_reminded = 1;
                        $booking_client->save();
                        if ($booking_client->client) {
                            Mail::to($booking_client->client->email)->locale($language ?? $LANGUAGE)->send(new GenericBookingMail($booking, CompanyBooking::REMINDER, true, $should_use_lang, $booking_client, $language));
                        }
                    }
                }
            } else {
                if (!$booking->shouldRemind()) {
                    return false;
                }
                if ($booking->client) {
                    Mail::to($booking->client->email)->locale($language ?? $LANGUAGE)->send(new GenericBookingMail($booking, CompanyBooking::REMINDER, true, $should_use_lang, $company_booking_client, $language));
                } else {
                    Mail::to($booking->email)->locale($language ?? $LANGUAGE)->send(new GenericBookingMail($booking, CompanyBooking::REMINDER, true, $should_use_lang, $company_booking_client, $language));
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
        }

        $booking->is_reminded = 1;
        $booking->save();
        return true;
    }

    static function remindBookingSMS(CompanyBooking $booking, CompanyBookingClient $company_booking_client = null)
    {
        $company = $booking->company;

        $template = SMS::CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
            $company,
            Setting::SMS_CLIENT_BOOKING_REMINDER_TIME,
            Setting::SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID
        );

        if (!$template) return 1;

        $message = $template->description;

        $video_call = $booking->video_call()?->first();

        if ($message && $video_call?->other_member?->url) {
            $message .= "\n" . __('strings.to_join_video_call');
            $message .= "\n" . ShortUrl::shorten($video_call?->other_member?->url);
        }

        try {
            if ($booking->service->category && $booking->service->category?->group_booking) {
                if ($company_booking_client) {
                    if (!$company_booking_client->shouldRemind()) {
                        return false;
                    }
                    $company_booking_client->is_reminded_sms = 1;
                    $company_booking_client->save();
                    if ($company_booking_client->client) {
                        SMS::SEND_BOOKING_SMS($message, $booking, $company_booking_client->client, $company, "booking_reminder_client_sms");
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->cursor();
                    foreach ($company_booking_clients as $booking_client) {
                        if (!$booking_client->shouldRemind()) {
                            continue;
                        }
                        $booking_client->is_reminded_sms = 1;
                        $booking_client->save();
                        if ($booking_client->client) {
                            SMS::SEND_BOOKING_SMS($message, $booking, $booking_client->client, $company, "booking_reminder_client_sms");
                        }
                    }
                }
            } else {
                if (!$booking->shouldRemind()) {
                    return false;
                }
                if ($booking->client) {
                    SMS::SEND_BOOKING_SMS($message, $booking, $booking->client, $company, "booking_reminder_client_sms");
                } else {
                }
            }
        } catch (\Throwable $th) {
        }

        $booking->is_reminded_sms = 1;
        $booking->save();
        return true;
    }

    static function remindExtraSteps(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null)
    {
        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        try {
            if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
                if ($company_booking_client) {
                    if ($company_booking_client->client) {
                        if (!$company_booking_client->shouldRemind()) {
                            return false;
                        }
                        Mail::to($company_booking_client->client->email)->locale($language ?? $LANGUAGE)->send(new BookingExtraStepMail($company_booking_client->client, $booking, $language));
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();
                    foreach ($company_booking_clients as $booking_client) {
                        if (!$booking_client->shouldRemind()) {
                            continue;
                        }
                        Mail::to($booking_client->client->email)->locale($language ?? $LANGUAGE)->send(new BookingExtraStepMail($booking_client->client, $booking, $language));
                    }
                }
            } else {
                if ($booking->client) {
                    if (!$booking->shouldRemind()) {
                        return false;
                    }
                    if ($booking->client) {
                        Mail::to($booking->client->email)->locale($language ?? $LANGUAGE)->send(new BookingExtraStepMail($booking->client, $booking, $language));
                    } else {
                        Mail::to($booking->email)->locale($language ?? $LANGUAGE)->send(new BookingExtraStepMail($booking->client, $booking, $language));
                    }
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
            // dd($th);
        }
        $booking->is_extra_step_reminded = 1;
        $booking->save();
        return true;
    }

    static function remindExtraStepsSMS(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null)
    {
        $company = $booking->company;

        $template = SMS::CHECK_KEY_IS_ON_AND_RETURN_TEMPLATE(
            $company,
            Setting::BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME,
            Setting::BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID
        );
        if (!$template) return;

        $message = $template?->description;
        if (!$message) return;

        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        $lang = $language ?? $LANGUAGE;

        $url = BookingManager::getExtraStepUrl($booking->company, $booking);

        try {
            if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
                if ($company_booking_client) {
                    if (!$company_booking_client->shouldRemind()) {
                        return false;
                    }
                    if ($company_booking_client->client) {
                        $url = BookingManager::getExtraStepUrl($booking->company, $booking, $lang);
                        if ($url) {
                            $message .= "\n" . ShortUrl::shorten($url);
                        }
                        SMS::SEND_BOOKING_SMS($message, $booking, $company_booking_client->client, $company, "booking_extra_step_reminder_sms");
                    }
                } else {
                    $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();

                    foreach ($company_booking_clients as $booking_client) {
                        if (!$booking_client->shouldRemind()) {
                            continue;
                        }
                        if ($booking_client) {
                            $url = BookingManager::getExtraStepUrl($booking->company, $booking, $lang, $booking_client);
                            $msg = $message;
                            if ($url) {
                                $msg .= "\n" . ShortUrl::shorten($url);
                            }
                        }
                        SMS::SEND_BOOKING_SMS($msg, $booking, $booking_client->client, $company, "booking_extra_step_reminder_sms");
                    }
                }
            } else {
                if ($booking->client) {
                    if (!$booking->shouldRemind()) {
                        return false;
                    }
                    if ($booking->client) {
                        $url = BookingManager::getExtraStepUrl($booking->company, $booking, $lang);
                        if ($url) {
                            $message .= "\n" . ShortUrl::shorten($url);
                        }
                        SMS::SEND_BOOKING_SMS($message, $booking, $booking->client, $company, "booking_extra_step_reminder_sms");
                    } else {
                        $url = BookingManager::getExtraStepUrl($booking->company, $booking, $lang);
                        if ($url) {
                            $message .= "\n" . ShortUrl::shorten($url);
                        }
                        SMS::SEND_BOOKING_SMS($message, $booking, $booking->client, $company, "booking_extra_step_reminder_sms");
                    }
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
            // dd($th);
        }
        $booking->is_extra_step_reminded_sms = 1;
        $booking->save();
        return true;
    }

    static function rescheduleBooking(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null, $send_mail = true)
    {
        $booking->autoUpdateVideoCall();

        $booking->is_rescheduled = 1;
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        $company_booking_clients = null;
        $client_email_rescheduled_on = Setting::getSetting($booking->company, Setting::EMAIL_CLIENT_BOOKING_RESCHEDULED_ON)?->value;


        if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
            if ($company_booking_client) {
                if ($company_booking_client->client) {
                    $should_add_log = true;
                    $companyBooking = $company_booking_client->booking;
                    if ($companyBooking->service?->is_online_payment) {
                        $should_add_log = false;
                        if ($company_booking_client->receipt && $company_booking_client->receipt?->status == CompanyReceipt::PAID) {
                            $should_add_log = true;
                        }
                    }
                    if ($should_add_log && $companyBooking->start_at && $companyBooking->end_at && $companyBooking->user_id) {
                        $data = CompanyBooking::getBookingMailData($companyBooking);
                        $msg = "{$companyBooking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} rescheduled with practitioner {$companyBooking->user->fullName()}";
                        $activity = activity()
                            ->performedOn($company_booking_client)
                            ->by($companyBooking->user)
                            ->log($msg);
                    }
                }
            } else {
                $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();
                $data = CompanyBooking::getBookingMailData($booking);
                foreach ($company_booking_clients as $booking_client) {
                    $companyBooking = $booking_client->booking;
                    $should_add_log = true;
                    $companyBooking = $booking_client->booking;
                    if ($companyBooking->service?->is_online_payment) {
                        $should_add_log = false;
                        if ($booking_client->receipt && $booking_client->receipt?->status == CompanyReceipt::PAID) {
                            $should_add_log = true;
                        }
                    }
                    if ($should_add_log && $companyBooking->start_at && $companyBooking->end_at && $companyBooking->user_id) {
                        $msg = "{$companyBooking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} rescheduled with practitioner {$companyBooking->user->fullName()}";
                        $activity = activity()
                            ->performedOn($booking_client)
                            ->by($companyBooking->user)
                            ->log($msg);
                    }
                }
            }
        }


        try {
            if ($send_mail && $client_email_rescheduled_on) {
                if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
                    if ($company_booking_client) {
                        if ($company_booking_client->client) {
                            Mail::to($company_booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, true, $should_use_lang, $company_booking_client, $language));
                        }
                    } else {
                        $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();
                        $data = CompanyBooking::getBookingMailData($booking);
                        foreach ($company_booking_clients as $booking_client) {
                            Mail::to($booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, true, $should_use_lang, $booking_client, $language));
                        }
                    }
                } else {
                    if ($booking->client) {
                        Mail::to($booking->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, true, $should_use_lang, $company_booking_client, $language));
                    } else {
                        Mail::to($booking->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, true, $should_use_lang, $company_booking_client, $language));
                    }
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
            // dd($th);
        }
        $INTERNAL_BOOKING_RESCHEDULE = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_RESCHEDULE)->value;
        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        if ($INTERNAL_BOOKING_RESCHEDULE) {
            try {
                if (!$company_booking_client && $booking?->service?->category?->group_booking) {
                    foreach ($company_booking_clients as $booking_client) {
                        Mail::to($INTERNAL_BOOKING_RESCHEDULE)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, false, $should_use_lang, $booking_client, $LANGUAGE));
                    }
                } else {
                    Mail::to($INTERNAL_BOOKING_RESCHEDULE)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                }
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }
        $INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER)->value;
        if ($INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER) {
            try {
                if ($booking->user->email) {
                    if (!$company_booking_client && $booking?->service?->category?->group_booking) {
                        foreach ($company_booking_clients as $booking_client) {
                            Mail::to($booking->user->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, false, $should_use_lang, $booking_client, $LANGUAGE));
                        }
                    } else {
                        Mail::to($booking->user->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::RESCHEDULED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                    }
                }
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }

        $should_add_log = true;
        if ($booking->service?->is_online_payment) {
            $should_add_log = false;
            if ($booking->receipt && $booking->receipt?->status == CompanyReceipt::PAID) {
                $should_add_log = true;
            }
        }
        if (
            !($booking->service->category && $booking->service->category?->group_booking) &&
            $should_add_log && $booking->start_at && $booking->end_at && $booking->user_id
        ) {
            $data = CompanyBooking::getBookingMailData($booking);
            $msg = "{$booking->service->name} booking from {$data['booking_start_at']} to {$data['booking_custom_end_at']} rescheduled with practitioner {$booking->user->fullName()}";
            $activity = activity()
                ->performedOn($booking)
                ->by($booking->user)
                ->log($msg);
        }


        $booking->save();
        return true;
    }

    static function updatedBooking(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null, $send_mail = true)
    {
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;

        try {
            if ($send_mail) {
                if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
                    if ($company_booking_client) {
                        if ($company_booking_client->client) {
                            Mail::to($company_booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::UPDATED, true, $should_use_lang, $company_booking_client, $language));
                        }
                    } else {
                        $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();
                        foreach ($company_booking_clients as $booking_client) {
                            Mail::to($booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::UPDATED, true, $should_use_lang, $booking_client, $language));
                        }
                    }
                } else {
                    if ($booking->client) {
                        Mail::to($booking->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::UPDATED, true, $should_use_lang, $company_booking_client, $language));
                    } else {
                        Mail::to($booking->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::UPDATED, true, $should_use_lang, $company_booking_client, $language));
                    }
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
            // dd($th);
        }
        $INTERNAL_BOOKING_CONFIRMATION = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CONFIRMATION)->value;
        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        if ($INTERNAL_BOOKING_CONFIRMATION) {
            try {
                Mail::to($INTERNAL_BOOKING_CONFIRMATION)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::UPDATED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }
        $INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER)->value;
        if ($INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER) {
            try {
                if ($booking->user->email) {
                    Mail::to($booking->user->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::UPDATED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                }
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }
        return true;
    }

    static function practitionerChanged(CompanyBooking $booking, $should_use_lang = false, CompanyBookingClient $company_booking_client = null, $send_mail = true)
    {
        $language = Setting::getSetting($booking->company, Setting::CUSTOMER_LANGUAGE)?->value;
        $company_booking_clients = null;
        try {
            if ($send_mail) {
                if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
                    if ($company_booking_client) {
                        if ($company_booking_client->client) {
                            Mail::to($company_booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, true, $should_use_lang, $company_booking_client, $language));
                        }
                    } else {
                        $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)->where('is_cancelled', 0)->where('is_verified', 1)->get();
                        foreach ($company_booking_clients as $booking_client) {
                            Mail::to($booking_client->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, true, $should_use_lang, $booking_client, $language));
                        }
                    }
                } else {
                    if ($booking->client) {
                        Mail::to($booking->client->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, true, $should_use_lang, $company_booking_client, $language));
                    } else {
                        Mail::to($booking->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, true, $should_use_lang, $company_booking_client, $language));
                    }
                }
            }
        } catch (\Throwable $th) {
            //TODO::handle this
            // dd($th);
        }

        $INTERNAL_BOOKING_CONFIRMATION = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CONFIRMATION)->value;
        $LANGUAGE = Setting::getSetting($booking->company, Setting::LANGUAGE)->value;
        if ($INTERNAL_BOOKING_CONFIRMATION) {
            try {
                if (!$company_booking_client && $booking?->service?->category?->group_booking) {
                    foreach ($company_booking_clients as $booking_client) {
                        Mail::to($INTERNAL_BOOKING_CONFIRMATION)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, false, $should_use_lang, $booking_client, $LANGUAGE));
                    }
                } else {
                    Mail::to($INTERNAL_BOOKING_CONFIRMATION)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                }
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }
        $INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER = Setting::getSetting($booking->company, Setting::INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER)->value;
        if ($INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER) {
            try {
                if ($booking->user->email) {
                    if (!$company_booking_client && $booking?->service?->category?->group_booking) {
                        foreach ($company_booking_clients as $booking_client) {
                            Mail::to($booking->user->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, false, $should_use_lang, $booking_client, $LANGUAGE));
                        }
                    } else {
                        Mail::to($booking->user->email)->locale($language ?? app()->getLocale())->send(new GenericBookingMail($booking, CompanyBooking::PRACTITIONER_CHANGED, false, $should_use_lang, $company_booking_client, $LANGUAGE));
                    }
                }
            } catch (\Throwable $th) {
                //TODO::handle this
            }
        }
        return true;
    }

    static function getBookingMailData(CompanyBooking $booking, CompanyBookingClient $company_booking_client = null)
    {
        $client_name = null;
        $booking_client_name = $booking->first_name . ' ' . $booking->last_name;
        $system_client_name = null;
        if ($booking->client) {
            $system_client_name = $booking->client->first_name . ' ' . $booking->client->last_name;
        }
        if ($booking_client_name == $system_client_name) {
            $client_name = $booking_client_name;
        } else {
            $client_name = $booking_client_name . ' (' . $system_client_name . ')';
        }
        if ($company_booking_client) {
            $client_name = $company_booking_client->client->first_name . ' ' . $company_booking_client->client->last_name;
        }

        $company = $booking->company;
        $place_name = $company->company_name;

        $time = Setting::formateDate($company, Carbon::parse($booking->start_at), true, true);


        $client_email = null;
        if ($booking->client) {
            $client_email = $booking->email ?? $booking->client->email;
        }
        if ($company_booking_client) {
            $client_email = $company_booking_client->client->email;
        }


        $client_mobile_number = null;
        if ($company_booking_client) {
            if ($company_booking_client->client->country_code) {
                $client_mobile_number = '+' . $company_booking_client->client->country_code . $company_booking_client->client->phone_number;
            }
        } else {
            if ($booking->phone_number) {
                if ($booking->client->country_code) {
                    $client_mobile_number = '+' . $booking->client->country_code . $booking->phone_number;
                } else {
                    $client_mobile_number = $booking->phone_number;
                }
            } else {
                if ($booking->client && $booking->client->country_code) {
                    $client_mobile_number = '+' . $booking->client->country_code . $booking->client->phone_number;
                } else {
                    $client_mobile_number = $booking->client ? $booking->client->phone_number : null;
                }
            }
        }

        $practitioner_name = $booking->user->first_name . ' ' . $booking->user->last_name;

        $practitioner_mobile_number = '-';
        if ($booking->user->country_code) {
            $practitioner_mobile_number = '+' . $booking->user->country_code . $booking->user->mobile_number;
        } else {
            $practitioner_mobile_number = $booking->user->mobile_number;
        }

        $practitioner_email = $booking->user->email;
        $full_address = $company->street_address;
        if ($company->zip_code) {
            if ($full_address && $full_address != '') {
                $full_address = $full_address . ', ' . $company->zip_code;
            } else {
                $full_address = $full_address . $company->zip_code;
            }
        }
        if ($company->city) {
            if ($full_address && $full_address != '') {
                $full_address = $full_address . ', ' . $company->city;
            } else {
                $full_address = $full_address . $company->city;
            }
        }
        if ($company->state) {
            if ($full_address && $full_address != '') {
                $full_address = $full_address . ', ' . $company->state;
            } else {
                $full_address = $full_address . $company->state;
            }
        }
        if ($company->country) {
            if ($full_address && $full_address != '') {
                $full_address = $full_address . ', ' . $company->country;
            } else {
                $full_address = $full_address . $company->country;
            }
        }
        // $client_name = $booking->client->first_name . ' ' . $booking->client->last_name;
        $mobile_number = '-';
        $email = null;
        $special_request = null;
        $booking_portal_text = null;
        if ($company_booking_client) {
            $mobile_number = '+' . $company_booking_client->client->country_code . $company_booking_client->client->phone_number;
            $email = $booking->email;
            $special_request = $company_booking_client->special_request;
            $booking_portal_text = Setting::getSetting($company, Setting::BOOKING_PORTAL_TEXT)->value;
        } else {
            $mobile_number = '+' . $booking->client?->country_code . $booking->phone_number;
            $email = $booking->email;
            $special_request = $booking->special_request;
            $booking_portal_text = Setting::getSetting($company, Setting::BOOKING_PORTAL_TEXT)->value;
        }



        $booking_custom_end_at = Carbon::parse($booking->end_at)->subMinutes($booking->time_margin ?? 0);

        $booking_custom_end_at = Setting::formateDate($company, $booking_custom_end_at, true, true);
        $booking_start_at = Setting::formateDate($company, Carbon::parse($booking->start_at), true, true);
        return [
            "company_name" => $company->company_name,
            "place_name" => $place_name,
            "time" => $time,
            "practitioner_name" => $practitioner_name,
            "practitioner_mobile_number" => $practitioner_mobile_number,
            "practitioner_email" => $practitioner_email,
            "client_name" => $client_name,
            "client_email" => $client_email,
            "client_mobile_number" => $client_mobile_number,
            "full_address" => $full_address,
            "mobile_number" => $mobile_number,
            "email" => $email,
            "special_request" => $special_request,
            "booking_portal_text" => $booking_portal_text,
            "booking_custom_end_at" => $booking_custom_end_at,
            "booking_start_at" => $booking_start_at,
        ];
    }

    public static function downloadPDF(CompanyBooking $booking, $is_twelve_hours)
    {
        $booking = $booking->loadMissing(['service', 'client', 'company', 'user']);
        return self::downloadFromView('exports.client.booking', [
            'booking' => $booking,
            'is_twelve_hours' => $is_twelve_hours,
            'company' => $booking->company,
        ]);
    }
}
