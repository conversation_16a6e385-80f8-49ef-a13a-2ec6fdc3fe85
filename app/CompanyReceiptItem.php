<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CompanyReceiptItem extends Model
{
    use HasFactory, Encryptable;

    const TAX_TYPE_INCLUDING = 'including',
        TAX_TYPE_EXCLUDING = 'excluding';

    //TABLE
    public $table = 'company_receipt_items';

    const
        PENDING = "PENDING",
        PROCESSING = "PROCESSING",
        PAID = "PAID",
        CANCELLED = "CANCELLED",
        REFUNDED = "REFUNDED",
        PARTIALLY_REFUNDED = "PARTIALLY_REFUNDED",
        ABORTED = "ABORTED";

    //FILLABLES
    protected $fillable = [
        'receipt_id',
        'name',
        'selling_price',
        'quantity',
        'tax_information',
        'refunded_quantity',
        'discount_percentage',
        'user_id',

        'receiptable_type',
        'receiptable_id',
    ];


    protected $encrypted = [
        'name',
        'selling_price',
        'quantity',
        'tax_information',
        'refunded_quantity',
        'discount_percentage'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [
        'tax_type', // (including | excluding)

        'sub_total', // (price - if(tax_type == "including") tax_amount) * quantity
        'total', // (discounted_price_with_tax) * quantity

        'discounted_amount', // (price * (discount_percent / 100))
        'total_discounted_amount', // (discounted_amount * quantity)

        'discounted_price', // (price - discounted_amount)

        'tax_amount', // (price * (tax_percent / 100))
        'total_tax_amount', // (tax_amount * quantity)

        'discounted_tax_amount', // (discounted_price * (tax_percent / 100))
        'total_discounted_tax_amount', // (discounted_tax_amount * quantity)

        'discounted_price_with_tax', // (discounted_price + if(tax_type == "excluding") discounted_tax_amount)

        'remaining_quantity', // (quantity - refunded_quantity)
        'remaining_amount',

        'status',
    ];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}
    public function receiptable(): MorphTo
    {
        return $this->morphTo();
    }

    public function receipt()
    {
        return $this->belongsTo(CompanyReceipt::class);
    }

    public function refund_item()
    {
        return $this->hasOne(CompanyReceiptRefundItem::class, 'receipt_item_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    //ATTRIBUTES
    public function getStatusAttribute()
    {
        if ($this->remaining_quantity == $this->quantity) {
            return null;
        }

        if ($this->remaining_quantity <= 0) {
            return self::REFUNDED;
        }

        return self::PARTIALLY_REFUNDED;
    }

    public function getRemainingAmountAttribute()
    {
        return  CompanyReceipt::roundValue((($this->total ?? 0 ) / ($this->quantity ?? 0) ) * ($this->remaining_quantity ?? 0));
    }

    //ATTRIBUTES
    public function getTaxTypeAttribute()
    {
        return self::TAX_TYPE_INCLUDING;
    }

    //ATTRIBUTES
    public function getSubTotalAttribute()
    {
        if ($this->tax_type == self::TAX_TYPE_INCLUDING) {
            return CompanyReceipt::roundValue(($this->selling_price - $this->tax_amount) * $this->quantity);
        }

        return CompanyReceipt::roundValue($this->selling_price * $this->quantity);
    }

    //ATTRIBUTES
    public function getTotalAttribute()
    {
        return CompanyReceipt::roundValue($this->discounted_price_with_tax * $this->quantity);
    }

    //ATTRIBUTES
    public function getDiscountedAmountAttribute()
    {
        $price = $this->selling_price;

        $discounted_price = ($this->discount_percentage / 100) * $price;

        return CompanyReceipt::roundValue($discounted_price);
    }

    //ATTRIBUTES
    public function getTotalDiscountedAmountAttribute()
    {
        return CompanyReceipt::roundValue($this->discounted_amount * $this->quantity);
    }

    //ATTRIBUTES
    public function getDiscountedPriceAttribute()
    {
        return CompanyReceipt::roundValue($this->selling_price - $this->discounted_amount);
    }

    //ATTRIBUTES
    public function getTaxAmountAttribute()
    {
        $tax_percentage = ($this->tax_information ?? 0);

        if ($this->tax_type == self::TAX_TYPE_INCLUDING) {
            // fix divide by zero error
            if ($tax_percentage == 0 || $this->selling_price == 0) {
                return 0;
            }
            $tax = ($this->selling_price * $tax_percentage) / ($this->selling_price + $tax_percentage);
            $tax = $this->selling_price - ($this->selling_price / (1 + ($tax_percentage / 100)));

            return CompanyReceipt::roundValue($tax);
        }

        $tax = $this->selling_price * ($tax_percentage / 100);

        return CompanyReceipt::roundValue($tax);
    }

    //ATTRIBUTES
    public function getTotalTaxAmountAttribute()
    {
        return CompanyReceipt::roundValue($this->tax_amount * $this->quantity);
    }

    //ATTRIBUTES
    public function getDiscountedTaxAmountAttribute()
    {
        $tax_percentage = ($this->tax_information ?? 0);

        if ($this->tax_type == self::TAX_TYPE_INCLUDING) {
            $tax = $this->discounted_price - ($this->discounted_price / (1 + ($tax_percentage / 100)));

            return CompanyReceipt::roundValue($tax);
        }

        $tax = $this->discounted_price * ($tax_percentage / 100);

        return CompanyReceipt::roundValue($tax);
    }

    //ATTRIBUTES
    public function getTotalDiscountedTaxAmountAttribute()
    {
        return CompanyReceipt::roundValue($this->discounted_tax_amount * $this->quantity);
    }

    //ATTRIBUTES
    public function getDiscountedPriceWithTaxAttribute()
    {
        if ($this->tax_type == self::TAX_TYPE_INCLUDING) {
            return CompanyReceipt::roundValue($this->discounted_price);
        }

        return CompanyReceipt::roundValue($this->discounted_price + $this->discounted_tax_amount);
    }

    //ATTRIBUTES
    public function getRemainingQuantityAttribute()
    {
        return $this->quantity - $this->refunded_quantity;
    }
}
