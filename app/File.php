<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Support\Facades\Crypt;
use Intervention\Image\Facades\Image;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Throwable;

class File extends Model
{
    use Encryptable, GetEncryptedFile;
    protected $encrypted = [
        'url',
        'filename',
        'thumbnail',
        'original_filename'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'url',
        'fileable_type',
        'fileable_id',
        'user_id',
        'size',
        'filename',
        'company_id',
        'thumbnail',
        'original_filename',
        'created_at',
        'updated_at',
    ];


    protected $appends = [];

    /**
     * Get the owning commentable model.
     */
    public function fileable()
    {
        return $this->morphTo();
    }

    protected $dates = [
        'created_at',
        'updated_at'
    ];

    function client_zip_file()
    {
        return $this->belongsTo(Client::class, 'fileable_id', 'id')->where('fileable_type', 'client_zip');
    }

    public function getFileNameDataAttribute($value)
    {
        // if($value) {
        //     $values = explode(',',$value);
        //     // return collect($values)->map(function($value){
        //     //     try {
        //     //         return Crypt::decrypt($value);
        //     //     } catch (\Throwable $th) {
        //     //         return $value;
        //     //     }
        //     // });
        // }
        return $value;
    }

    public function getIsImageAttribute()
    {
        $filename = $this->filename;
        $ext = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array($ext, ['jpg', 'jpeg', 'png', 'webp'])) {
            return true;
        }
        return false;
    }

    public function getExtensionAttribute()
    {
        $filename = $this->filename;
        return pathinfo($filename, PATHINFO_EXTENSION);
    }

    public function getUrlAttribute($value)
    {
        if ($this->filename) {
            return Crypt::encrypt((string)$this->getS3SignedUrl($this->filename));
        }
        return $this->filename;
    }

    public function getThumbnailAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    public function getIdsAttribute($value)
    {
        if ($value) {
            $values = explode(',', $value);
            return $values;
        }
        return $value;
    }

    public function generateThumbnail()
    {
        if (Storage::disk('s3')->exists($this->filename)) {
            try {
                $ext = pathinfo(basename($this->filename), PATHINFO_EXTENSION);

                Storage::disk('public')->put("test.$ext", Storage::disk('s3')->get($this->filename));

                $image = Image::make(Storage::disk('public')->path("test.$ext"))->resize(200, 200, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                })->orientate();

                $basename = basename($this->filename);
                $basepath = str_replace($basename, '', $this->filename);

                $new_filename = $basepath . 'thumbnail/' . $basename;

                Storage::disk('s3')->put($new_filename, $image->stream()->__toString());
                return $new_filename;
            } catch (\Throwable $th) {
                throw $th;
            }
        }
    }
}