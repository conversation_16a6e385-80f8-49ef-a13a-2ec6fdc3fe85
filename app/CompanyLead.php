<?php

namespace App;

use App\Company;
use Illuminate\Database\Eloquent\Model;

class CompanyLead extends Model
{
    const WIN='win', LOST='lost', NOT_DECIDED='not_decided';
    const STATUS = [
        self::WIN,
        self::LOST,
        self::NOT_DECIDED,
    ];

    protected $fillable = [
        'company_id', 'notes', 'status', 'contacted'
    ];

    protected $casts = [
        'contacted' => 'boolean',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
