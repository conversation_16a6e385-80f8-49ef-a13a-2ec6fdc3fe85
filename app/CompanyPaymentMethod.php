<?php

namespace App;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyPaymentMethod extends Model
{
    use HasFactory;

    const SMS = "SMS";
    const MAL = "MAL";
    const RECORD = "RECORD";

    const ALL_LABELS = [
        CompanyPaymentMethod::SMS,
        CompanyPaymentMethod::MAL,
        CompanyPaymentMethod::RECORD
    ];

    //TABLE
    public $table = 'company_payment_methods';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'payment_method_id',
        'labels',
        'meta_data'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'labels' => 'array',
        'meta_data' => 'array',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    static function getPaymentMethodByLabel(Company $company, $label)
    {
        $payment_method = CompanyPaymentMethod::where('company_id', $company->id)->whereJsonContains('labels', $label)->first();
        if (!$payment_method) {
            $default_payment_id = $company->defaultPaymentMethod()->id;
            $payment_method = CompanyPaymentMethod::where('company_id', $company->id)->where('payment_method_id', $default_payment_id)->first();
            if (!$payment_method) {
                $payment_method_info = $company->findPaymentMethod($default_payment_id);
                $payment_method = CompanyPaymentMethod::create([
                    'company_id' => $company->id,
                    'payment_method_id' => $default_payment_id,
                    'labels' => [$label],
                    'meta_data' => [
                        'last4' => (string)$payment_method_info->card->last4,
                        'exp_year' => (string)$payment_method_info->card->exp_year,
                        'exp_month' => (string)$payment_method_info->card->exp_month,
                    ]
                ]);
            }
        }
        if ($payment_method && $payment_method->labels) {
            if (!in_array($label, $payment_method->labels)) {
                $payment_method->labels = array_merge($payment_method->labels, [$label]);
                $payment_method->save();
            }
        } else {
            $payment_method->labels = [$label];
            $payment_method->save();
        }

        return $payment_method;
    }

    static function clearAllLabels(CompanyPaymentMethod $payment_method)
    {
        $payment_method->labels =  [];
        $payment_method->save();
    }

    static function addLabel(CompanyPaymentMethod $payment_method, $label)
    {
        $payment_methods_to_update = CompanyPaymentMethod::where('company_id', $payment_method->company_id)->whereJsonContains('labels', $label)->cursor();
        foreach ($payment_methods_to_update as $payment_method_to_update) {
            $payment_method_to_update->labels =  collect($payment_method_to_update->labels)->forget(collect($payment_method_to_update->labels)->search($label))->values()->all();
            $payment_method_to_update->save();
        }
        $payment_method = $payment_method->refresh();
        $payment_method->labels = collect($payment_method->labels)->add($label)->values()->all();
        $payment_method->save();
    }

    static function removeLabel(CompanyPaymentMethod $payment_method, $label)
    {
        $payment_method->labels = collect($payment_method->labels)->forget(collect($payment_method->labels)->search($label))->values()->all();
        $payment_method->save();
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
