<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScheduleTemplateTimeSlot extends Model
{
    use HasFactory, Encryptable;

    //TABLE
    public $table = 'schedule_template_time_slots';

    protected $encrypted = [
        'note',
    ];

    //FILLABLES
    protected $fillable = [
        'schedule_template_id',
        'note',
        'week_day',
        'type',
        'start_time',
        'end_time',
        'is_for_all_day',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}