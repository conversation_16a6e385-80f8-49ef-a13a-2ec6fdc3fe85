<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class ClientPrescription extends Model
{
    use HasFactory, Encryptable, GetEncryptedFile, SoftDeletes;

    protected $encrypted = [
        'title',
        'description',
        'filenames',
        'sign',
        'cancel_note',
        'signed_by_bank_id'
    ];

    //TABLE
    public $table = 'client_prescriptions';

    //FILLABLES
    protected $fillable = [
        'client_id',
        'sign_by_id',
        'assign_to_id',
        'fees',
        'title',
        'description',
        'filenames',
        'expire_at',
        'signed_at',
        'sign',
        'platform_fees',
        'signed_by_name',
        'is_cancelled',
        'cancel_note',
        'cancelled_by_id',
        'cancelled_at',
        'is_signed_by_bank_id',
        'signed_by_bank_id',
    ];

    //HIDDEN
    protected $hidden = ['pivot'];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'filenames' => 'array',
    ];

    //RULES
    public static $getListRules = [];


    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function company()
    {
        return $this->hasOneThrough(Company::class, Client::class, 'id', 'id', 'client_id', 'company_id');
    }
    //for creation of 1 file
    public function file()
    {
        return $this->morphOne(File::class, 'fileable')->latest();
    }
    public function files()
    {
        return $this->morphMany(File::class, 'fileable')->latest();
    }
    public function signed_by()
    {
        return $this->belongsTo('App\User', 'sign_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }
    public function cancelled_by()
    {
        return $this->belongsTo('App\User', 'cancelled_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function assigned_to()
    {
        return $this->belongsTo(User::class, 'assign_to_id');
    }
    public function getSignAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    public function logs()
    {
        return $this->morphMany(Activity::class, 'subject')->latest();
    }

    public function scopePlucked($query)
    {
        return $query->select([
            'client_prescriptions.id',
            'client_prescriptions.title',
            'client_prescriptions.expire_at',
        ]);
    }

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
