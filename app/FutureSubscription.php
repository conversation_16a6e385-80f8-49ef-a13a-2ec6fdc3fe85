<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FutureSubscription extends Model
{
    use HasFactory;

    const TYPE_POS = 'pos';
    const TYPE_RECORD = 'record';
    const TYPE_BOOKING = 'booking';

    //TABLE
    public $table = 'future_subscriptions';

    //FILLABLES
    protected $fillable = [
        'stripe_plan',
        'type', // (pos,record, etc)
        'start_datetime',
        'coupon',
        'company_id',
        'quantity',
        'is_yearly',
        'is_cancelled'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'is_yearly' => 'boolean',
    ];

    //RULES
    public static $getListRules = [];

    function isActive()
    {
        if ($this->start_datetime == null) {
            return true;
        }

        return Carbon::parse($this->start_datetime) >= Carbon::now()->startOfDay();
    }

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
