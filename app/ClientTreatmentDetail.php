<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class ClientTreatmentDetail extends Model
{
    use Encryptable, LogsActivity, GetEncryptedFile;
    const TYPE_TREATMENT = 'treatment';
    const TYPE_TEXT = 'text';

    protected $encrypted = [
        'name',
        'description',
        'cost',
        'color',
        'notes',
        'unit',
        'actual_unit',
        'type',
    ];

    protected $fillable = [
        'id',
        'name',
        'description',
        'cost',
        'unit',
        'color',
        'notes',
        'type',
        'actual_cost',
        'actual_unit',
        'client_treatment_id',
        'treatment_id',
    ];

    public function setActualCostAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['actual_cost'] = $value;
    }

    public function getActualCostAttribute($value)
    {
        try {
            if ($value != null) {
                return (string) json_decode(Crypt::decrypt($value));
            }
        } catch (\Throwable $th) {
        }

        return $value;
    }

    public function clientTreatment()
    {
        return $this->belongsTo(ClientTreatment::class);
    }

    public function treatment()
    {
        return $this->hasOne(Treatment::class);
    }

    public function unitSymbol(): string
    {
        if ($this->unit === 'usd') {
            return '$';
        }
        if ($this->unit === 'eur') {
            return '€';
        }
        if ($this->unit === 'sek') {
            return 'kr';
        }
        if ($this->unit === 'gbp') {
            return '£';
        }

        return $this->unit;
    }
}
