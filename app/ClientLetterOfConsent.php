<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GeneratePDF;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class ClientLetterOfConsent extends Model
{
    use Encryptable, LogsActivity, GetEncryptedFile, GeneratePDF, SoftDeletes;

    protected $encrypted = [
        'consent_id',
        'is_bad_allergic_shock',
        'signature',
        'consent_title',
        'letter',
        'is_publish_before_after_pictures',
        'version',
        'signed_file',
        'verified_sign',
        'cancel_note',
        'signed_by_bank_id'
    ];

    protected $fillable = [
        'signed_file',
        'client_id',
        'consent_id',
        'is_bad_allergic_shock',
        'signature',
        'consent_title',
        'letter',
        'is_publish_before_after_pictures',
        'version',
        'verified_sign',
        'verified_signed_at',
        'verified_sign_by_id',
        'is_cancelled',
        'cancel_note',
        'cancelled_by_id',
        'cancelled_at',
        'is_signed_by_bank_id',
        'signed_by_bank_id'
    ];

    protected $casts  = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $dates = [
        'created_at',
        'updated_at'
    ];

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function verified_signed_by()
    {
        return $this->belongsTo('App\User', 'verified_sign_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }
    public function cancelled_by()
    {
        return $this->belongsTo('App\User', 'cancelled_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function files()
    {
        return $this->morphMany('App\File', 'fileable');
    }

    public function getSignatureAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }
    public function getVerifiedSignAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }


    protected static $logAttributes = [];

    protected static $logName = 'client letter of consent';

    public function getDescriptionForEvent(string $eventName): string
    {
        $client_user = $this->client()->first();
        return $client_user->first_name . " " . $client_user->last_name . "'s letter of consent :subject.consent_title has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public static function downloadPDF(ClientLetterOfConsent $loc, $is_twelve_hours)
    {
        return self::downloadFromView('exports.client.LOC', [
            'client' => $loc->client,
            'letter_of_consent' => $loc,
            'is_twelve_hours' => $is_twelve_hours,
            'company' => $loc->client->company,
        ]);
    }
}
