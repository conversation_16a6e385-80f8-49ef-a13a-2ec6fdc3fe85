<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class CompanyExport implements FromView
{
    public $companies;

    public function __construct(Collection $companies)
    {
        $this->companies = $companies;
    }

    public function view(): View
    {
        return view('exports.companies', [
            'companies' => $this->companies
        ]);
    }
}
