<?php

namespace App\Exports;

use App\CompanyGiftCard;
use App\CompanyProduct;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class CompanyProductExport implements FromQuery, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping
{
    use Exportable;

    public $user;

    public function __construct($user)
    {
        $this->user = $user;
    }

    public function query()
    {
        return CompanyProduct::query()
            ->where('company_id', $this->user->company_id)
            ->withTrashed()
            ->with(['category']);
    }

    public function headings(): array
    {
        return [
            __('pos_strings.product_id'),
            __('pos_strings.product_barcode'),
            __('pos_strings.product_name'),
            __('pos_strings.product_category'),
            __('pos_strings.product_description'),
            __('pos_strings.product_selling_price'),
            __('pos_strings.product_base_price'),
            __('pos_strings.product_tax_information'),
            __('pos_strings.product_stock'),
            __('pos_strings.product_status'),
        ];
    }

    /**
     * @var CompanyProduct $product
     */
    public function map($product): array
    {
        return [
            $product->product_code,
            $product->barcode,
            $product->name,
            $product?->category?->name,
            $product->description,
            $this->user->company->withCurrency($product->selling_price),
            $this->user->company->withCurrency($product->base_price),
            "$product->tax_information%",
            $product->stock ?? '0',
            $product->deleted_at ? __('pos_strings.gift_card.' . CompanyGiftCard::STATUS_INACTIVE) :  __('pos_strings.gift_card.' . CompanyGiftCard::STATUS_ACTIVE),
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->user->company->name . "'s " . __('pos_strings.products');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
