<?php

namespace App\Exports;

use App\Client;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ClientExportPDF implements FromView
{
    public $client;

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function view(): View
    {
        return view('exports.client', [
            'client' => $this->client
        ]);
    }
}
