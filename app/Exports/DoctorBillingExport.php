<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class DoctorBillingExport implements FromView
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public $user_billings;

    public function __construct($user_billings)
    {
        $this->user_billings = $user_billings;
    }

    public function view(): View
    {
        return view('exports.user_billing_excel', [
            'user_billings' => $this->user_billings,
        ]);
    }
}