<?php

namespace App\Exports;

use App\Company;
use App\CompanyGiftCard;
use App\Setting;
use App\Traits\TimeZoneManager;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class CompanyGiftCardExport implements FromQuery, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping
{
    use Exportable, TimeZoneManager;

    public $company;
    public $dateFormat;
    public $dateTimeFormat;
    public $timezone;

    public function __construct(Company $company)
    {
        $this->company = $company;

        $this->timezone = $this->getCarbonApprovedTimeZone($company->timezone);
        $this->dateFormat = Setting::getDateTimeFormat($company);
        $this->dateTimeFormat = Setting::getDateTimeFormat($company, true);
    }

    public function query()
    {
        return CompanyGiftCard::query()
            ->with(['client' => function ($query) {
                $query->setEagerLoads([]);
            }])
            ->where('company_id', $this->company->id);
    }

    public function headings(): array
    {
        return [
            __('pos_strings.gift_card_number'),
            __('pos_strings.gift_card_status'),
            __('pos_strings.gift_card_paid_at'),
            __('pos_strings.gift_card_start_date'),
            __('pos_strings.gift_card_end_date'),
            __('pos_strings.gift_card_customer'),
            __('pos_strings.gift_card_initial_value'),
            __('pos_strings.gift_card_remaining_value'),
        ];
    }

    /**
     * @var CompanyGiftCard $gift_card
     */
    public function map($gift_card): array
    {
        return [
            $gift_card->padded_gift_code,
            __("pos_strings.gift_card.{$gift_card->status}"),
            $gift_card?->paid_at?->setTimezone($this->timezone)?->format($this->dateTimeFormat),
            $gift_card?->created_at?->setTimezone($this->timezone)?->format($this->dateFormat),
            $gift_card?->expired_at?->setTimezone($this->timezone)?->format($this->dateFormat),
            $gift_card?->client?->full_name ?? '',
            $this->company->withCurrency($gift_card->current_value),
            $this->company->withCurrency($gift_card->initial_value),
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('pos_strings.gift_card.company_s_gift_card', ['company' => $this->company->name]);
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
