<?php

namespace App\Exports\Reports\Booking;

use App\Company;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BookingCancelledSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $period = $this->request['period'] ?? 'week';

        $periods = CarbonPeriod::create(Carbon::parse($start_date)->startOfDay(), "1 {$period}", Carbon::parse($end_date)->endOfDay());

        $total_cancelled = 0;
        $total_no_show = 0;

        foreach ($periods as $date) {
            if ($period == 'month') {
                $key = $date->format("M y");
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            } elseif ($period == 'week') {
                $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
            } elseif ($period == 'day') {
                $key = $date->format("d M");
                $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
            } else {
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            }

            $bookings = $company->bookings()
                ->select('company_bookings.*')
                ->with(['service.category:id,group_booking'])
                ->withCount([
                    'clients as cancelled_clients_count' => function ($query) {
                        $query->where('is_cancelled', 1);
                    },
                    'clients as no_show_clients_count' => function ($query) {
                        $query->where('is_shown', 0);
                    }
                ])
                ->when($user_ids, function ($query) use ($user_ids) {
                    return $query->whereIn('company_bookings.user_id', $user_ids);
                })
                ->whereBetween('created_at', [$start_date, $end_date])
                ->get();

            $single_booking_cancelled = $bookings->filter(function ($booking) {
                return $booking->is_cancelled == 1 &&
                    (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
            })->count();

            $group_booking_client_cancelled = $bookings->filter(function ($booking) {
                return $booking->slot_released_at === null &&
                    $booking->service?->category?->group_booking == 1;
            })->sum('cancelled_clients_count');

            $booking_cancelled = $single_booking_cancelled + $group_booking_client_cancelled;

            $single_booking_no_show = $bookings->filter(function ($booking) {
                return $booking->is_shown == 0 &&
                    (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
            })->count();

            $group_booking_client_no_shows = $bookings->filter(function ($booking) {
                return $booking->slot_released_at === null &&
                    $booking->service?->category?->group_booking == 1;
            })->sum('no_show_clients_count');

            $booking_no_show = $single_booking_no_show + $group_booking_client_no_shows;

            $total_cancelled += $booking_cancelled;
            $total_no_show += $booking_no_show;

            $data[] = ['name' => $key, 'value' => $booking_cancelled, 'value2' => $booking_no_show];
        }

        $data[] = ['name' => 'Total', 'value' => $total_cancelled, 'value2' => $total_no_show];
        return collect($data);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.date'),
            __('strings.reports.booking_cancelled'),
            __('strings.reports.booking_no_show'),
        ];
    }

    public function map($data): array
    {
        return [
            $data['name'],
            (string) ($data['value'] ?? 0),
            (string) ($data['value2'] ?? 0),
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.bookings.booking_cancelled_and_no_show');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
