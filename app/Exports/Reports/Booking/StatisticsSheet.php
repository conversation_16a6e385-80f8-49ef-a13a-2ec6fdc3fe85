<?php

namespace App\Exports\Reports\Booking;

use App\Company;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class StatisticsSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $bookings = $company->bookings()
            ->select('company_bookings.*')
            ->with(['service.category:id,group_booking'])
            ->withCount([
                'clients' => function ($query) {
                    $query->whereNull('slot_released_at');
                },
                'clients as no_show_clients_count' => function ($query) {
                    $query->where('is_shown', 0)->whereNull('slot_released_at');
                },
                'clients as active_clients_count' => function ($query) {
                    $query->where(['is_cancelled' => 0, 'is_shown' => 1])->whereNull('slot_released_at');
                },
                'clients as cancelled_clients_count' => function ($query) {
                    $query->where('is_cancelled', 1)->whereNull('slot_released_at');
                }
            ])
            ->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('company_bookings.user_id', $user_ids);
            })
            ->whereNull('slot_released_at')
            ->whereBetween('created_at', [$start_date, $end_date])
            ->get();

        $single_bookings = $bookings->filter(function ($booking) {
            return (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
        })->count();

        $group_bookings = $bookings->filter(function ($booking) {
            return $booking->service?->category?->group_booking == 1 && $booking->clients_count > 0;
        })->count();

        $group_bookings_individual = $bookings->filter(function ($booking) {
            return $booking->service?->category?->group_booking == 1;
        })->sum('clients_count');

        $bookings_revenue = $bookings->filter(function ($booking) {
            return $booking->is_cancelled == 0 && $booking->is_shown == 1;
        })->sum(function ($booking) {
            if ($booking->service?->category?->group_booking == 1) {
                return $booking->active_clients_count > 0 ? $booking->price * $booking->active_clients_count : 0;
            }
            return $booking->price;
        });

        $single_booking_cancelled = $bookings->filter(function ($booking) {
            return $booking->is_cancelled == 1 &&
                (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
        })->count();

        $group_booking_client_cancelled = $bookings->filter(function ($booking) {
            return $booking->service?->category?->group_booking == 1;
        })->sum('cancelled_clients_count');

        $booking_cancelled = $single_booking_cancelled + $group_booking_client_cancelled;

        $single_booking_no_show = $bookings->filter(function ($booking) {
            return $booking->is_shown == 0 &&
                (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
        })->count();

        $group_booking_client_no_shows = $bookings->filter(function ($booking) {
            return $booking->service?->category?->group_booking == 1;
        })->sum('no_show_clients_count');

        $booking_no_show = $single_booking_no_show + $group_booking_client_no_shows;

        return collect([[
            'total_single_bookings' => (string) ($single_bookings ?? 0),
            'total_group_bookings' => (string) ($group_bookings ?? 0),
            'total_group_bookings_individual' => (string) ($group_bookings_individual ?? 0),
            'total_bookings_revenue' => (string) ($bookings_revenue ?? 0),
            'total_cancelled_bookings' => (string) ($booking_cancelled ?? 0),
            'total_bookings_no_show' => (string) ($booking_no_show ?? 0),
        ]]);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.total_single_bookings'),
            __('strings.reports.total_group_bookings'),
            __('strings.reports.booking_revenue'),
            __('strings.reports.cancelled_bookings'),
            __('strings.reports.no_show_bookings'),
        ];
    }

    public function map($data): array
    {
        return [
            $data['total_single_bookings'],
            $data['total_group_bookings'] . ' (' . $data['total_group_bookings_individual'] . ')',
            $data['total_bookings_revenue'],
            $data['total_cancelled_bookings'],
            $data['total_bookings_no_show'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.bookings.booking_statistics');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
