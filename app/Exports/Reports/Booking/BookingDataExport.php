<?php

namespace App\Exports\Reports\Booking;

use App\Company;
use App\Exports\Reports\Booking\StatisticsSheet;
use App\Exports\Reports\Booking\BookingSheet;
use App\Exports\Reports\Booking\BookingCancelledSheet;
use App\Exports\Reports\Booking\TopPractitionersSheet;
use App\Exports\Reports\Booking\TopBookingServicesSheet;
use App\Exports\Reports\Booking\TopServiceCategoriesSheet;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class BookingDataExport implements WithMultipleSheets
{
    use Exportable;

    protected $company;

    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new StatisticsSheet($this->company);
        $sheets[] = new BookingSheet($this->company);
        $sheets[] = new TopPractitionersSheet($this->company);
        $sheets[] = new TopBookingServicesSheet($this->company);
        $sheets[] = new TopServiceCategoriesSheet($this->company);
        $sheets[] = new BookingCancelledSheet($this->company);

        return $sheets;
    }
}