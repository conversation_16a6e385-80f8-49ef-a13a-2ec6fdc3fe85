<?php

namespace App\Exports\Reports\Booking;

use App\Company;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class TopPractitionersSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $users = $company->users()
            ->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('users.id', $user_ids);
            })
            ->withOut('company')
            ->with('bookings', function ($query) use ($user_ids, $start_date, $end_date) {
                $query->when($user_ids, function ($query) use ($user_ids) {
                    return $query->whereIn('company_bookings.user_id', $user_ids);
                })
                    ->whereNull('slot_released_at')
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->withCount([
                        'clients' => function ($query) {
                            $query->whereNull('slot_released_at');
                        },
                        'clients as active_clients_count' => function ($query) {
                            $query->where(['is_cancelled' => 0, 'is_shown' => 1])->whereNull('slot_released_at');
                        }
                    ]);
            })
            ->get()
            ->map(function ($user) {
                $total_price = $user->bookings->sum(function ($booking) {
                    if ($booking->is_cancelled == 0 && $booking->is_shown == 1) {
                        if ($booking->service?->category?->group_booking == 1) {
                            return $booking->active_clients_count > 0 ? $booking->price * $booking->active_clients_count : 0;
                        }
                        return $booking->price;
                    }
                });

                $single_bookings = $user->bookings->filter(function ($booking) {
                    return (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                })->count() ?? 0;

                $group_bookings = $user->bookings->filter(function ($booking) {
                    return $booking->service?->category?->group_booking == 1 && $booking->clients_count > 0;
                })->count() ?? 0;

                $user->bookings_sum_price = $total_price;
                $user->bookings_count = $single_bookings + $group_bookings;

                return $user;
            });

        $users = $users->sortByDesc('bookings_count')->values();

        return $users;
    }

    public function headings(): array
    {
        return [
            __('strings.reports.name'),
            __('strings.reports.booked'),
            __('strings.reports.revenue'),
        ];
    }

    public function map($data): array
    {
        return [
            $data['first_name'] . ' ' . $data['last_name'],
            (string) ($data['bookings_count'] ?? 0),
            (string) ($data['bookings_sum_price'] ?? 0),
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.bookings.top_practitioners');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
