<?php

namespace App\Exports\Reports\Booking;

use App\Company;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BookingTypeSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $bookings_online = (string) $company->bookings()->when($user_ids, function ($query) use ($user_ids) {
            return $query->whereIn('company_bookings.user_id', $user_ids);
        })->whereBetween('created_at', [$start_date, $end_date])->has('receipt')->count();

        $bookings_manual = (string) $company->bookings()->when($user_ids, function ($query) use ($user_ids) {
            return $query->whereIn('company_bookings.user_id', $user_ids);
        })->whereBetween('created_at', [$start_date, $end_date])->doesntHave('receipt')->count();

        $data[] = ["key" => "Online", "value" => $bookings_online];
        $data[] = ["key" => "Manual", "value" => $bookings_manual];

        return collect($data);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.booking_type'),
            __('strings.reports.bookings'),
        ];
    }

    public function map($data): array
    {
        return [
            $data['key'],
            $data['value'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.bookings.booking_by_online_and_manual');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
