<?php

namespace App\Exports\Reports\POS;

use App\Company;
use App\CompanyReceipt;
use App\CompanyReceiptItem;
use App\Http\Controllers\Api\v3\ReportController;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class StatisticsSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $data = ReportController::posStatistics($company, $start_date, $end_date, $user_ids);

        return collect([[
            "revenue" =>  (string) $data['total_revenue'],
            "transactions" => (string) $data['no_of_transactions'],
            "avg_transaction" => (string) $data['avg_transaction'],
            "refunds" => (string) $data['total_refunds'],
        ]]);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.total_revenue'),
            __('strings.reports.num_transactions'),
            __('strings.reports.avg_transaction'),
            __('strings.reports.refunds'),
        ];
    }

    public function map($data): array
    {
        return [
            $data['revenue'],
            $data['transactions'],
            $data['avg_transaction'],
            $data['refunds'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.pos.pos_statistics');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
