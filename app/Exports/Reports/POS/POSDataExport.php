<?php

namespace App\Exports\Reports\POS;

use App\Company;
use App\Exports\Reports\POS\StatisticsSheet;
use App\Exports\Reports\POS\RevenueSheet;
use App\Exports\Reports\POS\PaymentGatewaySheet;
use App\Exports\Reports\POS\ClientLifetimeValueSheet;
use App\Exports\Reports\POS\PractitionerPerformanceSheet;
use App\Exports\Reports\POS\DiscountSheet;
use App\Exports\Reports\POS\TopSellingProductsSheet;
use App\Exports\Reports\POS\TopBookedServicesSheet;
use App\Exports\Reports\POS\LowStockProductsSheet;
use App\Exports\Reports\POS\RefundsSheet;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class POSDataExport implements WithMultipleSheets
{
    use Exportable;

    protected $company;

    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new StatisticsSheet($this->company);
        $sheets[] = new RevenueSheet($this->company);
        $sheets[] = new PaymentGatewaySheet($this->company);
        $sheets[] = new ClientLifetimeValueSheet($this->company);
        $sheets[] = new PractitionerPerformanceSheet($this->company);
        $sheets[] = new DiscountSheet($this->company);
        $sheets[] = new TopSellingProductsSheet($this->company);
        $sheets[] = new TopBookedServicesSheet($this->company);
        $sheets[] = new LowStockProductsSheet($this->company);
        $sheets[] = new RefundsSheet($this->company);

        return $sheets;
    }
}
