<?php

namespace App\Exports\Reports\Record;

use App\Client;
use App\Company;
use App\UserCompany;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class PrescriptionSignedSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $doctor_ids = $company->doctors()
            ->where('user_companies.invite_status', UserCompany::ACCEPTED)
            ->pluck('users.id')
            ->toArray();

        if ($user_ids) {
            $prescriber_ids = array_values(array_intersect($user_ids, $doctor_ids));
            $user_ids = array_values(array_diff($user_ids, $prescriber_ids));

            $prescriptions_by_own = $company->prescriptions()
                ->whereIn('sign_by_id', $user_ids)
                ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                ->count();

            $prescriptions_by_mal = $company->prescriptions()
                ->whereIn('sign_by_id', $prescriber_ids)
                ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                ->count();
        } else {
            $company_users = $company->users()->pluck('id')->toArray();
            $prescriptions_by_own = $company->prescriptions()
                ->whereIn('sign_by_id', $company_users)
                ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                ->count();

            $prescriptions_by_mal = $company->prescriptions()
                ->whereIn('sign_by_id', $doctor_ids)
                ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                ->count();
        }

        $data[] = ["key" => "Own", "value" => (string) $prescriptions_by_own];
        $data[] = ["key" => "MAL", "value" => (string) $prescriptions_by_mal];

        return collect($data);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.performed_by'),
            __('strings.reports.prescriptions_signed'),
        ];
    }

    /**
     * @var Client $client
     */
    public function map($data): array
    {
        return [
            $data['key'],
            $data['value'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.records.prescription_signed');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
