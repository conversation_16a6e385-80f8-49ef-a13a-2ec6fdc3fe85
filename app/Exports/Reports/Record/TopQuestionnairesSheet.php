<?php

namespace App\Exports\Reports\Record;

use App\Client;
use App\Company;
use App\QuestionaryData;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class TopQuestionnairesSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $question = QuestionaryData::with('modelable')
            ->whereHas('client', function ($query) use ($user_ids, $company) {
                if ($user_ids) {
                    $query = $query->whereIn('user_id', $user_ids);
                }
                return $query->where('company_id', $company->id);
            })
            ->whereBetween('created_at', [$start_date, $end_date])
            ->get()->append('title')->groupBy('modelable_type');

        $data = collect();

        $question = $question->map(function ($item, $key) use ($data) {
            if ($key === 'App\\Questionary') {
                return $item->map(function ($item, $key) use ($data) {
                    $count = $data[$item->modelable->title]['value'] ?? 0;
                    $data->put($item->modelable->title, ['name' => $item->modelable->title, 'value' => $count + 1]);
                });
            }
            $data->put($key, ['name' => $item->first()->title, 'value' => $item->count()]);
        });

        $data =  $data->sortByDesc('value')->values();


        return $data;
    }

    public function headings(): array
    {
        return [
            __('strings.reports.title'),
            __('strings.reports.complete'),
        ];
    }

    /**
     * @var Client $client
     */
    public function map($data): array
    {
        return [
            $data['name'],
            $data['value'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.records.top_questionnaires');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
