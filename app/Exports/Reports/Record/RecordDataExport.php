<?php

namespace App\Exports\Reports\Record;

use App\Client;
use App\Company;
use App\Exports\Reports\Record\StatisticsSheet;
use App\Exports\Reports\Record\ClientRegistrationSheet;
use App\Exports\Reports\Record\MostProceduresSheet;
use App\Exports\Reports\Record\PrescriptionSignedSheet;
use App\Exports\Reports\Record\ClientAgeSheet;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class RecordDataExport implements WithMultipleSheets
{
    use Exportable;

    protected $company;

    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new StatisticsSheet($this->company);
        $sheets[] = new ClientRegistrationSheet($this->company);
        $sheets[] = new ProcedurePerformSheet($this->company);
        $sheets[] = new TopQuestionnairesSheet($this->company);
        $sheets[] = new MostNotesSheet($this->company);
        $sheets[] = new MostProceduresSheet($this->company);
        $sheets[] = new PrescriptionSignedSheet($this->company);
        $sheets[] = new ClientAgeSheet($this->company);

        return $sheets;
    }
}
