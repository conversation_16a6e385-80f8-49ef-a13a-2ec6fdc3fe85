<?php

namespace App\Exports\Reports\Record;

use App\Client;
use App\Company;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class StatisticsSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        return collect([[
            "clients" => (string) $company->clients()->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('clients.user_id', $user_ids);
            })->whereBetween('created_at', [$start_date, $end_date])->count(),
            "procedures" => (string) $company->procedures()->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('client_treatments.user_id', $user_ids);
            })->whereBetween('client_treatments.created_at', [$start_date, $end_date])->count(),
            "prescriptions" => (string) $company->prescriptions()->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('client_prescriptions.sign_by_id', $user_ids);
            })->whereNotNull('client_prescriptions.sign_by_id')->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])->count(),
            "client_sms" => (string) $company->client_sms()->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('client_sms.user_id', $user_ids);
            })->whereBetween('client_sms.created_at', [$start_date, $end_date])->count(),
        ]]);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.total_clients'),
            __('strings.reports.total_procedures'),
            __('strings.reports.prescriptions_signed'),
            __('strings.reports.sms_sent'),
        ];
    }

    /**
     * @var Client $client
     */
    public function map($data): array
    {
        return [
            $data['clients'],
            $data['procedures'],
            $data['prescriptions'],
            $data['client_sms'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.records.record_statistics');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}