<?php

namespace App\Exports\Reports\Record;

use App\Client;
use App\Company;
use App\Setting;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class ClientAgeSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;

        $clients = $company->clients()->withOut('addresses')->when($user_ids, function ($query) use ($user_ids) {
            return $query->whereIn('clients.user_id', $user_ids);
        })
            ->whereBetween('created_at', [$start_date, $end_date])
            ->cursor()
            ->filter(function ($client) {
                if ($client->personal_id != "" || $client->social_security_number != "" || $client->cpr_id != "") {
                    return true;
                }
            });

        $ageGroups = $clients->reduce(function ($data, $client) use ($company) {

            $birth_date = $client->dateOfBirthFormatted($company);
            $format = Setting::getDateTimeFormat($company, false, false);

            $age = Carbon::createFromFormat($format, $birth_date)->age;

            if ($age <= 20) {
                $data["<20"] += 1;
            } elseif ($age > 20 && $age <= 30) {
                $data["21-30"] += 1;
            } elseif ($age > 30 && $age <= 40) {
                $data["31-40"] += 1;
            } elseif ($age > 40 && $age <= 50) {
                $data["41-50"] += 1;
            } elseif ($age > 50 && $age <= 60) {
                $data["51-60"] += 1;
            } elseif ($age > 60) {
                $data["61+"] += 1;
            }

            return $data;
        },  ["<20" => "0", "21-30" => "0", "31-40" => "0", "41-50" => "0", "51-60" => "0", "61+" => "0"]);


        $data[] = ["key" => "<20", "value" => $ageGroups["<20"]];
        $data[] = ["key" => "21-30", "value" => $ageGroups["21-30"]];
        $data[] = ["key" => "31-40", "value" => $ageGroups["31-40"]];
        $data[] = ["key" => "41-50", "value" => $ageGroups["41-50"]];
        $data[] = ["key" => "51-60", "value" => $ageGroups["51-60"]];
        $data[] = ["key" => "61+", "value" => $ageGroups["61+"]];

        return collect($data);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.age_group'),
            __('strings.reports.clients'),
        ];
    }

    /**
     * @var Client $client
     */
    public function map($data): array
    {
        return [
            $data['key'],
            $data['value'],
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.records.client_age');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}
