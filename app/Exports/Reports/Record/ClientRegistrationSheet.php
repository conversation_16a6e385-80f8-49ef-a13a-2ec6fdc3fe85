<?php

namespace App\Exports\Reports\Record;

use App\Client;
use App\Company;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class ClientRegistrationSheet implements FromCollection, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;
    public array $request;

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->request = request()->all();
    }

    public function collection()
    {
        $company = $this->company;
        $start_date = Carbon::parse($this->request['start_date'])->startOfDay();
        $end_date = Carbon::parse($this->request['end_date'])->endOfDay();
        $user_ids = $this->request['user_ids'] ?? null;
        $period = $this->request['period'] ?? 'week';

        $periods = CarbonPeriod::create(Carbon::parse($start_date)->startOfDay(), "1 {$period}", Carbon::parse($end_date)->endOfDay());

        $per_page = $request->per_page ?? 5;

        $total_clients = 0;
        foreach ($periods as $date) {
            if ($period == 'month') {
                $key = $date->format("M y");
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            } elseif ($period == 'week') {
                $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
            } elseif ($period == 'day') {
                $key = $date->format("d M");
                $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
            } else {
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            }

            $clients = $company->clients()->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('clients.user_id', $user_ids);
            })->whereBetween('created_at', [$start_date, $end_date])->count();

            $data[] = ['name' => $key, 'value' => $clients];
            $total_clients += $clients;
        }

        $data[] = ['name' => 'Total', 'value' => $total_clients];
        return collect($data);
    }

    public function headings(): array
    {
        return [
            __('strings.reports.date'),
            __('strings.reports.clients_registered'),
        ];
    }

    /**
     * @var Client $client
     */
    public function map($data): array
    {
        return [
            $data['name'],
            (string) ($data['value'] ?? 0),
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return __('strings.reports.records.new_client_registrations');
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}