<?php

namespace App\Exports;

use App\Client;
use App\Company;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomQuerySize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class AllClientExport implements FromQuery, ShouldAutoSize, WithCustomQuerySize, WithHeadings, WithTitle, WithMapping, WithColumnFormatting
{
    use Exportable;

    public Company $company;

    public function __construct(Company $company)
    {
        $this->company = $company;
    }

    public function query()
    {
        return Client::where('company_id', $this->company->id);
    }

    public function headings(): array
    {
        return [
            __('strings.FirstName'),
            __('strings.LastName'),
            __('strings.Email'),
            __('strings.DateBirth'),
            __('strings.personal_number'),
            __('strings.PhoneNumber'),
        ];
    }

    /**
     * @var Client $client
     */
    public function map($client): array
    {
        $phone_number = $client->country_code ? '+' . $client->country_code . ' ' . $client->phone_number : $client->phone_number;
        return [
            $client->first_name,
            $client->last_name,
            $client->email,
            $client->social_security_number,
            $client->personal_id,
            $phone_number
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->company->company_name . "-Clients";
    }

    /**
     * Queued exportables are processed in chunks; each chunk being a job pushed to the queue by the QueuedWriter.
     * In case of exportables that implement the FromQuery concern, the number of jobs is calculated by dividing the $query->count() by the chunk size.
     * Depending on the implementation of the query() method (eg. When using a groupBy clause), this calculation might not be correct.
     *
     * When this is the case, you should use this method to provide a custom calculation of the query size.
     *
     * @return int
     */
    public function querySize(): int
    {
        return 300;
    }
}