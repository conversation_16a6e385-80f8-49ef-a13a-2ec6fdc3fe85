<?php

namespace App;

use App\Client;
use App\Traits\Encryptable;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CompanyGiftCard extends Model
{
    use HasFactory, Encryptable, HasUniqueCode;

    const STATUS_ACTIVE = 'active',
        STATUS_INACTIVE = 'in_active',
        STATUS_EXPIRED = 'expired';
    //TABLE
    // public $table = 'gift_cards';

    //FILLABLES
    protected $fillable = [
        'client_id',
        'company_id',

        'gift_code',

        'initial_value',
        'current_value',

        'expired_at',

        'paid_at',

        'payable_id',
        'payable_type',

        'tax_information',
    ];

    protected $encrypted = [
        'current_value',
        'initial_value',
        'tax_information',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [
        'status',
        'padded_gift_code',
    ];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    protected $dates = [
        'expired_at',
        'paid_at',
    ];

    //RULES
    public static $getListRules = [];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $value = self::getIncrementalValueModel($query->company_id);

            $query->gift_code = $value->max_gift_code + 1;

            $value->max_gift_code += 1;
            $value->save();
        });
    }

    //RELATIONSHIPS
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function payable()
    {
        return $this->morphTo();
    }

    public function isValid(): bool
    {
        return $this->expired_at >= now();
    }

    public function hasMoney($value): bool
    {
        return $this->current_value >= $value;
    }

    function scopeInActive($query): void
    {
        $query->whereNull('paid_at');
    }

    function scopeActive($query): void
    {
        $query->where('expired_at', '>=', now()->startOfDay()->format('Y-m-d'))->whereNotNull('paid_at');
    }

    function scopeExpired($query): void
    {
        $query->where('expired_at', '<', now()->startOfDay()->format('Y-m-d'))->whereNotNull('paid_at');
    }

    public function debit($value)
    {
        if ($this->isExpired()) {
            throw new \Exception(__('pos_strings.gift_card_expired'));
        }

        if (!$this->hasMoney($value)) {
            throw new \Exception(__('pos_strings.requested_amount_exceed_available_balance'));
        }

        $this->current_value = $this->current_value - $value;

        return $this;
    }

    public function credit($value)
    {
        $this->current_value = $this->current_value + $value;

        return $this;
    }

    public function isExpired(): bool
    {
        if (!$this->expired_at) return true;

        return $this->expired_at->format('Y-m-d') < now()->startOfDay()->format('Y-m-d');
    }

    public function isActive(): bool
    {
        return $this->status == self::STATUS_ACTIVE;
    }

    public function isInActive(): bool
    {
        return $this->status == self::STATUS_INACTIVE;
    }

    function getStatusAttribute()
    {
        if ($this->isExpired()) {
            return self::STATUS_EXPIRED;
        }

        if (!$this->paid_at) {
            return self::STATUS_INACTIVE;
        }

        return self::STATUS_ACTIVE;
    }

    function getPaddedGiftCodeAttribute()
    {
        return Str::of($this->gift_code)->padLeft(5, "0");
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
