<?php

namespace App;

use App\Company;
use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyTerminal extends Model
{
    use HasFactory, Encryptable, SoftDeletes;

    //TABLE
    // public $table = '';

    //FILLABLES
    protected $fillable = [
        'merchant_id',
        'status_id',
        'source_code',
        'terminal_id',
        'virtual_terminal_id',
        'nickname',
        'company_id',
    ];

    //ENCRYPT
    protected $encrypted = [
        'merchant_id',
        'status_id',
        'source_code',
        'terminal_id',
        'virtual_terminal_id',
        'nickname',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    function company()
    {
        return $this->belongsTo(Company::class);
    }

    // static function getCompanyTerminals($company_id)
    // {

    //     $company_terminal_devices = CompanyTerminal::where('company_id', $company_id)->first();

    //     if($company_terminal_devices) {
    //         return false;
    //     }

    //     return true;

    // }

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
