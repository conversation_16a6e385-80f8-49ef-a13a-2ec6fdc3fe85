<?php

namespace App;

use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyClientExtraField extends Model
{
    use HasFactory, GetEncryptedFile;

    protected $fillable = [
        'name',
        'view',
        'required',
        'company_id',
    ];

    protected $casts = [
        'required' => 'boolean',
        'view' => 'boolean',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}