<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyBookingLetterOfConsent extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'company_booking_letter_of_consents';

    //FILLABLES
    protected $fillable = [
        'company_booking_id',
        'client_letter_of_consent_id',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company_booking()
    {
        return $this->belongsTo(CompanyBooking::class);
    }
    public function client_letter_of_consent()
    {
        return $this->belongsTo(ClientLetterOfConsent::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
