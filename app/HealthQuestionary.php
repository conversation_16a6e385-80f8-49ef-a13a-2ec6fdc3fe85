<?php

namespace App;

use App\QuestionaryData;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\Model;

class HealthQuestionary extends Model
{
    use LogsActivity, GetEncryptedFile;

    protected $fillable = [
        'data', 'client_id', 'data_new'
    ];

    public function setDataAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['data'] = $value;
    }

    public function getDataAttribute($value)
    {
        try {
            if ($value != null) {
                return json_decode(Crypt::decrypt($value));
            }
        } catch (\Throwable $th) {
        }
        return $value;
    }

    public function setDataNewAttribute($value)
    {
        if ($value != null) {
            $value = Crypt::encrypt($value);
        }
        $this->attributes['data_new'] = $value;
    }

    public function getDataNewAttribute($value)
    {
        try {
            if ($value != null) {
                return json_decode(Crypt::decrypt($value));
            }
        } catch (\Throwable $th) {
        }
        return $value;
    }


    public function datas()
    {
        return $this->morphMany(QuestionaryData::class, 'modelable');
    }

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    protected static $logAttributes = [];

    protected static $logName = 'health questionary';

    public function getDescriptionForEvent(string $eventName): string
    {
        $client_user = $this->client()->first();
        return $client_user->first_name . " " . $client_user->last_name . "'s health questionary has been {$eventName} by :causer.first_name :causer.last_name";
    }
}
