<?php

namespace App;

use App\Models\Cashier\Subscription;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use VerumConsilium\Browsershot\Facades\PDF;

class SubscriptionCancellationData extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'subscription_cancellation_data';

    //FILLABLE
    protected $fillable = [
        'subscription_id',
        'data',
        'should_delete_data',
        'is_data_deleted',
        'data_deletion_date',
        'is_from_free_trail',
        'company_id',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'data' => 'array'
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}

    public static function downloadPDF(SubscriptionCancellationData $subscription_cancellation_data)
    {
        return PDF::loadView('exports.subscription.cancel', [
            'company' => $subscription_cancellation_data->company,
            'subscription_cancellation_data' => $subscription_cancellation_data,
            'subscription' => $subscription_cancellation_data->subscription ?? null,
            'plan' => $subscription_cancellation_data->subscription?->plan ?? null,
            'ends_at' => Carbon::parse($subscription_cancellation_data->data_deletion_date)->format('Y-m-d'),
        ])
            ->waitUntilNetworkIdle()
            ->format('A4')
            ->margins(15, 0, 15, 0)
            ->download();
    }
}
