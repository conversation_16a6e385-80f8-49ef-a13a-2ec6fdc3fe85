<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationLogs extends Model
{
    use HasFactory;
    const NOTIFICATION = "NOTIFICATION", EMAIL = "EMAIL", SMS = "SMS";
    //TABLE
    public $table = 'notification_logs';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'notified_user_count',
        'applied_filter',
        'type',
        'click_action',
        'external_link',
        'is_popup',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        "applied_filter" => "array"
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}