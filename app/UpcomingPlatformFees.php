<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UpcomingPlatformFees extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'upcoming_platform_fees';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'price',
        'price_id',
        'start_date'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules=[];

    //RELATIONSHIPS
    public function company(){
       return $this->belongsTo(Company::class);
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
