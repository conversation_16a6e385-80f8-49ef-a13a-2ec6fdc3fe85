<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScheduleTemplate extends Model
{
    use HasFactory, Encryptable;

    //TABLE
    public $table = 'schedule_templates';

    protected $encrypted = [
        'name',
    ];

    //FILLABLES
    protected $fillable = [
        'company_id',
        'user_id',
        'name',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function time_slots()
    {
        return $this->hasMany(ScheduleTemplateTimeSlot::class, 'schedule_template_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}