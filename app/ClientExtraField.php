<?php

namespace App;

use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientExtraField extends Model
{
    use HasFactory, GetEncryptedFile;

    protected $fillable = [
        'company_client_extra_field_id',
        'value',
        'client_id',
    ];

    public function company_client_extra_field()
    {
        return $this->belongsTo(CompanyClientExtraField::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
}
