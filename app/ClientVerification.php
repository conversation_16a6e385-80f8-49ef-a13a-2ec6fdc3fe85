<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientVerification extends Model
{
    use Encryptable, HasFactory;
    protected $encrypted = [
        'note'
    ];
    protected $fillable = [
        'client_id',
        'has_id',
        'has_driving_license',
        'has_passport',
        'other',
        'note',
    ];

    public function client()
    {
        return $this->belongsTo('App\Client');
    }
}
