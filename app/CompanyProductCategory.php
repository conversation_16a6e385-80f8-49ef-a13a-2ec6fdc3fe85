<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class CompanyProductCategory.
 *
 * @traitUses \Illuminate\Database\Eloquent\Model
 *
 *
 * @method static QueryBuilder where($column, $operator = null, $value = null, $boolean = 'and')
 */
class CompanyProductCategory extends Model
{
    use HasFactory, SoftDeletes, Encryptable, \AlexCrawford\Sortable\SortableTrait;

    //TABLE
    public $table = 'company_product_categories';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'name',
        'order',
        'deleted_at',
    ];

    protected static $sortableGroupField = 'company_id';

    protected static $sortableField = 'order';

    protected $encrypted = [
        'name',
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        // static::creating(function ($query) {
        //     $last_company = CompanyProductCategory::where('company_id', auth()->user()->company_id)->orderBy('order', 'desc')->first();

        //     $query->order = $last_company ? $last_company->order + 1 : 1;
        // });
    }

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function products()
    {
        return $this->hasMany(CompanyProduct::class, 'category_id');
    }

    // public function shiftCategory($level) {
    //     // size = 5
    //     // order = 1
    //     // level = -2
    //     // current = [1-1, 2-2, 3-3, 4-4, 5-5]
    //     // now = [2-1, 3-2, 1-3, 4-4, 5-5]

    //     for ($i = $this->order; $i < $level; $i++) {
    //         # code...
    //     }

    //     $this->order
    // }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
