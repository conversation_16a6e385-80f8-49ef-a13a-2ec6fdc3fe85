<?php

namespace App\Http\Controllers\Api\v2;

use App\BookingNote;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\CreateBookingNoteRequest;
use App\Http\Requests\v2\EditBookingNoteRequest;
use App\Http\Requests\v2\GetBookingNoteList;
use App\Http\Requests\v2\GetBookingNoteListRequest;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BookingNotesController extends Controller
{
    use ApiResponser;
    public function index(GetBookingNoteListRequest $request)
    {
        $data = BookingNote::with(['user' => function ($query) {
            $query->setEagerLoads([]);
        }]);
        if ($request->has('booking_id')) {
            $data = $data->where('booking_id', $request->booking_id);
        }
        if ($request->has('user_id')) {
            $data = $data->where('user_id', $request->user_id);
        }
        $data = $data->latest();
        $isPaginated = false;
        if ($request->has('search')) {
            if (!$isPaginated) {
                $data = $data->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);
            $data = $data->filter(function ($value) use ($search) {
                return
                    Str::contains(Str::lower($value->description), $search) ||
                    Str::contains(Str::lower($value->user?->first_name), $search) ||
                    Str::contains(Str::lower($value->user?->last_name), $search);
            });
        }


        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('booking_strings.booking_notes_returned'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($data) : $data->paginate($request->input('per_page')))
            );
        }

        return response()->json([
            'data' => $isPaginated ? $data : $data->get(),
            'message' => __('booking_strings.booking_notes_returned'),
            'status' => '1',
        ]);
    }

    public function store(CreateBookingNoteRequest $request)
    {
        $booking_note = BookingNote::create([
            'booking_id' => $request->booking_id,
            'user_id' => Auth::user()->id,
            'description' => $request->description,
        ]);
        return response()->json([
            'data' => $booking_note,
            'message' => __('booking_strings.booking_notes_created'),
            'status' => '1',
        ]);
    }

    public function update(BookingNote $note, EditBookingNoteRequest $request)
    {
        if ($request->has('description')) {
            $note->description = $request->description;
        }
        $note->user_id = Auth::user()->id;
        $note->save();
        return response()->json([
            'data' => $note,
            'message' => __('booking_strings.booking_notes_updated'),
            'status' => '1',
        ]);
    }

    public function destroy(BookingNote $note)
    {
        $note->delete();
        return response()->json([
            'message' => __('booking_strings.booking_notes_deleted'),
            'status' => '1',
        ]);
    }
}
