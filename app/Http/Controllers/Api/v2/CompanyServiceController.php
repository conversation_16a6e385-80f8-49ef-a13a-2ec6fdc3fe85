<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use App\CompanyCategory;
use App\CompanyService;
use App\CompanyServiceLetterOfConsent;
use App\CompanyServiceQuestionary;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\CompanyServiceIndexRequest;
use App\Http\Requests\v2\StoreServiceRequest;
use App\Http\Requests\v2\UpdateCompanyServiceRequest;
use App\Traits\ApiResponser;
use App\UserService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class CompanyServiceController extends Controller
{
    use ApiResponser;

    public function index(CompanyServiceIndexRequest $request)
    {
        $user = Auth::user();
        $company_services = [];
        if ($user->company_id) {
            $company_services = CompanyService::with(['category', 'company_service_questionnaires', 'letter_of_consents' => function ($query) {
                $query->select(['letter_of_consents.id', 'company_id', 'consent_title']);
            }])->where('company_id', $user->company_id);
        }

        if ($user->isMasterAdmin()) {
            $company_services = CompanyService::with(['category', 'company_service_questionnaires', 'letter_of_consents' => function ($query) {
                $query->select(['letter_of_consents.id', 'company_id', 'consent_title']);
            }])->where('company_id', $request->company_id);
        }

        $isPaginated = false;

        if ($request->has('only_without_category') && $request->only_without_category) {
            $company_services = $company_services->where('category_id', null);
        }

        if ($request->has('filter')) {
            $company_services = $company_services->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
        } else {
            $company_services = $company_services->where('is_active', true);
        }

        if ($request->has('user_ids')) {
            $company_services = $company_services->whereHas('users', function ($query) use ($request) {
                $query = $query->whereIn('users.id', $request->user_ids);
            });
        }

        if ($request->has('search') && $request->search != '') {
            if (!$isPaginated) {
                $company_services = $company_services->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $company_services = $company_services->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->name), $search) ||
                    Str::contains(Str::lower($value->product_code), $search) ||
                    (isset($value->category) && Str::contains(Str::lower($value->category?->name), $search));
            });
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $company_services = $company_services->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
            if ($orderBy == 'created_at' || $orderBy == 'name') {
                $company_services =  $company_services->sortBy($orderBy, $sortExtra, $isDescOrder);
            } else {
                if ($orderBy == 'category_name') {
                    $company_services =  $company_services->sortBy('category.name', $sortExtra, $isDescOrder);
                }
            }
        } else {
            if (!$isPaginated) {
                $company_services = $company_services->get();
                $isPaginated = true;
            }
            $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
            $company_services =  $company_services->sortBy('name', $sortExtra, false);
        }

        if ($request->has('price_only')) {
            $company_services = $company_services->where('price', '!=', null)->where('price', '>', 0);
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.company_service_list_return'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($company_services) : $company_services->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $company_services->values() : $company_services->get(),
                'message' => __('strings.company_service_list_return'),
                'status' => '1'
            ]);
        }
    }

    public function singleService(CompanyService $company_service)
    {
        return response()->json([
            'data' => $company_service->loadMissing(['category', 'company_service_questionnaires', 'letter_of_consents' => function ($query) {
                $query->select(['letter_of_consents.id', 'company_id', 'consent_title']);
            }]),
            'message' => __('strings.company_service_list_return'),
            'status' => '1'
        ]);
    }

    public function outsideIndex($id, Request $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
        $company_services = CompanyService::with(['category'])
            ->where('company_id', $company->id)
            ->where('is_active', 1)
            ->where('is_hidden', 0)
            ->where('category_id', null);
        if ($request->has('user_id')) {
            $company_services = $company_services->whereHas('users', function ($query) use ($request) {
                $query->where('users.id', $request->user_id);
            });
        }
        $company_services = $company_services->get();
        $company_services =  $company_services->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE)->values();

        return response()->json([
            'data' => $company_services,
            'message' => __('strings.company_category_list_return'),
            'status' => '1'
        ]);
    }

    public function outsideDetail($id, CompanyService $service)
    {
        return response()->json([
            'data' => $service->loadMissing(['questionnaires', 'letter_of_consents', 'company_service_questionnaires' => function ($query) {
                $query = $query->with(['questionary.questions']);
            }]),
            'message' => __('strings.company_category_list_return'),
            'status' => '1'
        ]);
    }

    public function store(StoreServiceRequest $request)
    {
        $user = Auth::user();

        $pdo = DB::getPdo();
        $pdo->exec('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE');

        $allowTax = Gate::allows('addUpdateTax');

        return DB::transaction(function () use ($user, $request, $allowTax) {
            $service = CompanyService::create([
                'company_id' => $user->company_id ?? null,
                'category_id' => $request->category_id,
                'name' => $request->name,
                'color' => $request->color,
                'price' => $request->price ?? null,
                'duration' => $request->duration,
                'time_margin' => $request->time_margin,
                'description' => $request->description,
                'parallel_booking_count' => $request->parallel_booking_count ?? null,
                'group_quantity' => $request->group_quantity ?? null,
                'tax_information' => $allowTax ? $request->tax_information ?? 0 : 0,
                'is_active' => 1,
                'is_hidden' => $request->has('is_hidden') ? $request->is_hidden : 0,
                'is_online_payment' => $request->is_online_payment ?? 0,
                'is_video_call' => $request->category_id && CompanyCategory::findOrFail($request->category_id)->group_booking ? false : $request->boolean('is_video_call'),
                'finished_email_template_id' => $request->finished_email_template_id,
                'finished_sms_template_id' => $request->finished_sms_template_id,
                'confirmation_sms_template_id' => $request->confirmation_sms_template_id,
                'confirmation_email_template_id' => $request->confirmation_email_template_id,
                'cancellation_policy_hours' => $request->cancellation_policy_hours,
            ]);

            if ($request->has('user_ids')) {
                foreach ($request->user_ids as $user_id) {
                    $user_service = UserService::where('user_id', $user_id)->where('service_id', $service->id)->first();
                    if (!$user_service) {
                        $user_service = UserService::create([
                            'user_id' => $user_id,
                            'service_id' => $service->id,
                        ]);
                    }
                }
            }

            if ($request->has('letter_of_content_ids')) {
                foreach ($request->letter_of_content_ids as $letter_of_content_id) {
                    $company_service_letter_of_consent = CompanyServiceLetterOfConsent::where('company_service_id', $service->id)
                        ->where('letter_of_consent_id', $letter_of_content_id)
                        ->first();
                    if (!$company_service_letter_of_consent) {
                        CompanyServiceLetterOfConsent::create([
                            'company_service_id' => $service->id,
                            'letter_of_consent_id' => $letter_of_content_id
                        ]);
                    }
                }
            }

            if ($request->has('questionnairies')) {
                foreach ($request->questionnairies as $questionary) {
                    if (isset($questionary['id'])) {
                        CompanyServiceQuestionary::create([
                            'company_service_id' => $service->id,
                            'questionary_id' => $questionary['id'],
                            'type' => CompanyServiceQuestionary::CUSTOM,
                        ]);
                    } else {
                        CompanyServiceQuestionary::create([
                            'company_service_id' => $service->id,
                            'questionary_id' => null,
                            'type' => $questionary['type'],
                        ]);
                    }
                }
            }

            return response()->json([
                'data' => $service->refresh(),
                'message' => __('strings.company_service_created'),
                'status' => '1'
            ]);
        });
    }

    public function update(CompanyService $company_service, UpdateCompanyServiceRequest $request)
    {
        $user = Auth::user();
        $this->authorize('update', [CompanyService::class, $company_service]);
        $allowTax = Gate::allows('addUpdateTax');

        if ($request->has('name')) {
            $company_service->name = $request->name;
        }
        if ($request->has('category_id')) {
            if (($company_service->category_id != $request->category_id) && $company_service->bookings()->exists()) {
                $company_category = CompanyCategory::findOrFail($request->category_id);
                if ($company_category?->group_booking != $company_service?->category?->group_booking) {
                    return response()->json([
                        'message' => __('strings.can_not_change_category'),
                        'status' => '0'
                    ]);
                }
            }
            $company_service->category_id = $request->category_id;
        }
        if ($request->has('color')) {
            $company_service->color = $request->color;
        }
        if ($request->has('price')) {
            $company_service->price = $request->price;
        }
        if ($request->has('duration')) {
            $company_service->duration = $request->duration;
        }
        if ($request->has('description')) {
            $company_service->description = $request->description;
        }
        if ($request->has('time_margin')) {
            $company_service->time_margin = $request->time_margin;
        }
        if ($request->has('parallel_booking_count')) {
            $company_service->parallel_booking_count = $request->parallel_booking_count;
        }
        if ($request->has('group_quantity')) {
            $company_service->group_quantity = $request->group_quantity;
        }
        if ($request->has('is_hidden')) {
            $company_service->is_hidden = $request->is_hidden;
        }

        if ($request->has('is_online_payment')) {
            $company_service->is_online_payment = $request->is_online_payment;
        }

        if ($request->has('finished_email_template_id')) {
            $company_service->finished_email_template_id = $request->finished_email_template_id;
        } else {
            $company_service->finished_email_template_id = null;
        }

        if ($request->has('finished_sms_template_id')) {
            $company_service->finished_sms_template_id = $request->finished_sms_template_id;
        } else {
            $company_service->finished_sms_template_id = null;
        }

        if ($request->has('is_video_call') && ($company_service->category_id == null || !CompanyCategory::findOrFail($company_service->category_id)->group_booking)) {
            $company_service->is_video_call = $request->boolean('is_video_call');
        }

        if ($request->has('tax_information') && $allowTax) {
            $company_service->tax_information = $request->tax_information;
        }

        if ($request->has('user_ids')) {
            UserService::where('service_id', $company_service->id)->delete();
            foreach ($request->user_ids as $user_id) {
                $user_service = UserService::where('user_id', $user_id)->where('service_id', $company_service->id)->first();
                if (!$user_service) {
                    $user_service = UserService::create([
                        'user_id' => $user_id,
                        'service_id' => $company_service->id,
                    ]);
                }
            }
        }
        if ($request->has('letter_of_content_ids')) {
            CompanyServiceLetterOfConsent::where('company_service_id', $company_service->id)->delete();
            foreach ($request->letter_of_content_ids as $letter_of_content_id) {
                $company_service_letter_of_consent = CompanyServiceLetterOfConsent::where('company_service_id', $company_service->id)
                    ->where('letter_of_consent_id', $letter_of_content_id)
                    ->first();
                if (!$company_service_letter_of_consent) {
                    CompanyServiceLetterOfConsent::create([
                        'company_service_id' => $company_service->id,
                        'letter_of_consent_id' => $letter_of_content_id
                    ]);
                }
            }
        }
        if ($request->has('questionnairies')) {
            CompanyServiceQuestionary::where('company_service_id', $company_service->id)->delete();
            foreach ($request->questionnairies as $questionary) {
                if (isset($questionary['id'])) {
                    CompanyServiceQuestionary::create([
                        'company_service_id' => $company_service->id,
                        'questionary_id' => $questionary['id'],
                        'type' => CompanyServiceQuestionary::CUSTOM,
                    ]);
                } else {
                    CompanyServiceQuestionary::create([
                        'company_service_id' => $company_service->id,
                        'questionary_id' => null,
                        'type' => $questionary['type'],
                    ]);
                }
            }
        }

        if ($request->has('clear_letter_of_consent') && $request->clear_letter_of_consent) {
            CompanyServiceLetterOfConsent::where('company_service_id', $company_service->id)->delete();
        }
        if ($request->has('clear_user') && $request->clear_user) {
            UserService::where('service_id', $company_service->id)->delete();
        }
        if ($request->has('clear_questionnairies') && $request->clear_questionnairies) {
            CompanyServiceQuestionary::where('company_service_id', $company_service->id)->delete();
        }
        if ($request->has('confirmation_sms_template_id')) {
            $company_service->confirmation_sms_template_id = $request->confirmation_sms_template_id;
        } else {
            $company_service->confirmation_sms_template_id = null;
        }

        if ($request->has('confirmation_email_template_id')) {
            $company_service->confirmation_email_template_id = $request->confirmation_email_template_id;
        } else {
            $company_service->confirmation_email_template_id = null;
        }


        if ($request->has('cancellation_policy_hours')) {
            $company_service->cancellation_policy_hours = $request->cancellation_policy_hours;
        } else {
            $company_service->cancellation_policy_hours = null;
        }
        $company_service->save();

        return response()->json([
            'data' => $company_service->refresh(),
            'message' => __('strings.company_service_updated'),
            'status' => '1'
        ]);
    }

    public function activeToggle(CompanyService $company_service)
    {
        $user = Auth::user();
        $this->authorize('toggle', [CompanyService::class, $company_service]);

        $company_service->is_active = !$company_service->is_active;
        $company_service->save();
        return response()->json([
            'data' => $company_service->refresh(),
            'message' => __('strings.company_service_updated'),
            'status' => '1'
        ]);
    }

    public function destroy(CompanyService $company_service)
    {
        $company_service->delete();
        return response()->json([
            'message' => __('strings.company_service_deleted'),
            'status' => '1'
        ]);
    }
}
