<?php

namespace App\Http\Controllers\Api\v2;

use App\Traits\BookingManager;
use App\User;
use Exception;
use App\Company;
use App\CompanyPlatform;
use App\CompanyService;
use App\Events\PasswordChangeEvent;
use Carbon\Carbon;
use App\UserDevice;
use App\Traits\SaveFile;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Notifications\EmailVerify2fa;
use App\Http\Requests\v1\StoreUserRequest;
use App\Http\Requests\v2\LoginUserRequest;
use App\Http\Requests\v1\UpdateUserRequest;
use App\Http\Requests\v2\GetAvailableSlotsRequest;
use App\Http\Requests\v2\GetPractitionerListRequest;
use App\Http\Requests\v2\GetUnavailableDaysRequest;
use App\Notifications\PasswordResetRequest;
use App\Http\Requests\v2\ResentOtpUserRequest;
use App\Setting;
use App\Traits\ApiResponser;
use App\UserPasswordHistory;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Throwable;
use Tymon\JWTAuth\Facades\JWTAuth;

class UserController extends Controller
{
    use SaveFile;
    use ApiResponser;

    /**
     * login into user account.
     *
     * @return \Illuminate\Http\Response
     */
    public function login(LoginUserRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $user = User::where('email', $request->email)->firstOrFail();
            if (CompanyPlatform::where('company_id', $user->company_id)->doesntExist()) {
                CompanyPlatform::create([
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'company_id' => $user->company_id
                ]);
            }
            $company = $user->company;
            if ($request->has('platform')) {
                if (CompanyPlatform::where(['platform' => $request->platform, 'company_id' => $company->id])->doesntExist()) {
                    return response()->json([
                        'message' => __('prescription_strings.register_first'),
                        'status' => '0'
                    ]);
                }
            }


            if ($user->is_account_lock) {
                if ($user->account_unlock_at <= Carbon::now()) {
                    $user->is_account_lock = 0;
                    $user->invalid_password_tries = 0;
                    $user->account_unlock_at = null;
                    $user->save();
                } else {
                    $difference = Carbon::parse($user->account_unlock_at)->diffInMinutes(Carbon::now());
                    $message = __('strings.account_lockout', ['difference' => $difference]);
                    if ($difference <= 0) {
                        $difference = Carbon::parse($user->account_unlock_at)->diffInSeconds(Carbon::now());
                        $message = __('strings.account_lockout_sec', ['difference' => $difference]);
                    }
                    return response()->json([
                        'message' => $message,
                        'status' => '0'
                    ]);
                }
            }

            // Changed status for verify account from 0 to 2
            if (!Hash::check($request->password, $user->password)) {
                $invalid_tries_allowed = 3;
                $user->invalid_password_tries = $user->invalid_password_tries + 1;
                $user->save();
                if ($user->invalid_password_tries < $invalid_tries_allowed) {
                    return response()->json([
                        'message' => __('strings.wrong_password_tries_increased', ['invalid_attempts' => $invalid_tries_allowed - $user->invalid_password_tries]),
                        'status' => '3'
                    ]);
                } else {
                    $user->is_account_lock = 1;
                    $user->account_unlock_at = Carbon::now()->addMinutes(1);
                    $user->save();
                    return response()->json([
                        'message' => __('strings.account_lockout', ['difference' => 1]),
                        'status' => '3'
                    ]);
                }
            }

            $user->is_account_lock = 0;
            $user->invalid_password_tries = 0;
            $user->account_unlock_at = null;
            $user->save();

            if ($user->hasRejectedEmail()) {
                return response()->json([
                    'message' => __('strings.user_rejected_message'),
                    'status' => '0'
                ]);
            }

            if (!$user->is_active) {
                return response()->json([
                    'message' => __('strings.user_inactive_message'),
                    'status' => '3'
                ]);
            }

            if ($user->company->is_blocked) {
                return response()->json([
                    'message' => __('strings.auth_blocked'),
                    'status' => '0'
                ]);
            }

            $setting = $user->company->settings()->where('key', 'SHOW_2FA')->where('value', 1)->exists();

            if ($setting) {
                $device = $user->devices()->where('token', $request->device_token)->first();

                if (!$device || ($device && !$device->isVerified() && !$request->has('otp'))) {
                    $device = $user->createDevice();
                    $user->notify(new EmailVerify2fa($device->otp));

                    return response()->json([
                        'data' => [
                            'device_token' => $device->token,
                        ],
                        'message' => __('strings.otp_send_to_your_email'),
                        'status' => '4',
                    ]);
                }

                if (!$device->isVerified() && $request->otp && !$device->isOtpValid($request->otp)) {
                    return response()->json([
                        'message' => __('strings.invalid_otp'),
                        'status' => '0'
                    ]);
                }

                if (!$device->isVerified() && $request->otp && $device->isOtpValid($request->otp)) {
                    $device->setVerified();
                    $device->save();
                }
            }

            if ($user->company->is_cancelled && $user->email != $user->company->email) {
                return response()->json([
                    'message' => __('strings.Your_company_subscription_has_been_cancelled'),
                    'status' => '3',
                ]);
            }

            if (!$user->hasVerifiedEmail()) {
                $user->sendEmailVerificationNotification();

                return response()->json([
                    'message' => __('strings.verify_email'),
                    'status' => '0',
                ]);
            }

            $user->last_login_at = now();
            $user->save();

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('User has logged in to system.');
            });

            if (Auth::attempt($request->only(['email', 'password']))) {
                $request->session()->regenerate();

                return response()->json([
                    'status' => '1',
                    'message' => $user->company->is_read_only ? __('strings.account_read_only_message') : __('strings.Welcome_to_MERIDIQ')
                ], 200);
            }

            return response()->json([
                'message' => __('strings.wrong_password_tries_increased'),
                'status' => '3'
            ]);
        });
    }

    public function loginAdmin(LoginUserRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $user = User::where('email', $request->email)->firstOrFail();

            if ($user->user_role != User::MASTER_ADMIN) {
                return response()->json([
                    'message' => __('strings.Please_provide_valid_email_and_password'),
                    'status' => '3'
                ]);
            }

            if (!Hash::check($request->password, $user->password)) {
                return response()->json([
                    'message' => __('strings.Please_provide_valid_email_and_password'),
                    'status' => '3'
                ]);
            }

            $setting = $user->company->settings()->where('key', 'SHOW_2FA')->where('value', 1)->exists();

            if ($setting) {
                $device = $user->devices()->where('token', $request->device_token)->first();

                if (!$device || ($device && !$device->isVerified() && !$request->has('otp'))) {
                    $device = $user->createDevice();
                    $user->notify(new EmailVerify2fa($device->otp));

                    return response()->json([
                        'data' => [
                            'device_token' => $device->token,
                        ],
                        'message' => __('strings.otp_send_to_your_email'),
                        'status' => '4',
                    ]);
                }

                if (!$device->isVerified() && $request->otp && !$device->isOtpValid($request->otp)) {
                    return response()->json([
                        'message' => __('strings.invalid_otp'),
                        'status' => '0'
                    ]);
                }

                if (!$device->isVerified() && $request->otp && $device->isOtpValid($request->otp)) {
                    $device->setVerified();
                    $device->save();
                }
            }

            $user->last_login_at = now();
            $user->save();

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('User has logged in to system.');
            });

            if (Auth::attempt($request->only(['email', 'password']))) {
                $request->session()->regenerate();

                return response()->json([
                    'status' => '1',
                    'message' => $user->company->is_read_only ? __('strings.account_read_only_message') : __('strings.Welcome_to_MERIDIQ')
                ], 200);
            }

            return response()->json([
                'status' => '0',
                'message' => __('strings.Please_provide_valid_email_and_password'),
            ]);
        });
    }


    public function availableDays($id, GetAvailableSlotsRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $start_date = null;
        $end_date = null;
        if ($request->has('start_date') && $request->has('end_date')) {
            $start_date = Carbon::parse($request->start_date);
            $end_date = Carbon::parse($request->end_date);
        }
        $user = Auth::user();
        $user_ids = $request->user_ids ?? null;
        if ($user && $user->user_role == User::USER) {
            $user_ids  = [$user->id];
        }
        $data = User::getUnAvailableDays($company, $start_date, $end_date, $service, $user_ids);
        return response()->json([
            'data' => $data,
            'message' => __('strings.available_days_returned'),
            'status' => '1'
        ]);
    }


    public function unavailableDays(GetUnavailableDaysRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        // $business_hours = CompanyBusinessHour::where('company_id', $company->id);
        // $data = User::getActiveDays($company, Carbon::parse($request->start_at), Carbon::parse($request->end_at), $service = null, $request->user_ids ?? null);
        $new_data = User::getNewActiveDays($company, Carbon::parse($request->start_at), Carbon::parse($request->end_at), $service = null, $request->user_ids ?? null);
        return response()->json([
            'data' => $new_data,
            'message' => __('strings.unavailable_slots_returned'),
            'status' => '1'
        ]);
    }

    public function availableSlots($id, GetAvailableSlotsRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $company = Company::findOrFail($id);

        $start_date = null;
        $end_date = null;
        if ($request->has('start_date') && $request->has('end_date')) {
            $start_date = Carbon::parse($request->start_date);
            $end_date = Carbon::parse($request->end_date);
        }
        $user = Auth::user();
        $user_ids = $request->user_ids ?? null;
        if ($user && $user->user_role == User::USER) {
            $user_ids  = [$user->id];
        }

        $setting = Setting::getSetting($company, Setting::MAGNETIC_BOOKING_ENABLED);
        $data = [];
        if ($setting->value) {
            $data = BookingManager::getAvailableSlots($company, $start_date, $end_date, $service, $user_ids, $company->timezone);
        } else {
            $data = User::getAvailableSlots($company, $start_date, $end_date, $service, $user_ids, $company->timezone);
        }

        return response()->json([
            'data' => $data,
            'message' => __('strings.available_slots_returned'),
            'status' => '1'
        ]);
    }

    public function availableSlotsTemp($id, GetAvailableSlotsRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $company = Company::findOrFail($id);

        $start_date = null;
        $end_date = null;
        if ($request->has('start_date') && $request->has('end_date')) {
            $start_date = Carbon::parse($request->start_date);
            $end_date = Carbon::parse($request->end_date);
        }
        $user = Auth::user();
        $user_ids = $request->user_ids ?? null;
        if ($user && $user->user_role == User::USER) {
            $user_ids  = [$user->id];
        }
        $data = User::getAvailableSlotsTemp($company, $start_date, $end_date, $service, $user_ids, $company->timezone);
        return response()->json([
            'data' => $data,
            'message' => __('strings.available_slots_returned'),
            'status' => '1'
        ]);
    }


    public function findAvailableSlots($id, Request $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $company = Company::findOrFail($id);

        $start_date = null;
        $end_date = null;

        $user = Auth::user();
        $user_ids = $request->user_ids ?? null;
        if ($user && $user->user_role == User::USER) {
            $user_ids  = [$user->id];
        }

        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date);
        } else {
            $start_date = Carbon::now();
        }

        $maximum_lead_time_setting = Setting::getSetting($company, Setting::MAXIMUM_LEAD_TIME);
        $end_date = Carbon::now()->addMinutes($maximum_lead_time_setting->value);
        $is_found = false;
        $found_date = null;
        while ($start_date->lessThan($end_date) && !$is_found) {
            $temp_date_1 = Carbon::parse($start_date->format('Y-m-d'));
            $temp_date_2 = Carbon::parse($start_date->format('Y-m-d'))->addDay();
            $data = User::getAvailableSlots($company, $temp_date_1, $temp_date_2, $service, $user_ids);

            if (count($data) > 0) {
                if (count($data[0]['slots']) > 0) {
                    $is_found = true;
                    $found_date = $data[0]['date'];
                }
            }
            $start_date = $start_date->addDay();
        }

        return response()->json([
            'data' => $found_date,
            'message' => __('strings.available_slots_returned'),
            'status' => '1'
        ]);
    }

    public function outsideIndex($id, GetPractitionerListRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);

        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }

        $practitioners = null;
        if ($request->has('available_at')) {
            $duration = 30;
            if ($request->has('service_id')) {
                $service = CompanyService::findOrFail($request->service_id);
                $duration = $service->duration - 2;
            }
            $start_at = Carbon::parse($request->available_at);
            $end_at = Carbon::parse($request->available_at)->addMinutes($duration);
            $practitioners = User::getAvailablePractitioners($company, $start_at, $end_at, $service);
        } else {
            $practitioners = User::where('company_id', $company->id)->where('is_active', 1)->where('is_booking_on', 1);
        }

        if ($request->has('service_id')) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($request) {
                $query = $query->where('company_services.id', $request->service_id);
            });
        }

        if ($request->has('user_ids')) {
            $practitioners =  $practitioners->whereIn('id', $request->user_ids);
        }

        // filter practitioners by verified email
        $practitioners = $practitioners->verifiedEmail();

        $isPaginated = false;
        if (!$isPaginated) {
            $practitioners = $practitioners->get();
            $isPaginated = true;
        }
        // $practitioners = $practitioners->where('user_role', User::USER);

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.practitioner_list_returned'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($practitioners) : $practitioners->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $practitioners->values() : $practitioners->get(),
                'message' => __('strings.practitioner_list_returned'),
                'status' => '1'
            ]);
        }
    }

    public function resentOtp(ResentOtpUserRequest $request)
    {
        $device = UserDevice::where('token', $request->device_token)->firstOrFail();

        if ($device->isVerified()) {
            return response([
                'status' => '1',
                'message' => "device already verified",
            ], 200);
        }

        $device = $device->user->updateDevice($device);

        $device->user->notify(new EmailVerify2fa($device->otp));

        return response()->json([
            'data' => [
                'device_token' => $device->token,
            ],
            'message' => __('strings.otp_send_to_your_email'),
            'status' => '4',
        ]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $users = Auth::user()->company->users()->latest();

        if ($request->has('withTrashed')) {
            $users = $users->withTrashed();
        }

        if (!$request->has('withUnverified')) {
            $users = $users->verifiedEmail();
        }

        if ($request->input('page')) {
            return response()->json(
                collect(
                    [
                        'message' => 'Users return successfully',
                        'status' => '1'
                    ]
                )->merge($users->paginate())
            );
        } else {
            return response()->json(
                [
                    'data' => $users->get(),
                    'message' => 'Users return successfully',
                    'status' => '1'
                ]
            );
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function publicIndex($id)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $company = Company::findOrFail($id);

        $users = $company->users()->select(['users.first_name', 'users.last_name', 'users.id', 'users.company_id'])
            ->setEagerLoads([])
            ->with(['company' => function ($query) {
                $query->select('id', 'company_name');
            }]);

        $users = $users->verifiedEmail()->get();

        $users = $users->map(function ($user) {
            $company = collect($user->company)->forget('subscriptions');
            $user = collect($user)->forget('company');

            $user['company'] = $company;
            return $user;
        });

        $users = $users->values();

        return response()->json(
            collect(
                [
                    'message' => 'Users return successfully',
                    'status' => '1'
                ]
            )->merge(['data' => $users])
        );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreUserRequest $request)
    {
        $this->authorize('create', [User::class]);

        $input = $request->validated();

        $file = null;
        if ($request->hasFile('profile_photo')) {
            $file = $this->saveFile($request->file('profile_photo'), 'user_photo', null, true);

            $input['profile_photo'] = $file->filename;
        } else {
            $input['profile_photo'] = '';
        }

        $input['password'] = bcrypt($input['password']);
        $input['company_id'] = Auth::user()->company_id;
        unset($input['password_confirmation']);

        if ($request->has('street_address')) {
            $input['street_address'] = $request->street_address;
        } else {
            $input['street_address'] = '';
        }

        if ($request->has('zip_code')) {
            $input['zip_code'] = $request->zip_code;
        } else {
            $input['zip_code'] = '';
        }

        if ($request->has('city')) {
            $input['city'] = $request->city;
        } else {
            $input['city'] = '';
        }

        if ($request->has('state')) {
            $input['state'] = $request->state;
        } else {
            $input['state'] = '';
        }

        if ($request->has('country')) {
            $input['country'] = $request->country;
        } else {
            $input['country'] = '';
        }

        if ($request->has('mobile_number')) {
            $input['mobile_number'] = $request->mobile_number ?? '';
        } else {
            $input['mobile_number'] = '';
        }

        if ($request->has('country_code')) {
            $input['country_code'] = $request->country_code ?? '';
        }
        $user = User::create($input);

        if ($file) {
            $user->file()->save($file);
        }

        foreach ($user->company->clients as $index => $client) {
            $client->accesses()->toggle([$user->id]);
        }

        event(new Registered($user));

        return response()->json([
            'message' => __('strings.User_created_successfully'),
            'status' => '1'
        ]);
    }

    public function verified(Request $request)
    {
        $userId = $request->id;
        $userEmailHash = $request->hash;
        $reject = $request->status == 'reject';

        $user = User::findOrFail($userId);
        abort_if(!hash_equals(sha1($user->getEmailForVerification()), (string) $userEmailHash), 500, __('strings.invalid_link'));

        if ($user->hasRejectedEmail()) {
            return response()->json([
                'message' => __('strings.email_already_rejected'),
                'status' => '0'
            ]);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'message' => __('strings.email_already_verified'),
                'status' => '0'
            ]);
        }

        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        if ($reject) {
            $user->rejectEmail();
            $user->save();

            return response()->json([
                'message' => __('strings.user_rejected_successfully'),
                'status' => '1'
            ]);
        }

        $user->markEmailAsVerified();

        event(new Verified($user));

        return response()->json([
            'message' => __('strings.email_verified_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Display a specific of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function single(Request $request, User $user)
    {
        $this->authorize('view', $user);

        return response()->json([
            'data' => $user,
            'message' => 'User return successfully',
            'status' => '1'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show()
    {
        $user = Auth::user();

        $user->company->loadMissing(['platforms']);

        $user->company->append([
            'is_subscribed',
            'is_cancelled',
            'has_pending_payment',
            'active_subscriptions',
            'record_plan',
            'management_plan',
            'pos_plan',
            'booking_plan',
            'has_system',
        ]);

        if ($user->company->is_read_only) {
            return response()->json([
                'data' => $user,
                'meta' => [
                    'message' => __('strings.account_read_only_message', ['message' => "<a href='mailto:<EMAIL>'><EMAIL></a>"]),
                ],
                'message' => 'User returned successfully.',
                'status' => '1'
            ]);
        }

        return response()->json([
            'data' => $user,
            'message' => 'User returned successfully.',
            'status' => '1'
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateUserRequest $request, User $user)
    {

        return DB::transaction(function () use ($user) {
            $request = app(UpdateUserRequest::class);

            $this->authorize('update', [User::class, $user, $request]);

            $company = $user->company;

            $authUser = Auth::user();

            if ($request->has('profile_photo')) {
                $file = $this->saveFile($request->file('profile_photo'), 'user_photo', null, true);

                if ($user->file) {
                    $user->file->delete();
                }

                $user->file()->save($file);

                $user->profile_photo = $file->filename;
            }

            if ($request->has('user_role')) {
                if (Auth::user()->user_role == User::ADMIN) {
                    if ($user->email != $user->company->email) {
                        $user->user_role = $request->input('user_role');
                    }
                }
            }

            if ($request->input('title')) {
                $user->title = $request->input('title');
            }

            if ($request->input('first_name')) {
                $user->first_name = $request->input('first_name');
            }

            if ($request->input('last_name')) {
                $user->last_name = $request->input('last_name');
            }

            if ($request->input('mobile_number')) {
                $user->mobile_number = $request->input('mobile_number', '');
            }
            if ($request->input('country_code')) {
                $user->country_code = $request->input('country_code', '');
            }

            if ($request->input('street_address')) {
                $user->street_address = $request->input('street_address');
            }

            if ($request->input('zip_code')) {
                $user->zip_code = $request->input('zip_code');
            }

            if ($request->input('city')) {
                $user->city = $request->input('city');
            }

            if ($request->input('state')) {
                $user->state = $request->input('state');
            }

            if ($request->input('country')) {
                $user->country = $request->input('country');
            }

            if ($request->input('booking_description')) {
                $user->booking_description = $request->booking_description;
            }

            if ($request->input('user_count')) {
                $company->user_count = $request->user_count;
            }

            if ($request->has('personal_id')) {
                if ($user->personal_id != $request->personal_id) {

                    //CHECKING FOR PERSONAL ID
                    $count = User::get()->filter(function ($user) use ($request) {
                        if ($user->personal_id) {
                            return strtolower($user->personal_id) === strtolower($request->personal_id);
                        }

                        return false;
                    })->count();

                    if ($count > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_personal_id'),
                            'status' => '0'
                        ]);
                    }

                    $user->personal_id = $request->personal_id;
                    $user->is_bankid_verified = 0;
                    $user->save();
                }
            }

            if ($request->has('resend_email_verification') && $user->hasRejectedEmail()) {
                $user->email_verified_at = null;
                $user->sendEmailVerificationNotification();

                $user->save();

                return response()->json([
                    'data' => $user->refresh(),
                    'message' => __('strings.mail_send_to_client'),
                    'status' => '1'
                ]);
            }

            if ($request->has('email')) {
                if ($request->email != $user->email) {
                    $user->email_verified_at = null;
                }

                if ($user->company->email == $user->email) {
                    $company = $user->company;
                    $user->email = $request->input('email');
                    $user->save();
                    $company->email = $request->input('email');
                    $company->save();
                } else {
                    $user->email = $request->input('email');
                }

                if (!$user->email_verified_at) {
                    $user->sendEmailVerificationNotification();
                }
            }

            if ($request->has('is_booking_on')) {
                $user->is_booking_on = $request->input('is_booking_on');
            }

            if ($request->has('is_record_on')) {
                $user->is_record_on = $request->input('is_record_on');
            }

            if ($request->has('is_pos_on')) {
                $user->is_pos_on = $request->input('is_pos_on');
            }

            if ($request->has('password')) {
                if (!Hash::check($request->input('old_password'), $user->password) && Auth::user()->user_role != User::MASTER_ADMIN) {
                    return response()->json([
                        'message' => __('strings.Old_password_did_not_match'),
                        'status' => '0'
                    ]);
                }

                if ($request->old_password == $request->password) {
                    return response()->json([
                        'message' => __('strings.old_password_and_new_password_same'),
                        'status' => '0'
                    ]);
                }

                $user_password_history = UserPasswordHistory::where('user_id', $user->id)->get();

                foreach ($user_password_history as $user_password) {
                    if (password_verify($request->input('password'), $user_password->password)) {
                        return response()->json([
                            'message' => __('strings.please_user_different_password'),
                            'status' => '0'
                        ]);
                    }
                }

                event(new PasswordChangeEvent($user, $user['password']));

                $user->password = bcrypt($request->input('password'));
                $user->is_account_lock = 0;
                $user->invalid_password_tries = 0;
                $user->account_unlock_at = null;

                try {
                    $user->tokens()->delete();
                } catch (\Throwable $th) {
                }
            }

            if ($request->has('firebase_token') && $request->firebase_token) {
                $user['firebase_tokens'] = $user->firebase_tokens ? collect($user->firebase_tokens)->push($request->firebase_token)->unique()->values()->all() : [$request->firebase_token];
            }

            if ($user->isDirty()) {
                $user->save();
            }

            if ($company->isDirty()) {
                $company->save();
            }

            $authUser = $authUser->refresh();

            if (!$user->hasVerifiedEmail() && $user->email == $authUser->email) {
                Auth::guard('web')->logout();

                $request->session()->invalidate();

                $request->session()->regenerateToken();
            }

            return response()->json([
                'data' => $user->refresh(),
                'message' => __('strings.User_updated_successfully'),
                'status' => '1'
            ]);
        });
    }

    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => 'required']);
        $email = $request->input('email');

        $user = User::where('email', $email)->first();
        if (!$user) {
            return response()->json([
                'message' => __('strings.No_user_found_with_this_specific_user'),
                'status' => '0'
            ]);
        }

        $new_pass = Str::random(11);
        $for_int = strpbrk($new_pass, '1234567890');

        while (!$for_int) {
            $new_pass = Str::random(11);
            $for_int = strpbrk($new_pass, '1234567890');
        }

        $user->password = bcrypt($new_pass);
        $user->save();
        if ($user) {
            try {
                $user->notify(new PasswordResetRequest($new_pass));
            } catch (Throwable $th) {
            }
        }
        return response()->json([
            'message' => __('strings.password_reset_has_been_send_to_your_email'),
            'status' => '1'
        ]);
    }


    public function delete(User $user)
    {
        $this->authorize('delete', [User::class, $user]);
        if ($user->delete()) {
            return response()->json([
                'message' => __('strings.user_removed_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.user_removal_failed'),
                'status' => '0'
            ]);
        }
    }

    public function restore($id)
    {
        $user = User::withTrashed()->findOrFail($id);
        $this->authorize('restore', [User::class, $user]);

        if ($user->restore()) {
            return response()->json([
                'message' => __('strings.user_restored_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.user_restoration_failed'),
                'status' => '0'
            ]);
        }
    }

    public function logout(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return response()->json([
            'message' => __('strings.user_logout_success'),
            'status' => '1'
        ]);
    }

    public function connectToStripe(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $company->createOrGetStripeCustomer();
        $intent = $company->createSetupIntent();
        return response()->json([
            'data' => [
                'intent' => $intent->client_secret,
            ],
            'message' => __('strings.user_connected_to_stripe'),
            'status' => '1'
        ]);
    }

    public function getJWTToken(Request $request)
    {
        $user = Auth::user();

        $token = JWTAuth::fromUser($user);

        return response()->json([
            'data' => $token,
            'message' => __('strings.jwt_token_returned'),
            'status' => '1'
        ]);
    }
}
