<?php

namespace App\Http\Controllers\Api\v2;

use App\AfterCareData;
use App\Client;
use App\ClientAfterCare;
use App\ClientSMS;
use App\Contracts\Services\VideoCall\VideoCallServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\ClientSmsGetPartsLengthRequest;
use App\Http\Requests\v2\ClientSMSSendRequest;
use App\LetterOfConsent;
use App\Questionary;
use App\Setting;
use App\ShortUrl;
use App\Traits\PortalManager;
use App\Traits\Sinch;
use App\Traits\SMS;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ClientSMSController extends Controller
{
    use Sinch;
    use SMS;

    public function index()
    {
        //
    }

    public function send(Client $client, ClientSMSSendRequest $request, VideoCallServiceInterface $videoCallService)
    {
        return DB::transaction(function () use ($request, $videoCallService, $client) {
            $user = Auth::user();

            $sms_credits = $user->company->sms_credits;
            $company = $user->company;

            // Check client mobile number
            if (!$client->phone_number || !$client->country_code) {
                return response()->json([
                    'message' => __('sms.client_doesnt_have_a_valid_mobile_number_please_enter_valid_mobile_number'),
                    'status' => '0',
                ]);
            }

            $number = SMS::FORMAT_NUMBER($client->country_code, $client->phone_number);
            $is_alpha_sender_id_supported = SMS::IS_ALPHA_NUMBERIC_SENDER_ID_SUPPORTED($client->country_code);
            $sender_id = $is_alpha_sender_id_supported ? Setting::getSetting($company, Setting::SMS_SENDER_ID)?->value ?? null : null;

            // Check supported country code // country_code
            $supported_country_codes = SMS::GET_COUNTRY_CODES();
            if (!in_array($client->country_code, $supported_country_codes)) {
                return response()->json([
                    'message' => __('sms.we_dont_support_the_country_code_that_you_selected_to_send_sms'),
                    'status' => '0',
                ]);
            }

            $msg = SMS::REPLACE_KEYS($request->notes, $user, $client);

            $activity_message = "SMS has been sent to $number with the following information: ";
            $activity_data = collect();

            $length = Sinch::getPartLength($msg, $number, $sender_id, $client->country_code);

            $auto_pay_success = SMS::tryAutoPay($user->company);
            if ($auto_pay_success) {
                $sms_credits->refresh();
            }

            if ($sms_credits->credits < $length) {
                throw new HttpException(200, __('sms.insufficient_sms_credits_required_n_credits', ['length' => $length]));
            }

            $client_after_care = ClientAfterCare::create([
                'client_id' => $client->id,
                'notes' => $request->notes,
            ]);

            if ($request->has('loc_ids')) {
                foreach ($request->loc_ids as $loc_id) {
                    AfterCareData::create([
                        'client_after_care_id' => $client_after_care->id,
                        'aftercarable_id' => $loc_id,
                        'aftercarable_type' => LetterOfConsent::class,
                    ]);
                    $loc = LetterOfConsent::find($loc_id);
                    $activity_data->push($loc->consent_title);
                }
            }

            if ($request->has('questionnairies')) {
                foreach ($request->questionnairies as $questionary) {
                    if (isset($questionary['id'])) {
                        AfterCareData::create([
                            'client_after_care_id' => $client_after_care->id,
                            'aftercarable_id' => $questionary['id'],
                            'aftercarable_type' => Questionary::class,
                        ]);
                        $ques = Questionary::find($questionary['id']);
                        $activity_data->push($ques->title);
                    } else {
                        AfterCareData::create([
                            'client_after_care_id' => $client_after_care->id,
                            'aftercarable_id' => null,
                            'aftercarable_type' => $questionary['type'],
                        ]);
                        if ($questionary['type'] === 'App\AestheticInterest') {
                            $activity_data->push('Aesthetic Interest');
                        }
                        if ($questionary['type'] === 'App\HealthQuestionary') {
                            $activity_data->push('Health Questionnaire');
                        }
                        if ($questionary['type'] === 'App\Covid19') {
                            $activity_data->push('Covid-19 Questionnaire');
                        }
                    }
                }
            }

            $activity_message .= $activity_data->join(', ');

            if ($client_after_care->aftercare()->count() > 0) {
                $portal_url = PortalManager::getAfterCareUrl($user->company, $client_after_care);
                if ($portal_url) {
                    $msg .= "\n" . ShortUrl::shorten($portal_url);
                }
            }

            $videoCall = null;

            if ($request->boolean('video_call')) {

                $videoCall = $videoCallService->single(Auth::user(), $client);
                $videoCallMemberOwner = $videoCall->owner_member;
                $videoCallMemberClient = $videoCall->other_members()->first();

                if ($videoCallMemberClient) {
                    $msg .= "\n" . __('strings.to_join_video_call');
                    $msg .= "\n" . ShortUrl::shorten($videoCallMemberClient->url);
                }
            }

            $response = Sinch::sendSMS($msg, [$number], $sender_id, $client->country_code);

            if ($response) {
                $sms_credits->credits = $sms_credits->credits - $length;
                $sms_credits->save();
                $activity = activity('aftercare_sms')->performedOn($client);

                $activity = $activity->by($user);
                $activity = $activity->log($activity_message);

                if ($videoCall) {
                    $videoCall->callable()->associate($activity);
                    $videoCall->save();
                }

                $sms = ClientSMS::create([
                    'text' => $response['body'],
                    'number' => $response['to'],
                    'type' => 'client_sms',
                    'log_id' => $activity->id,
                    'company_id' => $user->company->id,
                    'total_message_count' => $length,
                    'client_after_care_id' => $client_after_care->id,
                    'batch_id' => $response['id'],
                    'client_id' => $client->id,
                    'user_id' => $user->id,
                ]);
                AfterCareData::create([
                    'client_after_care_id' => $client_after_care->id,
                    'aftercarable_id' => $sms->id,
                    'aftercarable_type' => ClientSMS::class,
                ]);
            }

            return response()->json([
                'data' => [
                    "sms" => $response,
                    "video_call" => $videoCall,
                ],
                'message' => __('sms.sms_sent_successfully'),
                'status' => '1',
            ]);
        });
    }

    public function getPartLength(ClientSmsGetPartsLengthRequest $request)
    {
        $notes = $request->notes;
        if ($request->input('has-questionners')) {
            $notes .= "\n" . ShortUrl::sample_short_url();
        }

        if ($request->input('has-video-call')) {
            $notes .= "\n" . __('strings.to_join_video_call');
            $notes .= "\n" . ShortUrl::sample_short_url();
        }

        $length = Sinch::getPartLength($notes ?? '');

        return response()->json([
            'data' => $length,
            'message' => 'SMS counts returned.',
            'status' => '1',
        ]);
    }
}