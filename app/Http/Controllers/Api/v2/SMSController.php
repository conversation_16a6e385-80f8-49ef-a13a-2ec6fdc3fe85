<?php

namespace App\Http\Controllers\Api\v2;

use App\Client;
use App\ClientSMS;
use App\Company;
use App\CompanyBooking;
use App\CompanyPaymentMethod;
use App\CompanyPlatform;
use App\Exceptions\SMSFailedException;
use App\Exceptions\SubscriptionPaymentIncompleteException;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\SendWeeklySMSRequest;
use App\Http\Resources\InvoiceResource;
use App\Mail\SMSUnsendMail;
use App\Models\CompanyCampaignSMS;
use App\Setting;
use App\Traits\ApiResponser;
use App\Traits\CustomInvoiceManager;
use App\Traits\Sinch;
use App\Traits\SinchStatus;
use App\Traits\SMS;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Laravel\Cashier\Invoice;
use Symfony\Component\HttpKernel\Exception\HttpException;

class SMSController extends Controller
{
    use SMS;
    use Sinch;
    use CustomInvoiceManager;
    use ApiResponser;

    public function sendFromWeeklyCalendar(SendWeeklySMSRequest $request)
    {
        $notes = $request->notes;
        $client_ids = $request->client_ids;
        $booking_ids = $request->booking_ids;
        $user = Auth::user();
        $supported_country_codes = SMS::GET_COUNTRY_CODES();
        $practitioner = User::find($request->user_id);
        $sms_credits = $user->company->sms_credits;
        $company = $user->company;

        $keys = SMS::GET_KEYS_IN_MESSAGE($notes);

        $parameters = SMS::GENERATE_DEFAULT_KEYS();
        $numbers = collect();
        $clients = collect();
        $bookings = collect();
        $log_messages = collect();
        $errors = collect();

        foreach ($client_ids as $index => $client_id) {
            $client = Client::setEagerLoads([])->find($client_id);
            $booking_id = $booking_ids[$index];
            $booking = CompanyBooking::find($booking_id);
            if (!$client || !$client->phone_number || !$client->country_code) {
                $errors->push(['client' => $client, 'reason' => __('sms.client_doesnt_have_a_valid_mobile_number')]);
                continue;
            }

            // Not supported country code
            if (!in_array($client->country_code, $supported_country_codes)) {
                $errors->push(['client' => $client, 'reason' => __('sms.country_code_not_supported')]);
                continue;
            }

            $number = SMS::FORMAT_NUMBER($client->country_code, $client->phone_number);
            $number2 = SMS::FORMAT_NUMBER($client->country_code, $client->phone_number, false);
            $parameters = SMS::GENERATE_PARAMETERS($keys, $parameters, $number2, $client, $user, $booking);

            $numbers->push($number);
            $clients->push($client);
            $bookings->push($booking);
            $log_messages->push(SMS::REPLACE_KEYS($notes, $booking->user, $client, $booking));
        }

        $numbers = $numbers->values()->toArray();

        if (!count($numbers)) {
            throw new SMSFailedException(500, __('sms.no_valid_phone_numbers_found'));
        }

        if ($errors->count()) {
            $language = Setting::getSetting($user->company, Setting::CUSTOMER_LANGUAGE)?->value;
            Mail::to($user->email)->locale($language ?? app()->getLocale())->send(new SMSUnsendMail($company, $errors));
        }

        $message_to_send = SMS::BULK_GENERATE_MESSAGE_KEYS($notes);
        $parameters = $parameters->toArray();

        $length = Sinch::bulkGetPartLength($message_to_send, $numbers, $parameters);

        $auto_pay_success = SMS::tryAutoPay($company);
        if ($auto_pay_success) {
            $sms_credits->refresh();
        }

        // MAIN LOGIC
        if ($sms_credits->credits < $length) {
            throw new SMSFailedException(200, __('sms.insufficient_sms_credits_required_n_credits', ['length' => $length]));
        }

        $is_alpha_sender_id_supported = SMS::IS_ALPHA_NUMBERIC_SENDER_ID_SUPPORTED($client->country_code);
        $sender_id = $is_alpha_sender_id_supported ? Setting::getSetting($company, Setting::SMS_SENDER_ID)?->value ?? null : null;
        $response = Sinch::bulkSendSMS($message_to_send, $numbers, $parameters, $sender_id);

        if ($response) {
            foreach ($clients as $index => $client) {
                $activity = activity('sms_send')->performedOn($client);
                $activity = $activity->by($user);
                $activity = $activity->log("SMS has been sent to $number from booking calendar");
                ClientSMS::create([
                    'text' => $log_messages[$index],
                    'number' => $response['to'],
                    'type' => 'booking_calendar_sms',
                    'total_message_count' => $length,
                    'client_after_care_id' => false,
                    'log_id' => $activity->id,
                    'company_id' => $company->id,
                    'batch_id' => $response['id'],
                    'client_id' => $client->id,
                    'user_id' => $user->id,
                ]);
            }
            $sms_credits->credits = $sms_credits->credits - $length;
            $sms_credits->save();
        }

        return response()->json([
            'response' => $response,
            'message' => __('sms.sms_sent_successfully'),
            'status' => '1',
        ]);
    }

    public function companyCredits(Request $request)
    {
        if (Auth::user()->isMasterAdmin()) {
            $sms_credit = Company::findOrFail($request->company_id)->sms_credits;
        } else {
            $sms_credit = Auth::user()->company->sms_credits()->firstOrCreate([], ['credits' => 0]);
        }

        return response()->json([
            'data' => $sms_credit,
            'message' => 'success',
            'status' => '1',
        ]);
    }

    public function smsPriceList(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $currency = strtolower($user->company->unit);
        $customer = $company->createOrGetStripeCustomer();
        //code to get currency from stripe
        $currency = strtolower($customer->currency);
        if (!$currency) {
            $customerCountry = $company->country;
            $countryDetail = collect(config('stripe.country'))->filter(function ($country) use ($customerCountry) {
                return in_array($customerCountry, $country['countries']);
            })->first();
            $currency = strtolower($countryDetail['currency']);
        }



        // getting only sms prices
        $sms_prices = collect(config('stripe.prices'))->filter(fn($price) => $price['platform'] === CompanyPlatform::SMS);

        // only price list
        $sms_prices = $sms_prices->map(function ($price) use ($currency) {
            $price['prices'] = collect($price['prices'])->first(fn($pr) => strtolower($pr['currency']) === $currency);

            return $price;
        });

        return [
            'currency' => $currency,
            'data' => $sms_prices->values()->toArray(),
            'message' => __('strings.Invoices_returned_successfully'),
            'status' => '1',
        ];
    }

    public function payment(Request $request)
    {
        // return [
        //     'message' => __('sms.sms_not_available'),
        //     'status' => '0',
        // ];
        $user = Auth::user();
        $company = $user->company;


        if (!$company->verified_for_sms) {
            return [
                'message' => __('sms.verify_first'),
                'status' => '0',
            ];
        }

        $count = $request->count;
        $card_id = $request->card_id;
        $auto_pay = $request->input('auto_pay');

        $currency = $user->company->unit;
        $customer = $company->createOrGetStripeCustomer();
        $currency = $customer->currency;
        $currency = strtolower($customer->currency);

        if (!$currency) {
            $customerCountry = $company->country;
            $countryDetail = collect(config('stripe.country'))->filter(function ($country) use ($customerCountry) {
                return in_array($customerCountry, $country['countries']);
            })->first();
            $currency = strtolower($countryDetail['currency']);
        }


        $price = SMS::GET_SMS_PRICE($count, strtoupper($currency)); //SMS trait

        $default_taxes = $company->subscriptionTaxRates();

        $invoice = null;
        $sms_credit = $user->company->sms_credits()->firstOrCreate([], ['credits' => 0]);

        $paymentMethod = CompanyPaymentMethod::where('company_id', $company->id)->where('id', $card_id)->firstOrFail();

        $invoice = CustomInvoiceManager::generateInvoice($company->stripe_id, $price['stripe_id'], $default_taxes, 1);

        if ($invoice) {
            try {
                $payment = CustomInvoiceManager::payInvoice($invoice->id, $paymentMethod->payment_method_id);

                if (!$payment) {
                    return [
                        'message' => __('sms.payment_failed'),
                        'status' => '0',
                    ];
                }

                if ($auto_pay) {
                    $sms_credit->auto_pay = true;
                    $sms_credit->auto_pay_sms_counts = $count;
                    CompanyPaymentMethod::addLabel($paymentMethod, CompanyPaymentMethod::SMS);
                } else {
                    $sms_credit->auto_pay = false;
                    $sms_credit->auto_pay_sms_counts = 0;
                    CompanyPaymentMethod::removeLabel($paymentMethod, CompanyPaymentMethod::SMS);
                }

                //removing it since credits will be added by WebHook only
                // if ($payment) {
                //     $sms_credit->credits = $sms_credit->credits + $count;
                // }
                $sms_credit->save();

                return [
                    'data' => $payment,
                    'message' => __('sms.payment_successful'),
                    'status' => '1',
                ];
            } catch (IncompletePayment $th) {
                if ($th->payment->requiresAction()) {
                    throw new SubscriptionPaymentIncompleteException([
                        'intent' => $th->payment->clientSecret(),
                        'invoice_id' => $invoice->id,
                    ], $th);
                }

                $invoice = $company->findInvoice($th->payment->invoice);
                if ($invoice) {
                    $invoice->void();
                }

                throw new SubscriptionPaymentIncompleteException(null, $th);
            }
        }

        return [
            'data' => $invoice,
            'message' => __('sms.payment_draft'),
            'status' => '1',
        ];
    }

    public function voidInvoice(Request $request, $invoice_id)
    {
        $user = Auth::user();
        $invoice  = $user->company->findInvoiceOrFail($invoice_id);

        $invoice->void();

        return [
            'message' => __('sms.payment_void'),
            'status' => '1',
        ];
    }

    public function payInvoice(Request $request, $invoice_id)
    {
        $user = Auth::user();
        $company = $user->company;
        $invoice  = $user->company->findInvoiceOrFail($invoice_id);

        try {
            $paymentMethod = CompanyPaymentMethod::getPaymentMethodByLabel($company, CompanyPaymentMethod::SMS);

            $payment = CustomInvoiceManager::payInvoice($invoice->id, $paymentMethod?->payment_method_id);
        } catch (IncompletePayment $th) {
            if ($th->payment->requiresAction()) {
                throw new SubscriptionPaymentIncompleteException([
                    'intent' => $th->payment->clientSecret(),
                    'invoice_id' => $invoice->id,
                ], $th);
            }

            $invoice = $company->findInvoice($th->payment->invoice);
            if ($invoice) {
                $invoice->void();
            }

            throw new SubscriptionPaymentIncompleteException(null, $th);
        }

        return [
            'message' => __('sms.payment_successful'),
            'status' => '1',
        ];
    }

    public function setupAutoPay(Request $request)
    {
        // return [
        //     'message' => __('sms.sms_not_available'),
        //     'status' => '0',
        // ];
        $user = Auth::user();
        $company = $user->company;

        if (!$company->verified_for_sms) {
            return [
                'message' => __('sms.verify_first'),
                'status' => '0',
            ];
        }

        $sms_credit = $company->sms_credits;

        $card_id = $request->card_id;
        $paymentMethod = CompanyPaymentMethod::where('company_id', $company->id)->where('id', $card_id)->firstOrFail();

        CompanyPaymentMethod::addLabel($paymentMethod, CompanyPaymentMethod::SMS);

        $sms_credit->auto_pay = true;
        $sms_credit->auto_pay_sms_counts = $request->input('count', 100);
        $sms_credit->save();

        return [
            'data' => $sms_credit,
            'message' => __('sms.sms_autopay_setup_successfully'),
            'status' => '1',
        ];
    }

    public function cancelAutoPay()
    {
        // return [
        //     'message' => __('sms.sms_not_available'),
        //     'status' => '0',
        // ];
        $user = Auth::user();
        $company = $user->company;

        $sms_credit = $user->company->sms_credits()->firstOrCreate([], ['credits' => 0]);

        $paymentMethod = CompanyPaymentMethod::getPaymentMethodByLabel($company, CompanyPaymentMethod::SMS);

        if ($paymentMethod) {
            CompanyPaymentMethod::removeLabel($paymentMethod, CompanyPaymentMethod::SMS);
        }

        $sms_credit->auto_pay = false;
        $sms_credit->auto_pay_sms_counts = 0;
        $sms_credit->save();

        return [
            'data' => $sms_credit,
            'message' => __('sms.sms_autopay_cancelled_successfully'),
            'status' => '1',
        ];
    }

    public function invoices(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $upcomming_invoice = $company->upcomingInvoice();
        $data = collect(array_merge($upcomming_invoice ? [$upcomming_invoice] : [], $company->invoices(true, [
            'limit' => 100,
            'created' => [
                'gt' => Carbon::now()->subYear()->timestamp,
            ],
        ])->all()));

        $platform = CompanyPlatform::SMS;
        $product_to_use = null;
        foreach (config('stripe.products') as $product) {
            if ($product['platform'] == $platform) {
                $product_to_use = $product;
            }
        }

        if ($product_to_use) {
            $data = $data->filter(function ($invoice) use ($product_to_use) {
                if ($invoice->lines->data && is_array($invoice->lines->data) && count($invoice->lines->data) > 0 && $invoice->status != 'draft') {
                    if (in_array($invoice->lines->data[0]->price->product, $product_to_use['stripe_ids'])) {
                        return true;
                    } else {
                        return false;
                    }
                }

                return false;
            });
        }
        $data = InvoiceResource::collection($data);
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra = in_array($orderBy, ['number', 'status']) ? (SORT_NATURAL | SORT_FLAG_CASE) : SORT_FLAG_CASE;
            $data = $data->sortBy($orderBy, $sortExtra, $isDescOrder);
        }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.Invoices_returned_successfully'),
                    'status' => '1',
                ])->merge($this->paginate($data))
            );
        }

        return [
            'data' => $data,
            'message' => __('strings.Invoices_returned_successfully'),
            'status' => '1',
        ];
    }

    public function webhook(Request $request)
    {
        $data = $request->all();
        $type = $request->input('type');
        $batch_id = $request->input('batch_id');

        // Delivered to Sinch
        if ($type === 'delivery_report_sms') {
            $statuses = $request->input('statuses');

            $sms_batch_counts = ClientSMS::where('batch_id', $batch_id)->count();
            $sms = ClientSMS::query()->where('batch_id', $batch_id)->first();

            $credits_to_add_back = 0;
            if ($sms_batch_counts > 1 && $sms) {
                $credits_to_add_back = $sms?->total_message_count / $sms_batch_counts;
            } else {
                $credits_to_add_back = $sms?->total_message_count;
            }
            $sms = ClientSMS::query()->where('batch_id', $batch_id)->first();

            $company = null;
            if ($sms) {
                $company = $sms->company;
                $sms->statuses = $statuses;
                $sms->save();
            }

            $statuses = collect($data['statuses']);

            $status = $statuses->firstWhere("status", SinchStatus::FAILED->value);

            if ($status && $company) {
                $sms_credits = $company->sms_credits;
                $sms_credits->credits = $sms_credits->credits + $credits_to_add_back;
                $sms_credits->save();

                if ($sms_batch_counts === 1 && $sms) {
                    $sms->total_message_count = 0;
                    $sms->save();
                }
            }
        }

        // Delivered to client
        // Not implemented Yet
        if ($type === 'recipient_delivery_report_sms') {
            Log::channel('slack')->critical("recipient_delivery_report_sms: ",  $request->all());

            $status = $request->input('status');
            $recipient = $request->input('recipient');

            $sms = ClientSMS::query()->where('batch_id', $batch_id)->lazy()->where('number', "+$recipient")->first();

            if ($sms) {
                $company = $sms->company;

                $sms->statuses = $request->all();
                $sms->save();

                $sendable = $sms->sendable;

                if ($status == SinchStatus::FAILED->value) {
                    $total_messages = $request->input('number_of_message_parts', $sms?->total_message_count ?? 1);
                    $sms_credits = $company->sms_credits;
                    $sms_credits->credits = $sms_credits->credits + $total_messages;
                    $sms_credits->save();

                    if ($sendable && $sendable instanceof CompanyCampaignSMS) {
                        $sendable->failed = $sendable->failed + 1;
                        $sendable->credit_used = $sendable->credit_used - $total_messages;
                        $sendable->save();
                    }
                }
            }
        }

        return response()->json([
            'status' => '1',
        ]);
    }

    public function sendOTP(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if (!$user->mobile_number || !$user->country_code) {
            return response()->json([
                'message' => __('sms.user_does_not_have_valid_mobile_number'),
                'status' => '0',
            ]);
        }

        $supported_country_codes = SMS::GET_COUNTRY_CODES();
        if (!in_array($user->country_code, $supported_country_codes)) {
            return response()->json([
                'message' => __('sms.we_dont_support_country_code'),
                'status' => '0',
            ]);
        }

        $number = SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number);

        // 6 letter code
        $code = random_int(100000, 999999);

        $msg = __('sms.verification_message', ['code' => $code]);

        $is_alpha_sender_id_supported = SMS::IS_ALPHA_NUMBERIC_SENDER_ID_SUPPORTED($user->country_code);
        $sender_id = $is_alpha_sender_id_supported ? Setting::getSetting($company, Setting::SMS_SENDER_ID)?->value ?? null : null;
        $response = Sinch::sendSMS($msg, [$number], $sender_id, $user->country_code);

        if ($response && $response['id']) {
            $company->settings()->updateOrCreate([
                'key' => Setting::SMS_VERIFICATION_CODE
            ], [
                'value' => $code
            ]);
        }

        return response()->json([
            'data' => $response,
            'message' => __('sms.otp_sent_successfully'),
            'status' => '1',
        ]);
    }

    public function verifyOTP(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $otp = $request->input('otp');

        $storedOtp = Setting::getSetting($company, Setting::SMS_VERIFICATION_CODE);
        if (!$storedOtp) {
            return response()->json([
                'message' => __('sms.invalid_otp'),
                'status' => '0',
            ]);
        }

        if ($storedOtp->value !== $otp) {
            return response()->json([
                'message' => __('sms.invalid_otp'),
                'status' => '0',
            ]);
        }

        $company->verified_for_sms = true;
        $company->save();
        $storedOtp->delete();

        return response()->json([
            'message' => __('sms.verification_successful'),
            'status' => '1',
        ]);
    }
}
