<?php

namespace App\Http\Controllers\Api\v2;

use App\CompanyPaymentMethod;
use App\CompanyPlatform;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\CreatePaymentMethodRequest;
use App\Http\Requests\v2\UpdatePaymentMethodRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentMethodController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if (CompanyPaymentMethod::where('company_id', $company->id)->count() <= 0) {
            $labels_to_add = [];
            foreach (CompanyPlatform::ALL_PLATFORMS as $platform) {
                if (CompanyPlatform::where('company_id', $company->id)->where('platform', $platform)->exists()) {
                    switch ($platform) {
                        case CompanyPlatform::PRESCRIPTION:
                            array_push($labels_to_add, CompanyPaymentMethod::MAL);
                            break;
                        case CompanyPlatform::RECORD_SYSTEM:
                        case CompanyPlatform::POS_SYSTEM:
                            array_push($labels_to_add, CompanyPaymentMethod::RECORD);
                            break;
                        case CompanyPlatform::SMS:
                            array_push($labels_to_add, CompanyPaymentMethod::SMS);
                            break;

                        default:
                            # code...
                            break;
                    }
                }
            }
            $labels_to_add = array_unique($labels_to_add);
            $default_payment_id = null;
            $default_payment_method = $company->defaultPaymentMethod();

            if ($default_payment_method) {
                $default_payment_id = $default_payment_method->id;
            }

            $payment_methods = $company->paymentMethods();
            foreach ($payment_methods as $payment_method) {
                if (!CompanyPaymentMethod::where('company_id', $company->id)->where('payment_method_id', $payment_method->id)->exists()) {
                    if ($payment_method->card && $payment_method->card->last4) {
                        CompanyPaymentMethod::create([
                            'company_id' => $company->id,
                            'payment_method_id' => $payment_method->id,
                            'labels' => $default_payment_id == $payment_method->id ? $labels_to_add : null,
                            'meta_data' => [
                                'last4' => (string)$payment_method->card->last4,
                                'exp_year' => (string)$payment_method->card->exp_year,
                                'exp_month' => (string)$payment_method->card->exp_month,
                            ]
                        ]);
                    }
                }
            }
        } else {
            $labels_to_add = [CompanyPaymentMethod::SMS];
            foreach (CompanyPlatform::ALL_PLATFORMS as $platform) {
                if (CompanyPlatform::where('company_id', $company->id)->where('platform', $platform)->exists()) {
                    switch ($platform) {
                        case CompanyPlatform::PRESCRIPTION:
                            array_push($labels_to_add, CompanyPaymentMethod::MAL);
                            break;
                        case CompanyPlatform::RECORD_SYSTEM:
                        case CompanyPlatform::POS_SYSTEM:
                            array_push($labels_to_add, CompanyPaymentMethod::RECORD);
                            break;
                        case CompanyPlatform::SMS:
                            array_push($labels_to_add, CompanyPaymentMethod::SMS);
                            break;

                        default:
                            # code...
                            break;
                    }
                }
            }

            foreach ($labels_to_add as $label) {
                if (!CompanyPaymentMethod::where('company_id', $company->id)->whereJsonContains('labels', $label)->exists()) {
                    $payment_method_to_update = CompanyPaymentMethod::where('company_id', $company->id)->first();
                    if (!in_array($label, $payment_method_to_update?->labels ?? [])) {
                        $payment_method_to_update->labels = array_merge($payment_method_to_update?->labels ?? [], [$label]);
                        $payment_method_to_update->save();
                    }
                }
            }
        }

        $data = CompanyPaymentMethod::where('company_id', $company->id);

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('payment_methods.payment_method_returned'),
                    'status' => '1',
                ])->merge($data->simplePaginate($request->has('per_page') ? $request->per_page : 10))
            );
        }

        return response()->json([
            'data' => $data->get(),
            'message' => __('payment_methods.payment_method_returned'),
            'status' => '1'
        ]);
    }

    public function store(CreatePaymentMethodRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if (!$company->hasStripeId()) {
            $company->createAsStripeCustomer();
        }

        $company->addPaymentMethod($request->payment_method_id);

        $payment_method_info = $company->findPaymentMethod($request->payment_method_id);

        if (!$payment_method_info || !$payment_method_info->card) {
            return response()->json([
                'message' => __('payment_methods.invalid_payment_method'),
                'status' => '0'
            ]);
        }

        foreach ($request->labels as $label) {
            $payment_methods_to_update = CompanyPaymentMethod::where('company_id', $company->id)->whereJsonContains('labels', $label)->cursor();
            foreach ($payment_methods_to_update as $payment_method) {
                $payment_method->labels =  collect($payment_method->labels)->forget(collect($payment_method->labels)->search($label))->values()->all();
                $payment_method->save();
            }
        }

        CompanyPaymentMethod::create([
            'company_id' => $company->id,
            'payment_method_id' => $request->payment_method_id,
            'labels' => $request->labels,
            'meta_data' => [
                'last4' => (string)$payment_method_info->card->last4,
                'exp_year' => (string)$payment_method_info->card->exp_year,
                'exp_month' => (string)$payment_method_info->card->exp_month,
            ]
        ]);

        return response()->json([
            'message' => __('payment_methods.payment_method_created'),
            'status' => '1'
        ]);
    }

    public function update(CompanyPaymentMethod $payment_method, UpdatePaymentMethodRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if ($request->has('payment_method_id')) {
            if ($payment_method->payment_method_id != $request->payment_method_id) {
                $temp = $company->findPaymentMethod($payment_method->payment_method_id);
                $temp->delete();
                // $company->deletePaymentMethod($payment_method->payment_method_id);
                $company->addPaymentMethod($request->payment_method_id);
                $payment_method->payment_method_id = $request->payment_method_id;
            }
        }

        if ($request->has('labels')) {
            foreach ($request->labels as $label) {
                $payment_methods_to_update = CompanyPaymentMethod::where('company_id', $company->id)->whereJsonContains('labels', $label)->cursor();
                foreach ($payment_methods_to_update as $payment_method_to_update) {
                    CompanyPaymentMethod::removeLabel($payment_method_to_update, $label);
                }
            }
            $payment_method->labels = $request->labels;
        }

        $payment_method->save();

        return response()->json([
            'message' => __('payment_methods.payment_method_updated'),
            'status' => '1'
        ]);
    }

    public function destroy(CompanyPaymentMethod $payment_method)
    {
        $user = Auth::user();
        $company = $user->company;

        $temp = $company->findPaymentMethod($payment_method->payment_method_id);
        $temp->delete();

        $payment_method->delete();

        return response()->json([
            'message' => __('payment_methods.payment_method_deleted'),
            'status' => '1'
        ]);
    }
}
