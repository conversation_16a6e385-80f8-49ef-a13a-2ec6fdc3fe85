<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use App\CompanyCategory;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\CompanyCategoryIndexRequest;
use App\Http\Requests\v2\StoreCompanyCategoryRequest;
use App\Http\Requests\v2\UpdateCompanyCategoryRequest;
use App\Traits\ApiResponser;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CompanyCategoryController extends Controller
{
    use ApiResponser;
    public function index(CompanyCategoryIndexRequest $request)
    {
        $user = Auth::user();
        $company_categories = [];
        if ($user->company_id) {
            $company_categories = CompanyCategory::where('company_id', $user->company_id);
        }
        $isPaginated = false;


        // if ($request->has('is_active')) {
        //     $company_categories = $company_categories->where('is_active', $request->is_active);
        // }

        if ($request->has('with_service') && $request->with_service) {
            $company_categories = $company_categories->with(['services']);
        }
        if ($request->has('filter')) {
            $company_categories = $company_categories->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if ($orderBy == 'created_at' || $orderBy == 'name') {
                if (!$isPaginated) {
                    $company_categories = $company_categories->get();
                    $isPaginated = true;
                }

                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
                $company_categories =  $company_categories->sortBy($orderBy, $sortExtra, $isDescOrder);
            } else {
                $orderDirection = $orderDirection->orderBy($orderBy, $orderDirection);
            }
        } else {
            $company_categories = $company_categories->orderBy('order', 'asc');
        }


        if ($request->has('search') && $request->search != '') {
            if (!$isPaginated) {
                $company_categories = $company_categories->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $company_categories = $company_categories->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->name), $search);
            });
        }
        if ($request->has('user_id')) {
            $company_categories = $company_categories->whereHas('services', function ($query) use ($request) {
                $query->whereHas('users', function ($query) use ($request) {
                    $query->where('users.id', $request->user_id);
                });
            });
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.company_category_list_return'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($company_categories) : $company_categories->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $company_categories : $company_categories->get(),
                'message' => __('strings.company_category_list_return'),
                'status' => '1'
            ]);
        }
    }

    public function shiftCategory(CompanyCategory $category, Request $request)
    {
        $user = Auth::user();
        $company_categories = null;
        if ($user->company_id) {
            $company_categories = CompanyCategory::where('company_id', $user->company_id)->where('is_active', 1)->orderBy('order', 'asc')->get();
            foreach ($company_categories as $index => $company_category) {
                if (is_null($company_category->order)) {
                    $last_company = CompanyCategory::where('company_id', $user->company_id)->where('is_active', 1)->orderBy('order', 'desc')->first();
                    $company_category->order = $last_company ? $last_company->order + 1 : 1;
                    $company_category->save();
                } else {
                    if ($index > 0) {
                        if ($company_category->order <= $company_categories[$index - 1]->order) {
                            $company_category->order = $company_categories[$index - 1]->order + 1;
                            $company_category->save();
                        }
                    }
                }
            }
        }
        $index_to_move = null;
        $company_categories = CompanyCategory::where('company_id', $user->company_id)->where('is_active', 1)->orderBy('order', 'asc')->get();
        foreach ($company_categories as $index => $company_category) {
            if ($company_category->id == $category->id) {
                $index_to_move = $index;
            }
        }
        switch ($request->level) {
            case '1':
                if ($index_to_move != 0) {
                    $category_to_replace_with = CompanyCategory::findOrFail($company_categories[$index_to_move - 1]->id);
                    $my_category = CompanyCategory::findOrFail($company_categories[$index_to_move]->id);
                    if ($category_to_replace_with->order == $my_category->order) {
                        $my_category->order = $my_category->order - 1;
                        $my_category->save();
                    } else {
                        $temp = $category_to_replace_with->order;
                        $category_to_replace_with->order = $my_category->order;
                        $my_category->order  = $temp;
                        $my_category->save();
                        $category_to_replace_with->save();
                    }
                }
                break;
            case '-1':
                if ($index_to_move != (count($company_categories) - 1)) {
                    $category_to_replace_with = CompanyCategory::findOrFail($company_categories[$index_to_move + 1]->id);
                    $my_category = CompanyCategory::findOrFail($company_categories[$index_to_move]->id);
                    if ($category_to_replace_with->order == $my_category->order) {
                        $my_category->order = $my_category->order + 1;
                        $my_category->save();
                    } else {
                        $temp = $category_to_replace_with->order;
                        $category_to_replace_with->order = $my_category->order;
                        $my_category->order  = $temp;
                        $my_category->save();
                        $category_to_replace_with->save();
                    }
                }
                break;

            default:
                # code...
                break;
        }
        return response()->json([
            'message' => __('strings.company_category_updated'),
            'status' => '1'
        ]);
    }

    public function outsideIndex($id, CompanyCategoryIndexRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);

        $company_categories = CompanyCategory::with(['services' => function ($query) use ($request) {
            $query = $query->where('is_active', 1);

            if ($request->missing('category_id')) {
                $query = $query->where('is_hidden', 0);
            }
            if ($request->has('user_id')) {
                $query->whereHas('users', function ($query) use ($request) {
                    $query->where('users.id', $request->user_id);
                });
            }
        }])->orderBy('order', 'asc')
            ->where('company_id', $company->id)
            ->where('is_active', 1);


        if ($request->has('category_id')) {
            $company_categories = $company_categories->where('id', $request->category_id);
        } else {
            $company_categories = $company_categories->where('is_hidden', 0);
        }
        $company_categories = $company_categories->get();

        //SORT SERVICES BY NAME
        $final_company_categories = [];
        foreach ($company_categories as $company_category) {
            $temp = CompanyCategory::findOrFail($company_category->id);
            $temp->services = $company_category->services->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE)->values();
            array_push($final_company_categories, $temp);
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.company_category_list_return'),
                    'status' => '1'
                ])->merge($company_categories->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $final_company_categories,
                'message' => __('strings.company_category_list_return'),
                'status' => '1'
            ]);
        }
    }

    public function store(StoreCompanyCategoryRequest $request)
    {
        $user = Auth::user();
        $last_company = CompanyCategory::where('company_id', $user->company_id)->orderBy('order', 'desc')->first();
        if ($user->company_id) {
            $category = CompanyCategory::create([
                'company_id' => $user->company_id,
                'name' => $request->name,
                'is_active' => 1,
                'order' => $last_company ? $last_company->order + 1 : 1,
                'group_booking' => $request->group_booking ?? 0,
            ]);
        }
        return response()->json([
            'data' => $category->refresh(),
            'message' => __('strings.company_category_created'),
            'status' => '1'
        ]);
    }

    public function update(CompanyCategory $category, UpdateCompanyCategoryRequest $request)
    {
        $user = Auth::user();
        $this->authorize('update', [CompanyCategory::class, $category]);

        if ($request->has('name') && !empty($request->name)) {
            $category->name = $request->name;
        }
        if ($request->has('group_booking')) {
            $category->group_booking = $request->group_booking ?? 0;
        }
        if ($request->has('is_hidden')) {
            $category->is_hidden = $request->is_hidden;
        }

        $category->save();
        return response()->json([
            'data' => $category->refresh(),
            'message' => __('strings.company_category_updated'),
            'status' => '1'
        ]);
    }
    public function activeToggle(CompanyCategory $category)
    {
        $user = Auth::user();
        $this->authorize('toggle', [CompanyCategory::class, $category]);
        $category->is_active = !$category->is_active;
        $last_company = CompanyCategory::where('company_id', $category->company_id)->orderBy('order', 'desc')->first();
        $category->order = $last_company->order + 1;
        $category->save();
        return response()->json([
            'data' => $category->refresh(),
            'message' => __('strings.company_category_updated'),
            'status' => '1'
        ]);
    }
}
