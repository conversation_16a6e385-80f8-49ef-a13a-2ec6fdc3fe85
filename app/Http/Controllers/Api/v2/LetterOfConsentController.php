<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use \EditorJS\EditorJS;
use App\LetterOfConsent;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Traits\EditorJs as TraitsEditorJs;
use App\Http\Requests\v1\LetterOfConsentRequest;

class LetterOfConsentController extends Controller
{
    use ApiResponser, TraitsEditorJs;

    public function index(Request $request)
    {
        $isPaginated = false;

        $letterOfConsents = Auth::user()->company->letterOfConsents();

        if ($request->has('filter')) {
            if ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed') {
                $letterOfConsents = $letterOfConsents->{$request->filter}();
            } else {
                $letterOfConsents = $letterOfConsents->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
            }
        }

        if ($request->has('orderBy')) {
            if ($request->orderBy == 'consent_title') {
                if (!$isPaginated) {
                    $letterOfConsents = $letterOfConsents->get();
                    $isPaginated = true;
                }
                $letterOfConsents = in_array($request->input('orderDirection', 'asc'), ['asc', 'ASC']) ? $letterOfConsents->sortBy('consent_title') : $letterOfConsents->sortByDesc('consent_title');
            } else {
                $letterOfConsents = $letterOfConsents->orderBy($request->orderBy, $request->input('orderDirection', 'asc'));
            }
        }

        if ($request->has('search')) {
            if (!$isPaginated) {
                $letterOfConsents = $letterOfConsents->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $letterOfConsents = $letterOfConsents->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->consent_title), $search);
            });
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => 'Letter of Consent return successfully',
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($letterOfConsents) : $letterOfConsents->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $letterOfConsents : $letterOfConsents->get(),
                'message' => 'Letter of Consent return successfully',
                'status' => '1'
            ]);
        }
    }

    public function indexPublic($id)
    {
        try {
            $id = Company::decryptId($id);
        } catch (\Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $company = Company::with(['letterOfConsents' => function ($query) {
            return $query->latest();
        }])->findOrFail($id);

        return response()->json(
            [
                'data' => $company->letterOfConsents,
                'message' => 'Letter of Consent return successfully',
                'status' => '1'
            ]
        );
    }

    public function store(LetterOfConsentRequest $request)
    {
        $company = Auth::user()->company;

        if (!$company) {
            return response()->json([
                'message' => __('strings.Invalid_company'),
                'status' => '0'
            ]);
        }
        $this->authorize('create', [LetterOfConsent::class]);


        $input = $request->validated();

        if ($request->has('letter_html')) {
            $input['letter_html'] = $request->input('letter_html');
        }

        if ($request->has('letter_json')) {
            try {
                // Initialize Editor backend and validate structure
                $editor = new EditorJS($input['letter_json'], config('editorjs.config'));

                // Get sanitized blocks (according to the rules from configuration)
                $blocks = $editor->getBlocks();

                $input['letter_json'] = json_decode($input['letter_json']);

                $input['letter_json']->blocks =  $blocks;

                $input['letter_json'] = $input['letter_json'];

                $input['letter_html'] = $this->toHtml($blocks);
            } catch (\EditorJS\EditorJSException $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    'status' => '0',
                ]);
            }
        }


        $input['company_id'] = $company->id;
        $input['version'] = '0';

        LetterOfConsent::create($input);

        return response()->json([
            'message' => __('strings.Letter_of_Consent_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(LetterOfConsentRequest $request, LetterOfConsent $letterOfConsent)
    {
        $this->authorize('update', [LetterOfConsent::class, $letterOfConsent]);

        if ($request->has('consent_title')) {
            $letterOfConsent->consent_title = $request->input('consent_title');
        }

        if ($request->has('letter_html')) {
            $letterOfConsent->letter_html = $request->input('letter_html');
        }

        if ($request->has('letter_json')) {
            try {
                // Initialize Editor backend and validate structure
                $editor = new EditorJS($request->input('letter_json'), config('editorjs.config'));

                // Get sanitized blocks (according to the rules from configuration)
                $blocks = $editor->getBlocks();

                $letter_json = json_decode($request->letter_json);

                $letter_json->blocks =  $blocks;

                $letterOfConsent->letter_json = $letter_json;

                $letterOfConsent->letter_html = $this->toHtml($blocks);
            } catch (\EditorJS\EditorJSException $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                    'status' => '0',
                ]);
            }
        }

        if ($request->has('letter')) {
            $letterOfConsent->letter = $request->input('letter');
        }

       

        if ($request->has('is_publish_before_after_pictures')) {
            $letterOfConsent->is_publish_before_after_pictures = $request->input('is_publish_before_after_pictures');
        }

        if ($letterOfConsent->isDirty()) {
            $version = (int) $letterOfConsent->version;
            $version = $version + 1;
            $letterOfConsent->version = (string) $version;
            $letterOfConsent->save();
        }

        return response()->json([
            'message' => __('strings.Letter_of_Consent_Updated_successfully'),
            'status' => '1'
        ]);
    }

    public function restore()
    {
        $letterOfConsent = LetterOfConsent::withTrashed()->find(request('letter_of_consent_id'));

        $this->authorize('restore', [LetterOfConsent::class, $letterOfConsent]);

        if ($letterOfConsent) {
            if ($letterOfConsent->restore()) {
                return response()->json([
                    'message' => __('strings.Letter_of_consent_restored_successfully'),
                    'status' => '1'
                ]);
            } else {
                return response()->json([
                    'message' => __('strings.Letter_of_consent_restoration_failed'),
                    'status' => '0'
                ]);
            }
        } else {
            return response()->json([
                'message' => __('strings.Does_not_exists_any_letter_of_consent'),
                'status' => '0'
            ]);
        }
    }

    public function delete(LetterOfConsent $letterOfConsent)
    {
        $this->authorize('delete', [LetterOfConsent::class, $letterOfConsent]);

        $letterOfConsent->delete();

        return response()->json([
            'message' => __('strings.letter_Of_Consent_removed_successfully'),
            'status' => '1'
        ]);
    }
}
