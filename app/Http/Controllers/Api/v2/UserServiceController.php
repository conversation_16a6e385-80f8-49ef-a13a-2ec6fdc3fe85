<?php

namespace App\Http\Controllers\Api\v2;

use App\CompanyService;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\CreateUserServiceRequest;
use App\Http\Requests\v2\DeleteUserServiceRequest;
use App\Http\Requests\v2\GetUserServiceIndexRequest;
use App\Traits\ApiResponser;
use App\User;
use App\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class UserServiceController extends Controller
{
    use ApiResponser;
    public function index(GetUserServiceIndexRequest $request)
    {
        $user = User::where('id', $request->user_id)->verifiedEmail()->firstOrFail();
        $user_services =  $user->services()->with('category');
        $isPaginated = false;

        if ($request->has('search') && $request->search != '') {
            if (!$isPaginated) {
                $user_services = $user_services->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $user_services = $user_services->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->name), $search) ||
                    Str::contains(Str::lower($value->category->name), $search);
            });
        }


        if ($request->has('show_count') && $request->show_count) {
            return response()->json([
                'data' => $user_services->count(),
                'message' => __('strings.user_service_list_return'),
                'status' => '1'
            ]);
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $user_services = $user_services->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
            if ($orderBy == 'created_at' || $orderBy == 'name') {
                $user_services =  $user_services->sortBy($orderBy, $sortExtra, $isDescOrder);
            } else {
                if ($orderBy == 'category_name') {
                    $user_services =  $user_services->sortBy('category.name', $sortExtra, $isDescOrder);
                }
            }
        }
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.user_service_list_return'),
                    'status' => '1',
                    'count' => $user_services->count(),
                ])->merge($isPaginated ? $this->paginate($user_services) : $user_services->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'count' => $user_services->count(),
                'data' => $isPaginated ? $user_services : $user_services->get(),
                'message' => __('strings.user_service_list_return'),
                'status' => '1'
            ]);
        }
    }

    public function store(CreateUserServiceRequest $request)
    {
        $user = User::where('id', $request->user_id)->verifiedEmail()->firstOrFail();
        foreach ($request->service_ids as $service_id) {
            UserService::updateOrCreate([
                'user_id' => $user->id,
                'service_id' => $service_id,
            ]);
        }
        return response()->json([
            'message' => __('strings.user_services_added'),
            'status' => '1',
        ]);
    }

    public function destroy(CompanyService $service, DeleteUserServiceRequest $request)
    {
        $user = User::where('id', $request->user_id)->verifiedEmail()->firstOrFail();
        $user_service =  UserService::where('user_id', $user->id)->where('service_id', $service->id);
        if (!$user_service) {
            return response()->json([
                'message' => __('strings.user_service_not_found'),
                'status' => '0',
            ]);
        }
        $user_service->delete();
        return response()->json([
            'message' => __('strings.user_service_deleted'),
            'status' => '1',
        ]);
    }

    public function attachedUsers(CompanyService $service)
    {
        $attached_users = User::where('company_id', $service->company_id)->whereHas('services', function ($query) use ($service) {
            $query  = $query->where('company_services.id', $service->id);
        })->verifiedEmail()->get();
        return response()->json([
            'data' => $attached_users->pluck('id')->toArray(),
            'message' => __('strings.attached_users_returned'),
            'status' => '1',
        ]);
    }

    public function toggleUserAttachment(CompanyService $service, Request $request)
    {
        $user_service = UserService::where('user_id', $request->user_id)->where('service_id', $service->id)->first();
        if ($user_service) {
            $user_service->delete();
        } else {
            $user_service = UserService::create([
                'user_id' => $request->user_id,
                'service_id' => $service->id,
            ]);
        }
        return response()->json([
            'message' => __('strings.updated'),
            'status' => '1',
        ]);
    }
}
