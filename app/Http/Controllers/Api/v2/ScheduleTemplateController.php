<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Controllers\Controller;
use App\Http\Requests\v2\ApplyScheduleTemplateRequest;
use App\Http\Requests\v2\DeleteScheduleTemplateRequest;
use App\Http\Requests\v2\GetScheduleListRequest;
use App\Http\Requests\v2\SaveScheduleTemplateRequest;
use App\ScheduleTemplate;
use App\ScheduleTemplateTimeSlot;
use App\Traits\ApiResponser;
use App\Traits\BookingManager;
use App\User;
use App\UserTimeSlot;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ScheduleTemplateController extends Controller
{
    use ApiResponser;
    public function index(GetScheduleListRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $schedule_template  = ScheduleTemplate::where('company_id', $company->id);


        $isPaginated = false;
        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.schedule_template_returned'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($schedule_template) : $schedule_template->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $schedule_template : $schedule_template->get(),
                'message' => __('strings.schedule_template_returned'),
                'status' => '1',
            ]);
        }
    }

    public function store(SaveScheduleTemplateRequest $request)
    {
        $user = User::findOrFail($request->user_id);
        $company = $user->company;
        if (
            !UserTimeSlot::where('user_id', $user->id)
                ->whereDate('start_at', '>=', Carbon::parse($request->start_date)->format('Y-m-d'))
                ->whereDate('end_at', '<=', Carbon::parse($request->end_date)->format('Y-m-d'))
                ->exists()
        ) {
            return response()->json([
                'message' => __('strings.no_schedule_to_save_in_template'),
                'status' => '0',
            ]);
        }

        $schedule_template = ScheduleTemplate::create([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'name' => $request->name,
        ]);
        $user_time_slots = UserTimeSlot::where('user_id', $user->id)
            ->whereDate('start_at', '>=', Carbon::parse($request->start_date)->format('Y-m-d'))
            ->whereDate('end_at', '<=', Carbon::parse($request->end_date)->format('Y-m-d'))
            ->cursor();
        foreach ($user_time_slots as $user_time_slot) {
            ScheduleTemplateTimeSlot::create([
                'schedule_template_id' => $schedule_template->id,
                'note' => $user_time_slot->note,
                'week_day' => BookingManager::getWeekDay(Carbon::parse($user_time_slot->start_at)),
                'type' => $user_time_slot->type,
                'start_time' => Carbon::parse($user_time_slot->start_at)->format('H:i:s'),
                'end_time' => Carbon::parse($user_time_slot->end_at)->format('H:i:s'),
                'is_for_all_day' => $user_time_slot->is_for_all_day,
            ]);
        }
        return response()->json([
            'data' => $schedule_template->loadMissing(['time_slots']),
            'message' => __('strings.schedule_templated_created'),
            'status' => '1',
        ]);
    }
    public function apply(ScheduleTemplate $schedule_template, ApplyScheduleTemplateRequest $request)
    {
        $users_to_apply_schedule = User::whereIn('id', $request->user_ids);
        foreach ($users_to_apply_schedule->cursor() as $users_to_apply_schedule) {
            //REMOVING ALL THE TIME-SLOTS FROM THE USERS
            UserTimeSlot::where('user_id', $users_to_apply_schedule->id)
                ->whereDate('start_at', '>=', Carbon::parse($request->start_date)->format('Y-m-d'))
                ->whereDate('end_at', '<=', Carbon::parse($request->end_date)->format('Y-m-d'))
                ->delete();

            //now adding the slots to user from schedule template (starting from start date to end date)
            $start_date = Carbon::parse($request->start_date);
            $end_date = Carbon::parse($request->end_date);

            $temp_date = $start_date;
            while ($temp_date->lessThanOrEqualTo($end_date)) {

                //getting the weekday of given date
                $week_day = BookingManager::getWeekDay(Carbon::parse($temp_date));

                //getting the template time slots for given week day
                $schedule_template_slots = ScheduleTemplateTimeSlot::where('schedule_template_id', $schedule_template->id)->where('week_day', $week_day);

                //adding all the template slots to user's time slots for given date
                foreach ($schedule_template_slots->cursor() as $schedule_template_slot) {
                    UserTimeSlot::create([
                        'user_id' => $users_to_apply_schedule->id,
                        'note' => $schedule_template_slot->note,
                        'week_day' => $schedule_template_slot->week_day,
                        'type' => $schedule_template_slot->type,
                        'start_at' => Carbon::parse(Carbon::parse($temp_date)->format('Y-m-d') . ' ' . Carbon::parse($schedule_template_slot->start_time)->format('H:i:s')),
                        'end_at' => Carbon::parse(Carbon::parse($temp_date)->format('Y-m-d') . ' ' . Carbon::parse($schedule_template_slot->end_time)->format('H:i:s')),
                        'is_for_all_day' => $schedule_template_slot->is_for_all_day,
                    ]);
                }

                //adding 1 day into temp_date to go throw next date
                $temp_date = $temp_date->addDay();
            }
        }

        return response()->json([
            'message' => __('strings.schedule_templated_applied'),
            'status' => '1',
        ]);
    }
    public function delete(ScheduleTemplate $schedule_template, DeleteScheduleTemplateRequest $request)
    {
        $schedule_template->delete();
        return response()->json([
            'message' => __('strings.schedule_templated_deleted'),
            'status' => '1',
        ]);
    }
}