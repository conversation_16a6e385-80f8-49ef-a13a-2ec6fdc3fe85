<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use App\Http\Controllers\Controller;
use App\Traits\FreeTrailManager;
use Illuminate\Http\Request;

class FreeTrilController extends Controller
{
    use FreeTrailManager;
    public function test()
    {
        // return FreeTrailManager::isEligibleForFreeTrail(Company::findOrFail(25));
        if (FreeTrailManager::isEligibleForFreeTrail(Company::findOrFail(419))) {
            if (FreeTrailManager::cancelFreePlan(Company::findOrFail(419))) {
                return FreeTrailManager::startsFreeTrail(Company::findOrFail(419));
            }
            return false;
        }
        return false;
    }

    public function store(Request $request)
    {
        //
    }
}
