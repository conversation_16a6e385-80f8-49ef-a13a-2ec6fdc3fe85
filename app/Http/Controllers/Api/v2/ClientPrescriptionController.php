<?php

namespace App\Http\Controllers\Api\v2;

use App\Client;
use App\ClientPrescription;
use App\Events\PrescriptionSignedEvent;
use App\File;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\CommonIndexRequest;
use App\Http\Requests\v2\PrescriptionCreateRequest;
use App\Http\Requests\v2\PrescriptionEditRequest;
use App\Mail\DoctorNotificationMail;
use App\Setting;
use App\Traits\ApiResponser;
use App\Traits\SaveFile;
use App\User;
use App\UserCompany;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ClientPrescriptionController extends Controller
{
    use SaveFile;
    use ApiResponser;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Client $client, CommonIndexRequest $request)
    {
        $data = $client->prescriptions()
            ->with(['files', 'signed_by', 'cancelled_by']);
        $isPaginated = false;

        if ($request->has('filter')) {
            $data = $data->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $data = $data->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $data = $data->sortBy($orderBy, SORT_NATURAL | SORT_FLAG_CASE, $isDescOrder);
        } else {
            $data = $data->latest();
        }

        if ($request->has('search')) {
            if (!$isPaginated) {
                $data = $data->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);
            $data = $data->filter(function ($value) use ($search) {
                return
                    Str::contains(Str::lower($value->title), $search) ||
                    Str::contains(Str::lower($value->description), $search) ||
                    Str::contains(Str::lower($value->filenames ? implode(',', $value->filenames) : ''), $search) ||
                    // Str::contains(Str::lower($value->signed_by->email), $search) ||
                    Str::contains(Str::lower($value->signed_by?->first_name), $search) ||
                    Str::contains(Str::lower($value->signed_by?->last_name), $search);
            });
        }
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('messages.prescription_returned'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($data) : $data->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $data : $data->get(),
                'message' => __('messages.prescription_returned'),
                'status' => '1',
            ]);
        }
    }

    public function procedureIndex(Client $client, Request $request)
    {
        //TODO::try to use select but still get the dynamic keys like 'is_active' and 'is_inactive'
        $data = $client->prescriptions()
            ->where('sign_by_id', '!=', null)
            // ->whereDate('expire_at', '>', Carbon::now())//we don't need expire at check
            // ->select(['id', 'title', 'expire_at','is_inactive','is_active'])
            ->get();

        return response()->json([
            'data' => $data,
            'message' => __('messages.prescription_returned'),
            'status' => '1',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Client $client, PrescriptionCreateRequest $request)
    {
        $this->authorize('create', [ClientPrescription::class, $client]);
        $client_prescription = DB::transaction(function () use ($client, $request) {
            $filenames = [];
            if ($request->has('files')) {
                foreach ($request->files as $file) {
                    foreach ($file as $f) {
                        array_push($filenames, $f->getClientOriginalName());
                    }
                }
            }

            $prescription = $client->prescriptions()->create([
                'title' => $request->title,
                'description' => $request->description,
                'filenames' => count($filenames) > 0 ? array_values($filenames) : null,
                'expire_at' => $request->has('expire_at') ? Carbon::parse($request->expire_at) : null,
                'assign_to_id' => $request->assign_to_id,
            ]);

            if ($request->is_custom_date_selected == 1) {
                $timezone = $client->company->timezone ?? 'Europe/Stockholm';
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time", $timezone)->utc();
                $prescription->created_at = $created_at;
                $prescription->save();
            }
            if ($request->has('assign_to_id')) {
                $assignUser = User::findOrFail($request->assign_to_id);
                $company_setting = Setting::getSetting($assignUser->company, Setting::RECEIVE_EMAIL_NOTIFICATION);
                if ($company_setting->value) {
                    $language = Setting::getSetting($client->company, Setting::CUSTOMER_LANGUAGE)?->value;

                    Mail::to($assignUser->email)->locale($language ?? app()->getLocale())->send(new DoctorNotificationMail($assignUser, $prescription));
                }
            }

            $user = Auth::user();
            $msg = "{$client->first_name} {$client->last_name} client's Prescription " . $prescription->title . " created by {$user->fullName()}";
            $activity = activity()
                ->performedOn($prescription)
                ->by($user)
                ->log($msg);

            if ($request->has('files')) {
                foreach ($request->files as $file) {
                    foreach ($file as $f) {
                        $name = $this->saveFile($f, 'clients/' . md5($prescription->client_id) . '/prescriptions/media');
                        $prescription->file()->save($name);
                    }
                }
            }

            return $prescription;
        });

        return response()->json([
            'message' => __('strings.prescription_created'),
            'status' => '1',
            'data' => $client_prescription->loadMissing('files'),
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Client $client, ClientPrescription $prescription, PrescriptionEditRequest $request)
    {
        $this->authorize('update', [ClientPrescription::class, $prescription]);
        DB::transaction(function () use ($request, $prescription, $client) {
            if ($request->has('title')) {
                $prescription->title = $request->title;
            }

            if ($request->has('description')) {
                $prescription->description = $request->description;
            }
            if ($request->has('expire_at')) {
                $prescription->expire_at = Carbon::parse($request->expire_at);
            }

            if ($request->has('files')) {
                $filenames = $prescription->filenames ?? [];
                foreach ($request->files as $index => $file) {
                    foreach ($file as $f) {
                        array_push($filenames, $f->getClientOriginalName());
                        $f = $this->saveFile($f, 'clients/' . md5($prescription->client_id) . '/prescriptions/media');
                        $prescription->file()->save($f);
                    }
                }
                $prescription->filenames = array_values($filenames);
            }
            if ($request->has('assign_to_id') && $prescription->assign_to_id == null) {
                $prescription->assign_to_id = $request->assign_to_id;
            }

            if ($request->hasFile('sign') && $prescription->assign_to_id == null) {
                $user = Auth::user();
                $file = $this->saveFile($request->file('sign'), 'clients/' . md5($prescription->client_id) . '/prescriptions/sign');
                $prescription->sign = $file->filename;
                $prescription->sign_by_id = $user->id;
                $prescription->signed_by_name = $user->first_name . " " . $user->last_name;
                $prescription->signed_at = now();
                PrescriptionSignedEvent::dispatch($prescription);
            }

            if ($request->has('is_signed_by_bank_id') && $request->is_signed_by_bank_id) {
                $user = Auth::user();
                $prescription->is_signed_by_bank_id = 1;
                $prescription->sign_by_id = $user->id;
                $prescription->signed_by_bank_id = $user->personal_id;
                $prescription->signed_by_name = $user->first_name . " " . $user->last_name;
                $prescription->signed_at = now();
                PrescriptionSignedEvent::dispatch($prescription);
            }

            if ($request->has('is_cancelled') && $request->is_cancelled) {
                $prescription->is_cancelled = $request->is_cancelled;
                $prescription->cancel_note = $request->cancel_note;
                $prescription->cancelled_by_id = Auth::id();
                $prescription->cancelled_at = Carbon::now();



                $user = Auth::user();
                $msg = "{$client->first_name} {$client->last_name} client's Prescription " . $prescription->title . " cancelled by {$user->fullName()}";
                $activity = activity()
                    ->performedOn($prescription)
                    ->by($user)
                    ->log($msg);
            }

            $prescription->save();
        });

        return response()->json([
            'data' => $prescription->loadMissing('files'),
            'message' => __('strings.prescription_updated'),
            'status' => '1',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Client $client, ClientPrescription $prescription)
    {
        $this->authorize('delete', [ClientPrescription::class, $prescription]);
        $user = Auth::user();
        $msg = "{$client->first_name} {$client->last_name} client's Prescription " . $prescription->title . " deleted by {$user->fullName()}";
        $activity = activity()
            ->performedOn($prescription)
            ->by($user)
            ->log($msg);

        $prescription->delete();

        return response()->json([
            'message' => __('strings.prescription_delete'),
            'status' => '1',
        ]);
    }

    public function destroyFile(Client $client, ClientPrescription $prescription, File $file, Request $request)
    {
        if ($prescription->filenames) {
            $names = $prescription->filenames;
            unset($names[$request->index]);
            $prescription->filenames = array_values($names);
            $prescription->save();
        }
        $file = $prescription->files()->where('files.id', $file->id)->firstOrFail();
        $file->delete();
        $prescription->refresh();

        return response()->json([
            'data' => $prescription->loadMissing('files'),
            'message' => 'media file deleted successfully.',
            'status' => '1',
        ]);
    }
}
