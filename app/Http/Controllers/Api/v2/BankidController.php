<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Traits\BankID;
use App\User;
use Illuminate\Support\Facades\Auth;

class BankidController extends Controller
{

    public function test()
    {
        return view('take_tokens');
    }
    public function testTab(Request $request)
    {
        if ($request->has('close_self')) {
            return view('take_tokens_tab_close');
        }
        return view('take_tokens_tab', [
            'autoStartToken' => $request->autoStartToken,
        ]);
    }
    public function startOrder(Request $request)
    {
        try {
            $response = BankID::authOrder($request->input('personal_number', null));
            $order_ref = null;
            $start_token = null;
            $start_time = null;
            $start_secret = null;
            $auto_start_token = null;

            if (isset($response->orderRef)) {
                $order_ref = (string)$response->orderRef;
            }
            if (isset($response->qrStartToken)) {
                $start_token = (string)$response->qrStartToken;
            }
            if (isset($response->qrStartSecret)) {
                $start_secret = (string)$response->qrStartSecret;
            }
            if (isset($response->autoStartToken)) {
                $auto_start_token = (string)$response->autoStartToken;
            }
            date_default_timezone_set('UTC');
            $start_time  = new \DateTime();
            $start_time  = (string)$start_time->getTimestamp();
            return response()->json([
                'data' => [
                    'order_ref' => $order_ref,
                    'start_token' => $start_token,
                    'start_time' => $start_time,
                    'start_secret' => $start_secret,
                    'auto_start_token' => $auto_start_token,
                ],
                'message' => 'order started',
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0'
            ]);
        }
    }

    public function generateQr(Request $request)
    {
        date_default_timezone_set('UTC');
        $qrStartToken   = $request->start_token;
        $qrStartSecret  = $request->start_secret;
        $startTime  = $request->start_time;

        $endTime = new \DateTime();
        $endTime = $endTime->getTimestamp();
        $qrTime = $endTime - $startTime;
        $qrAuthCode = hash_hmac('sha256', $qrTime, $qrStartSecret);
        $qrData = implode('.', [
            'bankid',
            $qrStartToken,
            $qrTime,
            $qrAuthCode
        ]);
        return response()->json([
            'data' => $qrData,
            'message' => 'gr generated',
            'status' => '1'
        ]);
    }

    public function collectOrder(Request $request)
    {
        try {
            $response = BankID::collectOrder($request->order_ref);
            if (!isset($response->status)) {
                return response()->json([
                    'data' => $response,
                    'message' => 'Something went wrong',
                    'status' => '0'
                ]);
            }
            switch ($response->status) {
                case 'complete':
                    return response()->json([
                        'data' => $response->completionData->user,
                        'message' => "Order completed",
                        'status' => '1'
                    ]);
                    break;
                case 'pending':
                    return response()->json([
                        'message' => "Order Pending please wait",
                        'status' => '2'
                    ]);
                    break;
                case 'failed':
                    return response()->json([
                        'message' => "Order failed",
                        'status' => '0'
                    ]);
                    break;
                default:
                    return response()->json([
                        'message' => $response->status,
                        'status' => '0'
                    ]);
                    break;
            }
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0'
            ]);
        }
    }

    public function startVerificationOrder(Request $request)
    {
        $user = Auth::user();

        if ($user->is_bankid_verified) {
            return response()->json([
                'message' => __('strings.already_verified'),
                'status' => '0'
            ]);
        }
        try {
            $response = BankID::authOrder($request->input('personal_number', null));
            $order_ref = null;
            $start_token = null;
            $start_time = null;
            $start_secret = null;
            $auto_start_token = null;

            if (isset($response->orderRef)) {
                $order_ref = (string)$response->orderRef;
            }
            if (isset($response->qrStartToken)) {
                $start_token = (string)$response->qrStartToken;
            }
            if (isset($response->qrStartSecret)) {
                $start_secret = (string)$response->qrStartSecret;
            }
            if (isset($response->autoStartToken)) {
                $auto_start_token = (string)$response->autoStartToken;
            }
            date_default_timezone_set('UTC');
            $start_time  = new \DateTime();
            $start_time  = (string)$start_time->getTimestamp();
            return response()->json([
                'data' => [
                    'order_ref' => $order_ref,
                    'start_token' => $start_token,
                    'start_time' => $start_time,
                    'start_secret' => $start_secret,
                    'auto_start_token' => $auto_start_token,
                ],
                'message' => 'order started',
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0'
            ]);
        }
    }

    public function generateVerificationQr(Request $request)
    {
        $user = Auth::user();

        if ($user->is_bankid_verified) {
            return response()->json([
                'message' => __('strings.already_verified'),
                'status' => '0'
            ]);
        }
        date_default_timezone_set('UTC');
        $qrStartToken   = $request->start_token;
        $qrStartSecret  = $request->start_secret;
        $startTime  = $request->start_time;

        $endTime = new \DateTime();
        $endTime = $endTime->getTimestamp();
        $qrTime = $endTime - $startTime;
        $qrAuthCode = hash_hmac('sha256', $qrTime, $qrStartSecret);
        $qrData = implode('.', [
            'bankid',
            $qrStartToken,
            $qrTime,
            $qrAuthCode
        ]);
        return response()->json([
            'data' => $qrData,
            'message' => 'gr generated',
            'status' => '1'
        ]);
    }

    public function collectVerificationOrder(Request $request)
    {
        $user = Auth::user();

        if ($user->is_bankid_verified) {
            return response()->json([
                'message' => __('strings.already_verified'),
                'status' => '0'
            ]);
        }
        try {
            $response = BankID::collectOrder($request->order_ref);
            if (!isset($response->status)) {
                return response()->json([
                    'data' => $response,
                    'message' => 'Something went wrong',
                    'status' => '0'
                ]);
            }
            switch ($response->status) {
                case 'complete':
                    if (!$response->completionData->user->personalNumber) {
                        return response()->json([
                            'message' => "Order failed",
                            'status' => '0'
                        ]);
                    }

                    $personal_id = substr_replace($response->completionData->user->personalNumber, '-', 8, 0);

                    // if ($user->personal_id != $personal_id) {

                    //CHECKING FOR PERSONAL ID
                    $count = User::where('id', '!=', $user->id)->get()->filter(function ($user) use ($personal_id) {
                        if ($personal_id) {
                            return strtolower($user->personal_id) === strtolower($personal_id);
                        }
                        return false;
                    })->count();

                    if ($count > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_personal_id'),
                            'status' => '5'
                        ]);
                    }

                    $user->is_bankid_verified = 1;
                    $user->personal_id = $personal_id;
                    $user->save();
                    return response()->json([
                        'data' => $response->completionData->user,
                        'message' => "Order completed",
                        'status' => '1'
                    ]);
                    // }

                    break;
                case 'pending':
                    return response()->json([
                        'message' => "Order Pending please wait",
                        'status' => '2'
                    ]);
                    break;
                case 'failed':
                    return response()->json([
                        'message' => "Order failed",
                        'status' => '0'
                    ]);
                    break;
                default:
                    return response()->json([
                        'message' => $response->status,
                        'status' => '0'
                    ]);
                    break;
            }
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0'
            ]);
        }
    }
}
