<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\CompanyCategory;
use App\CompanyService;
use App\Covid19;
use App\Events\PasswordChangeEvent;
use App\Exceptions\GenericImportException;
use App\Exports\CompanyExport;
use App\GeneralNote;
use App\HealthQuestionary;
use App\Http\Controllers\Api\v1\AfterCareTreatmentController;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\GetAvailableSlotsRequest;
use App\Imports\GenericImport;
use App\Mail\BookingExtraStepMail;
use App\Models\UserCalendar;
use App\Models\UserOauthToken;
use App\Plan;
use App\Setting;
use App\Support;
use App\Traits\SaveFile;
use App\Traits\Sinch;
use Carbon\Carbon;
use Exception;
use Maatwebsite\Excel\Facades\Excel;
use App\Traits\FreeTrailManager;
use App\Traits\OAuth\Google\GoogleAuth;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Stripe\Coupon;
use Stripe\PromotionCode;
use Stripe\Stripe;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

class TestController extends Controller
{
    use SaveFile;
    use Sinch;


    public function disconnectGoogle(Request $request)
    {
        if ($request->has('email')) {
            $user = User::where('email', $request->email)->first();
            if ($user) {
                $user_calendars = UserCalendar::where('user_id', $user->id)->get();
                foreach ($user_calendars as $user_calendar) {
                    if ($user_calendar->calendar_id) {
                        $oauth_token = GoogleAuth::refreshTokenForUser($user);

                        $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
                        try {
                            GoogleAuth::deleteCalendarById($google_auth, $user_calendar->calendar_id);
                        } catch (\Throwable $th) {
                            //throw $th;
                        }
                        $user_calendar->delete();
                    }
                }

                UserOauthToken::where('user_id', $user->id)->delete();
            }
            return response()->json([
                'message' => "connection removed",
                'status' => '1',
            ]);
        } else {
            return response()->json([
                'message' => "Please provide email",
                'status' => '0',
            ]);
        }
    }
    public function test(Request $request)
    {

        // $data = array(
        //     "title" => "hello",
        //     "description" => "test test test"
        //   );

        // return PDF::loadView('exports.client.view', $data)
        // ->margins(20, 0, 0, 20)
        // ->download();

        // $phone_number = array(
        //     '+919265155946'
        // );
        // return $this->sendSMS($phone_number, 'HEllo');
        // $compony_cate = CompanyCategory::where('company_id',2)->first();
        // $user = User::where('id',2)->first();
        // $policy = $this->authorize('update', [User::class, $user]);

        $start_id = $request->start_id;
        $end_id = $request->end_id;



        if (!($start_id && $end_id)) {
            return "error";
        }

        $companies = Company::where('id', '>=', $start_id)->where('id', '<=', $end_id)->cursor();


        $companies_with_problem = [];
        foreach ($companies as $company) {
            if ($company->stripe_id) {
                try {
                    $company->createOrGetStripeCustomer();
                } catch (\Throwable $th) {
                    array_push($companies_with_problem, $company->email);
                }
            }
        }

        return $companies_with_problem;
    }

    public function finishFreeTrail(Request $request)
    {
        if ($request->has('email')) {
            $company = Company::where('email', $request->email)->first();
            $company->free_trail_start_date = Carbon::now()->subDay(1)->format('Y-m-d');
            $company->free_trail_end_date = Carbon::now()->subDay(1)->format('Y-m-d');
            $company->save();
            FreeTrailManager::finishFreeTrail($company);
        }
        Artisan::call('handle:free:trail');
        return response()->json([
            'message' => "Trail finished",
            'status' => '1',
        ]);
    }
    function testSlots($id, GetAvailableSlotsRequest $request)
    {
        if ($request->has('email')) {
            $company = Company::where('email', $request->email)->first();
            $company->free_trail_start_date = Carbon::now()->subDay(1)->format('Y-m-d');
            $company->free_trail_end_date = Carbon::now()->subDay(1)->format('Y-m-d');
            $company->save();
        }
        return response()->json([
            'message' => "Trail finished",
            'status' => '1',
        ]);
    }

    // function testSlots($id, GetAvailableSlotsRequest $request)
    // {
    //     try {
    //         $id = Company::decryptId($id);
    //     } catch (Exception $e) {
    //         return response()->json([
    //             'message' => __('strings.Please_provide_valid_url'),
    //             'status' => '0',
    //         ]);
    //     }
    //     $service = null;
    //     if ($request->has('service_id')) {
    //         $service = CompanyService::findOrFail($request->service_id);
    //     }
    //     $company = Company::findOrFail($id);

    //     $start_date = null;
    //     $end_date = null;
    //     if ($request->has('start_date') && $request->has('end_date')) {
    //         $start_date = Carbon::parse($request->start_date);
    //         $end_date = Carbon::parse($request->end_date);
    //     }
    //     $user = Auth::user();
    //     $user_ids = $request->user_ids ?? null;
    //     if ($user && $user->user_role == User::USER) {
    //         $user_ids  = [$user->id];
    //     }
    //     $data = User::tempGetAvailableSlots($company, $start_date, $end_date, $service, $user_ids, $company->timezone);
    //     return response()->json([
    //         'data' => $data,
    //         'message' => __('strings.available_slots_returned'),
    //         'status' => '1'
    //     ]);
    // }
}