<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Controllers\Controller;
use App\Plan;
use Illuminate\Http\Request;

class PlaneController extends Controller
{
    public function index(Request $request)
    {
        $planes = Plan::query();
        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => 'Planes return successfully',
                    'status' => '1',
                ])->merge($planes->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $planes->get(),
                'message' => 'Planes return successfully',
                'status' => '1',
            ]);
        }
    }

    public function store(Request $request)
    {
        //
    }
}