<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Controllers\Controller;
use App\Http\Requests\v1\TreatmentRequest;
use App\Http\Requests\v2\TreatmentIndexRequest;
use App\Traits\ApiResponser;
use App\Traits\ColorGenerator;
use App\Treatment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class TreatmentController extends Controller
{
    use ColorGenerator, ApiResponser;

    public function index(TreatmentIndexRequest $request)
    {
        $isPaginated = false;
        $treatments = Auth::user()->company->treatments();
        if ($request->has('company_id')) {
            $treatments = Treatment::where('company_id',$request->company_id);
        }else{
            $treatments = Auth::user()->company->treatments();
        }
        if ($request->has('filter')) {
            if ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed') {
                $treatments = $treatments->{$request->filter}();
            } else {
                $treatments = $treatments->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
            }
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            if ($request->orderBy == 'name') {
                if (!$isPaginated) {
                    $treatments = $treatments->get();
                    $isPaginated = true;
                }
                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = $orderBy == 'name' ? (SORT_NATURAL | SORT_FLAG_CASE) : SORT_FLAG_CASE;
                // $treatments =  $treatments->sort(function ($a, $b) use ($orderBy) {
                //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
                // });
                $treatments = in_array($request->input('orderDirection', 'asc'), ['asc', 'ASC']) ? $treatments->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE) : $treatments->sortByDesc('name', SORT_NATURAL | SORT_FLAG_CASE);
            } else {
                $treatments = $treatments->orderBy($request->orderBy, $request->input('orderDirection', 'asc'));
            }
        }

        if ($request->has('search')) {
            if (!$isPaginated) {
                $treatments = $treatments->get();
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $treatments = $treatments->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->name), $search);
            });
            $isPaginated = true;
        }

        if ($request->has('type')) {
            if (!$isPaginated) {
                $treatments = $treatments->get();
            }
            $treatments = $treatments->where('type', $request->type);
            $isPaginated = true;
        }

        if ($request->input('page')) {
            return response()->json(
                collect(
                    [
                        'message' => 'Treatments returned successfully',
                        'status' => '1',
                    ]
                )->merge($isPaginated ? $this->paginate($treatments) : $treatments->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $treatments : $treatments->get(),
                'message' => 'Treatments returned successfully',
                'status' => '1',
            ]);
        }
    }

    public function store(TreatmentRequest $request)
    {
        $this->authorize('create', [Treatment::class]);

        $company = Auth::user()->company;

        if (!$company) {
            return response()->json([
                'message' => __('strings.Invalid_company'),
                'status' => '0',
            ]);
        }

        $input = $request->validated();

        $input['company_id'] = $company->id;
        $input['unit'] = $request->input('unit', $company->unit ?? 'usd');
        $input['color'] = $this->generate();

        Treatment::create($input);

        return response()->json([
            'message' => __('strings.Treatment_created_successfully'),
            'status' => '1',
        ]);
    }

    public function update(TreatmentRequest $request, Treatment $treatment)
    {
        $this->authorize('update', [Treatment::class, $treatment]);

        if ($request->has('name')) {
            $treatment->name = $request->input('name');
        }

        if ($request->has('description')) {
            $treatment->description = $request->input('description', '');
        }

        if ($request->has('cost')) {
            $treatment->cost = $request->input('cost', 0);
        }

        if ($request->has('unit')) {
            $treatment->unit = $request->input('unit');
        }

        if ($request->has('notes')) {
            $treatment->notes = $request->input('notes');
        }

        if ($treatment->isDirty()) {
            $treatment->save();
        }

        return response()->json([
            'message' => __('strings.Treatment_Updated_successfully'),
            'status' => '1',
        ]);
    }

    public function delete(Treatment $treatment)
    {
        $this->authorize('delete', [Treatment::class, $treatment]);

        if ($treatment->delete()) {
            return response()->json([
                'message' => __('strings.Treatment_removed_successfully'),
                'status' => '1',
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Treatment_removal_failed'),
                'status' => '0',
            ]);
        }
    }

    public function restore()
    {
        $treatment = Treatment::withTrashed()->find(request('treatment_id'));
        $this->authorize('restore', [Treatment::class, $treatment]);
        if ($treatment) {
            if ($treatment->restore()) {
                return response()->json([
                    'message' => __('strings.Treatment_restored_successfully'),
                    'status' => '1',
                ]);
            } else {
                return response()->json([
                    'message' => __('strings.Treatment_restoration_failed'),
                    'status' => '0',
                ]);
            }
        } else {
            return response()->json([
                'message' => __('strings.Does_not_exists_treatment'),
                'status' => '0',
            ]);
        }
    }
}