<?php

namespace App\Http\Controllers\Api\v2;

use App\Exceptions\GenericImportException;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\GetExcelDataRequest;
use App\Http\Requests\v2\ImportBookingRequest;
use App\Http\Requests\v2\ImportClientsRequest;
use App\Imports\BookingImport;
use App\Imports\ClientImport;
use App\Imports\CustomBookingImport;
use App\Imports\CustomClientImport;
use App\Imports\GenericImport;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class ExcelController extends Controller
{
    public function getExcelData(GetExcelDataRequest $request)
    {
        try {
            $generic_import = new GenericImport;
            Excel::import($generic_import, $request->file('file')->store('temp'));
        } catch (GenericImportException $th) {
            $data = $th->data['data'];
            $count = $th->data['count'];
            if ($data && is_array($data) && count($data) > 0) {
                $heading = array_shift($data);

                return response()->json([
                    'data' => [
                        'count' => $count,
                        'heading' => $heading,
                        'values' => $data,
                    ],
                    'message' => 'Data returned',
                    'status' => '1',
                ]);
            }
        } catch (Exception $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }

        return response()->json([
            'message' => __('strings.something_wrong'),
            'status' => '0',
        ]);
    }

    public function importBooking(ImportBookingRequest $request)
    {
        $start_time = microtime(true);
        $user = Auth::user();
        $data = [];
        if ($request->has('interval')) {
            $data['interval_index'] = $request->interval;
        }
        if ($request->has('date')) {
            $data['date_index'] = $request->date;
        }
        if ($request->has('status')) {
            $data['status_index'] = $request->status;
        }
        if ($request->has('special_request')) {
            $data['special_request_index'] = $request->special_request;
        }
        if ($request->has('client_first_name')) {
            $data['client_first_name_index'] = $request->client_first_name;
        }
        if ($request->has('client_last_name')) {
            $data['client_last_name_index'] = $request->client_last_name;
        }
        if ($request->has('client_email')) {
            $data['client_email_index'] = $request->client_email;
        }
        if ($request->has('client_personal_number')) {
            $data['client_personal_number_index'] = $request->client_personal_number;
        }
        if ($request->has('client_phone_number')) {
            $data['client_phone_number_index'] = $request->client_phone_number;
        }
        if ($request->has('practitioner_name')) {
            $data['practitioner_name_index'] = $request->practitioner_name;
        }
        if ($request->has('practitioner_email')) {
            $data['practitioner_email_index'] = $request->practitioner_email;
        }
        if ($request->has('service_name')) {
            $data['service_name_index'] = $request->service_name;
        }
        if ($request->has('price')) {
            $data['price_index'] = $request->price;
        }
        if ($request->has('duration')) {
            $data['duration_index'] = $request->duration;
        }

        $booking_import = null;
        if ($request->has('get_summery') && $request->get_summery) {
            try {
                $booking_import = new BookingImport($data, $user->company, $user, 1);
                Excel::import($booking_import, $request->file('file')->store('temp'));
            } catch (Exception $th) {
                return response()->json([
                    'message' => $th->getMessage(),
                    'status' => '0',
                ]);
            }

            return response()->json([
                'data' => $booking_import->output,
                'message' => __('strings.data_summery'),
                'status' => '1',
            ]);
        } else {
            try {
                $booking_import = new BookingImport($data, $user->company, $user, 0);
                Excel::import($booking_import, $request->file('file')->store('temp'));
            } catch (Exception $th) {
                return response()->json([
                    'message' => $th->getMessage(),
                    'status' => '0',
                ]);
            }

            $end_time = microtime(true);
            $time = $end_time - $start_time;

            if ($booking_import->output['not_imported_booking_count'] > 0) {
                return response()->json([
                    'data' => $booking_import->output,
                    'time' => $time,
                    'message' => __(
                        'strings.bookings_imported_with_not_imported_count',
                        [
                            'imported_booking_count' => $booking_import->output['imported_booking_count'],
                            'not_imported_booking_count' => $booking_import->output['not_imported_booking_count'],
                        ]
                    ),
                    'status' => '1',
                ]);
            } else {
                return response()->json([
                    'data' => $booking_import->output,
                    'time' => $time,
                    'message' => __('strings.bookings_imported_without_not_imported_count', ['imported_booking_count' => $booking_import->output['imported_booking_count']]),
                    'status' => '1',
                ]);
            }
        }
    }

    public function getTemplates()
    {
        $url = config('app.url');

        $client_template_url = $url . '/templates/client_import_template.xlsx';
        $booking_template_url = $url . '/templates/booking_import_template.xlsx';

        return response()->json([
            'data' => [
                'clients' => $client_template_url,
                'bookings' => $booking_template_url,
            ],
            'message' => __('strings.import_template_returned'),
            'status' => '1',
        ]);
    }

    public function importClient(ImportClientsRequest $request)
    {
        $start_time = microtime(true);

        $user = Auth::user();
        $data = [];
        if ($request->has('email')) {
            $data['email_index'] = $request->email;
        }
        if ($request->has('first_name')) {
            $data['first_name_index'] = $request->first_name;
        }
        if ($request->has('last_name')) {
            $data['last_name_index'] = $request->last_name;
        }
        if ($request->has('phone_number')) {
            $data['phone_number_index'] = $request->phone_number;
        }
        if ($request->has('personal_id')) {
            $data['personal_number_index'] = $request->personal_id;
        }
        if ($request->has('danish_id')) {
            $data['danish_id_index'] = $request->danish_id;
        }
        try {
            $client_import = new ClientImport($data, $user->company, $user);
            Excel::import($client_import, $request->file('file')->store('temp'));
        } catch (Exception $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }

        $end_time = microtime(true);
        if ($client_import->output['not_imported_client_count'] > 0) {
            return response()->json([
                'data' => $client_import->output,
                'time' => $end_time - $start_time,
                'message' => __(
                    'strings.clients_imported_with_not_imported_count',
                    [
                        'imported_client_count' => $client_import->output['imported_client_count'],
                        'not_imported_client_count' => $client_import->output['not_imported_client_count'],
                    ]
                ),
                'status' => '1',
            ]);
        } else {
            return response()->json([
                'data' => $client_import->output,
                'time' => $end_time - $start_time,
                'message' => __('strings.clients_imported_without_not_imported_count', ['imported_client_count' => $client_import->output['imported_client_count']]),
                'status' => '1',
            ]);
        }
    }

    public function importCustomBooking(Request $request)
    {
        $start_time = microtime(true);
        $user = Auth::user();
        $data = [];
        if ($request->has('service_name')) {
            $data['service_name_index'] = $request->service_name;
        }
        if ($request->has('date')) {
            $data['date_index'] = $request->date;
        }
        if ($request->has('interval')) {
            $data['interval_index'] = $request->interval;
        }
        if ($request->has('duration')) {
            $data['duration_index'] = $request->duration;
        }
        if ($request->has('practitioner_name')) {
            $data['practitioner_name_index'] = $request->practitioner_name;
        }
        if ($request->has('practitioner_email')) {
            $data['practitioner_email_index'] = $request->practitioner_email;
        }
        if ($request->has('client_personal_number')) {
            $data['client_personal_number_index'] = $request->client_personal_number;
        }
        if ($request->has('client_first_name')) {
            $data['client_first_name_index'] = $request->client_first_name;
        }
        if ($request->has('client_last_name')) {
            $data['client_last_name_index'] = $request->client_last_name;
        }
        if ($request->has('client_email')) {
            $data['client_email_index'] = $request->client_email;
        }
        if ($request->has('client_phone_number')) {
            $data['client_phone_number_index'] = $request->client_phone_number;
        }


        if ($request->has('status')) {
            $data['status_index'] = $request->status;
        }
        if ($request->has('special_request')) {
            $data['special_request_index'] = $request->special_request;
        }
        if ($request->has('price')) {
            $data['price_index'] = $request->price;
        }

        $booking_import = null;
        if ($request->has('get_summery') && $request->get_summery) {
            // try {
            $booking_import = new CustomBookingImport($data, $user->company, $user, 1);
            Excel::import($booking_import, $request->file('file')->store('temp'));
            // } catch (Exception $th) {
            //     return response()->json([
            //         'message' => $th->getMessage(),
            //         'status' => '0',
            //     ]);
            // }

            return response()->json([
                // 'data' => $booking_import->output,
                'message' => __('strings.data_summery'),
                'status' => '1',
            ]);
        } else {
            try {
                $booking_import = new CustomBookingImport($data, $user->company, $user, 0);
                Excel::import($booking_import, $request->file('file')->store('temp'));
            } catch (Exception $th) {
                return response()->json([
                    'message' => $th->getMessage(),
                    'status' => '0',
                ]);
            }

            $end_time = microtime(true);
            $time = $end_time - $start_time;

            if ($booking_import->output['not_imported_booking_count'] > 0) {
                return response()->json([
                    'data' => $booking_import->output,
                    'time' => $time,
                    'message' => __(
                        'strings.bookings_imported_with_not_imported_count',
                        [
                            'imported_booking_count' => $booking_import->output['imported_booking_count'],
                            'not_imported_booking_count' => $booking_import->output['not_imported_booking_count'],
                        ]
                    ),
                    'status' => '1',
                ]);
            } else {
                return response()->json([
                    'data' => $booking_import->output,
                    'time' => $time,
                    'message' => __('strings.bookings_imported_without_not_imported_count', ['imported_booking_count' => $booking_import->output['imported_booking_count']]),
                    'status' => '1',
                ]);
            }
        }
    }

    public function importCustomClient(Request $request)
    {
        $user = Auth::user();
        $data = [];
        if ($request->has('email')) {
            $data['email_index'] = $request->email;
        }
        if ($request->has('first_name')) {
            $data['first_name_index'] = $request->first_name;
        }
        if ($request->has('last_name')) {
            $data['last_name_index'] = $request->last_name;
        }
        if ($request->has('phone_number')) {
            $data['phone_number_index'] = $request->phone_number;
        }
        if ($request->has('personal_id')) {
            $data['personal_number_index'] = $request->personal_id;
        }
        if ($request->has('danish_id')) {
            $data['danish_id_index'] = $request->danish_id;
        }
        try {
            $client_import = new CustomClientImport($data, $user->company, $user);
            Excel::import($client_import, $request->file('file')->store('temp'));
        } catch (Exception $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }
        return $client_import->output;
    }
}
