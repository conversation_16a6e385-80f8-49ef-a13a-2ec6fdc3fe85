<?php

namespace App\Http\Controllers\Api\v2;

use App\Client;
use App\ClientTreatment;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\ClientTreatmentDeleteMobileRequest;
use App\Http\Requests\v2\ClientTreatmentRequest;
use App\Http\Requests\v2\ClientTreatmentUpdateMobileRequest;
use App\Http\Requests\v2\ClientTreatmentUpdateRequest;
use App\Traits\ColorGenerator;
use App\Traits\SaveFile;
use App\Treatment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ClientTreatmentController extends Controller
{
    use ColorGenerator, SaveFile;

    public function store(ClientTreatmentRequest $request, Client $client)
    {
        $this->authorize('update', [Client::class, $client]);

        $user = Auth::user();
        $default_unit = $user->company->unit;

        $client_treatment_images = [];

        $files = [];
        foreach ($request->file('images', []) as $index => $image) {
            $file = $this->saveFile($image, 'clients/' . md5($client->id) . '/treatments');
            $files[] = $file;

            array_push($client_treatment_images, $file->filename);
        }

        $clientTreatment = DB::transaction(function () use ($client, $request, $client_treatment_images, $files, $default_unit) {
            $clientTreatment = Auth::user()->client_treatments()->create([
                'client_id' => $client->id,
                'name' => $request->input('name'),
                'description' => $request->input('description', '') ?? '',
                'cost' => $request->input('cost', '') ?? '',
                'unit' => $default_unit ?? $request->input('unit', $default_unit || 'usd'),
                'color' => $request->input('color', '#000000'),
                'notes' => $request->input('notes', '') ?? '',
                'notes_html' => $request->input('notes_html', '') ?? '',
                'date' => $request->input('date'),
                'treatment_cost' => $request->input('treatment_cost', '') ?? '',
                'treatment_id' => $request->input('treatment_id', '') ?? NULL,
                'images' => json_encode($client_treatment_images),
                'nrs_rating' => $request->input('nrs_rating') ?? NULL,
            ]);

            if ($request->has('treatments')) {
                foreach ($request->input('treatments', []) as $index => $treatmentData) {
                    $treatment = Treatment::findOrFail($treatmentData['id']);
                    $clientTreatmentDetail = $clientTreatment->details()->create([
                        'name' => $treatment->name,
                        'description' => $treatment->description,
                        'cost' => $treatment->cost,
                        'unit' => $default_unit ?? $treatment->unit,
                        'color' => $treatment->color,
                        'notes' => $treatment->notes,
                        'type' => $treatment->type,
                        'actual_cost' => isset($treatmentData['actual_cost']) ? $treatmentData['actual_cost'] : 0,
                        'actual_unit' => $default_unit ?? $request->input('unit', $default_unit || 'usd'),
                        'treatment_id' => $treatment->id,
                    ]);
                }
            }

            if ($request->has('prescriptions')) {
                $clientTreatment->prescriptions()->sync($request->prescriptions);
            }

            $clientTreatment->files()->saveMany($files);
            return $clientTreatment;
        });

        return response()->json([
            'data' => $clientTreatment,
            'message' => __('strings.Client_Treatment_created_successfully'),
            'status' => '1',
        ]);
    }

    public function update(ClientTreatmentUpdateRequest $request, ClientTreatment $client_treatment)
    {
        $this->authorize('update', [ClientTreatment::class, $client_treatment, $request]);

        $user = Auth::user();
        $default_unit = $user->company->unit;
        DB::transaction(function () use ($request, $client_treatment, $default_unit) {
            if ($request->has('name')) {
                $client_treatment->name = $request->input('name', '') ?? '';
            }
            if ($request->has('date')) {
                $client_treatment->date = $request->input('date', NULL) ?? NULL;
            }

            if ($request->has('notes')) {
                $client_treatment->notes = $request->input('notes', '') ?? '';
            }
            if ($request->has('notes_html')) {
                $client_treatment->notes_html = $request->input('notes_html', '') ?? '';
            }
            if ($request->has('unit')) {
                $client_treatment->unit = $default_unit ?? $request->input('unit', $default_unit ?? 'usd') ?? '';
            }

            if ($request->has('images')) {
                $client_treatment_images = [];

                $files = [];
                foreach ($request->file('images', []) as $index => $image) {
                    $file = $this->saveFile($image, 'clients/' . md5($client_treatment->client_id) . '/treatments');
                    $files[] = $file;

                    array_push($client_treatment_images, $file->filename);
                }

                if (count($files) && count($client_treatment_images)) {
                    $client_treatment->files->each->delete();
                    $client_treatment->images = json_encode($client_treatment_images);
                    $client_treatment->files()->saveMany($files);
                }
            }

            if ($request->has('treatments') && count($request->input('treatments', []))) {
                $client_treatment->details()->delete();

                foreach ($request->input('treatments', []) as $index => $treatmentData) {
                    $treatment = Treatment::findOrFail($treatmentData['id']);
                    $clientTreatmentDetail = $client_treatment->details()->create([
                        'name' => $treatment->name,
                        'description' => $treatment->description,
                        'cost' => $treatment->cost,
                        'unit' => $default_unit ?? $treatment->unit,
                        'color' => $treatment->color,
                        'notes' => $treatment->notes,
                        'type' => $treatment->type,
                        'actual_cost' => isset($treatmentData['actual_cost']) ? $treatmentData['actual_cost'] : 0,
                        'actual_unit' => $default_unit ?? $treatment->unit,
                        'treatment_id' => $treatment->id,
                    ]);
                }
            } else if (!$request->hasFile('sign')) {
                $client_treatment->details()->delete();
            }

            if ($request->hasFile('sign')) {
                $file = $this->saveFile($request->file('sign'), 'clients/' . md5($client_treatment->client_id) . '/treatments/sign');
                $client_treatment->sign = $file->filename;
                $client_treatment->signed_at = now();
                $client_treatment->signed_by_id = Auth::id();
                $client_treatment->timestamps = false;

                $activity = activity()
                    ->performedOn($client_treatment);

                $activity = $activity->by(Auth::user());
                $activity->log("{$client_treatment->client->first_name} {$client_treatment->client->last_name} client 's procedure (:subject.name) had be signed by :causer.first_name :causer.last_name");
            }

            if ($request->has('is_signed_by_bank_id') && $request->is_signed_by_bank_id) {
                $user = Auth::user();

                if (!$user->is_bankid_verified) {
                    return response()->json([
                        'message' => __('prescription_strings.please_verify_bank_id'),
                        'status' => '0'
                    ]);
                }
                $client_treatment->is_signed_by_bank_id = 1;
                $client_treatment->signed_by_bank_id = $user->personal_id;
                $client_treatment->signed_by_id = Auth::id();
                $client_treatment->signed_at = now();
                $client_treatment->timestamps = false;

                $activity = activity()
                    ->performedOn($client_treatment);

                $activity = $activity->by(Auth::user());
                $activity->log("{$client_treatment->client->first_name} {$client_treatment->client->last_name} client 's procedure (:subject.title) had be signed with bank id by :causer.first_name :causer.last_name");
            }

            if ($request->has('is_cancelled') && $request->is_cancelled) {
                $client_treatment->is_cancelled = $request->is_cancelled;
                $client_treatment->cancel_note = $request->cancel_note;
                $client_treatment->cancelled_by_id = Auth::id();
                $client_treatment->cancelled_at = Carbon::now();


                $activity = activity()
                    ->performedOn($client_treatment);

                $activity = $activity->by(Auth::user());
                $activity->log("{$client_treatment->client->first_name} {$client_treatment->client->last_name} client 's procedure (:subject.name) had be cancelled by :causer.first_name :causer.last_name");
            }

            if ($client_treatment->isDirty()) {
                $client_treatment->user_id = Auth::id();
                $client_treatment->save();
            }
        });

        return response()->json([
            'message' => __('strings.Client_Treatment_updated_successfully'),
            'status' => '1',
        ]);
    }

    public function show($client, ClientTreatment $client_treatment)
    {
        $this->authorize('view', [ClientTreatment::class, $client_treatment]);

        return response()->json([
            'data' => $client_treatment->loadMissing('details', 'prescriptions'),
            'message' => 'procedure returned successfully',
            'status' => '1',
        ]);
    }

    public function updateMobile(ClientTreatmentUpdateMobileRequest $request, ClientTreatment $client_treatment)
    {
        $this->authorize('update', [ClientTreatment::class, $client_treatment]);
        $user = Auth::user();

        $default_unit = $user->company->unit;
        DB::transaction(function () use ($request, $client_treatment, $default_unit) {
            if ($request->has('name')) {
                $client_treatment->name = $request->input('name', '') ?? '';
            }
            if ($request->has('date')) {
                $client_treatment->date = $request->input('date', NULL) ?? NULL;
            }

            if ($request->has('notes')) {
                $client_treatment->notes = $request->input('notes', '') ?? '';
            }
            if ($request->has('notes_html')) {
                $client_treatment->notes_html = $request->input('notes_html', '') ?? '';
            }
            if ($request->has('unit')) {
                $client_treatment->unit = $default_unit ?? $request->input('unit', $default_unit ?? 'usd') ?? '';
            }
            if ($request->has('nrs_rating')) {
                $client_treatment->nrs_rating = $request->nrs_rating;
            }

            if ($request->has('images')) {
                $client_treatment_files = $client_treatment->files;
                $client_treatment_images = $client_treatment_files->map(function ($file) {
                    return $file->filename;
                })->toArray();

                $files = [];
                foreach ($request->file('images', []) as $index => $image) {
                    $file = $this->saveFile($image, 'clients/' . md5($client_treatment->client_id) . '/treatments');
                    $files[] = $file;

                    $treatment_file = $client_treatment_files->where('id', $request->input("files.$index", -1))->first();
                    if ($treatment_file) {
                        $key = array_search($treatment_file->filename, $client_treatment_images); // $key = 2;

                        $file->id = $treatment_file->id;
                        $file->created_at = $treatment_file->created_at;
                        $file->updated_at = $treatment_file->updated_at;

                        $treatment_file->delete();

                        $file->save();

                        if ($key != false) {
                            $client_treatment_images[$index] = $file->filename;
                        } else {
                            array_push($client_treatment_images, $file->filename);
                        }
                    } else {
                        array_push($client_treatment_images, $file->filename);
                    }
                }

                if (count($files)) {
                    $client_treatment->files()->saveMany($files);

                    $client_treatment_images = ClientTreatment::with(['files'])->find($client_treatment->id)->files->map(function ($file) {
                        return $file->filename;
                    });

                    $client_treatment->images = json_encode($client_treatment_images);
                }
            }

            if ($request->has('treatments') && count($request->input('treatments', []))) {
                $client_treatment->details()->delete();

                foreach ($request->input('treatments', []) as $index => $treatmentData) {
                    $treatment = Treatment::findOrFail($treatmentData['id']);
                    $clientTreatmentDetail = $client_treatment->details()->create([
                        'name' => $treatment->name,
                        'description' => $treatment->description,
                        'cost' => $treatment->cost,
                        'unit' => $default_unit ?? $treatment->unit,
                        'color' => $treatment->color,
                        'notes' => $treatment->notes,
                        'type' => $treatment->type,
                        'actual_cost' => isset($treatmentData['actual_cost']) ? $treatmentData['actual_cost'] : 0,
                        'actual_unit' => $default_unit ?? $treatment->unit,
                        'treatment_id' => $treatment->id,
                    ]);
                }
            } else {
                $client_treatment->details()->delete();
            }

            if ($request->has('prescriptions')) {
                $client_treatment->prescriptions()->sync($request->prescriptions);
            } else {
                $client_treatment->prescriptions()->detach();
            }

            if ($client_treatment->isDirty()) {
                $client_treatment->user_id = Auth::id();
                $client_treatment->save();
            }
        });

        return response()->json([
            'data' => $client_treatment,
            'message' => __('strings.Client_Treatment_updated_successfully'),
            'status' => '1',
        ]);
    }

    public function deleteImageMobile(ClientTreatmentDeleteMobileRequest $request, ClientTreatment $client_treatment)
    {
        $this->authorize('update', [ClientTreatment::class, $client_treatment]);

        if ($request->has('files')) {
            $client_treatment_files = $client_treatment->files;

            $files = [];
            foreach ($request->input('files', []) as $index => $image) {
                $treatment_file = $client_treatment_files
                    ->where('id', $request->input("files.$index", -1))
                    ->first();

                if ($treatment_file) {
                    $treatment_file->delete();
                }
            }

            $client_treatment_images = ClientTreatment::with('files')->find($client_treatment->id)->files->map(function ($file) {
                return $file->filename;
            });

            $client_treatment->images = json_encode($client_treatment_images);
        }

        if ($client_treatment->isDirty()) {
            $client_treatment->user_id = Auth::id();
            $client_treatment->save();
        }

        return response()->json([
            'message' => __('strings.Client_Treatment_images_deleted_successfully'),
            'status' => '1',
        ]);
    }

    public function delete(ClientTreatment $clientTreatment)
    {
        $this->authorize('update', [Client::class, $clientTreatment->client]);

        if ($clientTreatment->delete()) {
            return response()->json([
                'message' => __('strings.Client_Treatment_deleted_successfully'),
                'status' => '1',
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Client_Treatment_deletion_failed'),
                'status' => '0',
            ]);
        }
    }
}
