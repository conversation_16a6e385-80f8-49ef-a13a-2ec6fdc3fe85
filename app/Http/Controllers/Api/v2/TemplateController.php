<?php

namespace App\Http\Controllers\Api\v2;

use App\File;
use App\Template;
use App\Traits\SaveFile;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\TemplateRequest;
use App\Http\Requests\v2\TreatmentIndexRequest;
use App\Traits\ApiResponser;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class TemplateController extends Controller
{
    use SaveFile, ApiResponser;

    public function index(TreatmentIndexRequest $request)
    {
        $isPaginated = false;

        $user = Auth::user();

        $templates = $user->company->templates()->orderBy('created_at', 'asc');

        if (request()->has('withTrashed')) {
            $templates = $templates->withTrashed();
        }

        if (!request()->boolean('withDefaultTemplate', true)) {
            $templates = $templates->where('is_editable', true);
        }

        if ($request->has('filter')) {
            if ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed') {
                $templates = $templates->{$request->filter}();
            } else {
                $templates = $templates->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
            }
        }

        if ($request->has('orderBy')) {
            if ($request->orderBy == 'name') {
                if (!$isPaginated) {
                    $templates = $templates->get();
                }
                $templates = in_array($request->input('orderDirection', 'asc'), ['asc', 'ASC']) ? $templates->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE) : $templates->sortByDesc('name', SORT_NATURAL | SORT_FLAG_CASE);
                $isPaginated = true;
            } else {
                $templates = $templates->orderBy($request->orderBy, $request->input('orderDirection', 'asc'));
            }
        }

        if ($request->input('page')) {
            return response()->json(
                collect(
                    [
                        'message' => 'Template returned successfully.',
                        'status' => '1'
                    ]
                )->merge($isPaginated ? $this->paginate($templates) : $templates->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $templates : $templates->get(),
                'message' =>  'Template returned successfully.',
                'status' => '1'
            ]);
        }
    }

    public function store(TemplateRequest $request)
    {
        $this->authorize('create', [Template::class]);

        $input = $request->validated();

        $file = null;
        if ($request->hasFile('image')) {
            $file = $this->saveFile($request->file('image'), 'template');

            $image = $file->filename;
        } else {
            $image = "";
        }

        $input['image'] = $image;
        $input['company_id'] = Auth::user()->company->id;

        $template = Template::create($input);

        if ($file) {
            $template->file()->save($file);
        }

        return response()->json([
            'message' => __('strings.Template_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(TemplateRequest $request, Template $template)
    {
        if (!$template->is_editable) {
            throw (new ModelNotFoundException())->setModel(Template::class);
        }

        $this->authorize('update', [Template::class, $template]);

        if ($request->has('image')) {
            $file = $this->saveFile($request->file('image'), 'template');

            try {
                if ($template->file) {
                    $template->file->delete();
                }
            } catch (\Throwable $th) {
                //throw $th;
            }
            $template->file()->save($file);
            $template->image = $file->filename;
        }

        if ($request->has('name')) {
            $template->name = $request->input('name');
        }

        if ($template->isDirty()) {
            $template->save();
        }

        return response()->json([
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(Template $template)
    {
        $this->authorize('delete', [Template::class, $template]);

        if ($template->is_editable) {
            $template->forceDelete();
            return response()->json([
                'message' => __('strings.Template_deleted_successfully'),
                'status' => '1'
            ]);
        } else {
            $template->delete();
            return response()->json([
                'message' => __('strings.Template_inactivated_successfully'),
                'status' => '1'
            ]);
        }
    }

    public function restore(Template $template)
    {
        if ($template) {
            $this->authorize('restore', [Template::class, $template]);

            if ($template->restore()) {
                return response()->json([
                    'message' => __('strings.template_restored_successfully'),
                    'status' => '1'
                ]);
            } else {
                return response()->json([
                    'message' => __('strings.template_restoration_failed'),
                    'status' => '0'
                ]);
            }
        } else {
            return response()->json([
                'message' => __('strings.Does_not_exists_any_template'),
                'status' => '0'
            ]);
        }
    }
}
