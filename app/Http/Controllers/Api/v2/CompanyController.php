<?php

namespace App\Http\Controllers\Api\v2;

use App\Client;
use App\ClientTreatment;
use App\User;
use App\Setting;
use App\Company;
use App\CompanyPlatform;
use App\File;
use App\GeneralTemplate;
use Carbon\Carbon;
use App\Traits\SaveFile;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Psr\Http\Message\ServerRequestInterface;
use App\Http\Requests\v2\StoreCompanyRequest;
use Illuminate\Foundation\Auth\VerifiesEmails;
use App\Traits\ColorGenerator;
use App\Treatment;
use Illuminate\Http\Request;
use Laravel\Passport\Http\Controllers\AccessTokenController;
use App\Http\Requests\v2\RegisterSetUpRequest;
use App\Jobs\AllClientZipDownloadJob;
use App\Jobs\ClientZipDownloadJob;
use App\Mail\NewAccountMail;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\LazyCollection;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CompanyController extends Controller
{
    use SaveFile, VerifiesEmails, ApiResponser, ColorGenerator;
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreCompanyRequest $request, ServerRequestInterface $server_request, AccessTokenController $accessTokenController)
    {
        return DB::transaction(function () use ($request, $server_request, $accessTokenController) {
            $company = Company::create([
                'company_name' => $request->company_name ?? '',
                'first_name' => $request->first_name ?? '',
                'last_name' => $request->last_name ?? '',
                'language_id' => 1,
                'email' => $request->email,
                'mobile_number' => $request->has('mobile_number') ? $request->mobile_number ?? "" : "",
                'country_code' => $request->has('country_code') ? $request->country_code ?? "" : "",
                'street_address' => $request->input('street_address', '') ?? '',
                'zip_code' => $request->input('zip_code', '') ?? '',
                'city' => $request->input('city', '') ?? '',
                'state' => $request->input('state', '') ?? '',
                'country' => $request->country ?? '',
                'password' => bcrypt($request->password),
                'unit' => 'usd',
                'is_booking_on' => '0',
                'is_record_on' => '0',
            ]);


            // 'company_photo' => 'required|image|mimes:jpeg,jpg,png',

            dispatch(function () use ($company) {
                activity()
                    ->performedOn($company)
                    ->by($company)
                    ->log('New Company has been registered.');
            });

            $user = User::create([
                'title' => $request->company_name ?? '',
                'first_name' => $request->first_name ?? '',
                'last_name' => $request->last_name ?? '',
                'company_id' => $company->id,
                'user_role' => User::ADMIN,
                'email' => $request->email,
                'mobile_number' => $request->has('mobile_number') ? $request->mobile_number ?? "" : "",
                'country_code' => $request->has('country_code') ? $request->country_code ?? "" : "",
                'password' => bcrypt($request->password),
                'street_address' => $request->input('street_address', '') ?? '',
                'zip_code' => $request->input('zip_code', '') ?? '',
                'city' => $request->input('city', '') ?? '',
                'state' => $request->input('state', '') ?? '',
                'country' => $request->country ?? '',
                'is_booking_on' => '0',
                'is_record_on' => '0',
                'email_verified_at' => now(),
            ]);

            $user->last_login_at = now();
            $user->save();

            if ($request->has('company_photo')) {
                $company_file = $this->saveFile($request->company_photo, 'company_photo', $user, true);
                $company_photo = $company_file->filename;
                $company->file()->save($company_file);
                $company->profile_photo = $company_photo;
                $company->save();
            }

            if ($request->has('profile_photo')) {
                $user_file = $this->saveFile($request->profile_photo, 'user_photo', $user, true);
                $profile_photo = $user_file->filename;
                $user->file()->save($user_file);
                $user->profile_photo = $profile_photo;
                $user->save();
            }

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('New User has been created.');
            });

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('User has logged in to system.');
            });

            if ($request->missing('platform') || $request->platform == CompanyPlatform::RECORD_SYSTEM) {
                $client = Client::create([
                    'user_id' => $user->id,
                    'company_id' => $company->id,
                    'profile_picture' => '',
                    'first_name' => 'Test',
                    'last_name' => 'Patient',
                    'social_security_number' => now()->subMonth()->format('Y-m-d'),
                    'email' => '<EMAIL>',
                    'phone_number' => '**********',
                ]);

                GeneralTemplate::getGeneralTemplate($user, GeneralTemplate::PRESCRIPTION);
                GeneralTemplate::getGeneralTemplate($user, GeneralTemplate::SMS);
                GeneralTemplate::getGeneralTemplate($user, GeneralTemplate::EMAIL);

                $treatment = Treatment::where('company_id', $company->id)->first();
                if ($treatment) {
                    $client_treatment_images = [];
                    $clientTreatment = ClientTreatment::create([
                        'client_id' => $client->id,
                        'name' => $treatment->name,
                        'description' => $treatment->description,
                        'cost' => 0,
                        'color' => $this->generate(),
                        'date' => Carbon::now()->format('Y-m-d'),
                        'notes' => '',
                        'notes_html' => $treatment->notes,
                        'images' => json_encode($client_treatment_images),
                        'unit' => $company->unit ?? 'eur',
                        'treatment_cost' => 0,
                        'treatment_id' => null,
                        'user_id' => $user->id,
                    ]);
                    $clientTreatmentDetail = $clientTreatment->details()->create([
                        'name' => $treatment->name,
                        'description' => $treatment->description,
                        'cost' => 0,
                        'unit' => $company->unit ?? 'eur',
                        'color' => $this->generate(),
                        'notes' => $treatment->notes,
                        'type' => $treatment->type,
                        'actual_cost' => 0,
                        'actual_unit' => $company->unit ?? 'eur',
                        'treatment_id' => $treatment->id,
                    ]);
                }
            }

            CompanyPlatform::create([
                'platform' => $request->has('platform') ? $request->platform : CompanyPlatform::RECORD_SYSTEM,
                'company_id' => $company->id
            ]);

            event(new Registered($user));

            Auth::login($user);
            $request->session()->regenerate();

            return response()->json([
                // 'message' => __('strings.verify_email'),
                'message' => __('strings.Company_registered_successfully_next'),
                'status' => '1'
            ]);
        });
    }


    public function outsideInfo($id)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $company = Company::select(
            'id',
            'company_name',
            'street_address',
            'zip_code',
            'email',
            'mobile_number',
            'country_code',
            'city',
            'state',
            'country',
            'theme',
            'cover_image',
            'is_black_text',
            'profile_photo',
            'is_blocked',
            'unit',
            'is_read_only',
            'is_pos_on',
            'viva_merchant_id',
        )->findOrFail($id);

        $company->viva_merchant_id = $company->viva_merchant_id ? true : false;

        $company->setAppends([
            'encrypted_id',
        ])->makeHidden('pos_plan');

        return response()->json([
            'data' => $company,
            'message' => __('strings.company_information'),
            'status' => '1'
        ]);
    }

    public function storeSetUp(RegisterSetUpRequest $request)
    {
        $user = Auth::user()->loadMissing('company.settings');
        $company = $user->company;

        if ($request->has('first_name')) {
            $company->first_name = $request->first_name;
            $user->first_name = $request->first_name;
        }
        if ($request->has('last_name')) {
            $company->last_name = $request->last_name;
            $user->last_name = $request->last_name;
        }
        if ($request->has('company_name')) {
            $company->company_name = $request->company_name;
            $user->title = $request->company_name;
        }
        if ($request->has('mobile_number')) {
            $company->mobile_number = $request->mobile_number;
            $user->mobile_number = $request->mobile_number;
        }
        if ($request->has('country_code')) {
            $company->country_code = $request->country_code;
            $user->country_code = $request->country_code;
        }

        if ($request->has('country')) {
            $company->country = $request->country;
            $user->country = $request->country;
        }
        if ($request->has('theme')) {
            $company->theme = $request->theme;
        }
        if ($request->has('organization_number')) {
            $company->organization_number = $request->organization_number;
        }
        $is_booking_on = 1;
        $is_record_on = 0;
        if ($request->has('is_booking_on')) {
            $company->is_booking_on = $request->is_booking_on;
            $user->is_booking_on = $request->is_booking_on;
            $is_booking_on = $request->is_booking_on;
        }

        if ($request->has('is_record_on')) {
            $company->is_record_on = $request->is_record_on;
            $user->is_record_on = $request->is_record_on;
            $is_record_on = $request->is_record_on;
        }

        // if (!$is_record_on && $is_booking_on) {
        //     $company->createOrGetStripeCustomer();
        // }

        if ($request->has('number_of_employees')) {
            $company_setting_num_of_emp = $company->settings()->where('key', Setting::NUMBER_OF_EMPLOYEES)->first();
            if ($company_setting_num_of_emp) {
                $company_setting_num_of_emp->value = $request->number_of_employees;
                $company_setting_num_of_emp->save();
            } else {
                $company_setting_num_of_emp  = Setting::create([
                    'key' => Setting::NUMBER_OF_EMPLOYEES,
                    'value' => $request->number_of_employees,
                    'company_id' => $company->id,
                ]);
            }
        }
        CompanyPlatform::updateOrCreate([
            'platform' => CompanyPlatform::RECORD_SYSTEM,
            'company_id' => $company->id,
        ], [
            'license_agreement' => NULL,
        ]);
        $company->save();
        $user->save();

        // Mail::to(env('NEW_ACCOUNT_MAIL'))->send(new NewAccountMail([
        //     "name" => $user->fullName(),
        //     "company" => $company->company_name,
        //     "email" => $company->email,
        //     "country" => $company->country,
        // ]));

        //   $user = Auth::user()->loadMissing('company.settings');
        // $user->company->append('is_cancelled', 'is_subscribed', 'has_pending_payment');
        // if ($user->company->is_read_only) {
        //     return response()->json([
        //         'data' => $user,
        //         'meta' => [
        //             'message' => __('strings.account_read_only_message', ['message' => "<a href='mailto:<EMAIL>'><EMAIL></a>"]),
        //         ],
        //         'message' => 'User returned successfully.',
        //         'status' => '1'
        //     ]);
        // }

        return response()->json([
            // 'data' => $user,
            'message' => __('strings.register_set_up'),
            'status' => '1'
        ]);
    }

    public function downloadClientsZip(Company $company, Request $request)
    {
        return DB::transaction(function () use ($request, $company) {
            $user = Auth::user();

            $this->authorize('downloadClientsZip', $company);

            $cacheKey = 'all_client_zip_' . $company->id;
            $cache_exists = Cache::get($cacheKey);

            if ($cache_exists === 'working') {
                throw new HttpException(208, __('zip.all_clients.zip_generating'));
            }

            if ($cache_exists) {
                throw new HttpException(208, __('zip.all_clients.zip_in_queue'));
            }

            $force = !!$request->input('force');

            if (!$force) {
                $zip_already_exists = File::where('fileable_type', 'all_client_zip')->where('fileable_id', $company->id)->first();

                if ($zip_already_exists) {
                    return response()->json([
                        'status' => '0',
                        'data' => $zip_already_exists,
                        'message' => __('zip.all_clients.zip_already_present'),
                    ], 210);
                }
            }

            // UserNotification::create([
            //     'user_id' => Auth::user()->id,
            //     'title' => __('zip.all_clients.zip_generating'),
            //     'description' => __('zip.all_clients.zip_in_queue'),
            //     'is_read' => 0
            // ]);

            $user = $company->users()->first();

            $setting = Setting::getSetting($company, Setting::LANGUAGE);
            $lang = $setting?->value ?? app()->getLocale();

            $batch = Bus::batch([])
                ->finally(function () use ($company, $lang) {
                    $addedAllJobs = cache()->get("added_all_job_$company->id", false);

                    if (!$addedAllJobs) return;

                    AllClientZipDownloadJob::dispatch($company,  $lang);

                    cache()->forget("added_all_job_$company->id");
                })
                ->catch(function () use ($cacheKey, $company) {
                    cache()->forget("added_all_job_$company->id");
                    Cache::forget($cacheKey);
                })->onQueue('client_zip')
                ->dispatch();

            $company->clients()
                ->cursor()
                ->map(fn($client) => new ClientZipDownloadJob($client, $user, $lang, false, false, false))
                ->filter()
                ->chunk(300)
                ->each(function (LazyCollection $jobs) use ($batch) {
                    $batch->add($jobs); // 300 jobs are now added in one go
                });

            cache()->set("added_all_job_$company->id", true);
            Cache::put($cacheKey, 'queue', 86400);

            return response()->json([
                'status' => '1',
                'message' => __('zip.all_clients.zip_accepted'),
            ]);
        });
    }
}
