<?php

namespace App\Http\Controllers\Api\v2;

use App\Http\Controllers\Controller;
use App\ShortUrl;
use Illuminate\Http\Request;

class ShortUrlController extends Controller
{
    public function shorten(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
        ]);
        $originalUrl = $request->input('url');
        $shortUrl = ShortUrl::shorten($originalUrl);

        return response()->json([
            'data' => $shortUrl,
            'message' => 'URL generated',
            'status' => '1',
        ]);
    }

    public function redirect($key)
    {
        $shortedURL = ShortUrl::where('key', $key)->first();
        if (! $shortedURL) {
            return abort(404);
        }

        return redirect($shortedURL->original_url);
    }
}
