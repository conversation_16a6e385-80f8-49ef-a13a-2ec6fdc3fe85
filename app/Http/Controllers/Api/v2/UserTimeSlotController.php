<?php

namespace App\Http\Controllers\Api\v2;

use App\CompanyBooking;
use App\CompanyBusinessHour;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\ClearScheduleRequest;
use App\Http\Requests\v2\CreatePrivateTimeRequest;
use App\Http\Requests\v2\DeleteUserTimeSlotRequest;
use App\Http\Requests\v2\EditPrivateTimeRequest;
use App\Http\Requests\v2\EditUserTimeSlotRequest;
use App\Http\Requests\v2\GetUserTimeSlotListRequest;
use App\Http\Requests\v2\PasteSlotsRequest;
use App\Http\Requests\v2\TimeSlotToggleRequest;
use App\Http\Requests\v2\ToggleTimeSlotRequest;
use App\Http\Requests\v2\UpdateScheduleRequest;
use App\Http\Requests\v2\UpdateUserTimeSlotRequest;
use App\Mail\ScheduleUpdateMail;
use App\Setting;
use App\Traits\ApiResponser;
use App\Traits\TimeZoneManager;
use App\User;
use App\UserTimeSlot;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Traits\BookingManager;
use App\UserTimeSlotService;
use Illuminate\Support\Facades\Mail;
use Throwable;

class UserTimeSlotController extends Controller
{
    use ApiResponser;
    public function unavailableIndex(GetUserTimeSlotListRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        if ($request->has('user_id')) {
            $user_time_slots = UserTimeSlot::where('user_id', $request->user_id);
        }
        if ($request->has('user_ids')) {
            $user_time_slots = UserTimeSlot::whereIn('user_id', $request->user_ids);
        }
        if ($request->missing('user_id') && $request->missing('user_ids')) {
            $user_time_slots = UserTimeSlot::where('user_id', $user->id);
        }
        $user_time_slots = $user_time_slots->where('type', UserTimeSlot::NOT_AVAILABLE);
        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.user_time_slots_returned'),
                    'status' => '1',
                ])->merge($user_time_slots->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' =>  $user_time_slots->get(),
                'message' => __('strings.user_time_slots_returned'),
                'status' => '1',
            ]);
        }
    }

    public function unavailableUpdate(UpdateUserTimeSlotRequest $request)
    {
        $user = Auth::user();
        if ($request->has('user_id')) {
            $user = User::where('id', $request->user_id)->verifiedEmail()->firstOrFail();
        }

        $users_to_add_time_slots = User::query();

        if ($request->has('user_ids')) {
            $users_to_add_time_slots = $users_to_add_time_slots->whereIn('id', $request->user_ids);
        } else {
            $users_to_add_time_slots = $users_to_add_time_slots->whereIn('id', [$user->id]);
        }

        $users_to_add_time_slots = $users_to_add_time_slots->verifiedEmail();

        $company = $user->company;

        $start_at = null;
        $end_at = null;

        if ($request->is_for_all_day) {
            $start_at = Carbon::parse($request->start_at)->format('Y-m-d');
            $end_at = Carbon::parse($request->end_at)->endOfDay();
        } else {
            $start_at = Carbon::parse($request->start_at)->format('Y-m-d H:i:s');
            $end_at = Carbon::parse($request->end_at)->format('Y-m-d H:i:s');
        }

        foreach ($users_to_add_time_slots->get() as $user_to_add_time_slot) {
            UserTimeSlot::create([
                'user_id' => $user_to_add_time_slot->id,
                'note' => $request->note,
                'week_day' => $request->week_day ?? null,
                'type' => UserTimeSlot::NOT_AVAILABLE,
                'start_at' => $start_at,
                'end_at' => $end_at,
                'is_for_all_day' => $request->is_for_all_day,
            ]);
        }

        return response()->json([
            'message' => __('strings.user_time_slots_updated'),
            'status' => '1'
        ]);
    }

    public function clearSchedule(ClearScheduleRequest $request)
    {
        $company = Auth::user()->company;
        $users_to_apply_schedule = User::whereIn('id', $request->user_ids)->verifiedEmail();

        // ADDED START DATE AND END DATE
        $now = TimeZoneManager::getTimeZoneCarbonNow($company->timezone)->format('Y-m-d');
        $start_date = Carbon::parse($now)->format('Y-m-d');
        $maximum_lead_date = Carbon::parse($now)->addMinutes(Setting::getSetting($company, Setting::MAXIMUM_LEAD_TIME)->value);


        $end_date = null;
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date)->format('Y-m-d');

            if (Carbon::parse($request->start_date) < Carbon::parse($now)->format('Y-m-d')) {
                return response()->json([
                    'message' => __('strings.only_future_date_in_start_date'),
                    'status' => '0'
                ]);
            }

            if (Carbon::parse($request->start_date)->greaterThan($maximum_lead_date)) {
                return response()->json([
                    'message' => __('strings.start_date_can_not_greater_then_max_time'),
                    'status' => '0'
                ]);
            }

            $start_date = Carbon::parse($request->start_date);
        }

        if ($request->has('end_date')) {
            $end_date = Carbon::parse($request->end_date)->format('Y-m-d');

            if ($request->end_date < Carbon::parse($now)->subDay()) {
                return response()->json([
                    'message' => __('strings.only_future_date_in_end_date'),
                    'status' => '0'
                ]);
            }
            if (Carbon::parse($request->end_date)->greaterThan($maximum_lead_date)) {
                return response()->json([
                    'message' => __('strings.end_date_can_not_greater_then_max_time'),
                    'status' => '0'
                ]);
            }
            $end_date = Carbon::parse($request->end_date);
        }

        foreach ($users_to_apply_schedule->cursor() as $user_to_apply_schedule) {
            $clear_schedule = UserTimeSlot::where('user_id', $user_to_apply_schedule->id)
                ->where('type', UserTimeSlot::AVAILABLE)
                ->whereDate('start_at', '>=', $start_date);
            if ($end_date) {
                $clear_schedule = $clear_schedule->whereDate('end_at', '<=', $end_date);
            }
            $clear_schedule = $clear_schedule->delete();

            if ($request->has('should_clear_private_time') && $request->should_clear_private_time) {
                UserTimeSlot::where('user_id', $user_to_apply_schedule->id)
                    ->whereIn('type', [UserTimeSlot::PRIVATE_SLOT_BLOCKING, UserTimeSlot::PRIVATE_SLOT_NON_BLOCKING])
                    ->whereDate('start_at', '>=', Carbon::now()->format('Y-m-d'))
                    ->delete();
            }
            //TODO::send mail if client want
        }
        return response()->json([
            'message' => __('strings.user_time_slots_cleared'),
            'status' => '1'
        ]);
    }


    public function updateSchedule(UpdateScheduleRequest $request)
    {
        //GETTING THE COMPANY FROM USER
        $company = Auth::user()->company;
        //CURRENT TIME-DATE BASED ON COMPANY TIMEZONE
        $now = TimeZoneManager::getTimeZoneCarbonNow($company->timezone);

        //CHECKING IF REQUEST HAS PROPER DATA FOR DATE-TIME
        $needle_time = null;
        $should_send_mail = false;
        $days = $request->days;
        if ($request->has("working_hours")) {
            foreach ($request->working_hours as $working_hour) {
                if (!$needle_time) {
                    $needle_time = Carbon::parse($working_hour['start_time']);
                }
                if (Carbon::parse($working_hour['start_time']) >= Carbon::parse($working_hour['end_time'])) {
                    return response()->json([
                        'message' => __('strings.please_fill_proper_working_hours'),
                        'status' => '0'
                    ]);
                }
                if (!($needle_time <= Carbon::parse($working_hour['start_time']) && $needle_time <= Carbon::parse($working_hour['end_time']))) {
                    return response()->json([
                        'message' => __('strings.please_fill_proper_working_hours'),
                        'status' => '0'
                    ]);
                }
                $needle_time = Carbon::parse($working_hour['end_time']);
            }
        }


        //SETTING UP SOME VARIABLES FOR LOGIC
        $occurences = null;
        $repeat_week = 1;

        //DECIDING HOW MUCH WEEK DO WE NEED TO SKIP IN EACH ITERATION
        if ($request->has('repeat_week')) {
            $repeat_week = $request->repeat_week;
        }
        $maximum_lead_date = Carbon::parse($now)->addMinutes(Setting::getSetting($company, Setting::MAXIMUM_LEAD_TIME)->value);
        $schedule_start_date = Carbon::parse($now)->format('Y-m-d');

        //SETTING UP THE START DATE IF REQUEST HAS START DATE
        if ($request->has('start_date')) {
            if (Carbon::parse($request->start_date) < Carbon::parse($now)->format('Y-m-d')) {
                return response()->json([
                    'message' => __('strings.only_future_date_in_start_date'),
                    'status' => '0'
                ]);
            }
            if (Carbon::parse($request->start_date)->greaterThan($maximum_lead_date)) {
                return response()->json([
                    'message' => __('strings.start_date_can_not_greater_then_max_time'),
                    'status' => '0'
                ]);
            }
            $schedule_start_date = Carbon::parse($request->start_date);
        }

        //CHECKING IF THE END DATE IF GREATER THAN MAXIMUM LEAD TIME
        $ends_on_date = null;
        if ($request->has('end_on_date')) {
            if ($request->end_on_date < Carbon::parse($now)->subDay()) {
                return response()->json([
                    'message' => __('strings.only_future_date_in_end_date'),
                    'status' => '0'
                ]);
            }
            if (Carbon::parse($request->end_on_date)->greaterThan($maximum_lead_date)) {
                return response()->json([
                    'message' => __('strings.end_date_can_not_greater_then_max_time'),
                    'status' => '0'
                ]);
            }
            $ends_on_date = Carbon::parse($request->end_on_date);
        }
        //SETTING UP THE END DATE TO MAX LEAD TIME IF GIVEN IN REQUEST
        if ($request->has('end_at_max_lead_time') && $request->end_at_max_lead_time) {
            $ends_on_date = Carbon::parse($maximum_lead_date);
        }

        //SETTING UP THE END DATE BASED ON OCCURRENCES
        if ($request->has('end_after_occurences') && $request->end_after_occurences) {
            $occurences = $request->end_after_occurences;
            $ends_on_date =  Carbon::parse($schedule_start_date)->endOfWeek()->addDays(($occurences - 1) * $repeat_week * 7);
        }


        //FINALLY CHECKING AGAIN IF END DATE IS GREATER THEN MAX-LEAD TIME
        if ($ends_on_date->greaterThan($maximum_lead_date)) {
            return response()->json([
                'message' => __('strings.end_date_can_not_greater_then_max_time'),
                'status' => '0'
            ]);
        }

        //DECIDING THE WEEKS ON WHICH WE NEED TO APPLY THIS(BASED ON GIVEN VALUES ABOVE)
        $weeks_to_apply = [];
        $temp_date = Carbon::parse(Carbon::parse($schedule_start_date)->format('Y-m-d'));
        while ($temp_date->lessThanOrEqualTo($ends_on_date)) {
            array_push(
                $weeks_to_apply,
                [
                    'start_date' => Carbon::parse($temp_date)->startOfWeek(),
                    'end_date' => Carbon::parse($temp_date)->endOfWeek(),
                ]
            );
            $temp_date = $temp_date->addDays($repeat_week * 7);
        }


        //FINALLY APPLYING THE SCHEDULE TO WEEKS
        foreach ($weeks_to_apply as $week) {

            //GETTING THE START AND END DATE OF WEEK
            $start_date = Carbon::parse(Carbon::parse($week['start_date'])->format('Y-m-d'));
            $end_date = Carbon::parse(Carbon::parse($week['end_date'])->format('Y-m-d'));

            //CHECKING FOR EACH DAYS WEEK
            while ($start_date->lessThanOrEqualTo($end_date)) {

                if ($start_date < $schedule_start_date && $start_date < $ends_on_date) {
                    $start_date->addDay();
                    continue;
                }

                if ($start_date > $schedule_start_date && $start_date > $ends_on_date) {
                    $start_date->addDay();
                    continue;
                }

                //FIRST CHECKING IF THAT DAY COMES IN THE DAYS FROM REQUEST OR NOT
                if (in_array(BookingManager::getWeekDay($start_date), $days)) {

                    //GETTING ALL THE USERS WHO WE NEED TO APPLY SCHEDULE ON
                    $users_to_apply_schedule = User::whereIn('id', $request->user_ids)->verifiedEmail();

                    //GOING THROUGH ALL THE USERS TO APPLY THE SCHEDULE
                    foreach ($users_to_apply_schedule->cursor() as $user_to_apply_schedule) {
                        //DELETING THE OLD SCHEDULE FOR THAT USER
                        $user_existing_slots =  UserTimeSlot::where('user_id', $user_to_apply_schedule->id)
                            ->where('type', UserTimeSlot::AVAILABLE)
                            ->whereDate('start_at', '>=', Carbon::parse($start_date)->format('Y-m-d'))
                            ->whereDate('end_at', '<=', Carbon::parse($start_date)->format('Y-m-d'))
                            ->orderBy('start_at', 'asc')
                            ->cursor();
                        foreach ($user_existing_slots as $user_existing_slot) {
                            foreach ($request->working_hours as $working_hour) {

                                if (Carbon::parse($now)->format('Y-m-d') == Carbon::parse($start_date)->format('Y-m-d')) {
                                    $currentHourMinute = $now->format('H:i');
                                    if (
                                        Carbon::parse($working_hour['start_time']) < Carbon::parse($currentHourMinute)
                                        &&
                                        Carbon::parse($working_hour['end_time']) < Carbon::parse($currentHourMinute)
                                    ) {
                                        continue;
                                    }
                                }

                                $start_at = Carbon::parse(Carbon::parse($start_date)->format('Y-m-d') . ' ' . $working_hour['start_time'])->format('Y-m-d H:i:s');
                                $end_at = Carbon::parse(Carbon::parse($start_date)->format('Y-m-d') . ' ' . $working_hour['end_time'])->format('Y-m-d H:i:s');
                                if (
                                    $user_existing_slot->start_at < $start_at && $user_existing_slot->end_at >= $start_at
                                    &&
                                    $user_existing_slot->start_at < $end_at && $user_existing_slot->end_at < $end_at
                                ) {
                                    $user_existing_slot->end_at = $start_at;
                                    $user_existing_slot->save();
                                    continue;
                                }
                                if (
                                    $user_existing_slot->start_at >= $start_at && $user_existing_slot->end_at > $start_at
                                    &&
                                    $user_existing_slot->start_at <= $end_at && $user_existing_slot->end_at > $end_at
                                ) {
                                    $user_existing_slot->start_at = $end_at;
                                    $user_existing_slot->save();
                                    continue;
                                }
                                if (
                                    $user_existing_slot->start_at >= $start_at && $user_existing_slot->end_at > $start_at
                                    &&
                                    $user_existing_slot->start_at < $end_at && $user_existing_slot->end_at <= $end_at
                                ) {
                                    $user_existing_slot->delete();
                                    continue;
                                }
                                if (
                                    $user_existing_slot->start_at < $start_at && $user_existing_slot->end_at > $start_at
                                    &&
                                    $user_existing_slot->start_at < $end_at && $user_existing_slot->end_at > $end_at
                                ) {
                                    $first_new_user_time_slot = UserTimeSlot::create([
                                        'user_id' => $user_existing_slot->user_id,
                                        'note' => $user_existing_slot->note,
                                        'week_day' => $user_existing_slot->week_day,
                                        'type' => $user_existing_slot->type,
                                        'start_at' => $user_existing_slot->start_at,
                                        'end_at' => $start_at,
                                        'is_for_all_day' => 0,
                                        'description' => $user_existing_slot->description,
                                        'color' => $user_existing_slot->color,
                                        'is_for_all_services' => $user_existing_slot->is_for_all_services
                                    ]);
                                    if ($user_existing_slot->services()->count() > 0) {
                                        foreach ($user_existing_slot->services as $service) {
                                            UserTimeSlotService::create([
                                                'company_service_id' => $service->id,
                                                'user_time_slot_id' => $first_new_user_time_slot->id
                                            ]);
                                        }
                                    }
                                    $last_new_user_time_slot = UserTimeSlot::create([
                                        'user_id' => $user_existing_slot->user_id,
                                        'note' => $user_existing_slot->note,
                                        'week_day' => $user_existing_slot->week_day,
                                        'type' => $user_existing_slot->type,
                                        'start_at' => $end_at,
                                        'end_at' => $user_existing_slot->end_at,
                                        'is_for_all_day' => 0,
                                        'description' => $user_existing_slot->description,
                                        'color' => $user_existing_slot->color,
                                        'is_for_all_services' => $user_existing_slot->is_for_all_services
                                    ]);
                                    $should_send_mail = true;
                                    if ($user_existing_slot->services()->count() > 0) {
                                        foreach ($user_existing_slot->services as $service) {
                                            UserTimeSlotService::create([
                                                'company_service_id' => $service->id,
                                                'user_time_slot_id' => $last_new_user_time_slot->id
                                            ]);
                                        }
                                    }
                                    $user_existing_slot->delete();
                                    continue;
                                }
                            }
                        }

                        //ADDING NEW SCHEDULE FOR THAT USER
                        foreach ($request->working_hours as $working_hour) {

                            //CHECK FOR VALID WORKING HOUR
                            if (Carbon::parse($now)->format('Y-m-d') == Carbon::parse($start_date)->format('Y-m-d')) {
                                $currentHourMinute = $now->format('H:i');
                                if (
                                    Carbon::parse($working_hour['start_time']) < Carbon::parse($currentHourMinute)
                                    &&
                                    Carbon::parse($working_hour['end_time']) < Carbon::parse($currentHourMinute)
                                ) {
                                    continue;
                                }
                            }

                            //CHECKING IS THIS IS FOR ALL SERVICES OR JUST SOME OF THEM
                            if ($request->has('service_ids')) {
                                //ADDING SLOT FOR ONLY PARTICULAR SERVICES
                                $user_time_slot = UserTimeSlot::create([
                                    'user_id' => $user_to_apply_schedule->id,
                                    'note' => UserTimeSlot::AVAILABLE,
                                    'week_day' => BookingManager::getWeekDay($start_date),
                                    'type' => UserTimeSlot::AVAILABLE,
                                    'start_at' => Carbon::parse(Carbon::parse($start_date)->format('Y-m-d') . ' ' . Carbon::parse($working_hour['start_time'])->format('H:i:s')),
                                    'end_at' => Carbon::parse(Carbon::parse($start_date)->format('Y-m-d') . ' ' . Carbon::parse($working_hour['end_time'])->format('H:i:s')),
                                    'is_for_all_services' => 0,
                                ]);
                                $should_send_mail = true;

                                //GOING THROUGH ALL THE SERVICES IDS AND CONNECTING IT WITH USER TIME SLOT
                                foreach ($request->service_ids as $service_id) {
                                    UserTimeSlotService::create([
                                        'company_service_id' => $service_id,
                                        'user_time_slot_id' => $user_time_slot->id
                                    ]);
                                }
                            } else {
                                //ADDING SLOT FOR ALL SERVICES
                                UserTimeSlot::create([
                                    'user_id' => $user_to_apply_schedule->id,
                                    'note' => UserTimeSlot::AVAILABLE,
                                    'week_day' => BookingManager::getWeekDay($start_date),
                                    'type' => UserTimeSlot::AVAILABLE,
                                    'start_at' => Carbon::parse(Carbon::parse($start_date)->format('Y-m-d') . ' ' . Carbon::parse($working_hour['start_time'])->format('H:i:s')),
                                    'end_at' => Carbon::parse(Carbon::parse($start_date)->format('Y-m-d') . ' ' . Carbon::parse($working_hour['end_time'])->format('H:i:s')),
                                    'is_for_all_services' => 1,
                                ]);
                                $should_send_mail = true;
                            }
                        }
                    }
                }
                $start_date->addDay();
            }
        }
        $users_to_apply_schedule = User::whereIn('id', $request->user_ids)->verifiedEmail()->get();
        foreach ($users_to_apply_schedule as $user_to_apply_schedule) {
            if (!$should_send_mail) continue;
            try {
                $language = Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)?->value;
                Mail::to($user_to_apply_schedule->email)->locale($language ?? app()->getLocale())->send(
                    new ScheduleUpdateMail(
                        $company,
                        $schedule_start_date,
                        $ends_on_date,
                        $repeat_week,
                        $days,
                        $request->working_hours
                    )
                );
            } catch (\Throwable $th) {
                // dd($th->getMessage());
            }
        }

        return response()->json([
            'message' => __('strings.user_time_slots_updated'),
            'status' => '1'
        ]);
    }

    public function toggleTime(ToggleTimeSlotRequest $request)
    {
        $user = User::findOrFail($request->user_id);
        $start_at = Carbon::parse($request->start_at);
        $end_at = Carbon::parse($request->end_at);

        if (
            UserTimeSlot::where('user_id', $user->id)->where(function ($query) use ($start_at, $end_at) {
                $query = $query->where(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '<=', $start_at)->where('end_at', '>=', $end_at);
                });
            })
            // ->where('type', UserTimeSlot::AVAILABLE)
            ->exists()
        ) {
            $user_time_slots = UserTimeSlot::where('user_id', $user->id)->where(function ($query) use ($start_at, $end_at) {
                $query = $query->where(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                })->orWhere(function ($q) use ($start_at, $end_at) {
                    $q = $q->where('start_at', '<=', $start_at)->where('end_at', '>=', $end_at);
                });
            })
                ->where('type', UserTimeSlot::AVAILABLE)
                ->orderBy('start_at', 'asc')
                ->cursor();


            $temp_date = Carbon::parse($start_at);
            foreach ($user_time_slots as $user_time_slot) {
                if (
                    Carbon::parse($user_time_slot->start_at)->greaterThanOrEqualTo($start_at)
                    &&
                    Carbon::parse($user_time_slot->end_at)->lessThanOrEqualTo($end_at)
                ) {
                    $user_time_slot->delete();
                    continue;
                }

                if (
                    Carbon::parse($user_time_slot->start_at)->lessThan($start_at)
                    &&
                    Carbon::parse($user_time_slot->start_at)->lessThan($end_at)
                    &&
                    Carbon::parse($user_time_slot->end_at)->greaterThan($start_at)
                    &&
                    Carbon::parse($user_time_slot->end_at)->greaterThan($end_at)
                ) {

                    $user_time_slot_1 = UserTimeSlot::create([
                        'user_id' => $user->id,
                        'note' => UserTimeSlot::AVAILABLE,
                        'week_day' => BookingManager::getWeekDay($start_at),
                        'type' => UserTimeSlot::AVAILABLE,
                        'start_at' => $user_time_slot->start_at,
                        'end_at' => $start_at,
                        'is_for_all_day' => 0,
                        'is_for_all_services' => $user_time_slot->is_for_all_services,
                    ]);
                    foreach ($user_time_slot->services as $service) {
                        UserTimeSlotService::create([
                            'company_service_id' => $service->id,
                            'user_time_slot_id' => $user_time_slot_1->id
                        ]);
                    }
                    // if ($request->has('service_ids')) {
                    //     foreach ($request->service_ids as $service) {
                    //         UserTimeSlotService::create([
                    //             'company_service_id' => $service,
                    //             'user_time_slot_id' => $user_time_slot_1->id
                    //         ]);
                    //     }
                    // }
                    $user_time_slot_2 = UserTimeSlot::create([
                        'user_id' => $user->id,
                        'note' => UserTimeSlot::AVAILABLE,
                        'week_day' => BookingManager::getWeekDay($end_at),
                        'type' => UserTimeSlot::AVAILABLE,
                        'start_at' => $end_at,
                        'end_at' => $user_time_slot->end_at,
                        'is_for_all_day' => 0,
                        'is_for_all_services' => $user_time_slot->is_for_all_services,
                    ]);
                    foreach ($user_time_slot->services as $service) {
                        UserTimeSlotService::create([
                            'company_service_id' => $service->id,
                            'user_time_slot_id' => $user_time_slot_2->id
                        ]);
                    }
                    // if ($request->has('service_ids')) {
                    //     foreach ($request->service_ids as $service) {
                    //         UserTimeSlotService::create([
                    //             'company_service_id' => $service,
                    //             'user_time_slot_id' => $user_time_slot_2->id
                    //         ]);
                    //     }
                    // }
                    $user_time_slot->delete();
                    continue;
                }

                if (
                    Carbon::parse($user_time_slot->start_at)->greaterThanOrEqualTo($start_at)
                    &&
                    Carbon::parse($user_time_slot->start_at)->lessThan($end_at)
                    &&
                    Carbon::parse($user_time_slot->end_at)->greaterThan($start_at)
                    &&
                    Carbon::parse($user_time_slot->end_at)->greaterThan($end_at)
                ) {
                    $user_time_slot->start_at = $end_at;
                    $user_time_slot->save();
                }

                if (
                    Carbon::parse($user_time_slot->end_at)->greaterThan($start_at)
                    &&
                    Carbon::parse($user_time_slot->end_at)->lessThanOrEqualTo($end_at)
                    &&
                    Carbon::parse($user_time_slot->start_at)->lessThan($start_at)
                    &&
                    Carbon::parse($user_time_slot->start_at)->lessThan($end_at)
                ) {
                    $user_time_slot->end_at = $start_at;
                    $user_time_slot->save();
                }
            }
        } else {
            $user_time_slot = UserTimeSlot::create([
                'user_id' => $user->id,
                'note' => UserTimeSlot::AVAILABLE,
                'week_day' => BookingManager::getWeekDay($start_at),
                'type' => UserTimeSlot::AVAILABLE,
                'start_at' => $start_at,
                'end_at' => $end_at,
                'is_for_all_day' => 0,
                'is_for_all_services' => $request->has('service_ids') ? 0 : 1,
            ]);
            if ($request->has('service_ids')) {
                foreach ($request->service_ids as $service) {
                    UserTimeSlotService::create([
                        'company_service_id' => $service,
                        'user_time_slot_id' => $user_time_slot->id
                    ]);
                }
            }
        }
        return response()->json([
            'message' => __('strings.user_time_slots_updated'),
            'status' => '1'
        ]);
    }

    public function pasteSlots(PasteSlotsRequest $request)
    {
        $user = User::findOrFail($request->user_id);
        $start_at = Carbon::parse($request->start_at);
        $end_at = Carbon::parse($request->end_at);

        if ($request->type) {
            UserTimeSlot::where('user_id', $user->id)
                ->where('type', UserTimeSlot::AVAILABLE)
                ->where(function ($query) use ($start_at, $end_at) {
                    $query = $query->where(function ($q) use ($start_at, $end_at) {
                        $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                    })->orWhere(function ($q) use ($start_at, $end_at) {
                        $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                    })->orWhere(function ($q) use ($start_at, $end_at) {
                        $q = $q->where('start_at', '<=', $start_at)->where('end_at', '>=', $end_at);
                    });
                })
                ->delete();
        }

        foreach ($request->slots as $slot) {
            $new_slot  =  UserTimeSlot::create([
                'user_id' => $user->id,
                'note' => $slot['note'],
                'type' => UserTimeSlot::AVAILABLE,
                'start_at' => $slot['start_at'],
                'end_at' => $slot['end_at'],
                'week_day' => $slot['week_day'],
                'is_for_all_services' => $slot['is_for_all_services'],
                'is_for_all_day' => $slot['is_for_all_day'],
                'description' => $slot['description'],
            ]);

            if (count($slot['services'])) {
                foreach ($slot['services'] as $service) {
                    UserTimeSlotService::create([
                        'company_service_id' => $service['id'],
                        'user_time_slot_id' => $new_slot->id,
                    ]);
                }
            }
        }

        return response()->json([
            'message' => 'Time slots',
            'status' => '1'
        ]);
    }

    public function unavailableEdit(UserTimeSlot $user_time_slot, EditUserTimeSlotRequest $request)
    {
        $user = Auth::user();
        if ($request->has('is_for_all_day')) {
            $user_time_slot->is_for_all_day = $request->is_for_all_day;
        }
        if ($request->has('start_at')) {
            if ($user_time_slot->is_for_all_day) {
                $start_at = Carbon::parse($request->start_at)->format('Y-m-d');
            } else {
                $start_at = Carbon::parse($request->start_at)->format('Y-m-d H:i:s');
            }
            $user_time_slot->start_at = $start_at;
        }
        if ($request->has('end_at')) {
            if ($user_time_slot->is_for_all_day) {
                $end_at = Carbon::parse($request->end_at)->endOfDay();
            } else {
                $end_at = Carbon::parse($request->end_at)->format('Y-m-d H:i:s');
            }
            $user_time_slot->end_at = $end_at;
        }
        if ($request->has('week_day')) {
            $user_time_slot->week_day = $request->week_day;
        }
        if ($request->has('note')) {
            $user_time_slot->note = $request->note;
        }
        $user_time_slot->save();
        return response()->json([
            'message' => __('strings.user_time_slots_updated'),
            'status' => '1'
        ]);
    }

    public function unavailableDelete(UserTimeSlot $user_time_slot, DeleteUserTimeSlotRequest $request)
    {
        $user_time_slot->delete();
        return response()->json([
            'message' => __('strings.user_time_slots_delete'),
            'status' => '1'
        ]);
    }


    public function userTimeIndex(GetUserTimeSlotListRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        if ($request->has('user_id')) {
            $user_time_slots = UserTimeSlot::with(['user' => fn($q) => $q->setEagerLoads([]), 'services.category'])->where('user_id', $request->user_id);
        }
        if ($request->has('user_ids')) {
            $user_time_slots = UserTimeSlot::with(['user' => fn($q) => $q->setEagerLoads([]), 'services.category'])->whereIn('user_id', $request->user_ids);
        }
        if ($request->missing('user_id') && $request->missing('user_ids')) {
            $user_time_slots = UserTimeSlot::with(['user' => fn($q) => $q->setEagerLoads([]), 'services.category'])->where('user_id', $user->id);
        }
        if ($request->has('type')) {
            $user_time_slots = $user_time_slots->where('type', $request->type);
        }
        if ($request->has('types')) {
            $user_time_slots = $user_time_slots->whereIn('type', $request->types);
        }
        if ($request->has('start_at')) {
            $user_time_slots = $user_time_slots->whereDate('start_at', '>=', Carbon::parse($request->start_at)->format('Y-m-d H:i:s'));
        }
        if ($request->has('end_at')) {
            $user_time_slots = $user_time_slots->whereDate('start_at', '<=', Carbon::parse($request->end_at)->format('Y-m-d H:i:s'));
        }
        if ($request->has('is_for_all_day')) {
            $user_time_slots = $user_time_slots->where('is_for_all_day', $request->is_for_all_day);
        }

        $isPaginated = false;
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if ($orderBy == 'note' || $orderBy == 'practitioner_name') {
                if (!$isPaginated) {
                    $user_time_slots = $user_time_slots->get();
                    $isPaginated = true;
                }

                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
                // $sortExtra = SORT_STRING;
                // $sortExtra = SORT_FLAG_CASE;
                if ($orderBy == 'note') {
                    $user_time_slots =  $user_time_slots->sortBy($orderBy, $sortExtra, $isDescOrder);
                } else {
                    $user_time_slots =  $user_time_slots->sortBy('user.first_name', $sortExtra, $isDescOrder);
                    $user_time_slots = collect($user_time_slots)->flatten();
                }
                // $clients =  $clients->sort(function ($a, $b) use ($orderBy) {
                //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
                // });
            } else {
                $user_time_slots = $user_time_slots->orderBy($orderBy, $orderDirection);
            }
        }

        if (
            ($request->missing('orderBy') || $request->orderBy == "start_at")
            &&
            $request->has('show_upcoming_first') && $request->show_upcoming_first
        ) {
            if (!$isPaginated) {
                $user_time_slots = $user_time_slots->get();
                $isPaginated = true;
            }

            $old_user_time_slots = $user_time_slots->where('start_at', '<', Carbon::now()->format('Y-m-d'))->sortByDesc('start_at')->pluck('id')->toArray();
            $upcoming_user_time_slots = $user_time_slots->where('start_at', '>=', Carbon::now()->format('Y-m-d'))->sortBy('start_at')->pluck('id')->toArray();

            $total_array = array_merge($upcoming_user_time_slots, $old_user_time_slots);
            if (count($total_array) > 0) {
                $ids_ordered = implode(',', $total_array);

                $user_time_slots = UserTimeSlot::whereIn('id', $total_array)
                    ->with(['user'])
                    ->orderByRaw("FIELD(id, $ids_ordered)")
                    ->get();
            }
        }


        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.user_time_slots_returned'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($user_time_slots) : $user_time_slots->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $user_time_slots : $user_time_slots->get(),
                'message' => __('strings.user_time_slots_returned'),
                'status' => '1',
            ]);
        }
    }

    public function toggleSlots(TimeSlotToggleRequest $request)
    {
        $user  = User::findOrFail($request->user_id);
        $company = $user->company;

        $action = $request->action;

        //getting the week day for given date
        $week_day = BookingManager::getWeekDay(Carbon::parse($request->date));

        $START_TIME = Setting::getSetting($user->company, 'CALENDAR_CUSTOM_START_TIME')->value;
        $END_TIME = Setting::getSetting($user->company, 'CALENDAR_CUSTOM_END_TIME')->value;

        $start_at = Carbon::parse($request->date)->startOfDay();
        $end_at = Carbon::parse($request->date)->endOfDay();

        if ($START_TIME || $END_TIME) {
            // Set hour and minute from $START_TIME and $END_TIME
            $start_at = Carbon::parse($request->date)->setHour((int)explode(':', $START_TIME)[0])->setMinute((int)explode(':', $START_TIME)[1]);
            $end_at = Carbon::parse($request->date)->setHour((int)explode(':', $END_TIME)[0])->setMinute((int)explode(':', $END_TIME)[1]);
        }

        switch ($action) {
            case UserTimeSlot::OPEN:
                //clearing all times of user for this date
                $user_unavailable_time_slot_for_date = UserTimeSlot::where('user_id', $user->id)
                    ->where('type', UserTimeSlot::AVAILABLE)
                    ->where(function ($query) use ($start_at, $end_at) {
                        $query = $query->where(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                        })->orWhere(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                        })->orWhere(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('start_at', '<=', $start_at)->where('end_at', '>=', $end_at);
                        });
                    })
                    ->delete();

                UserTimeSlot::create([
                    'user_id' => $user->id,
                    'note' => null,
                    'week_day' => $week_day,
                    'type' => UserTimeSlot::AVAILABLE,
                    'start_at' => $start_at,
                    'end_at' => $end_at,
                    'is_for_all_day' => 1,
                ]);

                break;
            case UserTimeSlot::CLOSE:

                //clearing all times of user for this date
                $user_unavailable_time_slot_for_date = UserTimeSlot::where('user_id', $user->id)
                    ->where('type', UserTimeSlot::AVAILABLE)
                    ->where(function ($query) use ($start_at, $end_at) {
                        $query = $query->where(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                        })->orWhere(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                        })->orWhere(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('start_at', '<=', $start_at)->where('end_at', '>=', $end_at);
                        });
                    })
                    ->delete();

                //adding the unavailable time slot for whole day for given date
                // UserTimeSlot::create([
                //     'user_id' => $user->id,
                //     'note' => null,
                //     'week_day' => $week_day,
                //     'type' => UserTimeSlot::NOT_AVAILABLE,
                //     'start_at' => $start_at,
                //     'end_at' => $end_at,
                //     'is_for_all_day' => 1,
                // ]);
                break;

            default:
                # code...
                break;
        }

        return response()->json([
            'message' => __('strings.slots_updated'),
            'status' => '1',
        ]);
    }

    public function userTimeUpdate(UpdateUserTimeSlotRequest $request)
    {
        $user = Auth::user();
        if ($request->has('user_id')) {
            $user = User::findOrFail($request->user_id);
        }

        $users_to_add_time_slots = User::query();

        if ($request->has('user_ids')) {
            $users_to_add_time_slots = $users_to_add_time_slots->whereIn('id', $request->user_ids);
        } else {
            $users_to_add_time_slots = $users_to_add_time_slots->whereIn('id', [$user->id]);
        }

        $users_to_add_time_slots = $users_to_add_time_slots->verifiedEmail();

        $company = $user->company;

        $start_at = null;
        $end_at = null;

        if ($request->is_for_all_day) {
            $start_at = Carbon::parse($request->start_at)->format('Y-m-d');
            $end_at = Carbon::parse($request->end_at)->endOfDay();
        } else {
            $start_at = Carbon::parse($request->start_at)->format('Y-m-d H:i:s');
            $end_at = Carbon::parse($request->end_at)->format('Y-m-d H:i:s');
        }

        foreach ($users_to_add_time_slots->get() as $user_to_add_time_slot) {
            UserTimeSlot::create([
                'user_id' => $user_to_add_time_slot->id,
                'note' => $request->note,
                'week_day' => $request->week_day ?? null,
                'type' => $request->type,
                'start_at' => $start_at,
                'end_at' => $end_at,
                'is_for_all_day' => $request->is_for_all_day,
            ]);
        }

        return response()->json([
            'message' => __('strings.user_time_slots_updated'),
            'status' => '1'
        ]);
    }

    public function userTimeEdit(UserTimeSlot $user_time_slot, EditUserTimeSlotRequest $request)
    {
        $user = Auth::user();
        if ($request->has('is_for_all_day')) {
            $user_time_slot->is_for_all_day = $request->is_for_all_day;
        }
        if ($request->has('start_at')) {
            if ($user_time_slot->is_for_all_day) {
                $start_at = Carbon::parse($request->start_at)->format('Y-m-d');
            } else {
                $start_at = Carbon::parse($request->start_at)->format('Y-m-d H:i:s');
            }
            $user_time_slot->start_at = $start_at;
        }
        if ($request->has('end_at')) {
            if ($user_time_slot->is_for_all_day) {
                $end_at = Carbon::parse($request->end_at)->endOfDay();
            } else {
                $end_at = Carbon::parse($request->end_at)->format('Y-m-d H:i:s');
            }
            $user_time_slot->end_at = $end_at;
        }
        if ($request->has('week_day')) {
            $user_time_slot->week_day = $request->week_day;
        }
        if ($request->has('note')) {
            $user_time_slot->note = $request->note;
        }
        if ($request->has('type')) {
            $user_time_slot->type = $request->type;
        }
        $user_time_slot->save();
        return response()->json([
            'message' => __('strings.user_time_slots_updated'),
            'status' => '1'
        ]);
    }


    public function addPrivateSlot(CreatePrivateTimeRequest $request)
    {
        $user = Auth::user();
        $company = null;
        if ($user->company) {
            $company = $user->company;
        }
        $start_at = null;
        $end_at = null;

        if ($request->has('is_for_all_day') && $request->is_for_all_day) {
            $start_at = Carbon::parse($request->date)->format('Y-m-d');
            $end_at = Carbon::parse($request->date)->endOfDay();
        } else {
            if ($request->has('start_time')) {
                $start_at = Carbon::parse($request->date . ' ' . $request->start_time)->format('Y-m-d H:i:s');
            }
            if ($request->has('end_time')) {
                $end_at = Carbon::parse($request->date . ' ' . $request->end_time)->format('Y-m-d H:i:s');
            }
        }

        if ($request->has('color')) {
            $setting = Setting::getSetting($company, Setting::PRIVATE_TIME_COLOR);
            $setting->value = $request->color;
            $setting->save();
        }

        foreach ($request->user_ids as $user_id) {
            if ($request->has('type')) {
                switch ($request->type) {
                    case UserTimeSlot::PRIVATE_SLOT_BLOCKING:
                        try {
                            CompanyBooking::checkForBooking(
                                User::findOrFail($user_id),
                                Carbon::parse($start_at),
                                Carbon::parse($end_at)
                            );

                            CompanyBooking::checkForPrivateSlot(
                                User::findOrFail($user_id),
                                Carbon::parse($start_at),
                                Carbon::parse($end_at)
                            );
                        } catch (Throwable $th) {
                            return response()->json([
                                'message' => $th->getMessage(),
                                'status' => '0',
                            ]);
                        }

                        UserTimeSlot::create([
                            'user_id' => $user_id,
                            'note' => $request->note,
                            'week_day' => BookingManager::getWeekDay(Carbon::parse($start_at)),
                            'type' => UserTimeSlot::PRIVATE_SLOT_BLOCKING,
                            'start_at' => $start_at,
                            'end_at' => $end_at,
                            'is_for_all_day' => $request->is_for_all_day ?? 0,
                            'description' => $request->description,
                            'color' => $request->color,
                        ]);
                        break;
                    case UserTimeSlot::PRIVATE_SLOT_NON_BLOCKING:
                        UserTimeSlot::create([
                            'user_id' => $user_id,
                            'note' => $request->note,
                            'week_day' => BookingManager::getWeekDay(Carbon::parse($start_at)),
                            'type' => UserTimeSlot::PRIVATE_SLOT_NON_BLOCKING,
                            'start_at' => $start_at,
                            'end_at' => $end_at,
                            'is_for_all_day' => $request->is_for_all_day ?? 0,
                            'description' => $request->description,
                            'color' => $request->color,
                        ]);
                        break;

                    default:
                }
            }
        }
        return response()->json([
            'message' => __('strings.private_time_created'),
            'status' => '1'
        ]);
    }

    public function editPrivateSlot(UserTimeSlot $user_time_slot, EditPrivateTimeRequest $request)
    {
        $start_at = $user_time_slot->start_at;
        $end_at = $user_time_slot->end_at;

        if ($request->has('is_for_all_day') && $request->is_for_all_day) {
            $start_at = Carbon::parse($request->date)->format('Y-m-d');
            $end_at = Carbon::parse($request->date)->endOfDay();
        } else {
            if ($request->has('start_time')) {
                $start_at = Carbon::parse($request->date . ' ' . $request->start_time)->format('Y-m-d H:i:s');
            }
            if ($request->has('end_time')) {
                $end_at = Carbon::parse($request->date . ' ' . $request->end_time)->format('Y-m-d H:i:s');
            }
        }

        if (
            $user_time_slot->start_at != $start_at
            ||
            $user_time_slot->end_at != $end_at
        ) {
            if ($user_time_slot->type == UserTimeSlot::PRIVATE_SLOT_BLOCKING) {
                try {
                    CompanyBooking::checkForBooking(
                        User::findOrFail($user_time_slot->user_id),
                        Carbon::parse($start_at),
                        Carbon::parse($end_at)
                    );

                    CompanyBooking::checkForPrivateSlot(
                        User::findOrFail($user_time_slot->user_id),
                        Carbon::parse($start_at),
                        Carbon::parse($end_at),
                        $user_time_slot
                    );
                } catch (Throwable $th) {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '0',
                    ]);
                }
            }
        }

        $user_time_slot->start_at = $start_at;
        $user_time_slot->end_at = $end_at;

        if ($request->has('note')) {
            $user_time_slot->note = $request->note;
        }
        if ($request->has('description')) {
            $user_time_slot->description = $request->description;
        }
        if ($request->has('color')) {
            $user_time_slot->color = $request->color;
        }
        $user_time_slot->save();

        return response()->json([
            'data' => $user_time_slot->refresh(),
            'message' => __('strings.private_time_updated'),
            'status' => '1'
        ]);
    }

    public function userTimeDelete(UserTimeSlot $user_time_slot, DeleteUserTimeSlotRequest $request)
    {
        $user_time_slot->delete();
        return response()->json([
            'message' => __('strings.user_time_slots_delete'),
            'status' => '1'
        ]);
    }
}
