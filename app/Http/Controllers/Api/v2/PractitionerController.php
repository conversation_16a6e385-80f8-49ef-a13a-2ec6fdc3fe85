<?php

namespace App\Http\Controllers\Api\v2;

use App\Company;
use App\CompanyService;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\GetPractitionerAvailableSlotsRequest;
use App\Http\Requests\v2\GetPractitionerListRequest;
use App\Http\Requests\v2\GetPractitionerUnavailableDaysRequest;
use App\Traits\ApiResponser;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PractitionerController extends Controller
{
    use ApiResponser;
    public function index(GetPractitionerListRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $practitioners = User::where('company_id', $company->id)->where('is_active', 1)->where('is_booking_on', 1)->setEagerLoads([]);

        // filter practitioners by verified email
        $practitioners = $practitioners->verifiedEmail();

        if ($request->service_id) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($request) {
                $query = $query->where('company_services.id', $request->service_id);
            });
        }
        if ($request->has('available_at')) {
            $practitioners  = $practitioners->whereDoesntHave('bookings', function ($query) use ($request) {
                $query = $query->where('start_at', '<=', $request->available_at)->where('end_at', '>=', $request->available_at);
            });
        }
        if ($request->has('user_ids')) {
            $practitioners =  $practitioners->whereIn('id', $request->user_ids);
        }

        if ($request->has('service_ids')) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($request) {
                $query = $query->whereIn('company_services.id', $request->service_ids);
            });
        }


        $isPaginated = false;
        if (!$isPaginated) {
            $practitioners = $practitioners->get();
            $isPaginated = true;
        }
        // $practitioners = $practitioners->where('user_role', User::USER);

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.practitioner_list_returned'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($practitioners) : $practitioners->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $practitioners->values()  : $practitioners->get(),
                'message' => __('strings.practitioner_list_returned'),
                'status' => '1'
            ]);
        }
    }

    public function getUnavailableDays(User $practitioner, GetPractitionerUnavailableDaysRequest $request)
    {
        $company = $practitioner->company;
        $start_date = Carbon::now();
        $end_date = Carbon::now()->addMonth();
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date);
        }
        if ($request->has('end_date')) {
            $end_date = Carbon::parse($request->end_date);
        }
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $unavailable_days = User::getUnAvailableDays($company, $start_date, $end_date, $service, [(string)$practitioner->id]);
        return response()->json([
            'data' => $unavailable_days,
            'message' => __('strings.practitioner_unavailable_days_returned'),
            'status' => '1'
        ]);
    }
    public function getAvailableSlots(User $practitioner, GetPractitionerAvailableSlotsRequest $request)
    {
        $company = $practitioner->company;

        $service = CompanyService::findOrFail($request->service_id);
        $start_date = Carbon::now();
        $end_date = Carbon::now()->addMonth();
        if ($request->has('start_date')) {
            $start_date = Carbon::parse($request->start_date);
        }
        if ($request->has('end_date')) {
            $end_date = Carbon::parse($request->end_date);
        }
        $unavailable_days = User::getAvailableSlots($company, $start_date, $end_date, $service, [$practitioner->id]);
        return response()->json([
            'data' => $unavailable_days[0],
            'message' => __('strings.practitioner_unavailable_days_returned'),
            'status' => '1'
        ]);
    }
}
