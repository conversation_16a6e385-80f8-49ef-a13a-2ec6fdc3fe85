<?php

namespace App\Http\Controllers\Api\v2;

use App\CompanyService;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\SMSTemplateStoreRequest;
use App\Http\Requests\v3\SMSTemplateUpdateRequest;
use App\Setting;
use App\SMSTemplate;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SMSTemplatesController extends Controller
{
    use ApiResponser;

    public function index(Request $request)
    {
        $data = Auth::user()->company->sms_templates();

        $isPaginated = false;

        if ($request->input('search')) {
            $search = preg_replace('/\s\s+/', ' ', Str::lower($request->input('search')));

            $data = $data->whereLike(['title', 'description'], $search);
        }

        if ($request->has('filter')) {
            $data = $data->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $data = $data->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $data = $data->sortBy($orderBy, SORT_NATURAL | SORT_FLAG_CASE, $isDescOrder);
        } else {
            $data = $data->latest();
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.Templates'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($data) : $data->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $data->values() : $data->get(),
                'message' => __('strings.Templates'),
                'status' => '1'
            ]);
        }
    }

    public function store(SMSTemplateStoreRequest $request)
    {
        try {
            $sms_template = Auth::user()->company->sms_templates()->create([
                'title' => $request->title,
                'description' => $request->description,
                'is_active' => '1',
            ]);

            return response()->json([
                'data' => $sms_template,
                'message' => __('strings.Template_created_successfully'),
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => "something went wrong, please contact admin.",
                'stack' => $th->getMessage(),
                'status' => "0",
            ], 500);
        }
    }

    public function update(SMSTemplate $sms_template, SMSTemplateUpdateRequest $request)
    {
        if ($request->has('title')) {
            $sms_template->title = $request->title;
        }
        if ($request->has('description')) {
            $sms_template->description = $request->description;
        }
        if ($sms_template->isDirty()) {
            $sms_template->is_changed = 1;
            $sms_template->save();
        }

        return response()->json([
            'data' => $sms_template,
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(SMSTemplate $sms_template)
    {
        $this->authorize('delete', [SMSTemplate::class, $sms_template]);

        if ($sms_template->is_active) {
            $company = Auth::user()->company;
            $sms_template_ids = collect([
                Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
            ]);

            if ($sms_template_ids->contains($sms_template->id)) {
                return response()->json([
                    'message' => "This template is selected in Booking SMS Configuration. You cannot delete it.",
                    'status' => '0'
                ]);
            }

            if (CompanyService::where('finished_sms_template_id', $sms_template->id)->exists()) {
                return response()->json([
                    'message' => "This template is selected in Booking SMS Configuration. You cannot delete it.",
                    'status' => '0'
                ]);
            }
        }

        $sms_template->delete();

        return response()->json([
            'message' => __('strings.Template_deleted_successfully'),
            'status' => '1'
        ]);
    }

    public function toggle(SMSTemplate $sms_template)
    {
        $this->authorize('toggle', [SMSTemplate::class, $sms_template]);
        if ($sms_template->is_active) {
            $company = Auth::user()->company;
            $sms_template_ids = collect([
                Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
            ]);

            if ($sms_template_ids->contains($sms_template->id)) {
                return response()->json([
                    'message' => "This template is selected in Booking SMS Configuration. You cannot disable it.",
                    'status' => '0'
                ]);
            }
        }
        $sms_template->is_active = !$sms_template->is_active;
        $sms_template->save();
        return response()->json([
            'data' => $sms_template->refresh(),
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }
}