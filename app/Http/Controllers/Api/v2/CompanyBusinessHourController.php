<?php

namespace App\Http\Controllers\Api\v2;

use App\CompanyBusinessHour;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\GetBusinessHoursList;
use App\Http\Requests\v2\UpdateBusinessRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CompanyBusinessHourController extends Controller
{
    public function index(GetBusinessHoursList $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $business_hours = CompanyBusinessHour::where('company_id', $company->id);

        if ($request->has('week_day')) {
            $business_hours = $business_hours->where('week_day', $request->week_day);
        }

        $week_days = [
            CompanyBusinessHour::MONDAY,
            CompanyBusinessHour::TUESDAY,
            CompanyBusinessHour::WEDNESDAY,
            CompanyBusinessHour::THURSDAY,
            CompanyBusinessHour::FRIDAY,
            CompanyBusinessHour::SATURDAY,
            CompanyBusinessHour::SUNDAY,
        ];
        $orders = array_map(function ($item) {
            return "week_day = '{$item}' desc";
        }, $week_days);
        $rawOrder = implode(', ', $orders);
        $business_hours = $business_hours->orderByRaw($rawOrder);

        if ($request->has('with_breaks') && $request->with_breaks) {
            $custom_data = [];
            foreach ($week_days as $week_day) {
                $week_hours = CompanyBusinessHour::where('company_id', $company->id)->where('week_day', $week_day)->orderBy('start_time', 'asc')->get();
                if (count($week_hours) <= 0) {
                    continue;
                }
                $week_start_time = Carbon::parse($week_hours[0]->start_time);
                $week_end_time = Carbon::parse($week_hours[count($week_hours) - 1]->end_time);
                $temp_time = Carbon::parse($week_start_time);
                foreach ($week_hours as $index => $week_hour) {
                    $week_hour->type = "OPEN";
                    array_push($custom_data, $week_hour);
                    // return $custom_data;
                    if (isset($week_hours[$index + 1])) {
                        $temp_data = [];
                        $temp_data['id'] = $week_hour->id;
                        $temp_data['company_id'] = $week_hour->company_id;
                        $temp_data['week_day'] = $week_hour->week_day;
                        $temp_data['start_time'] = $week_hour->end_time;
                        $temp_data['end_time'] = $week_hours[$index + 1]->start_time;
                        $temp_data['created_at'] = $week_hour->created_at;
                        $temp_data['updated_at'] = $week_hour->updated_at;
                        $temp_data['type'] = 'CLOSE';
                        array_push($custom_data, $temp_data);
                    }
                }
            }
            return response()->json([
                'data' =>  $custom_data,
                'message' => __('strings.business_hours_returned'),
                'status' => '1',
            ]);
        }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.business_hours_returned'),
                    'status' => '1',
                ])->merge($business_hours->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' =>  $business_hours->get(),
                'message' => __('strings.business_hours_returned'),
                'status' => '1',
            ]);
        }
    }

    public function update(UpdateBusinessRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if ($request->has('hours')) {
            CompanyBusinessHour::where('company_id', $company->id)->delete();
            foreach ($request->hours as $hour) {
                if (isset($hour['day']) && isset($hour['start_time']) && isset($hour['end_time'])) {
                    CompanyBusinessHour::create([
                        'company_id' => $company->id,
                        'week_day' => $hour['day'],
                        'start_time' => Carbon::parse($hour['start_time'])->format('H:i:s'),
                        'end_time' => Carbon::parse($hour['end_time'])->format('H:i:s'),
                    ]);
                }
            }
        }
        return response()->json([
            'message' => __('strings.business_hours_updated'),
            'status' => '1'
        ]);
    }
}