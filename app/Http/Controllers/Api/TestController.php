<?php

namespace App\Http\Controllers\Api;

use App\ClientLetterOfConsent;
use App\ClientTreatment;
use App\Company;
use App\CompanyBooking;
use App\Http\Controllers\Controller;
use App\Mail\GenericBookingMail;
use App\Mail\NewAccountUserMail;
use App\Setting;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use App\User;
use Illuminate\Http\Request;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Throwable;
use App\Traits\TimeZoneManager;
use Illuminate\Support\Facades\Cache;
use App\Plan;
use App\Models\Cashier\Subscription;
use App\Traits\BookingManager;
use App\Traits\PlanManager;
use App\UserTimeSlot;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Exceptions\IncompletePayment;

class TestController extends Controller
{
    use GetEncryptedFile;
    use SaveFile;
    use PlanManager;
    use BookingManager;

    public function getRegPortalURLs()
    {
        $companies = Company::all();
        $csv = "id,company name,encID\n";
        foreach ($companies as $key => $company) {
            $csv = $csv . "$company->id,\"$company->company_name\",$company->encrypted_id\n";
        }
        return "<p style='white-space: pre-wrap;'>$csv</p>";
    }

    public function getWeekDay(Request $request)
    {
        return config('phone_country')['46'];
        return [
            'start_date' => Carbon::now(),
            'start_day' => BookingManager::getWeekDay(Carbon::now()),
            'end_date' => Carbon::now()->addDay(),
            'end_day' => BookingManager::getWeekDay(Carbon::now()->addDay()),
            'lang' => app()->getLocale(),
        ];
    }

    public function syncSubscription(Request $request)
    {
        // $company = Company::findOrFail($request->company_id)->firstOrFail();
        $sub = Subscription::findOrFail($request->subscription_id);
        if ($sub && $request->has('stripe_id') && $request->has('stripe_plan') && $request->has('quantity')) {
            $sub->stripe_id = $request->stripe_id;
            $sub->stripe_plan = $request->stripe_plan;
            $sub->quantity = $request->quantity;
            $sub->stripe_status = 'active';
            $sub->ends_at = null;
            $sub->save();
        }

        return $sub;
    }

    public function updatePlane()
    {
        $free_plans = Plan::where('is_2022', 1)->get()->where('is_free', true);
        foreach ($free_plans as $free_plan) {
            $plan_to_update = Plan::findOrFail($free_plan->id);
            $plan_to_update->client = '21';
            $plan_to_update->save();
        }
        return "dome update";
    }
    public function changePlanToNew(Request $request)
    {
        $companies = Company::query();
        $problematic_companies = [];

        if ($request->has('company_id')) {
            $companies = $companies->where('id', $request->company_id);
        }
        if ($request->has('start_company_id')) {
            $companies = $companies->where('id', '>=', $request->start_company_id);
        }
        if ($request->has('end_company_id')) {
            $companies = $companies->where('id', '<=', $request->end_company_id);
        }

        if ($request->has('show_count_only')) {
            return $companies->count();
        }

        $companies = $companies->cursor();
        if ($request->has('show_all_company')) {
            return $companies;
        }
        foreach ($companies as $company) {
            try {
                PlanManager::updateNewPlanForThisCompany($company, $request);
            } catch (\Throwable $th) {
                array_push($problematic_companies, [
                    "company_id" => $company->id,
                    "reason" => $th->getMessage(),
                ]);
            }
        }
        return $problematic_companies;
    }


    public function pullTest(Request $request)
    {
        return 'this is pull test test';
    }

    public function checkForPlans(Request $request)
    {
        $companies = Company::query();
        $companies_to_check = [];

        if ($request->has('start_company_id')) {
            $companies = $companies->where('id', '>=', $request->start_company_id);
        }
        if ($request->has('end_company_id')) {
            $companies = $companies->where('id', '<=', $request->end_company_id);
        }
        foreach ($companies->cursor() as $company) {
            try {
                PlanManager::checkForPlan($company);
            } catch (\Throwable $th) {
                array_push($companies_to_check, [
                    'id' => $company->id,
                    'company_email' => $company->email,
                    'problem' => $th->getMessage()
                ]);
            }
        }

        return $companies_to_check;
    }

    public function takePull(Request $request)
    {
        $torun = "cd /var/www/html/meridiq-api/ ; sudo git pull origin production";
        //echo $torun;
        try {
            exec($torun);
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public function updateCompanyStripeId(Request $request)
    {
        $company = Company::findOrFail($request->company_id);

        if ($request->has('stripe_id')) {
            $company->stripe_id = $request->stripe_id;
            $company->save();
        }


        return response()->json([
            'message' => 'updated',
            'status' => '1',
        ]);
    }

    public function getCompaniesWithoutCard(Request $request)
    {
        $companies = Company::cursor();

        if ($request->has('start_id')) {
            $companies = $companies->where('id', '>=', $request->start_id);
        }
        if ($request->has('end_id')) {
            $companies = $companies->where('id', '<=', $request->end_id);
        }

        $companies_without_cards = [];
        foreach ($companies as $company) {
            $company->createOrGetStripeCustomer();
            $payment_method = $company->defaultPaymentMethod();
            $data = [];
            if ($payment_method && $payment_method->card) {
                $data['card'] = [
                    'brand' => $payment_method->card->brand,
                    'exp_month' => $payment_method->card->exp_month,
                    'exp_year' => $payment_method->card->exp_year,
                    'last4' => $payment_method->card->last4,
                ];
            } elseif ($payment_method && $payment_method->brand && $payment_method->exp_month && $payment_method->exp_year && $payment_method->last4) {
                $data['card'] = [
                    'brand' => $payment_method->brand,
                    'exp_month' => $payment_method->exp_month,
                    'exp_year' => $payment_method->exp_year,
                    'last4' => $payment_method->last4,
                ];
            }
            if (!$data) {

                $is_free = false;
                $is_cancelled = false;
                $subscription_name = 'Subscription';
                if ($company->activeSubscription()) {
                    $subscription_name = $company->activeSubscription()->name;
                }
                $is_subscribed =  $company->subscribed($subscription_name);
                try {
                    $is_cancelled  = $company->subscription($subscription_name)->cancelled() || $company->subscription($subscription_name)->pastDue();
                } catch (\Throwable $th) {
                    $is_cancelled = false;
                }

                if ($company->activeSubscription()) {
                    if ($company->activeSubscription()->plan->is_free) {
                        $is_free = true;
                    }
                }


                array_push($companies_without_cards, [
                    'id' => $company->id,
                    'email' => $company->email,
                    'stripe_id' => $company->stripe_id,
                    'is_free' => $is_free,
                    'is_cancelled' => $is_cancelled,
                    'is_subscribed' => $is_subscribed,
                ]);
            }
        }

        return $companies_without_cards;
    }
    public function test33(Request $request)
    {
        $free_plans = Plan::where('is_2022', 1)->get()->where('is_free', true);
        foreach ($free_plans as $free_plan) {
            $plan_to_update = Plan::findOrFail($free_plan->id);
            $plan_to_update->client = '21';
            $plan_to_update->save();
        }
        return "dome update";
        // $company = Company::findOrFail(437);
        // return $company->profile_photo;
        // return Carbon::now()->format('l, F d Y g:i A');
        // $user = Auth::user();
        // $company = $user->company;
        // $subscription_name = 'Subscription';
        // if ($company->activeSubscription()) {
        //     $subscription_name = $company->activeSubscription()->name;
        // }
        // $company->subscription($subscription_name)->cancel();
        // $company->createOrGetStripeCustomer();
        // try {

        //     $company
        //         ->newSubscription('Subscription', $request->plan)
        //         ->quantity($plan->isFree() ? 1 : $request->quantity)
        //         ->create($request->has('payment_method.paymentMethod') ? $request->payment_method['paymentMethod'] : null, $billing_details, [
        //             'metadata' => $request->has('payment_method.metadata') ? $request->payment_method['metadata'] : '',
        //             'default_tax_rates' => $company->subscriptionTaxRates(),
        //         ]);
        //     // $company->subscription('Subscription')->swap([
        //     //     'price_1MABKoCBmBWVCuBdETKp49rW' => [
        //     //         'quantity' => 1,
        //     //     ]
        //     // ]);
        // } catch (IncompletePayment $th) {
        //     $invoice = $company->findInvoice($th->payment->invoice);
        //     if ($invoice) {
        //         $invoice->void();
        //     }
        //     Log::error($th);
        //     throw $th;
        // }
        // return $company->subscriptions;
    }
}
