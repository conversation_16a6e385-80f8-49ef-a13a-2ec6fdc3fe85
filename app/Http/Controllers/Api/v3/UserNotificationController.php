<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyPlatform;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\FilterUserRequest;
use App\Http\Requests\v3\ListNotificationRequest;
use App\Http\Requests\v3\ListUserNotificationRequest;
use App\Http\Requests\v3\StoreUserNotificationRequest;
use App\NotificationLogs;
use App\Traits\ApiResponser;
use App\Traits\FcmNotificationManager;
use App\User;
use App\UserNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class UserNotificationController extends Controller
{
    use FcmNotificationManager, ApiResponser;
    public function index(ListNotificationRequest $request)
    {
        $notification_logs = NotificationLogs::query();
        if ($request->has('start_date') && $request->has('end_date')) {
            $notification_logs = $notification_logs->whereBetween('created_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()]);
        }
        if ($request->has('search')) {
            $search = '%' . $request->search . '%';
            $notification_logs = $notification_logs->where(function ($query) use ($search) {
                $query->where('title', 'like', $search)->orWhere('description', 'like', $search);
            });
        }
        $orderBy = 'id';
        $orderDirection = 'desc';
        if ($request->has('orderDirection')) {
            $orderDirection = $request->orderDirection;
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
        }
        $notification_logs = $notification_logs->orderBy($orderBy, $orderDirection);
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.notification_returned'),
                    'status' => '1'
                ])->merge($notification_logs->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $notification_logs->get(),
                'message' => __('strings.notification_returned'),
                'status' => '1'
            ]);
        }
    }

    public function getIds(Request $request)
    {
        // $users = User::whereHas(['company.subscriptions'])->get();
        $users = $this->filterUsers($request);
        $user_notified = [];
        foreach ($users as $user) {
            // $this->sendNotification($user->firebase_tokens, $request->title, $request->message);
            array_push($user_notified, [
                'user_id' => $user->id,
                'title' => $request->title,
                'description' => $request->description,
                'is_read' => 0,
                'external_link'  => $request->external_link,
                'click_action' => $request->click_action,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        }
        return count($user_notified);
    }

    public function store(StoreUserNotificationRequest $request)
    {
        //Store Data/Notification , to which user is received notification

        $users = [];
        if ($request->has('user_ids')) {
            $users = User::whereIn('id', $request->user_ids)->get();
        } else {
            $users = $this->filterUsers($request);
        }


        $user_notified = [];
        foreach ($users as $user) {
            // $this->sendNotification($user->firebase_tokens, $request->title, $request->message);
            array_push($user_notified, [
                'user_id' => $user->id,
                'title' => $request->title,
                'description' => $request->description,
                'is_read' => 0,
                'external_link'  => $request->external_link,
                'click_action' => $request->click_action,
                'is_popup' => $request->is_popup ?? 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        }
        UserNotification::insert($user_notified);
        $applied_filter = [];
        if ($request->has('activity')) {
            $applied_filter['activity'] = $request->activity;
        }
        if ($request->has('subscription_preferences')) {
            $applied_filter['subscription_preferences'] = $request->subscription_preferences;
        }
        if ($request->has('user_types')) {
            $applied_filter['user_types'] = $request->user_types;
        }
        if ($request->has('user_ids')) {
            $applied_filter['user_ids'] = $request->user_ids;
        }
        if ($request->has('countries')) {
            $applied_filter['countries'] = $request->countries;
        }
        $auth_user = Auth::user();
        NotificationLogs::create([
            'user_id' => $auth_user->id,
            'title' => $request->title,
            'description' => $request->description,
            'type' => NotificationLogs::NOTIFICATION,
            'notified_user_count' => count($users),
            'external_link'  => $request->external_link,
            'click_action' => $request->click_action,
            'applied_filter' => $applied_filter,
            'is_popup' => $request->is_popup ?? 0,
        ]);

        return response()->json([
            'message' => __('strings.notification_sent'),
            'status' => '1'
        ]);
    }

    public function list(ListUserNotificationRequest $request)
    {
        $user = Auth::user();
        $user_notifications =  UserNotification::where('user_id', $user->id);

        if ($request->has('show_only_count') && $request->show_only_count) {
            $unread_notification_count = $user_notifications->where('is_read', 0)->count();
            return response()->json([
                'data' => [],
                'unread_notification_count' => $unread_notification_count,
                'message' => __('strings.notification_returned'),
                'status' => '1'
            ]);
        }

        if ($request->has('show_only_popups') && $request->show_only_popups) {
            $popup_notification = $user_notifications
                ->where('is_read', 0)
                ->where('is_popup', 1);
            return response()->json([
                'data' => $popup_notification->get(),
                'unread_notification_count' => 0,
                'message' => __('strings.notification_returned'),
                'status' => '1'
            ]);
        }

        if ($request->has('is_read')) {
            $user_notifications = $user_notifications->where('is_read', $request->is_read);
        }
        $user_notifications = $user_notifications->latest();
        $unread_notification_count = 0;


        if ($request->input('page')) {
            return response()->json(
                collect([
                    'unread_notification_count' => $unread_notification_count,
                    'message' => __('strings.notification_returned'),
                    'status' => '1'
                ])->merge($user_notifications->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $user_notifications->get(),
                'unread_notification_count' => $unread_notification_count,
                'message' => __('strings.notification_returned'),
                'status' => '1'
            ]);
        }
    }

    public function readNotification(UserNotification $notification)
    {
        $user = Auth::user();
        $notification->update(['is_read' => 1]);
        return response()->json([
            'message' => __('strings.notification_read'),
            'status' => '1'
        ]);
    }
    public function readAllNotification()
    {
        $user = Auth::user();
        UserNotification::where('user_id', $user->id)->update(['is_read' => 1]);
        return response()->json([
            'message' => __('strings.notification_read'),
            'status' => '1'
        ]);
    }

    public function filterUsersCount(FilterUserRequest $request)
    {
        $users = $this->filterUsers($request);
        return response()->json([
            'data' => $users->count(),
            'message' => __('strings.users_returned'),
            'status' => '1'
        ]);
    }

    private function filterUsers($filters)
    {
        $users = User::with(['company.subscriptions'])->verifiedEmail();
        $isPaginated = false;
        // if ($filters->has('user_ids')) {
        //     //TODO::Fix this method with user_ids array
        //     $users->where(function($query) use ($filters) {
        //         $query->whereIn('id', $filters->user_ids);
        //     });
        // }
        if ($filters->has('activity')) {
            switch ($filters->activity) {
                case 'last_login_before_week':
                    $users = $users->where('last_login_at', '<', Carbon::now()->subWeek());
                    break;
                case 'last_login_before_month':
                    $users = $users->where('last_login_at', '<', Carbon::now()->subMonth());
                    break;
                default:
                    break;
            }
        }

        if ($filters->has('is_licensed')) {
            if ($filters->is_licensed) {
                $users = User::whereHas('company', function ($query) {
                    $query->whereHas('subscriptions', function ($query) {
                        $query->where('stripe_status', 'active')
                            ->where(function ($query) {
                                $query->whereNotIn('stripe_plan', [
                                    "plan_HInwLF7p1lgjk2",
                                    "price_1It9oDCBmBWVCuBdxioStgMG",
                                    "price_1It9nLCBmBWVCuBdx3tuZ4ka",
                                    "price_1It9msCBmBWVCuBdlfNmXtgF",
                                    "price_1MErFlCBmBWVCuBdAxbSqw6r",
                                    "price_1MErGDCBmBWVCuBdH7JFHdmm",
                                    "price_1MErEzCBmBWVCuBdbSu1griI",
                                    "price_1MErGpCBmBWVCuBd8eSwFFV1",
                                ])->orWhere('stripe_plan', null);
                            });;
                    });
                });
            } else {
                $subscribed_user_ids =  User::whereHas('company', function ($query) {
                    $query->whereHas('subscriptions', function ($query) {
                        $query->where('stripe_status', 'active');
                    });
                })->get()->pluck('id')->toArray();
                $users = User::whereNotIn('id', $subscribed_user_ids);
            }
        }

        if ($filters->has('subscription_preferences')) {
            foreach ($filters->subscription_preferences as $subscription_preference) {
                switch ($subscription_preference) {
                    case 'record_on':
                        $users = $users->where('is_record_on', 1);
                        break;
                    case 'record_off':
                        $users = $users->where('is_record_on', 0);
                        break;
                    case 'booking_on':
                        $users = $users->where('is_booking_on', 1);
                        break;
                    case 'booking_off':
                        $users = $users->where('is_booking_on', 0);
                        break;
                    case 'pos_on':
                        $users = $users->where('is_pos_on', 1);
                        break;
                    case 'pos_off':
                        $users = $users->where('is_pos_on', 0);
                        break;
                    case 'all':
                        break;
                    default:
                }
            }
        }
        if ($filters->has('user_types')) {
            if (!$isPaginated) {
                $users = $users->cursor();
                $isPaginated = true;
            }
            $users = $users->filter(function ($user) use ($filters) {
                if (in_array('all', $filters->user_types)) {
                    return $user->user_role != User::MASTER_ADMIN;
                } else {
                    $should_add_user = false;
                    if (in_array('super_user', $filters->user_types)) {
                        if ($user->email == $user->company->email && $user->user_role != User::MASTER_ADMIN) {
                            $should_add_user = true;
                        }
                    }
                    if (in_array('admin', $filters->user_types)) {
                        if ($user->user_role == User::ADMIN && $user->email != $user->company->email) {
                            $should_add_user = true;
                        }
                    }
                    if (in_array('user', $filters->user_types)) {
                        if ($user->user_role == User::USER) {
                            $should_add_user = true;
                        }
                    }
                    if (in_array('prescriber', $filters->user_types)) {
                        if ($user->company->platforms->contains('platform', CompanyPlatform::PRESCRIPTION)) {
                            $should_add_user = true;
                        }
                    }
                    return $should_add_user;
                }
            });
        }
        if ($filters->has('countries')) {
            if (!$isPaginated) {
                $users = $users->cursor();
                $isPaginated = true;
            }
            $users = $users->filter(function ($user) use ($filters) {
                if (in_array('all', $filters->countries)) {
                    return $user;
                } else {
                    if (in_array($user->company->country, $filters->countries)) {
                        return $user;
                    }
                }
            });
        }


        return $users;
    }

    public function test(FilterUserRequest $request)
    {
        $users = $this->filterUsers($request);
        return $users->pluck('email')->toArray();
    }
}