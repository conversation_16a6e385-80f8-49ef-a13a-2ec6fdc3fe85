<?php

namespace App\Http\Controllers\Api\v3;

use App\GeneralTemplate;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\ListGeneralTemplateRequest;
use App\Http\Requests\v3\StoreGeneralTemplateRequest;
use App\Http\Requests\v3\SyncTemplateRequest;
use App\Http\Requests\v3\UpdateGeneralTemplateRequest;
use App\SMSTemplate;
use App\Treatment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GeneralTemplateController extends Controller
{
    public function index(ListGeneralTemplateRequest $request)
    {
        $templates = GeneralTemplate::query();
        $orderBy = 'id';
        $orderDirection = 'desc';
        if ($request->has('orderDirection')) {
            $orderDirection = $request->orderDirection;
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
        }
        if ($request->has('filter')) {
            switch ($request->filter) {
                case "ACTIVE":
                    $templates = $templates->where('is_active', 1);
                    break;
                case "INACTIVE":
                    $templates = $templates->where('is_active', 0);
                    break;
                case "ALL":
                    break;
                default:
            }
        }
        if ($request->has('search')) {
            $templates = $templates->where('name', 'LIKE', "%{$request->search}%");
        }
        if ($request->has('type')) {
            $templates = $templates->where('type', $request->type);
        }

        $templates = $templates->orderBy($orderBy, $orderDirection);
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('prescription_strings.template_list_returned'),
                    'status' => '1'
                ])->merge($templates->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $templates->get(),
                'message' => __('prescription_strings.template_list_returned'),
                'status' => '1'
            ]);
        }
    }

    public function store(StoreGeneralTemplateRequest $request)
    {
        $this->authorize('create', [GeneralTemplate::class]);
        $user = Auth::user();
        if (GeneralTemplate::where(['name' => $request->name,'type' => $request->type])->exists()) {
            return response()->json([
                'message' => __('prescription_strings.template_already_exist'),
                'status' => '0'
            ]);
        }

        if ($request->type == GeneralTemplate::EMAIL && $request->has('action') && $request->action != "" && $request->has('language') && $request->language != "") {
            if (GeneralTemplate::where('action', $request->action)->where('language', $request->language)->exists()) {
                return response()->json([
                    'message' => __('strings.this_action_of_template_already_exist'),
                    'status' => '0'
                ]);
            }
        }


        GeneralTemplate::create([
            'user_id' => $user->id,
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'action' => $request->action ?? null,
            'language' => $request->language ?? null,
            'is_active' => 1,
            'process'=> $request->process ?? null
        ]);

        return response()->json([
            'message' => __('strings.Template_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(UpdateGeneralTemplateRequest $request, GeneralTemplate $generalTemplate)
    {
        $this->authorize('create', [GeneralTemplate::class]);
        if ($request->type == GeneralTemplate::EMAIL && $request->has('action') && $request->action != "" && $request->has('language') && $request->language != "") {
            $general_template =  GeneralTemplate::where('action', $request->action)->where('language', $request->language)->first();
            if ($general_template && $generalTemplate->id != $general_template->id) {
                return response()->json([
                    'message' => __('strings.this_action_of_template_already_exist'),
                    'status' => '0'
                ]);
            }
        }
        if ($request->has('name')) {
            $generalTemplate->name =  $request->name;
        }
        if ($request->has('description')) {
            $generalTemplate->description =  $request->description;
        }
        if ($request->has('is_active')) {
            $generalTemplate->is_active =  $request->is_active;
        }
        if ($request->has('action')) {
            $generalTemplate->action =  $request->action;
        }
        if ($request->has('language')) {
            $generalTemplate->language =  $request->language;
        }
        if ($request->has('process')) {
            $generalTemplate->process =  $request->process;
        }
        $generalTemplate->save();

        return response()->json([
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(GeneralTemplate $generalTemplate)
    {
        $this->authorize('delete', [GeneralTemplate::class]);

        if ($generalTemplate->delete()) {
            return response()->json([
                'message' => __('strings.Template_deleted_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Template_deletion_failed'),
                'status' => '0'
            ]);
        }
    }

    public function syncTemplates(SyncTemplateRequest $request)
    {
        $user = Auth::user();
        GeneralTemplate::getGeneralTemplate($user,$request->type);
        return response()->json([
            'message' => __('prescription_strings.template_sync_successfully'),
            'status' => '1'
        ]);
    }
}
