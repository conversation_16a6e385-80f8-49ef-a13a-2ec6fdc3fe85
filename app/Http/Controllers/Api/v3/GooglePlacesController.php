<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use App\Http\Integrations\GooglePlaces\PlacesNew\GooglePlaces;
use App\Http\Requests\v3\GooglePlaces\GetGooglePlacesRequest;
use App\Http\Requests\v3\GooglePlaces\StoreGooglePlaceRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class GooglePlacesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(GetGooglePlacesRequest $request)
    {
        $user = Auth::user();

        $this->authorize('googlePlaces', $user);

        $session = $request->input('sessionToken', (string) Str::uuid());

        $response = GooglePlaces::make()->autocomplete($request->input('input'), false, null, [
            'sessionToken' => $session,
        ]);

        $data = $response->json();

        $suggestions = isset($data['suggestions']) ? $data['suggestions'] : [];

        return response()->json([
            'data' => [
                'sessionToken' => $session,
                'suggestions' => $suggestions,
            ],
            'message' => __('strings.google_places_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  StoreGooglePlaceRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function place(StoreGooglePlaceRequest $request)
    {
        $user = Auth::user();

        $this->authorize('googlePlaces', $user);

        $session = $request->input('sessionToken', Str::uuid()->toString());

        $data = Cache::remember("google_places_" . $request->input('place_id'), 86400, function () use ($request, $session) {
            $response = GooglePlaces::make()->placeDetails($request->input('place_id'), [
                "name",
                "id",
                "rating",
                "reviews",
                "userRatingCount",
                "googleMapsLinks",
                "displayName",
                "formattedAddress",
            ], [
                'sessionToken' => $session,
            ]);

            return $response->json();
        });

        $company = $user->company;

        if ($request->boolean('save')) {
            $company->extra_field()->updateOrCreate([
                'company_id' => $user->company_id,
            ], [
                "place_id" => isset($data['id']) ? $data['id'] : null,
                "rating" =>  isset($data['rating']) ? $data['rating'] : null,
                "reviews" =>  isset($data['reviews']) ? $data['reviews'] : null,
                "user_rating_count" => isset($data['userRatingCount']) ? $data['userRatingCount'] : null,
                "google_maps_links" => isset($data['googleMapsLinks']) ? $data['googleMapsLinks'] : null,
            ]);
        }

        return response()->json([
            'data' => $data,
            'message' => __('strings.google_place_stored_successfully'),
            'status' => '1'
        ]);
    }
}
