<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyPlatform;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\POS\InfrasecEnrollmentServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Infrasec\StoreCompanyMerchantIdRequest;
use App\Http\Requests\v3\Infrasec\UpdateCompanyInfrasecRequest;
use App\Mail\POS\SendVivaVerifyMail;
use App\Setting;
use App\Traits\SaveFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class CompanyTaxControlUnitController extends Controller
{
    use SaveFile;

    function __construct(private readonly InfrasecEnrollmentServiceInterface $infrasecEnrollService)
    {
    }

    public function enroll(UpdateCompanyInfrasecRequest $request, PDFServiceInterface $pdfService)
    {
        return DB::transaction(function () use ($request, $pdfService) {
            $user = Auth::user();
            $company = $user->company;

            $this->authorize('enrollment', $company);

            $company->organization_number = $request->organization_number;
            $company->company_name = $request->company_name;
            $company->street_address = $request->street_address;
            $company->city = $request->city;
            $company->zip_code = $request->zip_code;
            $company->mobile_number = $request->mobile_number;
            $company->country_code = $request->country_code;
            $company->viva_account_id = "applied";

            $company->save();

            $language = Setting::getSetting($company, Setting::LANGUAGE)->value;
            $email = config('viva_wallet.viva_email');

            $filename = $this->generateFilePath('companies/' . md5($company->id) . '/pos_licence', $user, null, 'pdf');

            $file = $pdfService->pos_licence($company, request()->header('X-Time-Zone', 'UTC'))->saveFile($user, $filename);

            Setting::updateOrCreate([
                'key' => Setting::POS_LICENSE,
                'company_id' => $company->id
            ], [
                'value' => $file->filename,
            ]);

            CompanyPlatform::updateOrCreate([
                'platform' => CompanyPlatform::POS_SYSTEM,
                'company_id' => $company->id,
            ], [
                'license_agreement' => $file->filename,
            ]);

            if ($email) {
                Mail::to($email)->locale($language)->send(new SendVivaVerifyMail($company, $user));
            }

            return response()->json([
                'message' => __('pos_strings.pos_applied_success'),
                'status' => '1'
            ]);
        });
    }

    public function close(Request $request)
    {
        $user = Auth::user();

        $this->infrasecEnrollService->close($user->company);

        return response()->json([
            'message' => __('pos_strings.ccu.company_ccu_enrollment_close_successfully'),
            'status' => '1'
        ]);
    }

    public function open(Request $request)
    {
        $user = Auth::user();

        $this->infrasecEnrollService->open($user->company);

        return response()->json([
            'message' => __('pos_strings.ccu.company_ccu_enrollment_open_successfully'),
            'status' => '1'
        ]);
    }

    public function enrollmentStatus(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $data = $this->infrasecEnrollService->checkStatus($company);

        return response()->json([
            'data' => $data,
            'message' => __('pos_strings.ccu.company_ccu_enrollment_open_successfully'),
            'status' => '1'
        ]);
    }
}
