<?php

namespace App\Http\Controllers\Api\v3;

use App\ClientPrescription;
use App\Company;
use App\CompanyPlatform;
use App\Events\PrescriptionSignedEvent;
use App\Exports\DoctorBillingExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\CreateUserCompanyRequest;
use App\Http\Requests\v3\GetPrescriptionRequest;
use App\Http\Requests\v3\ListBillingRequest;
use App\Http\Requests\v3\ListCompanyReportRequest;
use App\Http\Requests\v3\ListCompanyRequest;
use App\Http\Requests\v3\ListDoctorBillingRequest;
use App\Http\Requests\v3\SetFeesRequest;
use App\Http\Requests\v3\SignPrescriptionRequest;
use App\Http\Requests\v3\UpdateBillingRequest;
use App\Http\Requests\v3\UpdateDoctorPricingRequest;
use App\Http\Requests\v3\VerificationOtpRequest;
use App\Mail\DoctorInviteMail;
use App\Mail\DoctorVerificationMail;
use App\Setting;
use App\Stripe\Invoice;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Http;
use App\Traits\PrescriptionManager;
use App\Traits\ReceiptManager;
use App\User;
use App\UserCompany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use App\Traits\SaveFile;
use App\UpcomingPlatformFees;
use App\UserBilling;
use App\UserCompanyBilling;
use Illuminate\Support\Facades\Schema;
use Maatwebsite\Excel\Facades\Excel;

class UserCompanyController extends Controller
{
    use SaveFile, ApiResponser;
    public function index(GetPrescriptionRequest $request)
    {

        $user = Auth::user();
        //TODO::try to find a optimum way to do this in just 1 query
        // $prescriptions = ClientPrescription::where('assign_to_id', $user->id)
        //     ->with(['files', 'company', 'client' => function ($query) {
        //         $query->setEagerLoads([]);
        //     }])
        //     ->whereHas('assigned_to', function ($query) {
        //         $query->whereHas('accepted_companies', function ($query) {
        //             $query = $query->where('companies.id', 'client_prescriptions.company.id');
        //         });
        //     });

        $isPaginated = false;
        //easy-but not optimized
        $accepted_companies_ids = $user->accepted_companies()->pluck('companies.id')->toArray();
        $prescriptions = ClientPrescription::where('assign_to_id', $user->id)
            ->with(['files', 'company', 'client' => function ($query) {
                $query->setEagerLoads([]);
            }])
            ->whereHas('client', function ($query) use ($accepted_companies_ids) {
                $query = $query->whereIn('clients.company_id', $accepted_companies_ids);
            });

        $orderBy = 'id';
        $orderDirection = 'desc';
        if ($request->has('orderDirection')) {
            $orderDirection = $request->orderDirection;
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
        }
        if ($request->has('filter')) {
            switch ($request->filter) {
                case "PRESCRIBED":
                    $prescriptions = $prescriptions->whereNotNull('signed_at');
                    break;
                case "NOT_PRESCRIBED":
                    $prescriptions = $prescriptions->whereNull('signed_at');
                    break;
                case "ALL":
                    break;
                default:
            }
        }
        if ($request->has('company_id')) {
            $prescriptions = $prescriptions->whereHas('client', function ($query) use ($request) {
                $query = $query->where('clients.company_id', $request->company_id);
            });
        }


        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            if ($orderBy == 'title' || $orderBy == 'company_name' || $orderBy == 'client_name') {
                if (!$isPaginated) {
                    $prescriptions = $prescriptions->get();
                    $isPaginated = true;
                }
                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

                switch ($orderBy) {
                    case "client_name":
                        $orderBy = "client.first_name";
                        break;
                    case "company_name":
                        $orderBy = "company.company_name";
                        break;
                    default:
                }
                $prescriptions =  $prescriptions->sortBy($orderBy, $sortExtra, $isDescOrder);
            } else {
                $prescriptions = $prescriptions->orderBy($orderBy, $orderDirection);
            }
        } else {
            $prescriptions = $prescriptions->orderBy($orderBy, $orderDirection);
        }
        if ($request->has('search')) {
            if (!$isPaginated) {
                $prescriptions = $prescriptions->get();
                $isPaginated = true;
            }
            //Search Logic
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $prescriptions = $prescriptions->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->title), $search)
                    || Str::contains(Str::lower($value->company->company_name), $search)
                    || Str::contains(Str::lower($value->client->first_name), $search);
            });
        }
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('prescription_strings.prescription_list_returned'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($prescriptions) : $prescriptions->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $prescriptions : $prescriptions->get(),
                'message' => __('prescription_strings.prescription_list_returned'),
                'status' => '1'
            ]);
        }
    }

    public function listDoctors(Request $request)
    {
        $user = Auth::user();
        $company_id = $user->company_id;

        if ($user->isMasterAdmin()) {
            $company_id = $request->input('company_id');
        }

        $doctors = UserCompany::with(['user' => function ($query) {
            $query->setEagerLoads([]);
        }])->where(['company_id' => $company_id, 'invite_status' => UserCompany::ACCEPTED])->get();

        return response()->json([
            'data' => $doctors,
            'message' => __('prescription_strings.doctor_list_returned'),
            'status' => '1'
        ]);
    }
    public function list(ListCompanyRequest $request)
    {
        if ($request->has('user_id')) {
            $this->authorize('viewAny', [UserCompany::class]);
            $user = User::findOrFail($request->user_id);
        } else {
            $user = Auth::user();
        }
        $user_companies = $user->companies()->whereNotIn('invite_status', [UserCompany::PENDING, UserCompany::EXPIRED])
            ->withCount(['prescriptions as signed_prescription' => function ($q) use ($user) {
                $q->where('sign_by_id', $user->id)->where('assign_to_id', $user->id);
            }, 'prescriptions as pending_prescription' => function ($q) use ($user) {
                $q->whereNull('sign_by_id')->where('assign_to_id', $user->id);
            }]);
        $isPaginated = false;
        $orderBy = 'id';
        $orderDirection = 'desc';
        if ($request->has('orderDirection')) {
            $orderDirection = $request->orderDirection;
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
        }
        if ($request->has('search')) {
            if (!$isPaginated) {
                $user_companies = $user_companies->get();
                $isPaginated = true;
            }
            //Search Logic
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $user_companies = $user_companies->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->company_name), $search);
            });
        }
        if (!$isPaginated) {
            $user_companies = $user_companies->orderBy($orderBy, $orderDirection);
        } else {
            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
            $user_companies =  $user_companies->sortBy($orderBy, $sortExtra, $isDescOrder);
        }

        // if ($request->input('page')) {
        //     return response()->json(
        //         collect([
        //             'message' => __('prescription_strings.companies_list_returned'),
        //             'status' => '1'
        //         ])->merge($isPaginated ? $this->paginate($user_companies) : $user_companies->paginate($request->input('per_page')))
        //     );
        // } else {
        return response()->json([
            'data' => $isPaginated ? $user_companies : $user_companies->get(),
            'message' => __('prescription_strings.companies_list_returned'),
            'status' => '1'
        ]);
        // }
    }
    public function listClinicReport(ListCompanyReportRequest $request, $id)
    {
        $user = Auth::user();
        $user_company_billing = UserCompanyBilling::where(['user_id' => $user->id, 'company_id' => $id]);


        if ($request->has('search')) {
            $user_company_billing = $user_company_billing
                ->where('earning', 'like', '%' . $request->search . '%')
                ->orWhere('payment_status', 'like', '%' . $request->search . '%');
        }

        $orderBy = 'id';
        $orderDirection = 'desc';
        if ($request->has('orderDirection')) {
            $orderDirection = $request->orderDirection;
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
        }
        $user_company_billing = $user_company_billing->orderBy($orderBy, $orderDirection);


        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('prescription_strings.billing_list_returned'),
                    'status' => '1'
                ])->merge($user_company_billing->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $user_company_billing->get(),
                'message' => __('prescription_strings.billing_list_returned'),
                'status' => '1'
            ]);
        }
    }
    public function downloadClinicReport(UserCompanyBilling $user_company_billing)
    {
        $this->authorize('download', [UserCompanyBilling::class, $user_company_billing]);
        $user = Auth::user();
        $prescriptions = ClientPrescription::where(['sign_by_id' => $user->id])
            ->whereHas('client', function ($query) use ($user_company_billing) {
                $query = $query->where('clients.company_id', $user_company_billing->company_id);
            })
            ->with(['client' => function ($query) {
                $query->setEagerLoads([]);
            }])
            ->whereBetween('signed_at', [Carbon::parse($user_company_billing->start_date)->startOfDay(), Carbon::parse($user_company_billing->end_date)->endOfDay()])
            ->get();

        $from_photo = $user_company_billing->company->profile_photo ? substr(Storage::url('/'), 0, -1) . $user_company_billing->company->profile_photo : '';
        $pdf = Pdf::loadView('pdf.prescription_report', [
            "from" => $user_company_billing->company,
            "from_sign" => $user_company_billing->company->mobile_number ? '+' : '',
            "from_photo" => $from_photo,
            "to" => $user->company,
            "to_sign" => $user->company->mobile_number ? '+' : '',
            "prescriptions" => $prescriptions,
            "currency" => "kr",
            "payment_cycle" => Carbon::parse($user_company_billing->start_date)->format('F Y'),
            "total" => $user_company_billing->earning,
        ]);
        return $pdf->download('report.pdf');
    }
    public function listClinicBilling(ListBillingRequest $request)
    {
        $user = Auth::user();
        $user_companies = $user->companies()->whereNotIn('invite_status', [UserCompany::PENDING, UserCompany::EXPIRED])->with('companyBills', function ($query) use ($user, $request) {
            $query->where('user_id', $user->id)->where(function ($query) use ($request) {
                $query->whereDate('start_date',  $request->start_date)
                    ->whereDate('end_date', $request->end_date)
                    ->orWhereBetween('start_date', [$request->start_date, $request->end_date])
                    ->orWhereBetween('end_date', [$request->start_date, $request->end_date]);
            });
        })->whereHas('companyBills', function ($query) use ($user, $request) {
            $query->where('user_id', $user->id)->where(function ($query) use ($request) {
                $query->whereDate('start_date',  $request->start_date)
                    ->whereDate('end_date', $request->end_date)
                    ->orWhereBetween('start_date', [$request->start_date, $request->end_date])
                    ->orWhereBetween('end_date', [$request->start_date, $request->end_date]);
            });
        })->get();

        $signed_prescription_count = 0;
        foreach ($user_companies as $user_company) {
            foreach ($user_company->companyBills as $bill) {
                $signed_prescription_count += $bill->signed_prescription_count;
            }
        }
        $revenue = 0;
        foreach ($user_companies as $user_company) {
            foreach ($user_company->companyBills as $bill) {
                $revenue += $bill->earning;
            }
        }
        $service_fees = PrescriptionManager::getServiceChargeByCompany($user->company, $signed_prescription_count, $revenue);

        return response()->json([
            'data' => [
                'company' => $user_companies,
                'revenue' => (string)$revenue,
                'service_fees' => (string)$service_fees,
                'clinics' => count($user_companies)
            ],
            'message' => __('prescription_strings.billing_list_returned'),
            'status' => '1',
        ]);
    }
    public function listDoctorBilling(ListDoctorBillingRequest $request)
    {
        $user = Auth::user();
        if ($request->has('user_id')) {
            $this->authorize('viewAny', [UserCompany::class]);
            $bills = UserBilling::where('user_id', $request->user_id);
            $orderBy = 'id';
            $orderDirection = 'desc';
            if ($request->has('orderDirection')) {
                $orderDirection = $request->orderDirection;
            }
            if ($request->has('orderBy')) {
                $orderBy = $request->orderBy;
            }
            $bills = $bills->orderBy($orderBy, $orderDirection);
            $bills = $bills->get();
        } else {
            $bills = $user->bills()->latest()->get();
        }

        return response()->json([
            'data' => $bills,
            'message' => __('prescription_strings.billing_list_returned'),
            'status' => '1',
        ]);
    }
    public function exportBillings(ListDoctorBillingRequest $request)
    {
        if ($request->has('user_id')) {
            $this->authorize('viewAny', [UserCompany::class]);
            $user_billings = UserBilling::where('user_id', $request->user_id)->latest()->get();
            return Excel::download(
                new DoctorBillingExport($user_billings),
                Carbon::now() . '.xlsx'
            );
        }
    }

    public function receiptBill(UserBilling $user_bill)
    {
        $this->authorize('billReceipt', [UserCompany::class, $user_bill]);

        $user = User::findOrFail($user_bill->user_id);
        $company = $user->company;

        $invoice = new Invoice($company, $company->findInvoiceOrFail($user_bill->stripe_payment_id)->asStripeInvoice());

        $response = Http::withoutVerifying()->get($invoice->invoice_pdf);

        $contentType = $response->header('Content-Type');

        $body = $response->getBody();

        $stream = new StreamedResponse(function () use ($body) {
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        });
        return $stream;
    }
    public function updateBillingStatus(UserCompanyBilling $user_company_billing, UpdateBillingRequest $request)
    {
        $this->authorize('update', [UserCompanyBilling::class, $user_company_billing]);
        $current_time = Carbon::now();
        if ($current_time->lt($user_company_billing->end_date)) {
            return response()->json([
                'message' => __('prescription_strings.unable_to_update_before_end_date'),
                'status' => '0',
            ]);
        }

        $user_company_billing->payment_status = $request->status;
        $user_company_billing->save();
        return response()->json([
            'data' => $user_company_billing,
            'message' => __('prescription_strings.bill_status_updated'),
            'status' => '1',
        ]);
    }

    public function view(Company $company)
    {
        $this->authorize('view', [UserCompany::class, $company]);
        $user = Auth::user();
        $user_company = UserCompany::where(['company_id' => $company->id, 'user_id' => $user->id])->with('company')->firstOrFail();
        return response()->json([
            'data' => $user_company,
            'message' => __('prescription_strings.company_returned'),
            'status' => '1',
        ]);
    }

    public function viewPrescription(ClientPrescription $prescription)
    {
        $user = Auth::user();
        if ($prescription->assign_to_id != $user->id) {
            return response()->json([
                'message' => __('prescription_strings.unable_to_access'),
                'status' => '0',
            ]);
        }
        return response()->json([
            'data' => $prescription->loadMissing('signed_by'),
            'message' => __('prescription_strings.prescription_returned'),
            'status' => '1',
        ]);
    }
    public function editFees(UserCompany $user_company, SetFeesRequest $request)
    {
        $this->authorize('update', [UserCompany::class, $user_company]);
        $user_company->price = $request->fees;
        $user_company->save();
        return response()->json([
            'data' => $user_company->refresh(),
            'message' => __('prescription_strings.connection_fees_updated'),
            'status' => '1',
        ]);
    }
    public function stopService(UserCompany $user_company)
    {
        $this->authorize('update', [UserCompany::class, $user_company]);
        UserCompany::setPrescription($user_company);
        $user_company->invite_status = UserCompany::STOPPED;
        $user_company->expire_at = Carbon::now();
        $user_company->save();
        return response()->json([
            'data' => $user_company->refresh(),
            'message' => __('prescription_strings.connection_deleted'),
            'status' => '1',
        ]);
    }

    public function sign(ClientPrescription $prescription, SignPrescriptionRequest $request)
    {
        $prescription = $prescription->loadMissing('company');
        $user = Auth::user();
        $user_company = UserCompany::where(['company_id' => $prescription->company->id, 'user_id' => $user->id, 'invite_status' => UserCompany::ACCEPTED])->first();
        $this->authorize('sign', [UserCompany::class, $prescription, $user_company]);
        if ($request->has('sign_by_bankid') && $request->sign_by_bankid) {
            if (!$user->is_bankid_verified) {
                return response()->json([
                    'message' => __('prescription_strings.bank_id_not_verified'),
                    'status' => '0',
                ]);
            }
            $prescription->is_signed_by_bank_id = 1;
            $prescription->signed_by_bank_id = $user->personal_id;
            $prescription->sign_by_id = $user->id;
            $prescription->signed_at = Carbon::now();
            $prescription->fees = $user_company->price;
            $prescription->signed_by_name = $user->first_name . " " . $user->last_name;
            $prescription->save();
            PrescriptionSignedEvent::dispatch($prescription);
            return response()->json([
                'data' => $prescription->refresh(),
                'message' => __('prescription_strings.prescription_signed'),
                'status' => '1',
            ]);
        }
        if ($request->hasFile('sign')) {
            $file = $this->saveFile($request->file('sign'), 'clients/' . md5($prescription->client_id) . '/prescriptions/sign');
            $prescription->sign = $file->filename;
            $prescription->sign_by_id = $user->id;
            $prescription->signed_at = Carbon::now();
            $prescription->fees = $user_company->price;
            $prescription->signed_by_name = $user->first_name . " " . $user->last_name;
            $prescription->save();
            PrescriptionSignedEvent::dispatch($prescription);
            return response()->json([
                'data' => $prescription->refresh(),
                'message' => __('prescription_strings.prescription_signed'),
                'status' => '1',
            ]);
        }
    }

    public function sendInvitation(CreateUserCompanyRequest $request)
    {
        $this->authorize('sendInvitation', [UserCompany::class]);
        $company_id = Auth::user()->company_id;
        $user = User::where(['email' => $request->email])->verifiedEmail()->firstOrFail();

        if ($user->company_id == $company_id) {
            return response()->json([
                'message' => __('prescription_strings.unable_to_connect'),
                'status' => '0',
            ]);
        }
        if (CompanyPlatform::where(['company_id' => $user->company_id, 'platform' => CompanyPlatform::PRESCRIPTION])->doesntExist()) {
            return response()->json([
                'message' => __('prescription_strings.not_registered_mal'),
                'status' => '0',
            ]);
        }
        if ($user->email != $user->company->email) {
            return response()->json([
                'message' => __('prescription_strings.not_registered_mal'),
                'status' => '0',
            ]);
        }
        $connection = UserCompany::where(['user_id' => $user->id, 'company_id' => $company_id])->first();
        $language = Setting::getSetting($user->company, Setting::CUSTOMER_LANGUAGE)?->value;
        if ($connection) {
            //Resend Invitation Logic
            if ($connection->invite_status == UserCompany::ACCEPTED) {
                return response()->json([
                    'message' => __('prescription_strings.connection_already_exist'),
                    'status' => '0',
                ]);
            }
            $current_time = Carbon::now();
            if ($current_time->lt($connection->expire_at)) {
                return response()->json([
                    'message' => __('prescription_strings.invite_link_already_sent'),
                    'status' => '0',
                ]);
            }
            $connection->invite_status = UserCompany::PENDING;
            $connection->expire_at = Carbon::now()->addDay();
            $connection->save();


            Mail::to($user->email)->locale($language ?? app()->getLocale())->send(new DoctorInviteMail($connection->id, $user));
            return response()->json([
                'message' => __('prescription_strings.invite_link_resent'),
                'status' => '1',
            ]);
        }
        //Create new connection of company - user(doctor)
        $connection = UserCompany::create([
            'user_id' => $user->id,
            'company_id' => $company_id,
            'price' => null,
            'invite_status' => UserCompany::PENDING,
            'expire_at' => Carbon::now()->addDay(),
        ]);
        Mail::to($user->email)->locale($language ?? app()->getLocale())->send(new DoctorInviteMail($connection->id, $user));
        return response()->json([
            'message' => __('prescription_strings.invite_link_sent'),
            'status' => '1',
        ]);
    }

    public function acceptInvitation($key)
    {
        $user_company_id = Crypt::decrypt($key);
        $connection = UserCompany::findOrFail($user_company_id);
        if ($connection->invite_status == UserCompany::DELETED) {
            return response()->json([
                'message' => __('prescription_strings.invite_link_expired'),
                'status' => '0',
            ]);
        }
        if ($connection->invite_status == UserCompany::ACCEPTED) {
            return response()->json([
                'message' => __('prescription_strings.invite_link_already_accepted'),
                'status' => '0',
            ]);
        }
        $current_time = Carbon::now();
        if ($current_time->gt($connection->expire_at)) {
            return response()->json([
                'message' => __('prescription_strings.invite_link_expired'),
                'status' => '0',
            ]);
        }
        $connection = $connection->loadMissing(['user', 'company']);
        $company = $connection->user->company;
        $default_fees = Setting::getSetting($company, Setting::DEFAULT_FEES_PRESCRIBER);
        $connection->invite_status = UserCompany::ACCEPTED;
        $connection->price = $default_fees->value;
        $connection->save();
        //TODO::send mail for invite accept if needed
        return response()->json([
            'data' => ['doctor_name' => $connection->user->first_name . ' ' . $connection->user->last_name, 'clinic_name' => $connection->company->company_name],
            'message' => __('prescription_strings.invite_accepted'),
            'status' => '1',
        ]);
    }

    public function deleInvitation(UserCompany $user_company)
    {
        $this->authorize('delete', [UserCompany::class, $user_company]);
        UserCompany::setPrescription($user_company);
        $user_company->invite_status = UserCompany::DELETED;
        $user_company->expire_at = Carbon::now();
        $user_company->save();
        return response()->json([
            'message' => __('prescription_strings.connection_deleted'),
            'status' => '1',
        ]);
    }

    public function sendOtp()
    {
        // $this->authorize('otpVerification', [UserCompany::class]);
        $user = Auth::user();
        $digits = 4;
        $otp = rand(pow(10, $digits - 1), pow(10, $digits) - 1);
        $user->otp = $otp;
        $user->save();

        $language = Setting::getSetting($user->company, Setting::CUSTOMER_LANGUAGE)?->value;

        Mail::to($user->email)->locale($language ?? app()->getLocale())->send(new DoctorVerificationMail($user));
        return response()->json([
            'message' => __('prescription_strings.otp_sent'),
            'status' => '1',
        ]);
    }

    public function verificationOtp(VerificationOtpRequest $request)
    {
        // $this->authorize('otpVerification', [UserCompany::class]);
        $user = Auth::user();
        if ($user->otp != $request->otp) {
            return response()->json([
                'message' => __('prescription_strings.otp_invalid'),
                'status' => '0',
            ]);
        }
        $user->email_verified_at = Carbon::now();
        $user->otp = null;
        $user->save();

        return response()->json([
            'message' => __('prescription_strings.verification_done'),
            'status' => '1',
        ]);
    }

    public function updatePricing(UpdateDoctorPricingRequest $request, Company $company)
    {

        $company_platform = CompanyPlatform::where('platform', CompanyPlatform::PRESCRIPTION)
            ->where('company_id', $company->id)
            ->first();

        if ($company_platform->price == $request->price && $company_platform->price_id == $request->stripe_id) {

            if (UpcomingPlatformFees::where('company_id', $company->id)->where('start_date', Carbon::now()->addMonthsNoOverflow()->startOfMonth()->format('Y-m-d'))->exists()) {
                UpcomingPlatformFees::where('company_id', $company->id)->where('start_date', Carbon::now()->addMonthsNoOverflow()->startOfMonth()->format('Y-m-d'))->delete();
                return response()->json([
                    'message' => __('prescription_strings.price_updated'),
                    'status' => '1',
                ]);
            }

            return response()->json([
                'message' => __('prescription_strings.price_already_updated'),
                'status' => '0',
            ]);
        }
        UpcomingPlatformFees::updateOrCreate([
            'company_id' => $company->id,
            'start_date' => Carbon::now()->addMonthsNoOverflow()->startOfMonth()->format('Y-m-d'),
        ], [
            'price' => $request->price,
            'price_id' => $request->stripe_id,
        ]);
        return response()->json([
            'message' => __('prescription_strings.price_updated'),
            'status' => '1',
        ]);
    }
}