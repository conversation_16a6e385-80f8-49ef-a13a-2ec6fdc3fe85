<?php

namespace App\Http\Controllers\Api\v3;

use App\Activity;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\ZIP\ZipServiceInterface;
use App\File;
use App\GeneralTemplate;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Managment\CreateCompanyDocumentRequest;
use App\Http\Requests\v3\Managment\GetCompanyDocumentRequest;
use App\Http\Requests\v3\Managment\IndexCompanyDocumentRequest;
use App\Http\Requests\v3\Managment\UpdateCompanyDocumentRequest;
use App\Jobs\QMSDocumentZipDownloadJob;
use App\Models\CompanyDocument;
use App\Models\GeneralTemplateQuestion;
use App\Traits\SaveFile;
use App\UserNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CompanyDocumentController extends Controller
{
    use SaveFile;

    public function index(IndexCompanyDocumentRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $documents = CompanyDocument::where('company_id', $company->id)->addVersion()->with(['signed_by']);

        if ($request->has('filter')) {
            switch ($request->filter) {
                case 'withTrashed':
                    $documents = $documents->withTrashed();
                    break;
                case 'onlyTrashed':
                    $documents = $documents->onlyTrashed();
                    break;
                default:
                    break;
            }
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            $documents = $documents->orderBy($orderBy, $orderDirection);
        } else {
            $documents = $documents->orderBy('created_at', 'desc');
        }

        if ($request->search) {
            $search = $request->search;
            $documents = $documents->whereLike(['title'], $search);
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('management_strings.company_documents_returned'),
                    'status' => '1',
                ])->merge($documents->paginate($request->input('per_page')))
            );
        }

        return response()->json([
            'data' => $documents->get(),
            'message' => __('management_strings.company_documents_returned'),
            'status' => '1',
        ]);
    }

    public function store(CreateCompanyDocumentRequest $request, PDFServiceInterface $pdfService, ZipServiceInterface $zipService)
    {
        $user = Auth::user();
        $company = $user->company;

        $this->authorize("create", [CompanyDocument::class]);

        return DB::transaction(function () use ($request, $company, $user, $pdfService, $zipService) {

            $company_document = CompanyDocument::create([
                'company_id' => $company->id,
                'modelable_type' => $request->general_template_id ? GeneralTemplate::class : null,
                'modelable_id' => $request->general_template_id,
                'title' => $request->title,
                'process' => $request->process,
            ]);

            $activity = activity()
                ->performedOn($company_document);
            $activity = $activity->by($user);
            $activity->log("{$company_document->title} company document has been created by {$user->first_name} {$user->last_name}");

            $general_template = GeneralTemplate::where('id', $request->general_template_id)->first();

            $file = $this->saveFile($request->file('signature'), 'company/' . md5($company->id) . "/documents/signature");

            $data = $request->data;
            $questions = [];

            $documentData = $company_document->versions()->create([
                'company_id' => $company->id,
                'pdf' => "",
                'questions' => "",
                'response' => "",
                "sign" => $file->filename,
                "signed_at" => now(),
                "signed_by_id" => $user->id,
                'process' => $request->process,
            ]);

            $zip_data = collect();

            foreach ($general_template?->questions ?? [] as $index => $question) {
                array_push($questions, (object) ['question' => $question->question, 'type' => $question->type]);

                if ($question->type == GeneralTemplateQuestion::IMAGE) {
                    $file = $this->saveFile($request->file("data.$index"), 'company/' . md5($company->id) . "/documents/images");
                    $data[$index] = $file->filename;
                }
                if ($question->type == GeneralTemplateQuestion::FILE_UPLOAD) {
                    $uploaded_files = $request->file("data.$index");
                    if ($uploaded_files) {
                        $files = [];
                        foreach ($uploaded_files['files'] as $i => $uploaded_file) {
                            $file = $this->saveFile($uploaded_file, 'company/' . md5($company->id) . "/documents/files");
                            $documentData->files()->save($file);
                            array_push($files, (object) ['file_id' => $file->id, 'file_name' => $request->input("data.$index.file_names.$i") ?? '']);

                            $zip_data->push([
                                'path' => $file->filename,
                                'zip_path' => "Files/Q" . ($index + 1) . "/" . $request->input("data.$index.file_names.$i") ?? '',
                                "delete" => false,
                            ]);
                        }
                        $data[$index] = (object) ['files' => $files];
                    }
                }
            }

            if (!$general_template) {
                array_push($questions, (object) ['question' => "", 'type' => GeneralTemplateQuestion::HTML_EDITOR]);
            }

            $documentData->response = collect($data)->values();
            $documentData->questions = collect($questions)->values();

            $filename = $this->generateFilePath('company/' . md5($company->id) . "/documents/pdfs", $user, null, 'pdf');
            $file = $pdfService->company($company)->document(
                $company_document,
                $documentData,
            )->saveFile($user, $filename);

            if ($zip_data->count()) {
                $zip_path = $this->generateFilePath('company/' . md5($company->id) . "/documents/zip", $user, null, "zip");

                $zip_data->push([
                    'path' => $file->filename,
                    'zip_path' => "{$company_document->title}.pdf",
                    "delete" => false,
                ]);

                // Generate and store zip
                $file = $zipService->make($zip_data)->saveFile($user, $zip_path);
            }

            $documentData->pdf = $file->filename;

            $documentData->save();

            return response()->json([
                'data' => $company_document,
                'message' => __('management_strings.company_documents_created'),
                'status' => '1'
            ]);
        });
    }

    public function update(CompanyDocument $company_document, UpdateCompanyDocumentRequest $request, PDFServiceInterface $pdfService, ZipServiceInterface $zipService)
    {
        $user = Auth::user();
        $company = $user->company;

        $this->authorize("update", $company_document);

        return DB::transaction(function () use ($request, $company, $user, $company_document, $pdfService, $zipService) {
            $company_document_version = $company_document->version;

            $file = $this->saveFile($request->file('signature'), 'company/' . md5($company->id) . "/documents/signature");

            $data = $request->data;

            if ($request->has('process')) {
                $company_document->process = $request->process;
                $company_document->save();

                $activity = activity()
                    ->performedOn($company_document);
                $activity = $activity->by($user);
                $activity->log("{$company_document->title} company document has been updated by {$user->first_name} {$user->last_name}");
            }
            $documentData = $company_document->versions()->create([
                'company_id' => $company->id,
                'pdf' => "",
                'questions' => $company_document_version?->questions,
                'response' => "",
                "sign" => $file->filename,
                "signed_at" => now(),
                "signed_by_id" => $user->id,
                'process' => $company_document->process,
            ]);

            $zip_data = collect();

            foreach ($company_document_version?->questions ?? [] as $index => $question) {
                if ($question->type == GeneralTemplateQuestion::IMAGE) {
                    $file = $this->saveFile($request->file("data.$index"), 'company/' . md5($company->id) . "/documents/images");
                    $data[$index] = $file->filename;
                }
                if ($question->type == GeneralTemplateQuestion::FILE_UPLOAD) {
                    $uploaded_files = $request->file("data.$index");
                    if ($uploaded_files) {
                        $files = [];
                        foreach ($uploaded_files['files'] as $i => $uploaded_file) {
                            $file = $this->saveFile($uploaded_file, 'company/' . md5($company->id) . "/documents/files");
                            $documentData->files()->save($file);
                            array_push($files, (object) ['file_id' => $file->id, 'file_name' => $request->input("data.$index.file_names.$i") ?? '']);

                            $zip_data->push([
                                'path' => $file->filename,
                                'zip_path' => "Files/Q" . ($index + 1) . "/" . $request->input("data.$index.file_names.$i") ?? '',
                                "delete" => false,
                            ]);
                        }
                        $data[$index] = (object) ['files' => $files];
                    }
                }
            }

            $documentData->response = collect($data)->values();

            $filename = $this->generateFilePath('company/' . md5($company->id) . "/documents/pdfs", $user, null, 'pdf');
            $file = $pdfService->company($company)->document(
                $company_document,
                $documentData,
            )->saveFile($user, $filename);

            if ($zip_data->count()) {
                $zip_path = $this->generateFilePath('company/' . md5($company->id) . "/documents/zip", $user, null, "zip");

                $zip_data->push([
                    'path' => $file->filename,
                    'zip_path' => "{$company_document->title}.pdf",
                    "delete" => false,
                ]);

                // Generate and store zip
                $file = $zipService->make($zip_data)->saveFile($user, $zip_path);
            }

            $documentData->pdf = $file->filename;

            $documentData->save();

            return response()->json([
                'data' => $documentData,
                'message' => __('management_strings.company_documents_updated'),
                'status' => '1'
            ]);
        });
    }

    public function restore()
    {
        $company_document = CompanyDocument::withTrashed()->find(request('company_document_id'));

        $this->authorize('restore', $company_document);

        if ($company_document) {
            if ($company_document->restore()) {
                return response()->json([
                    'message' => __('management_strings.company_documents_restored'),
                    'status' => '1'
                ]);
            } else {
                return response()->json([
                    'message' => __('management_strings.company_documents_restoration_failed'),
                    'status' => '0'
                ]);
            }
        } else {
            return response()->json([
                'message' => __('management_strings.does_not_exists_any_company_document'),
                'status' => '0'
            ]);
        }
    }

    public function delete(CompanyDocument $company_document)
    {
        $this->authorize("delete", $company_document);

        $company_document->delete();

        return response()->json([
            'message' => __('management_strings.company_documents_inactivated'),
            'status' => '1'
        ]);
    }

    public function get($company_document_id, GetCompanyDocumentRequest $request)
    {
        $company_document = CompanyDocument::withTrashed()->with('version')->addVersion()->where('id', $company_document_id)->firstOrFail();

        $this->authorize('view', [$company_document]);
        $user = Auth::user();

        if ($request->has('view_company_document') && $request->view_company_document) {
            $activity = activity()
                ->performedOn($company_document);
            $activity = $activity->by($user);
            $activity->log("{$company_document->title} company document has been viewed by {$user->first_name} {$user->last_name}");
        }
        return response()->json([
            'data' => $company_document,
            'message' => __('management_strings.company_documents_returned'),
            'status' => '1'
        ]);
    }

    public function logs(CompanyDocument $company_document, Request $request)
    {
        $activities = Activity::where(function ($query) use ($company_document) {
            $query = $query->where(function ($query) use ($company_document) {
                $query->where('subject_type', CompanyDocument::class)->where('subject_id', $company_document->getKey());
            });
        });

        if (request()->has('orderBy')) {
            $activities = $activities->orderBy(request()->input('orderBy'), request()->input('orderDirection', 'asc'));
        } else {
            $activities = $activities->latest();
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('management_strings.company_documents_logs_returned'),
                    'status' => '1',
                ])->merge($activities->paginate($request->input('per_page')))
            );
        }

        return response()->json([
            'data' => $activities->get(),
            'message' => __('management_strings.company_documents_logs_returned'),
            'status' => '1',
        ]);
    }

    public function download(Request $request)
    {
        $this->authorize('download', [CompanyDocument::class]);

        $company = Auth::user()->company;

        return DB::transaction(function () use ($request, $company) {

            $cacheKey = 'qms_document_zip_' . $company->id;
            $cache_exists = Cache::get($cacheKey);

            if ($cache_exists === 'working') {
                throw new HttpException(208, __('zip.zip_generating'));
            }

            if ($cache_exists) {
                throw new HttpException(208, __('zip.zip_in_queue'));
            }

            $force = $request->input('force');

            if (!$force) {
                $zip_already_exists = File::where('fileable_type', 'qms_documents_zip')->where('fileable_id', $company->id)->first();

                if ($zip_already_exists) {
                    return response()->json([
                        'status' => '0',
                        'data' => $zip_already_exists,
                        'message' => __('zip.zip_already_present'),
                    ], 210);
                }
            }

            Cache::put($cacheKey, 'queue', 86400);
            UserNotification::create([
                'user_id' => Auth::user()->id,
                'title' => __('zip.zip_qms_document.zip_generating'),
                'description' => __('zip.zip_qms_document.zip_in_queue'),
                'is_read' => 0
            ]);

            QMSDocumentZipDownloadJob::dispatch($company, Auth::user(), app()->getLocale());

            return response()->json([
                'status' => '1',
                'message' => __('zip.zip_accepted'),
            ]);
        });
    }
}
