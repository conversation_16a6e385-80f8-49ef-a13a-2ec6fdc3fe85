<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\v3;

use App\Client;
use App\Contracts\Services\Fortnox\FortnoxServiceInterface;
use App\Exceptions\Fortnox\FortnoxCustomerAlreadyExistsException;
use App\Exceptions\Fortnox\FortnoxNotAuthenticatedException;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Fortnox\CreateInvoiceRequest;
use App\Http\Requests\v3\Fortnox\GetInvoiceRequest;
use App\Models\FortnoxInvoice;
use App\Setting;
use App\Traits\ApiResponser;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class FortnoxInvoiceController extends Controller
{
    use ApiResponser;

    public function __construct(
        private FortnoxServiceInterface $fortnoxService
    ) {}

    /**
     * Create a new Fortnox invoice
     * Will create customer first if client doesn't have one
     *
     * @param CreateInvoiceRequest $request
     * @param Client $client
     * @return JsonResponse
     */
    public function store(CreateInvoiceRequest $request, Client $client): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            // Authorize access to this client
            $this->authorize('create', FortnoxInvoice::class);

            // Ensure company is connected to Fortnox
            if (!$company->fortnox_auth) {
                return response()->json([
                    'message' => __('strings.fortnox_company_must_be_connected'),
                    'status' => '0'
                ], 401);
            }

            // Ensure client has a Fortnox customer before creating invoice
            if (empty($client->fortnox_customer_id)) {
                try {
                    $customer = $this->fortnoxService->createCustomer($client);
                } catch (FortnoxCustomerAlreadyExistsException $e) {
                    report($e);
                }
            }

            // Create the invoice
            $invoice = $this->fortnoxService->createInvoice(
                client: $client,
                data: $request->all(),
            );

            return response()->json([
                'data' => $invoice,
                'message' => __('strings.fortnox_invoice_created_successfully'),
                'status' => '1'
            ], 201);
        } catch (FortnoxNotAuthenticatedException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'status' => '0'
            ], 401);
        } catch (Exception $e) {
            report($e);
            throw $e;

            return response()->json([
                'message' => __('strings.fortnox_invoice_creation_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    /**
     * Get invoice details
     *
     * @param FortnoxInvoice $fortnoxInvoice
     * @return JsonResponse
     */
    public function show(FortnoxInvoice $fortnoxInvoice): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            $this->authorize('view', $fortnoxInvoice);

            // Check if invoice belongs to the same company
            if ($fortnoxInvoice->company_id !== $company->id) {
                return response()->json([
                    'message' => 'Unauthorized access to invoice',
                    'status' => '0'
                ], 403);
            }

            // Load relationships
            $fortnoxInvoice->load(['client', 'items.user', 'user']);

            return response()->json([
                'data' => $fortnoxInvoice,
                'message' => __('strings.fortnox_invoice_retrieved_successfully'),
                'status' => '1'
            ]);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_invoice_retrieval_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    /**
     * List all invoices for the company
     *
     * @param GetInvoiceRequest $request
     * @return JsonResponse
     */
    public function index(GetInvoiceRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;


            // Authorize access to this company
            $this->authorize('viewAny', FortnoxInvoice::class);

            // Get invoices for the company with pagination
            $invoices = FortnoxInvoice::where('company_id', $company->id)
                ->with(['user']);

            if ($request->has('date')) {
                $invoices = $invoices->whereDate('created_at', $request->date);
            }

            if ($request->has('client_id')) {
                $invoices = $invoices->where('client_id', $request->input('client_id'));
            }

            if ($request->has('with')) {
                $invoices = $invoices->with($request->input('with', []));
            }

            $isPaginated = $request->has('search') || in_array($request->orderBy, ['fortnox_document_number', 'total', 'customer_name', 'user.full_name']);

            if ($isPaginated) {
                $invoices = $invoices->lazy();
            }

            if ($request->has('search')) {
                $search = strtolower($request->input('search'));
                $search = preg_replace('/\s\s+/', ' ', $search);
                //CURRENT TIME-DATE BASED ON COMPANY TIMEZONE
                $dateFormat = Setting::getDateTimeFormat($company);

                $invoices = $invoices->filter(function ($query) use ($search, $dateFormat) {
                    $fields = [
                        $query->created_at->format($dateFormat),
                        $query->total,
                        $query->customer_name,
                        $query->user->fullName(),
                        $query->fortnox_document_number,
                    ];

                    return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
                });
            }

            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

            // Collection level sorting
            if ($isPaginated && $request->has('orderBy')) {
                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

                $invoices =  $invoices->sortBy($orderBy, $sortExtra, $isDescOrder);
            }
            if ($isPaginated && !$request->has('orderBy')) {
                $invoices =  $invoices->sortBy('created_at', $sortExtra, $orderDirection);
            }

            // Database level sorting
            if (!$isPaginated && $request->has('orderBy')) {
                $invoices = $invoices->orderBy($orderBy, $orderDirection);
            }
            if (!$isPaginated && !$request->has('orderBy')) {
                $invoices = $invoices->latest();
            }

            if ($request->has('page')) {
                $per_page = $request->input('per_page', 10);

                return response()->json(
                    collect([
                        'message' => __('pos_strings.company_product_receipt_list_return'),
                        'status' => '1',
                    ])->merge($isPaginated ? $this->paginate($invoices) : $invoices->paginate($per_page))
                );
            }

            return response()->json([
                'data' => $isPaginated ? $invoices->values() : $invoices->lazy(),
                'message' => __('pos_strings.fortnox_invoices_retrieved_successfully'),
                'status' => '1'
            ]);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_invoices_listing_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    public function download(FortnoxInvoice $fortnoxInvoice)
    {
        try {
            // Authorize access to this company
            $this->authorize('download', $fortnoxInvoice);

            $response = $this->fortnoxService->downloadInvoice($fortnoxInvoice);

            return response()->streamDownload(function () use ($response) {
                while (!$response->eof()) {
                    echo $response->read(1024);
                }
            }, "invoice_{$fortnoxInvoice->fortnox_document_number}.pdf", [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="invoice.pdf"',
            ]);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_invoice_download_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }
}
