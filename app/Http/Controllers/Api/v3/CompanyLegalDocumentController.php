<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use App\Models\CompanyLegalDocument;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class CompanyLegalDocumentController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $company_legal_documents = CompanyLegalDocument::where('company_id', $company->id);

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.legal_documents_returned_successfully'),
                    'status' => '1',
                ])->merge($company_legal_documents->paginate($request->input('per_page')))
            );
        }

        return response()->json([
            'data' => $company_legal_documents->get(),
            'message' => __('strings.legal_documents_returned_successfully'),
            'status' => '1',
        ]);
    }

    public function getView(Request $request)
    {
        $company = Auth::user()->company;
        $type = $request->type ?? CompanyLegalDocument::PUBLIC;
        $super_user_name = $company->super_user->fullName();

        $language_to_use = $request->lang ?? 'en';

        $view_name = match ($type) {
            CompanyLegalDocument::PUBLIC => 'public_doc_' . $language_to_use,
            CompanyLegalDocument::DPA => 'dpa_doc_' . $language_to_use,
            CompanyLegalDocument::SUPPLIER => 'supplier_doc_' . $language_to_use,
            default => 'public_doc_' . $language_to_use,
        };

        $data =  [
            "company_name" => $company->company_name,
            "company_address" => $company->address(),
            "company_email" => $company->email,
            "date_signed" => Carbon::parse($company->created_at)->format('Y-m-d'),
            "representative_name" => $company->company_name,
            "platform_description" => $language_to_use == 'en' ? "manage data, analyze information, administer processes" : "hantera data, analysera information, administrera processer",
            "superuser_name" => $super_user_name,
        ];
        return response([
            "data" => view('views.legalDocuments.' . $view_name, ['data' => $data])->render(),
            "status" => '1',
            "message" => __('strings.legal_documents_returned_successfully')
        ]);
    }

    public function store()
    {
        $user = Auth::user();
        $company = $user->company;
        if (!$company) {
            return response()->json([
                'message' => __('strings.something_wrong'),
                'status' => '0',
            ]);
        }
        $legal_document = app(CompanyLegalDocument::class);
        $legal_document->generateLegalDocument($company);
        return response()->json([
            'message' => __('strings.legal_documents_returned_successfully'),
            'status' => '1',
        ]);
    }
}