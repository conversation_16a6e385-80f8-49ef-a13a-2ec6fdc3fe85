<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyProductCategory;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Product\Category\GetCompanyProductCategoryRequest;
use App\Http\Requests\v3\Product\Category\ShiftCompanyProductCategoryRequest;
use App\Http\Requests\v3\Product\Category\StoreCompanyProductCategoryRequest;
use App\Http\Requests\v3\Product\Category\UpdateCompanyProductCategoryRequest;
use App\Traits\ApiResponser;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CompanyProductCategoryController extends Controller
{
    use ApiResponser;

    public function index(GetCompanyProductCategoryRequest $request)
    {
        $this->authorize('viewAny', [CompanyProductCategory::class]);

        $user = Auth::user();

        $categories = CompanyProductCategory::where('company_id', $user->company_id);

        if ($request->has('filter') && ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed')) {
            $categories = $categories->{$request->filter}();
        }

        $isPaginated = $request->has('search') || in_array($request->orderBy, ['name']);

        if ($isPaginated) {
            $categories = $categories->lazy();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $categories = $categories->filter(function ($query) use ($search) {
                $fields = [
                    $query->name,
                ];

                return collect($fields)->reduce(fn ($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'desc');
        $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

        // Collection level sorting
        if ($isPaginated && $request->has('orderBy')) {
            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

            $categories =  $categories->sortBy($orderBy, $sortExtra, $isDescOrder);
        }

        // Database level sorting
        if (!$isPaginated && !$request->has('orderBy')) {
            $categories = $categories->sorted();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('pos_strings.company_product_category_list_return'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($categories) : $categories->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $isPaginated ? $categories->values() : $categories->lazy(),
            'message' => __('pos_strings.company_product_category_list_return'),
            'status' => '1'
        ]);
    }

    public function store(StoreCompanyProductCategoryRequest $request)
    {
        $this->authorize('create', [CompanyProductCategory::class]);

        $user = Auth::user();

        $category = CompanyProductCategory::create([
            'company_id' => $user->company_id,
            'name' => $request->name,
        ]);

        return response()->json([
            'data' => $category,
            'message' => __('pos_strings.company_product_category_created'),
            'status' => '1'
        ]);
    }

    public function update(CompanyProductCategory $product_category_with_trashed, UpdateCompanyProductCategoryRequest $request)
    {
        $this->authorize('update', $product_category_with_trashed);

        if ($request->has('name')) {
            $product_category_with_trashed->name = $request->name;
        }

        $product_category_with_trashed->save();

        return response()->json([
            'data' => $product_category_with_trashed,
            'message' => __('pos_strings.company_product_category_updated'),
            'status' => '1'
        ]);
    }

    public function activeToggle(CompanyProductCategory $product_category_with_trashed)
    {
        $category = $product_category_with_trashed;

        if ($category->trashed()) {
            $this->authorize('restore', $category);

            $category->restore();

            return response()->json([
                'message' => __('pos_strings.company_product_category_active'),
                'status' => '1'
            ]);
        }

        $this->authorize('delete', $category);

        $category->delete();

        return response()->json([
            'message' => __('pos_strings.company_product_category_in_active'),
            'status' => '1'
        ]);
    }

    public function shiftCategory(CompanyProductCategory $product_category, ShiftCompanyProductCategoryRequest $request)
    {
        $this->authorize('update', $product_category);

        $isNext = $request->level > 0;

        $new_company_product_category = $product_category->siblings($isNext,  abs($request->level))->get()?->last();

        if (!$new_company_product_category) {
            throw new Exception(__("pos_strings.no_category_available"));
        }

        $product_category->move($isNext ? 'moveAfter' : 'moveBefore', $new_company_product_category);

        return response()->json([
            'data' => $product_category->refresh(),
            'message' => __('strings.company_product_category_updated'),
            'status' => '1'
        ]);
    }
}
