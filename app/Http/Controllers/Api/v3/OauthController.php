<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Calendar\CreateCalendarRequest;
use App\Http\Requests\v3\Calendar\GetCalendarListRequest;
use App\Models\CompanyBookingMetadata;
use App\Models\UserCalendar;
use App\Models\UserOauthToken;
use App\Traits\Booking\BookingGoogleCalendarManager;
use App\Traits\OAuth\Google\GoogleAuth;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class OauthController extends Controller
{

    public function index(GetCalendarListRequest $request)
    {
        $user = Auth::user();

        $data = UserCalendar::where('user_id', $user->id);


        $data = $data->get();
        $user_email = null;
        $token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        if ($token) {
            $user_email = $token->email;
        }
        return response()->json([
            'email' => $user_email,
            'data' => $data,
            'status' => '1',
            'message' => __('oauth_calendar.calendar_lits_returned'),
        ]);
    }

    public function store(CreateCalendarRequest $request)
    {
        $user = Auth::user();
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();

        if (!$oauth_token) {
            return response()->json([
                'status' => '0',
                'message' => __('oauth_calendar.google_token_not_found'),
            ]);
        }

        if (UserCalendar::where(['user_id' => $user->id, 'type' => $request->type])->exists()) {
            return response()->json([
                'status' => '0',
                'message' => __('oauth_calendar.same_calendar_already_exists'),
            ]);
        }

        $user_calendar = UserCalendar::create([
            'user_id' => $user->id,
            'type' => $request->type,
            'calendar_id' => null,
            'name' => $request->name,
        ]);

        if (!$user_calendar->calendar_id) {
            $oauth_token = GoogleAuth::refreshTokenForUser($user);
            $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
            $google_calendar = GoogleAuth::createNewCalendar($google_auth, $user_calendar->name);
            $user_calendar->calendar_id = $google_calendar->getCalendarId();
            $user_calendar->save();
        }

        dispatch(function () use ($user) {
            $bookings = $user->bookings()->where('start_at', '>=', Carbon::now())
                ->where('is_cancelled', 0)
                ->get();

            foreach ($bookings as $booking) {
                BookingGoogleCalendarManager::syncWithBooking($booking);
            }
        });

        return response()->json([
            'data' => $user_calendar,
            'status' => '1',
            'message' => __('oauth_calendar.calendar_created'),
        ]);
    }

    public function delete(UserCalendar $google_calendar)
    {
        $user = Auth::user();
        $oauth_token = $user->oauth_tokens()->where('provider', UserOauthToken::GOOGLE)->first();
        if (!$oauth_token) {
            return response()->json([
                'status' => '0',
                'message' => __('oauth_calendar.google_token_not_found'),
            ]);
        }

        if ($google_calendar->calendar_id) {
            $oauth_token = GoogleAuth::refreshTokenForUser($user);

            $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
            GoogleAuth::deleteCalendarById($google_auth, $google_calendar->calendar_id);
        }
        $google_calendar->delete();
        return response()->json([
            'status' => '1',
            'message' => __('oauth_calendar.calendar_deleted'),
        ]);
    }

    public function connectGoogleCalender(Request $request)
    {
        $user = Auth::user();
        $oauth_token = $user->oauth_tokens()->where(['provider' => UserOauthToken::GOOGLE, 'type' => UserOauthToken::CALENDAR])->first();

        if (!$oauth_token) {
            $link = Socialite::driver('google')
                ->scopes(['https://www.googleapis.com/auth/calendar'])
                ->stateless()
                ->with([
                    'access_type' => 'offline', // Request offline access for refresh token
                    'prompt' => 'consent',
                    'state' => $user->id
                ])
                ->redirect()->getTargetUrl();
            if ($request->has('public_calendar_name') && $request->public_calendar_name) {
                UserCalendar::updateOrCreate([
                    'user_id' => $user->id,
                    'type' => UserCalendar::PUBLIC,
                    'name' => $request->public_calendar_name,
                ]);
            }
            if ($request->has('private_calendar_name') && $request->private_calendar_name) {
                UserCalendar::updateOrCreate([
                    'user_id' => $user->id,
                    'type' => UserCalendar::PRIVATE,
                    'name' => $request->private_calendar_name,
                ]);
            }
            return response()->json([
                'data' => $link,
                'status' => '2',
                'message' => __('strings.connect_google_calendar'),
            ]);
        }

        $social_user = Socialite::driver('google')->refreshToken($oauth_token->refresh_token);
        return response()->json([
            'message' =>  __('strings.connect_google_calendar'),
            'status' => '1',
            'data' => $social_user
        ]);
    }

    public function callback(Request $request)
    {
        $user = User::findOrFail($request->state);

        try {
            $social_user = Socialite::driver('google')->stateless()->with([
                'access_type' => 'offline', // Request offline access for refresh token
                'prompt' => 'consent',
                'state' => $user->id
            ])->user();
        } catch (\Throwable $th) {
            $redirect_url = config('google_calendar.portal_url');
            return redirect($redirect_url);
        }
        if (!in_array('https://www.googleapis.com/auth/calendar', $social_user->approvedScopes)) {
            $redirect_url = config('google_calendar.portal_url');
            return redirect($redirect_url);
        }


        if (!$social_user->refreshToken) {
            return response()->json([
                'message' => __('strings.failed_to_connect_google_calendar'),
                'status' => '0'
            ]);
        }
        UserOauthToken::updateOrCreate([
            'user_id' => $user->id,
            'provider' => UserOauthToken::GOOGLE,
            'type' => UserOauthToken::CALENDAR,
        ], [
            'email' => $social_user?->email,
            'scopes' => $social_user->approvedScopes,
            'access_token' => $social_user->token,
            'refresh_token' => $social_user->refreshToken,
            'token_expires_at' => Carbon::now()->addMinutes(58),
        ]);
        $user_calendars = UserCalendar::where('user_id', $user->id)->get();
        foreach ($user_calendars as $user_calendar) {
            if (!$user_calendar->calendar_id) {
                $oauth_token = GoogleAuth::refreshTokenForUser($user);
                $google_auth = GoogleAuth::createOAuthClient($oauth_token->access_token);
                $google_calendar = GoogleAuth::createNewCalendar($google_auth, $user_calendar->name);
                $user_calendar->calendar_id = $google_calendar->getCalendarId();
                $user_calendar->save();
            }
        }
        dispatch(function () use ($user) {
            $bookings = $user->bookings()->where('start_at', '>=', Carbon::now())
                ->where('is_cancelled', 0)
                ->get();

            CompanyBookingMetadata::whereHas('booking', function ($query) use ($user) {
                $query = $query->where('user_id', $user->id)
                    ->where('start_at', '>=', Carbon::now());
            })->delete();

            foreach ($bookings as $booking) {
                BookingGoogleCalendarManager::syncWithBooking($booking);
            }
        });
        $redirect_url = config('google_calendar.portal_url');
        return redirect($redirect_url);
    }
}