<?php

namespace App\Http\Controllers\Api\v3;

use App\GeneralTemplate;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Managment\CreateCompanyDocumentRequest;
use App\Http\Requests\v3\Managment\IndexCompanyDocumentRequest;
use App\Http\Requests\v3\Managment\IndexCompanyDocumentVersionRequest;
use App\Models\CompanyDocument;
use App\Models\CompanyDocumentData;
use App\Models\GeneralTemplateQuestion;
use App\Traits\SaveFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CompanyDocumentVersionController extends Controller
{
    use SaveFile;

    public function index(CompanyDocument $company_document, IndexCompanyDocumentVersionRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $documents = CompanyDocumentData::with('signed_by')
            ->where('company_document_id', $company_document->id)
            ->where('company_id', $company->id);

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            $documents = $documents->orderBy($orderBy, $orderDirection);
        } else {
            $documents = $documents->orderBy('created_at', 'desc');
        }


        if ($request->search) {
            $search = $request->search;
            $documents = $documents->whereLike(['version', 'created_at'], $search);
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('management_strings.company_documents_returned'),
                    'status' => '1',
                ])->merge($documents->paginate($request->input('per_page')))
            );
        }

        return response()->json([
            'data' => $documents->get(),
            'message' => __('management_strings.company_documents_returned'),
            'status' => '1',
        ]);
    }

    public function get(CompanyDocumentData $company_document_data)
    {
        $this->authorize('view', [$company_document_data]);

        return response()->json([
            'data' => $company_document_data,
            'message' => __('management_strings.company_documents_returned'),
            'status' => '1'
        ]);
    }
}
