<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyGiftCard;
use App\CompanyReceipt;
use App\Contracts\Services\POS\GiftCardPaymentServiceInterface;
use App\Exports\CompanyGiftCardExport;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;
use App\Traits\TimeZoneManager;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\GiftCard\GetCompanyGiftCardRequest;
use App\Http\Requests\v3\GiftCard\StoreCompanyGiftCardRequest;
use App\Http\Requests\v3\GiftCard\UpdateCompanyGiftCardRequest;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class CompanyGiftCardController extends Controller
{
    use ApiResponser;

    function __construct(private readonly GiftCardPaymentServiceInterface $giftCardPaymentService) {}

    public function index(GetCompanyGiftCardRequest $request)
    {
        $this->authorize('viewAny', [CompanyGiftCard::class]);

        $user = Auth::user();

        $gift_cards = CompanyGiftCard::where('company_id', $user->company_id)
            ->with(['client' => function ($query) {
                $query->setEagerLoads([]);
            }, 'payable']);

        if ($request->has('gift_code')) {
            $gift_cards = $gift_cards->where('gift_code', $request->gift_code);
        }

        if ($request->has('client_id')) {
            $gift_cards = $gift_cards->where('client_id', $request->client_id);
        }

        if ($request->has('filter')) {
            $gift_cards = $gift_cards->{$request->filter}();
        }

        $isPaginated = $request->has('search') || in_array($request->orderBy, ["initial_value", "current_value", "client.first_name", "status"]);

        if ($isPaginated) {
            $gift_cards = $gift_cards->lazy();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $company = Auth::user()->company;
            $timezone = TimeZoneManager::getCarbonApprovedTimeZone($company->timezone);

            $gift_cards = $gift_cards->filter(function ($query) use ($search, $timezone) {
                $fields = [
                    (string) $query->padded_gift_code,
                    (string) __("pos_strings.gift_card.{$query->status}"),
                    (string) $query->initial_value,
                    (string) $query->current_value,
                    $query?->created_at?->format('d M Y'),
                    $query?->expired_at?->format('d M Y'),
                    $query?->client?->first_name ?? '',
                    $query?->client?->last_name ?? '',
                    $query?->client?->full_name ?? '',
                ];

                return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        $orderBy = $request->input('orderBy', 'created_at');
        $orderDirection = $request->input('orderDirection', 'desc');
        $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

        // Collection level sorting
        if ($isPaginated && $request->orderBy != 'gift_code') {
            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

            $gift_cards =  $gift_cards->sortBy($orderBy, $sortExtra, $isDescOrder);
        }

        // Database level sorting
        if (!$isPaginated && $request->orderBy != 'gift_code') {
            $gift_cards =  $gift_cards->orderBy($orderBy, $orderDirection);
        }
        if (!$isPaginated && $request->orderBy == 'gift_code') {
            $gift_cards =  $gift_cards->orderByRaw("`$orderBy` + 0 $orderDirection");
        }

        if ($request->input('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('pos_strings.company_gift_list_return'),
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($gift_cards) : $gift_cards->paginate($per_page))
            );
        }
        return response()->json([
            'data' => $isPaginated ? $gift_cards->values() : $gift_cards->lazy(),
            'message' => __('pos_strings.company_gift_list_return'),
            'status' => '1'
        ]);
    }

    public function store(StoreCompanyGiftCardRequest $request)
    {
        $this->authorize('create', [CompanyGiftCard::class]);

        $user = Auth::user();

        $pdo = DB::getPdo();
        $pdo->exec('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE');

        $gift_card = $this->giftCardPaymentService->processPayment($request, $user);

        return response()->json([
            'data' => $gift_card->loadMissing('payable'),
            'message' => __('pos_strings.company_gift_card_receipt_created'),
            'status' => '1'
        ]);
    }

    public function update(CompanyGiftCard $gift_card, UpdateCompanyGiftCardRequest $request)
    {
        $this->authorize('update', [$gift_card]);

        if ($request->has('expired_at')) {
            $gift_card->expired_at = $request->expired_at;
        }

        $gift_card->save();

        return response()->json([
            'data' => $gift_card,
            'message' => __('pos_strings.company_gift_card_receipt_updated'),
            'status' => '1'
        ]);
    }

    public function get(CompanyGiftCard $gift_card_with_code)
    {
        $this->authorize('view', [$gift_card_with_code]);

        return response()->json([
            'data' => $gift_card_with_code,
            'message' => __('pos_strings.company_gift_card_return'),
            'status' => '1'
        ]);
    }

    public function export()
    {
        $this->authorize('export', [CompanyGiftCard::class]);

        $user = Auth::user();
        $company = $user->company;

        return Excel::download(
            new CompanyGiftCardExport($company),
            'gift_cards.xlsx'
        );
    }

    public function stats()
    {
        $this->authorize('viewAny', [CompanyGiftCard::class]);

        $user = Auth::user();

        $gift_cards = CompanyGiftCard::where('company_id', $user->company_id)->get();
        $company_receipt = CompanyReceipt::where('company_id', $user->company_id)->whereNotNull('gift_card_amount')->get();

        $stats = [];

        $stats['total_sales'] = count($gift_cards);
        $stats['avg_value'] = $gift_cards->sum('initial_value') / $stats['total_sales'];

        $stats['avg_redemption_value'] = $company_receipt->sum('gift_card_amount') / count($company_receipt);
        $stats['out_standing_balance'] = $gift_cards->sum('current_value');


        return response()->json([
            'data' => $stats,
            'message' => __('pos_strings.company_gift_list_return'),
            'status' => '1'
        ]);
    }
}
