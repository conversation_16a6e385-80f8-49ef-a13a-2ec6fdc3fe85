<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\UploadSwishQRRequest;
use App\Setting;
use App\Traits\SaveFile;
use App\User;
use Illuminate\Support\Facades\Auth;

class PointOfSaleAccountController extends Controller
{
    use SaveFile;

    public function create()
    {
        /** @var User $user */
        $user = Auth::user();

        /** @var Company $company */
        $company = $user->company;

        $this->authorize('createEcrAccount', $company);

        $account = $company->getOrCreateVivaAccount();
        if (!$account->verified) {
            return response()->json([
                'data' =>  config('viva_wallet.environment') == 'demo' ?
                    $account?->invitation?->demoRedirectUrl :
                    $account?->invitation?->redirectUrl,
                'message' => __('pos_strings.customer_need_to_connect'),
                'status' => '1',
            ]);
        }

        return response()->json([
            'message' => __('strings.customer_connected'),
            'status' => '1',
        ]);
    }

    public function get()
    {
        return $this->create();
    }

    function uploadSwishQr(UploadSwishQRRequest $request)
    {
        $user = Auth::user();

        $company = $user->company;

        $file = $this->saveFile($request->file('image'), 'companies/' . md5($company->id) . '/config', $user);

        Setting::updateOrCreate([
            'key' => Setting::POS_SWISH_QR,
            'company_id' => $company->id
        ], [
            'value' => $file?->filename,
        ]);

        return response()->json([
            'message' => __('pos_strings.swish_qr_uploaded_successfully'),
            'status' => '1',
        ]);
    }
}
