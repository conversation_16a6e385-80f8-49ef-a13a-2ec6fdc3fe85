<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyProduct;
use App\Exports\CompanyProductExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Product\GetCompanyProductRequest;
use App\Http\Requests\v3\Product\StoreCompanyProductRequest;
use App\Http\Requests\v3\Product\UpdateCompanyProductRequest;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;

class CompanyProductController extends Controller
{
    use ApiResponser;

    public function index(GetCompanyProductRequest $request)
    {
        $this->authorize('viewAny', [CompanyProduct::class]);

        $user = Auth::user();

        $products = CompanyProduct::where('company_id', $user->company_id)->with(['category']);

        if ($user->isMasterAdmin()) {
            $products = CompanyProduct::where('company_id', $request->company_id)->with(['category']);
        }

        if ($request->has('category_id')) {
            $products = $products->whereHas('category', function ($query) use ($request) {
                $query->where('id', $request->category_id);
            });
        }

        if ($request->has('filter') && ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed')) {
            $products = $products->{$request->filter}();
        }

        $isPaginated = $request->has('search') || $request->has('orderBy');

        if ($isPaginated) {
            $products = $products->lazy();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $products = $products->filter(function ($query) use ($search) {
                $fields = [
                    $query->name,
                    $query->product_code,
                    $query->barcode,
                    $query->selling_price,
                    $query->base_price,
                    $query->tax_information,
                    $query->stock,
                    $query->description,
                    $query->category?->name,
                ];

                return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');
        $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

        // Collection level sorting
        if ($isPaginated && $request->has('orderBy')) {
            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

            $products =  $products->sortBy($orderBy, $sortExtra, $isDescOrder);
        }
        if ($isPaginated && !$request->has('orderBy')) {
            $products =  $products->sortBy('created_at', $sortExtra, $orderDirection);
        }

        // Database level sorting
        if (!$isPaginated && $request->has('orderBy')) {
            $products = $products->orderBy($orderBy, $orderDirection);
        }
        if (!$isPaginated && !$request->has('orderBy')) {
            $products = $products->latest();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('pos_strings.company_product_list_return'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($products) : $products->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $isPaginated ? $products->values() : $products->lazy(),
            'message' => __('pos_strings.company_product_list_return'),
            'status' => '1'
        ]);
    }

    public function store(StoreCompanyProductRequest $request)
    {
        $this->authorize('create', [CompanyProduct::class]);

        $user = Auth::user();

        $pdo = DB::getPdo();
        $pdo->exec('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE');

        $allowTax = Gate::allows('addUpdateTax');

        return DB::transaction(function () use ($user, $request, $allowTax) {
            $product = CompanyProduct::create([
                'company_id' => $user->company_id ?? null,
                'category_id' => $request->category_id,
                'barcode' => $request->barcode,
                'name' => $request->name,
                'description' => $request->description,
                'selling_price' => $request->selling_price,
                'base_price' => $request->base_price,
                'tax_information' => $allowTax ? $request->tax_information : 0,
                'stock' => $request->stock,
            ]);

            return response()->json([
                'data' => $product,
                'message' => __('pos_strings.company_product_created'),
                'status' => '1'
            ]);
        });
    }

    public function update(CompanyProduct $product, UpdateCompanyProductRequest $request)
    {
        $this->authorize('update', $product);

        if ($request->has('category_id')) {
            $product->category_id = $request->category_id;
        }

        if ($request->has('name')) {
            $product->name = $request->name;
        }

        if ($request->has('description')) {
            $product->description = $request->description;
        }

        if ($request->has('selling_price')) {
            $product->selling_price = $request->selling_price;
        }

        if ($request->has('base_price')) {
            $product->base_price = $request->base_price;
        }

        $allowTax = Gate::allows('addUpdateTax');

        if ($request->has('tax_information') && $allowTax) {
            $product->tax_information = $request->tax_information;
        }

        if ($request->has('stock')) {
            $product->stock = $request->stock;
        }

        if ($request->has('barcode')) {
            $product->barcode = $request->barcode;
        }

        $product->save();

        return response()->json([
            'data' => $product->refresh(),
            'message' => __('pos_strings.company_product_updated'),
            'status' => '1'
        ]);
    }

    public function activeToggle(CompanyProduct $product_with_trashed)
    {
        if ($product_with_trashed->trashed()) {
            $this->authorize('restore', $product_with_trashed);

            $product_with_trashed->restore();

            return response()->json([
                'message' => __('pos_strings.company_product_active'),
                'status' => '1'
            ]);
        }

        $this->authorize('delete', $product_with_trashed);

        $product_with_trashed->delete();

        return response()->json([
            'message' => __('pos_strings.company_product_in_active'),
            'status' => '1'
        ]);
    }

    public function export()
    {
        $this->authorize('export', [CompanyProduct::class]);

        $user = Auth::user();

        return Excel::download(
            new CompanyProductExport($user),
            'products.xlsx'
        );
    }
}
