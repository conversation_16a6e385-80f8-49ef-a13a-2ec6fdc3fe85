<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\VideoCall;
use Illuminate\Support\Facades\DB;

class VideoCallController extends Controller
{
    public function end(VideoCall $videoCall)
    {
        return DB::transaction(function () use ($videoCall) {
            $company = Auth::user()->company;

            $this->authorize('end', $videoCall);

            $videoCall = $videoCall->end();

            return response()->json([
                'data' => $videoCall,
                'message' => __('strings.video_call_ended'),
                'status' => '1'
            ]);
        });
    }
}
