<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\ExtraField\UpdateExtraFieldRequest;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CompanyExtraFieldController extends Controller
{
    public function index(Request $request, $id)
    {
        $id = Company::decryptId($id);

        $company = Company::findOrFail($id);
        $data = $company->extra_field;

        return [
            'data' => $data,
            'message' => __('strings.Data_returned_successfully'),
            'status' => '1'
        ];
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateExtraFieldRequest $request)
    {
        $company = Auth::user()->company;

        $extra = $company->extra_field()->firstOrCreate([]);

        $this->authorize('update', $extra);

        if ($request->has('title')) {
            $extra->title = $request->title;
        }

        if ($request->has('sub_text')) {
            $extra->sub_text = $request->sub_text;
        }

        if ($request->has('opening_hours')) {
            $extra->opening_hours = $request->opening_hours;
        }

        if ($request->has('mode_of_payment')) {
            $extra->mode_of_payment = $request->mode_of_payment;
        }

        if ($request->has('about')) {
            $extra->about = $request->about;
        }

        if ($request->has('facebook')) {
            $extra->forceFill([
                'social_links->facebook' => $request->facebook
            ]);
        }

        if ($request->has('instagram')) {
            $extra->forceFill([
                'social_links->instagram' => $request->instagram
            ]);
        }

        if ($request->has('youtube')) {
            $extra->forceFill([
                'social_links->youtube' => $request->youtube
            ]);
        }

        if ($request->has('twitter')) {
            $extra->forceFill([
                'social_links->twitter' => $request->twitter
            ]);
        }

        if ($request->has('whatsapp')) {
            $extra->forceFill([
                'social_links->whatsapp' => $request->whatsapp
            ]);
        }

        if ($request->boolean('clear_google_place')) {
            $extra->forceFill([
                'place_id' => null,
                'rating' => null,
                'reviews' => null,
                'user_rating_count' => null,
                'google_maps_links' => null,
            ]);
        }

        if ($extra->isDirty()) {
            $extra->save();
        }

        return [
            'data' => $extra->makeHidden('company'),
            'message' => __('strings.Data_returned_successfully'),
            'status' => '1'
        ];
    }
}
