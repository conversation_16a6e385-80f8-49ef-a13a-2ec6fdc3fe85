<?php

namespace App\Http\Controllers\Api\v3;

use App\Contracts\Services\Subscription\SubscriptionServiceInterface;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\v2\GetCancelSubscriptionDataRequest;
use App\Http\Requests\v3\StoreSubscriptionRequest;
use App\Mail\CancelSubscriptionRequestMail;
use App\Mail\SubscriptionCancelConfirmation;
use App\Models\Cashier\Subscription;
use App\SubscriptionCancellationData;
use App\Traits\ApiResponser;
use App\Traits\PromocodeManager;
use Illuminate\Support\Facades\Crypt;

class SubscriptionController extends Controller
{
    use ApiResponser, PromocodeManager;

    public function plans()
    {
        $user = Auth::user();
        $company = $user->company;

        return response()->json([
            'data' => [
                ...$company->plans(false),
                ...$company->plans(true),
            ],
            'message' => "plans returned successfully.",
            'status' => '1'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $company = Auth::user()->company;

        $this->authorize('create', Subscription::class);

        try {
            $intent = $company->createSetupIntent();

            return response()->json([
                'data' => $intent->client_secret,
                'message' => "setup intent returned successfully.",
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            report($th);
            return response()->json([
                'error' => $th->getMessage(),
                'message' => 'please try again, something happen.',
                'status' => '0'
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreSubscriptionRequest $request, SubscriptionServiceInterface $subscriptionService)
    {
        $company = Auth::user()->company;

        $this->authorize('create', Subscription::class);

        $subscriptionService->subscribe($request, $company);

        return response()->json([
            'message' => __('strings.Successfully_subscribed_to_plan'),
            'status' => '1'
        ]);
    }

    public function expiry()
    {
        $user = Auth::user();
        $company = $user->company;

        $this->authorize('expiryDate', Subscription::class);

        $yearlyStripeSubscription =  $company->activeSubscription(true)?->asStripeSubscription();
        $monthlyStripeSubscription =  $company->activeSubscription(false)?->asStripeSubscription();

        return response()->json([
            'data' => [
                "monthly" => $monthlyStripeSubscription ? Carbon::createFromTimestamp($monthlyStripeSubscription->current_period_end) : null,
                "yearly" => $yearlyStripeSubscription ? Carbon::createFromTimestamp($yearlyStripeSubscription->current_period_end) : null
            ],
            'message' => __('strings.expiration_date_returned'),
            'status' => '1',
        ]);
    }

    public function requestToCancel(GetCancelSubscriptionDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company->loadCount(['users', 'clients', 'procedures']);

        $this->authorize('create', Subscription::class);

        Mail::to('<EMAIL>')->locale(app()->getLocale())->send(new CancelSubscriptionRequestMail($user, $company, $request->data));

        return response()->json([
            'message' => __('strings.cancel_request_sent'),
            'status' => '1',
        ]);
    }

    public function cancelConfirmation($id)
    {
        $subscription_cancellation_data = SubscriptionCancellationData::findOrFail(Crypt::decrypt($id));

        $company  = $subscription_cancellation_data->company;
        $super_user = null;
        foreach ($company->users as $user) {
            if ($user->email == $company->email) {
                $super_user = $user;
            }
        }

        if (!$subscription_cancellation_data->is_from_free_trail) {
            if (!$subscription_cancellation_data->subscription) {
                return response()->json([
                    'message' => __('strings.subscription_not_found'),
                    'status' => '0',
                ]);
            }

            $subscription = $subscription_cancellation_data->subscription;
            if (!$subscription->active()) {
                return response()->json([
                    'message' => __('strings.subscription_not_active'),
                    'status' => '0',
                ]);
            }

            if ($subscription->ends_at) {
                return response()->json([
                    'message' => __('strings.subscription_already_cancelled'),
                    'status' => '2',
                ]);
            }
            $subscription->cancel();
            $subscription_cancellation_data->should_delete_data = 1;
            $subscription_cancellation_data->save();
        } else {
            $subscription_cancellation_data->should_delete_data = 1;
            $subscription_cancellation_data->save();
            $company->is_free_trail_cancelled = 1;
            $company->save();
        }

        Mail::to($super_user->email)
            ->locale(app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, false));
        Mail::to("<EMAIL>")
            ->locale(app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, true));

        return response()->json([
            'message' => __('strings.Subscription_cancelled_successfully'),
            'status' => '1',
        ]);
    }

    public function delete(GetCancelSubscriptionDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $is_in_free_trail = false;

        if ($company->isFreeTrailActive()) {
            $is_in_free_trail = true;
        }
        $subscription_cancellation_data = null;
        if ($is_in_free_trail) {
            $expiration_date = Carbon::parse($company->free_trail_end_date)->format('Y-m-d');
            $subscription_cancellation_data = SubscriptionCancellationData::updateOrCreate([
                'company_id' => $company->id,
            ], [
                'data' => $request->data,
                'should_delete_data' => 1,
                'is_data_deleted' => 0,
                'data_deletion_date' => $expiration_date,
                'is_from_free_trail' => 1,
            ]);
            $company->is_free_trail_cancelled = 1;
            $company->save();
        } else {
            $subscription_to_cancel =  $user->company->activeSubscription();

            $subscription_id =  $user->company->activeSubscription()->stripe_id;
            \Stripe\Stripe::setApiKey(secret_env('STRIPE_SECRET'));

            $subscription = null;
            try {
                $subscription = \Stripe\Subscription::retrieve($subscription_id);
            } catch (\Throwable $th) {
                //throw $th;
            }

            if ($subscription) {
                $expiration_date =  Carbon::createFromTimestamp($subscription->current_period_end)->format('Y-m-d');
            }


            $subscription_cancellation_data = SubscriptionCancellationData::updateOrCreate([
                'company_id' => $company->id,
                'subscription_id' => $subscription_to_cancel->id,
            ], [
                'data' => $request->data,
                'should_delete_data' => 0,
                'is_data_deleted' => 0,
                'data_deletion_date' => $expiration_date,
                'is_from_free_trail' => 0,
            ]);
            $subscription = $subscription_cancellation_data->subscription;
            if ($subscription->ends_at) {
                return response()->json([
                    'message' => __('strings.subscription_already_cancelled'),
                    'status' => '0',
                ]);
            }
            $subscription->cancel();
            $subscription_cancellation_data->should_delete_data = 1;
            $subscription_cancellation_data->save();
        }
        // Mail::to($user->email)
        //     ->locale(app()->getLocale())
        //     ->send(new SubscriptionCancellationStartMail($subscription_cancellation_data));
        $super_user = null;
        foreach ($company->users as $user) {
            if ($user->email == $company->email) {
                $super_user = $user;
            }
        }
        Mail::to($super_user->email)
            ->locale(app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, false));
        Mail::to("<EMAIL>")
            ->locale(app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, true));


        return response()->json([
            'message' => __('strings.Subscription_cancelled_successfully'),
            'status' => '1',
        ]);
    }
}
