<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyPlatform;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\InvoiceIndexRequest;
use App\Http\Resources\InvoiceResource;
use App\Models\Cashier\Subscription;
use App\Stripe\Invoice;
use App\Traits\ApiResponser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Laravel\Cashier\Cashier;
use Laravel\Cashier\Payment;
use Stripe\PaymentIntent as StripePaymentIntent;


class InvoiceController extends Controller
{
    use ApiResponser;

    public function index(InvoiceIndexRequest $request)
    {
        $company = Auth::user()->company;
        $this->authorize('viewAnyInvoice', Subscription::class);

        $upcomming_invoice = $company->upcomingInvoice();

        $upcomming_invoices = $upcomming_invoice ? collect([$upcomming_invoice]) : collect([]);

        $invoices = $company->invoicesIncludingPending([
            'limit' => 100,
            'created' => [
                'gt' => Carbon::now()->subYear()->timestamp
            ],
        ]);

        $invoices = $upcomming_invoices->merge($invoices);


        if ($request->has('platforms')) {
            $prices_monthly = $company->plans();
            $prices_yearly = $company->plans(true);
            $prices = $prices_monthly->merge($prices_yearly);

            $rec_prices = $prices->whereIn('platform', $request->platforms);

            $price_ids = $rec_prices->pluck('stripe_id');

            $invoices = $invoices->filter(function ($invoice) use ($price_ids) {
                $invoice_prices = collect($invoice->lines->data)->pluck('price.id');

                return $invoice_prices->some(function ($price) use ($price_ids) {
                    return in_array($price, $price_ids->values()->all());
                });
            });
        }

        $invoices = InvoiceResource::collection($invoices);

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra =  in_array($orderBy, ['number', 'status']) ? (SORT_NATURAL | SORT_FLAG_CASE) : SORT_FLAG_CASE;
            $invoices =  $invoices->sortBy($orderBy, $sortExtra, $isDescOrder);
        }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.Invoices_returned_successfully'),
                    'status' => '1'
                ])->merge($this->paginate($invoices))
            );
        }

        return [
            'data' => $invoices,
            'message' => __('strings.Invoices_returned_successfully'),
            'status' => '1'
        ];
    }

    public function pay($id)
    {
        $company = Auth::user()->company;

        $this->authorize('payInvoice', Subscription::class);

        $invoice  = $company->invoicePay($id);

        if ($invoice->paid) {
            $company->is_read_only = false;
            $company->is_blocked = false;
            $company->payment_failed_attempts = false;
            $company->save();

            return [
                'data' => $invoice ? new InvoiceResource($invoice) : null,
                'message' => __('strings.Invoice_payed_successfully'),
                'status' => '1'
            ];
        }

        return [
            'message' => __('strings.Invoice_payed_failed'),
            'status' => '0'
        ];
    }

    public function download($id)
    {
        $company = Auth::user()->company;

        $this->authorize('downloadInvoice', Subscription::class);

        $invoice = new Invoice($company, $company->findInvoiceOrFail($id)->asStripeInvoice());

        $response = Http::withoutVerifying()->get($invoice->invoice_pdf);

        $contentType = $response->header('Content-Type');

        $body = $response->getBody();

        $stream = new StreamedResponse(function () use ($body) {
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        });

        $stream->headers->set('Content-Type', $contentType);

        return $stream;
    }

    public function void($id)
    {
        $company = Auth::user()->company;

        $this->authorize('viewBilling', Subscription::class);

        $invoice = $company->findInvoiceOrFail($id);

        $invoice->void();

        return [
            'message' => __('strings.Invoice_voided_successfully'),
            'status' => '1'
        ];
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function pendingSubscriptionInvoice()
    {
        $company = Auth::user()->company;

        $this->authorize('viewBilling', Subscription::class);

        $invoice = $company->pendingInvoice();

        if (!$invoice) {
            return response()->json([
                'message' => 'No pending invoice.',
                'status' => '0',
            ]);
        }

        return response()->json([
            'message' => 'invoice returned successfully.',
            'status' => '1',
            'data' => $invoice,
        ]);
    }
}
