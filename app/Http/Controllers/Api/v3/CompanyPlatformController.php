<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\CompanyPaymentMethod;
use App\CompanyPlatform;
use App\Http\Controllers\Controller;
use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CompanyPlatformController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $company_id = $user->company_id;
        if ($request->has('company_id')) {
            $company_id = $request->company_id;
        }
        $company_platforms = CompanyPlatform::where('company_id', $company_id)->get();
        return response()->json([
            'data' => $company_platforms,
            'message' => __('prescription_strings.platforms_returned'),
            'status' => '1'
        ]);
    }

    public function upcomingFees(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;

        return response()->json([
            'data' => $company->upcoming_platform_fees,
            'message' => __('prescription_strings.upcoming_fees_returned'),
            'status' => '1'
        ]);
    }

    public function store(Request $request)
    {
        $this->authorize('createPrescription', Company::class);

        $user = Auth::user()->loadMissing('company.settings');
        $company = $user->company;

        if ($company->email != $user->email) {
            return response()->json([
                'message' => __('prescription_strings.only_super_admin_can_register_for_mal'),
                'status' => '0',
            ]);
        }

        if ($request->has('first_name')) {
            $company->first_name = $request->first_name;
            $user->first_name = $request->first_name;
        }
        if ($request->has('last_name')) {
            $company->last_name = $request->last_name;
            $user->last_name = $request->last_name;
        }
        if ($request->has('company_name')) {
            $company->company_name = $request->company_name;
            $user->title = $request->company_name;
        }
        if ($request->has('mobile_number')) {
            $company->mobile_number = $request->mobile_number;
            $user->mobile_number = $request->mobile_number;
        }
        if ($request->has('country_code')) {
            $company->country_code = $request->country_code;
            $user->country_code = $request->country_code;
        }
        if ($request->has('personal_id')) {
            $user->personal_id = $request->personal_id;
        }

        if ($request->has('payment_method')) {
            $company->createOrGetStripeCustomer();

            $payment_method = CompanyPaymentMethod::where('company_id', $company->id)->where('payment_method_id', $request->payment_method)->first();

            if (!$payment_method) {
                $company->addPaymentMethod($request->payment_method);

                $payment_method_info = $company->findPaymentMethod($request->payment_method);
                $payment_methods_to_update = CompanyPaymentMethod::where('company_id', $company->id)->whereJsonContains('labels', CompanyPaymentMethod::MAL)->cursor();
                foreach ($payment_methods_to_update as $payment_method) {
                    $payment_method->labels =  collect($payment_method->labels)->forget(collect($payment_method->labels)->search(CompanyPaymentMethod::MAL))->values()->all();
                    $payment_method->save();
                }

                $payment_method = CompanyPaymentMethod::create([
                    'company_id' => $company->id,
                    'payment_method_id' => $request->payment_method,
                    'labels' => [CompanyPaymentMethod::MAL],
                    'meta_data' => [
                        'last4' => (string)$payment_method_info->card->last4,
                        'exp_year' => (string)$payment_method_info->card->exp_year,
                        'exp_month' => (string)$payment_method_info->card->exp_month,
                    ]
                ]);
            } else {
                if (!in_array(CompanyPaymentMethod::MAL, $payment_method->labels)) {
                    $payment_method->labels = array_merge($payment_method->labels, [CompanyPaymentMethod::MAL]);
                    $payment_method->save();
                }
            }
        }
        $data = [];
        if ($request->has('vat_number')) {
            $taxIds = $company->taxIds();
            $hasSaveTax = false;
            foreach ($taxIds as $key => $taxId) {
                if ($taxId->type == $request->vat_code && $taxId->value == $request->vat_number) {
                    $hasSaveTax = true;
                    break;
                }
                $company->deleteTaxId($taxId->id);
            }

            if ($request->vat_code && $request->vat_number) {
                if (!$hasSaveTax) {
                    try {
                        $taxId = $company->createTaxId($request->vat_code, $request->vat_number);
                    } catch (\Throwable $th) {
                        return response()->json([
                            'message' => __('strings.invalid_vat_number'),
                            'status' => '0'
                        ]);
                    }
                }
            }
            $data['invoice_settings'] = [
                'custom_fields' =>  $request->vat_number ? [
                    [
                        'name'  => 'VAT No',
                        'value' => $request->vat_number ?? '',
                    ]
                ] : '',
            ];
        }

        if ($request->has('address.city')) {
            $data['address']['city'] = $request->address['city'];
        }
        if ($request->has('address.country')) {
            $data['address']['country'] = $request->address['country'];
            // $data['address']['country'] = $company->country;
            $company->country = $request->address['country'];
            $user->country = $request->address['country'];
        }

        if ($request->has('address.line1')) {
            $data['address']['line1'] = $request->address['line1'];
        }
        if ($request->has('address.postal_code')) {
            $data['address']['postal_code'] = $request->address['postal_code'];
        }
        if ($request->has('mobile_number')) {
            $data['phone'] = $request->mobile_number;
        }
        if ($request->has('company_name')) {
            $data['name'] = $request->company_name;
        }
        if (count($data)) {
            $company->updateStripeCustomer($data);
        }

        $priscription_prices = collect(config('stripe.prices'))->filter(function ($price) {
            if ($price['platform'] == CompanyPlatform::PRESCRIPTION) {
                return true;
            } else {
                return false;
            }
        });
        $default_price = null;
        if (count($priscription_prices) > 0) {
            $default_price = collect($priscription_prices[0]['prices'])->filter(function ($price) {
                if ($price['price'] == config('prescription.service_charge')) {
                    return true;
                } else {
                    return false;
                }
            })->values();
        }

        CompanyPlatform::updateOrCreate([
            'platform' => CompanyPlatform::PRESCRIPTION,
            'company_id' => $company->id,
        ], [
            'license_agreement' => $request->license_agreement,
            'price_id' => $default_price[0]['stripe_id'],
            'price' => $default_price[0]['price'],
        ]);
        Setting::create([
            'key' => Setting::DEFAULT_FEES_PRESCRIBER,
            'value' => Setting::getDefaultValue(Setting::DEFAULT_FEES_PRESCRIBER),
            'company_id' => $company->id
        ]);
        Setting::create([
            'key' => Setting::RECEIVE_EMAIL_NOTIFICATION,
            'value' => Setting::getDefaultValue(Setting::RECEIVE_EMAIL_NOTIFICATION),
            'company_id' => $company->id
        ]);

        $company->verification = Company::COMPANY_VERIFIED;

        $company->save();
        $user->save();

        return response()->json([
            // 'data' => $user,
            'message' => __('strings.register_set_up'),
            'status' => '1'
        ]);
    }

    public function servicePrices()
    {
        $priscription_prices = collect(config('stripe.prices'))->filter(function ($price) {
            if ($price['platform'] == CompanyPlatform::PRESCRIPTION) {
                return true;
            } else {
                return false;
            }
        });
        return response()->json([
            'data' => $priscription_prices[0]['prices'],
            'message' => __('strings.register_set_up'),
            'status' => '1'
        ]);
    }
}
