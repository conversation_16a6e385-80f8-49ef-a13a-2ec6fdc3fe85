<?php

namespace App\Http\Controllers\Api\v3;

use App\Activity;
use App\File;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\ListMedicalDeviceRequest;
use App\Http\Requests\v3\StoreMedicalDeviceRequest;
use App\Http\Requests\v3\UpdateMedicalDeviceRequest;
use App\Jobs\QMSDocumentZipDownloadJob;
use App\Jobs\QMSMedicalDeviceZipDownloadJob;
use App\Models\MedicalDevice;
use App\Models\MedicalDeviceMaintenance;
use Illuminate\Contracts\Cache\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Traits\SaveFile;
use App\UserNotification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\HttpException;

class MedicalDeviceController extends Controller
{
    use SaveFile;
    public function index(ListMedicalDeviceRequest $request)
    {
        $company = Auth::user()->company;

        $medical_devices = MedicalDevice::withCount('maintenances')->where('company_id', $company->id);

        if (request()->has('withTrashed')) {
            $medical_devices = $medical_devices->withTrashed();
        }

        if ($request->has('filter')) {
            if ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed') {
                $medical_devices = $medical_devices->{$request->filter}();
            } else {
                $medical_devices = $medical_devices->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
            }
        }

        if ($request->has('orderBy') && $request->has('orderDirection')) {
            $medical_devices = $medical_devices->orderBy($request->orderBy, $request->orderDirection);
        } else {
            $medical_devices = $medical_devices->latest();
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('management_strings.medical_devices_returned'),
                    'status' => '1'
                ])->merge($medical_devices->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $medical_devices->get(),
                'message' => __('management_strings.medical_devices_returned'),
                'status' => '1'
            ]);
        }
    }

    public function store(StoreMedicalDeviceRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $medical_device = MedicalDevice::create([
            'company_id' => $company->id,
            'product_name' => $request->product_name,
            'model' => $request->model,
            'serial_number' => $request->serial_number,
            'brand' => $request->brand,
            'supplier' => $request->supplier,
            'performed_maintenance' => $request->performed_maintenance,
            'upcoming_maintenance' => $request->upcoming_maintenance,
            'compliance_declared' => $request->compliance_declared ?? 0,
        ]);

        if ($request->has('upload_manual')) {
            $file = $this->saveFile($request->file('upload_manual'), 'medical_devices');
            $medical_device->upload_manual = $file->filename;
        }
        if ($request->has('supplier_agreement')) {
            $file = $this->saveFile($request->file('supplier_agreement'), 'medical_devices');
            $medical_device->supplier_agreement = $file->filename;
        }

        $medical_device->save();

        $activity = activity()
            ->performedOn($medical_device);
        $activity = $activity->by($user);
        $activity->log("{$medical_device->product_name} medical device has been created by {$user->first_name} {$user->last_name}");

        return response()->json([
            'data' => $medical_device->refresh(),
            'message' => __('management_strings.medical_devices_created'),
            'status' => '1',
        ]);
    }

    public function update(UpdateMedicalDeviceRequest $request, MedicalDevice $medical_device)
    {
        $this->authorize('access', [MedicalDevice::class, $medical_device]);

        $user = Auth::user();

        if ($request->has('product_name')) {
            $medical_device->product_name = $request->product_name;
        }

        if ($request->has('model')) {
            $medical_device->model = $request->model;
        }

        if ($request->has('serial_number')) {
            $medical_device->serial_number = $request->serial_number;
        }

        if ($request->has('brand')) {
            $medical_device->brand = $request->brand;
        }

        if ($request->has('supplier')) {
            $medical_device->supplier = $request->supplier;
        }

        if ($request->has('performed_maintenance')) {
            $medical_device->performed_maintenance = $request->performed_maintenance;
        }

        if ($request->has('upcoming_maintenance')) {
            $medical_device->upcoming_maintenance = $request->upcoming_maintenance;
        }

        if ($request->has('compliance_declared')) {
            $medical_device->compliance_declared = $request->compliance_declared;
        }

        if ($request->has('upload_manual')) {
            $this->deleteFile($medical_device->upload_manual);
            $file = $this->saveFile($request->file('upload_manual'), 'medical_devices');
            $medical_device->upload_manual = $file->filename;
        }
        if ($request->has('supplier_agreement')) {
            $this->deleteFile($medical_device->supplier_agreement);
            $file = $this->saveFile($request->file('supplier_agreement'), 'medical_devices');
            $medical_device->supplier_agreement = $file->filename;
        }

        $medical_device->save();

        $activity = activity()
            ->performedOn($medical_device);
        $activity = $activity->by($user);
        $activity->log("{$medical_device->product_name} medical device has been updated by {$user->first_name} {$user->last_name}");

        return response()->json([
            'data' => $medical_device->refresh(),
            'message' => __('management_strings.medical_devices_updated'),
            'status' => '1',
        ]);
    }

    public function restore(Request $request)
    {
        $medical_device = MedicalDevice::withTrashed()->findOrFail($request->medical_device_id);

        $this->authorize('access', [MedicalDevice::class, $medical_device]);

        if ($medical_device->restore()) {
            return response()->json([
                'message' => __('management_strings.medical_devices_restored'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('management_strings.medical_devices_restoration_failed'),
                'status' => '0'
            ]);
        }
    }
    public function deleteMedicalDevice(MedicalDevice $medical_device)
    {
        $this->authorize('access', [MedicalDevice::class, $medical_device]);

        $medical_device->delete();

        return response()->json([
            'message' => __('management_strings.medical_devices_deleted'),
            'status' => '1',
        ]);
    }

    public function destroy(MedicalDevice $medical_device)
    {
        $this->authorize('access', [MedicalDevice::class, $medical_device]);

        $this->deleteFile($medical_device->upload_manual);
        $this->deleteFile($medical_device->supplier_agreement);
        $medical_device->delete();

        return response()->json([
            'message' => __('management_strings.medical_devices_deleted'),
            'status' => '1',
        ]);
    }

    public function createMaintenance(Request $request, MedicalDevice $medical_device)
    {
        $this->authorize('access', [MedicalDevice::class, $medical_device]);

        $medical_device_maintenance = MedicalDeviceMaintenance::create([
            'medical_device_id' => $medical_device->id,
            'title' => $request->title,
        ]);

        if ($request->has('protocol_path')) {
            $file = $this->saveFile($request->file('protocol_path'), 'medical_device_maintenances');
            $medical_device_maintenance->protocol_path = $file->filename;
        }

        $medical_device_maintenance->save();

        return response()->json([
            'data' => $medical_device_maintenance->refresh(),
            'message' => __('management_strings.medical_device_maintenance_created'),
            'status' => '1',
        ]);
    }

    public function listMaintenance(Request $request, MedicalDevice $medical_device)
    {
        $this->authorize('access', [MedicalDevice::class, $medical_device]);

        $medical_device_maintenance = $medical_device->maintenances();

        if ($request->has('orderBy') && $request->has('orderDirection')) {
            $medical_device_maintenance = $medical_device_maintenance->orderBy($request->orderBy, $request->orderDirection);
        } else {
            $medical_device_maintenance = $medical_device_maintenance->latest();
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('management_strings.medical_device_maintenances_returned'),
                    'status' => '1'
                ])->merge($medical_device_maintenance->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $medical_device_maintenance->get(),
                'message' => __('management_strings.medical_device_maintenances_returned'),
                'status' => '1'
            ]);
        }
    }

    public function logs(MedicalDevice $medical_device, Request $request)
    {
        $activities = Activity::where(function ($query) use ($medical_device) {
            $query = $query->where(function ($query) use ($medical_device) {
                $query->where('subject_type', MedicalDevice::class)->where('subject_id', $medical_device->getKey());
            });
        });
        if (request()->has('orderBy')) {
            $activities = $activities->orderBy(request()->input('orderBy'), request()->input('orderDirection', 'asc'));
        } else {
            $activities = $activities->latest();
        }
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('management_strings.medical_devices_logs_returned'),
                    'status' => '1',
                ])->merge($activities->paginate($request->input('per_page')))
            );
        }
        return response()->json([
            'data' => $activities->get(),
            'message' => __('management_strings.medical_devices_logs_returned'),
            'status' => '1',
        ]);
    }

    public function download(Request $request)
    {
        $this->authorize('download', [MedicalDevice::class]);

        $company = Auth::user()->company;

        return DB::transaction(function () use ($request, $company) {

            $cacheKey = 'qms_medical_device_zip_' . $company->id;
            $cache_exists = Cache::get($cacheKey);

            if ($cache_exists === 'working') {
                throw new HttpException(208, __('zip.zip_generating'));
            }

            if ($cache_exists) {
                throw new HttpException(208, __('zip.zip_in_queue'));
            }

            $force = $request->input('force');

            if (!$force) {
                $zip_already_exists = File::where('fileable_type', 'qms_medical_devices_zip')->where('fileable_id', $company->id)->first();

                if ($zip_already_exists) {
                    return response()->json([
                        'status' => '0',
                        'data' => $zip_already_exists,
                        'message' => __('zip.zip_already_present'),
                    ], 210);
                }
            }

            Cache::put($cacheKey, 'queue', 86400);
            UserNotification::create([
                'user_id' => Auth::user()->id,
                'title' => __('zip.zip_qms_medical_device.zip_generating'),
                'description' => __('zip.zip_qms_medical_device.zip_in_queue'),
                'is_read' => 0
            ]);

            QMSMedicalDeviceZipDownloadJob::dispatch($company, Auth::user(), app()->getLocale());

            return response()->json([
                'status' => '1',
                'message' => __('zip.zip_accepted'),
            ]);
        });
    }
}
