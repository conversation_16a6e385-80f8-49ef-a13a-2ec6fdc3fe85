<?php

namespace App\Http\Controllers\Api\v3;

use App\Contracts\Services\Subscription\SubscriptionServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\GetPromocodeRequest;
use App\Models\Cashier\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Cashier\Cashier;
use Stripe\Coupon;

class PromocodeController extends Controller
{
    public function show(GetPromocodeRequest $request, SubscriptionServiceInterface $subscriptionService)
    {
        $company = Auth::user()->company;

        $this->authorize('fetchPromocode', Subscription::class);

        abort_if($company->coupons()->where('code_name', $request->code_name)->first(), 400, __('strings.promocode_already_used'));

        $coupon = $subscriptionService->getCoupon($request->code_name);

        abort_if(!$coupon, 400, __('strings.invalid_promocode'));

        abort_if(!$coupon->percent_off, 400, __('strings.invalid_promocode'));

        // abort_if($coupon?->metadata?->type == 'monthly' && $request->type == 'yearly', 400, __('strings.only_for_monthly'));

        // abort_if($coupon?->metadata?->type == 'yearly' && $request->type == 'monthly', 400, __('strings.only_for_yearly'));

        return [
            'data' => $coupon,
            'message' => __('strings.promocode_return'),
            'status' => '1'
        ];
    }

    public function active()
    {
        $user = Auth::user();

        $company = $user->company;

        $this->authorize('activePromocode', Subscription::class);

        $discount = $company->getActiveCoupon();

        abort_if(!$discount, 400, __('strings.no_promocode_applied'));

        return response()->json([
            'expiration_date' => $discount?->expiry?->format('Y-m-d H:i:s'),
            'data' => $discount->coupon,
            'message' => __('strings.expiration_date_returned'),
            'status' => '1',
        ]);
    }
}
