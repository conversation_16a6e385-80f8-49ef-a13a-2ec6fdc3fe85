<?php

namespace App\Http\Controllers\Api\v3;

use App\Contracts\Services\SMS\SMSServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Campaign\SMS\CreditRequiredCampaignSMSRequest;
use App\Http\Requests\v3\Campaign\SMS\ListCampaignSMSRequest;
use App\Http\Requests\v3\Campaign\SMS\StoreCampaignSMSRequest;
use App\Models\CompanyCampaignSMS;
use App\ShortUrl;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Traits\SMS;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CompanyCampaignSMSController extends Controller
{
    use ApiResponser;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(ListCampaignSMSRequest $request)
    {
        $this->authorize('viewAny', [CompanyCampaignSMS::class]);

        /**
         * @var User $user
         */
        $user = Auth::user();
        $company = $user->company;

        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        $campaign_sms = $company->campaign_sms();

        if ($start_date && $end_date) {
            $campaign_sms = $campaign_sms
                ->whereDate('created_at', '>=', $start_date)
                ->whereDate('created_at', '<=', $end_date);
        }

        if ($request->has('search')) {
            $search = strtolower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $campaign_sms = $campaign_sms->whereLike([
                'created_at',
                'name',
                'credit_used',
                'client_count'
            ], $search);
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');

        // Database level sorting
        if ($request->has('orderBy')) {
            $campaign_sms = $campaign_sms->orderBy($orderBy, $orderDirection);
        }
        if (!$request->has('orderBy')) {
            $campaign_sms = $campaign_sms->latest();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('strings.sms_returned_successfully'),
                    'status' => '1',
                ])->merge($campaign_sms->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $campaign_sms->lazy(),
            'message' => __('strings.sms_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreCampaignSMSRequest $request, SMSServiceInterface $smsService)
    {
        $this->authorize('create', [CompanyCampaignSMS::class]);
        return DB::transaction(function () use ($request, $smsService) {
            $message = $request->content;

            if ($request->boolean('with_unsubscribe_link')) {
                $message = "\n" . __('strings.to_unsubscribe');
                $message = "\n" . "{{unsubscribe_marketing_link}}";
            }

            /**
             * @var User $user
             */
            $user = Auth::user();
            $company = $user->company;

            $campaign_sms = $company->campaign_sms();

            $clients = $company->filterClients($request?->client_filter ?? []);

            $clients = $clients->map(function ($client) {
                return [
                    "client" => $client,
                    "booking" => $client->relationLoaded('booking') || $client->relationLoaded('group_booking') ?
                        ($client?->booking ?? $client?->group_booking?->booking) : null,
                ];
            });

            $data = $campaign_sms->create([
                'company_id' => $company->id,
                'name' => $request->name,
                'content' => $message,
                'client_filter' => $request->client_filter ? collect($request->client_filter)->values()->all() : null,
                'credit_used' => 0,
                'client_count' => count($clients),
                // 'summary' => $request->summary,
            ]);

            $sms = $smsService->sendBulkSMSToClient($user, $message, $clients, $data);

            $data->credit_used = $sms->credit_used;
            $data->save();

            return response()->json([
                "data" => $data,
                'message' => __('strings.sms_sent_successfully'),
                'status' => '1'
            ]);
        });
    }

    /**
     * Display the how much credit are required.
     *
     * @param  CreditRequiredCampaignSMSRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function creditRequired(CreditRequiredCampaignSMSRequest $request, SMSServiceInterface $smsService)
    {
        $this->authorize('create', [CompanyCampaignSMS::class]);

        $message = $request->content;

        if ($request->boolean('with_unsubscribe_link')) {
            $message = "\n" . __('strings.to_unsubscribe');
            $message = "\n" . ShortUrl::sample_short_url();
        }

        /**
         * @var User $user
         */
        $user = Auth::user();
        $company = $user->company;

        // TODO: get all clients from filter
        $clients = $company->filterClients($request?->client_filter ?? []);

        $clients = $clients->map(function ($client) {
            return [
                "client" => $client,
                "booking" => $client->relationLoaded('booking') || $client->relationLoaded('group_booking') ?
                    ($client?->booking ?? $client?->group_booking?->booking) : null,
            ];
        });

        $credits = $smsService->getBulkSMSLength($user, $message, $clients);

        return response()->json([
            "data" => $credits,
            'message' => __('strings.sms_credit_required_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Fetch the client related to this sms.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return \Illuminate\Http\Response
     */
    public function list(Request $request, CompanyCampaignSMS $companyCampaignSMS)
    {
        $this->authorize('listClient', $companyCampaignSMS);

        /**
         * @var User $user
         */
        $user = Auth::user();
        $company = $user->company;

        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        $client_sms = $companyCampaignSMS->sendables()->with('client');

        if ($start_date && $end_date) {
            $client_sms = $client_sms
                ->whereDate('created_at', '>=', $start_date)
                ->whereDate('created_at', '<=', $end_date);
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');

        // Database level sorting
        if ($request->has('orderBy')) {
            $client_sms = $client_sms->orderBy($orderBy, $orderDirection);
        }
        if (!$request->has('orderBy')) {
            $client_sms = $client_sms->latest();
        }

        $isPaginated = $request->has('search');

        if ($isPaginated) {
            $client_sms = $client_sms->lazy();
        }

        if ($request->has('search')) {
            $search = strtolower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $client_sms = $client_sms->filter(function ($query) use ($search) {
                $fields = [
                    $query->type,
                    $query->text,
                    json_encode($query->number),
                    $query->client?->first_name ?? '',
                    $query->client?->last_name ?? '',
                    $query->client?->fullName() ?? '',
                    $query->client?->phone() ?? '',
                    $query->client?->email ?? '',
                ];

                return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        if (!$isPaginated && $request->has('page')) {
            $client_sms = $client_sms->paginate($request->input('per_page'));
        }

        if (!$isPaginated && !$request->has('page')) {
            $client_sms = $client_sms->get();
        }

        if ($isPaginated && $request->has('page')) {
            $client_sms = $this->paginate($client_sms);
        }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.clients_returned_successfully'),
                    'status' => '1',
                ])->merge($client_sms)
            );
        }

        return response()->json([
            'data' => $client_sms->values(),
            'message' => __('strings.clients_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\CompanyCampaignSMS  $companyCampaignSMS
     * @return \Illuminate\Http\Response
     */
    public function destroy(CompanyCampaignSMS $companyCampaignSMS)
    {
        //
    }
}
