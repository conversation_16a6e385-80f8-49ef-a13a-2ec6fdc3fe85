<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\CompanyTerminal;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Terminal\UpdateTerminalDeviceRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CompanyTerminalController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        $this->authorize('viewAny', [CompanyTerminal::class]);

        $company = $user->company;

        if ($request->has('company_id') && $user->isMasterAdmin()) {
            $company = Company::findOrFail($request->company_id);
        }

        return response()->json([
            'data' => $company->getAndUpdateTerminals(),
            'message' => __('pos_strings.company_terminals_list_return'),
            'status' => '1'
        ]);
    }

    public function update(CompanyTerminal $companyTerminal, UpdateTerminalDeviceRequest $request)
    {
        $this->authorize('update', $companyTerminal);

        if ($request->has('nickname')) {
            $companyTerminal->nickname = $request->nickname;
        }

        $companyTerminal->save();

        return response()->json([
            'data' => $companyTerminal,
            'message' => __('pos_strings.company_terminal_updated'),
            'status' => '1'
        ]);
    }
}
