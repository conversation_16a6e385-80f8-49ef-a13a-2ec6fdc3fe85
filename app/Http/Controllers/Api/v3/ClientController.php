<?php

namespace App\Http\Controllers\Api\v3;

use App\Client;
use App\ClientLetterOfConsent;
use App\ClientPrescription;
use App\ClientTreatment;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Exports\AllClientExport;
use App\GeneralNote;
use App\Http\Controllers\Api\v2\ClientController as V2ClientController;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Client\ClientDataDownloadRequest;
use App\Http\Requests\v3\Client\ClientIndexRequest;
use App\Http\Requests\v3\Client\ClientNewFilterRequest;
use App\LetterOfConsent;
use App\Traits\ApiResponser;
use App\Traits\SaveFile;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ClientController extends Controller
{
    use SaveFile;
    use ApiResponser;

    public function getExport()
    {
        $user = Auth::user();
        $company = $user->company;

        return Excel::download(
            new AllClientExport($company),
            'clients.xlsx'
        );
    }

    public function index(ClientIndexRequest $request, Client $client)
    {
        /**
         * @var User $user
         */
        $user = Auth::user();

        if ($user->user_role == User::ADMIN && $user->company->email == $user->email && $request->input('filter') != 'my') {
            $clients = $user->company->clients()->without('general_notes', 'letter_of_consents', 'treatments', 'general_questions', 'health_questionaries', 'aesthetic_insterests', 'covid19s', 'verification');
        } else {
            $clients = $user->accesses()->without('general_notes', 'letter_of_consents', 'treatments', 'general_questions', 'health_questionaries', 'aesthetic_insterests', 'covid19s', 'verification');
        }

        if ($request->with_count && count($request->with_count)) {
            $clients = $clients->withCount($request->with_count);
        }

        if ($request->has('priority_ids')) {
            $ids_ordered = implode(',', $request->priority_ids);
            $clients
                ->orderByRaw("FIELD(id, $ids_ordered) DESC");
        }

        $isPaginated = false;

        if ($request->has('filter')) {

            switch ($request->filter) {
                case 'same_personal_id':
                    if (!$isPaginated) {
                        $clients = $clients->get()->where('personal_id', '!=', '');
                        $isPaginated = true;
                    }
                    $clients = new EloquentCollection($clients->groupBy('personal_id')->filter(function ($groups) {
                        return $groups->count() > 1;
                    })->flatten());
                    break;
                case 'my':
                    # code...
                    break;

                default:
                    $clients = $clients->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
                    break;
            }
        }

        if ($request->missing('filter')) {
            if ($request->boolean('withTrashed', false)) {
                $clients = $clients;
            } elseif ($request->boolean('onlyTrashed', false)) {
                $clients = $clients->where('deleted_at', '!=', null);
            } else {
                $clients = $clients->where('deleted_at', null);
            }
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if ($orderBy == 'last_name' || $orderBy == 'phone_number' || $orderBy == 'email') {
                if (!$isPaginated) {
                    $clients = $clients->get();
                    $isPaginated = true;
                }

                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = in_array($orderBy, ['last_name', 'email']) ? (SORT_NATURAL | SORT_FLAG_CASE) : (in_array($orderBy, ['phone_number']) ? SORT_STRING : SORT_FLAG_CASE);
                $clients =  $clients->sortBy($orderBy, $sortExtra, $isDescOrder);
                // $clients =  $clients->sort(function ($a, $b) use ($orderBy) {
                //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
                // });
            } elseif ($orderBy == 'pending_sign') {
                $clients = $clients->where(function ($query) {
                    $query->whereHas('treatments', function ($query) {
                        $query->where('signed_at', null);
                    })->orWhereHas('letter_of_consents', function ($query) {
                        $query->where('verified_signed_at', null);
                    })->orWhereHas('general_notes', function ($query) {
                        $query->where('signed_at', null);
                    });
                });
            } elseif ($orderBy == 'needs_prescription') {
                $clients = $clients->where(function ($query) {
                    $query->whereHas('prescriptions', function ($query) {
                        $query->where('signed_at', null);
                    });
                });
            } elseif ($orderBy == 'prescribed') {
                $clients = $clients->whereHas('prescriptions', function ($query) {
                    $query->where('signed_at', '!=', null);
                });
                $clients = $clients->select('clients.*', DB::raw('(SELECT client_prescriptions.created_at FROM client_prescriptions WHERE client_prescriptions.client_id = clients.id ORDER BY client_prescriptions.created_at desc LIMIT 1) as prescriptions_created_at'));
                $clients->orderBy('prescriptions_created_at', 'desc');
            } else {
                $clients = $clients->orderBy($orderBy, $orderDirection);
            }
        }

        if ($request->has('search')) {
            if (!$isPaginated) {
                $clients = $clients->with('addresses')->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            if (count(explode(' ', $search)) > 1) {
                $search_array = explode(' ', $search);

                $last_name = end($search_array);
                $first_name = null;

                for ($i = 0; $i < count($search_array) - 1; $i++) {
                    $first_name = $first_name . " " . $search_array[$i];
                }

                $clients = $clients->filter(function ($value) use ($search) {
                    $search_array = explode(' ', $search);

                    $last_name = end($search_array);
                    $first_name = null;

                    for ($i = 0; $i < count($search_array) - 1; $i++) {
                        $first_name = $first_name . " " . $search_array[$i];
                    }
                    $first_name =  trim($first_name);

                    if (strlen($last_name) > 1) {
                        return (
                            (Str::contains(Str::lower($value->first_name), $first_name) &&
                                Str::contains(Str::lower($value->last_name), $last_name)))
                            ||
                            Str::contains(Str::lower($value->full_name), $search) ||
                            Str::contains(Str::lower($value->email), $search) ||
                            Str::contains(Str::lower("+" . $value->country_code . " " . $value->phone_number), $search) ||
                            Str::contains(Str::lower("+" . $value->social_security_number), $search) ||
                            Str::contains(Str::lower("+" . $value->personal_id), $search) ||
                            Str::contains(Str::lower($value->phone_number), $search) ||
                            $value->addresses->filter(function ($address) use ($search) {
                                return Str::contains(Str::lower($address->street_address), $search) ||
                                    Str::contains(Str::lower($address->state), $search) ||
                                    Str::contains(Str::lower($address->zip_code), $search) ||
                                    Str::contains(Str::lower($address->city), $search);
                            })->count() > 0;
                    } else {
                        return (Str::contains(Str::lower($value->first_name), $first_name)
                            &&
                            Str::lower($value->last_name)[0] == $last_name[0]
                        )
                            ||
                            Str::contains(Str::lower($value->full_name), $search) ||
                            Str::contains(Str::lower($value->email), $search) ||
                            Str::contains(Str::lower("+" . $value->country_code . " " . $value->phone_number), $search) ||
                            Str::contains(Str::lower("+" . $value->social_security_number), $search) ||
                            Str::contains(Str::lower("+" . $value->personal_id), $search) ||
                            $value->addresses->filter(function ($address) use ($search) {
                                return Str::contains(Str::lower($address->street_address), $search) ||
                                    Str::contains(Str::lower($address->state), $search) ||
                                    Str::contains(Str::lower($address->zip_code), $search) ||
                                    Str::contains(Str::lower($address->city), $search);
                            })->count() > 0;
                    }
                });
            } else {
                $clients = $clients->filter(function ($value) use ($search) {
                    return Str::contains(Str::lower($value->first_name), $search) ||
                        Str::contains(Str::lower($value->last_name), $search) ||
                        Str::contains(Str::lower($value->full_name), $search) ||
                        Str::contains(Str::lower($value->email), $search) ||
                        Str::contains(Str::lower("+" . $value->country_code . " " . $value->phone_number), $search) ||
                        Str::contains(Str::lower("+" . $value->social_security_number), $search) ||
                        Str::contains(Str::lower("+" . $value->personal_id), $search) ||
                        $value->addresses->filter(function ($address) use ($search) {
                            return Str::contains(Str::lower($address->street_address), $search) ||
                                Str::contains(Str::lower($address->state), $search) ||
                                Str::contains(Str::lower($address->zip_code), $search) ||
                                Str::contains(Str::lower($address->city), $search);
                        })->count() > 0;
                });
            }

            if (
                $request->missing('orderBy')
                ||
                ($request->has('orderBy') && (in_array($request->orderBy, ['created_at'])))
            ) {
                $clients = $clients->sortByDesc(function ($i, $k) use ($search) {
                    $props = [
                        // 'full_name' => 10,
                        'first_name' => 5,
                        'last_name' => 5,
                        'email' => 2,
                        'phone_number' => 1,
                        'country_code' => 1,
                    ];
                    $weight = 0;
                    foreach ($props as $prop => $acquire_weight) {
                        if (strpos(Str::lower($i->{$prop}), $search) !== false) {
                            $weight += $acquire_weight;
                        } // Increase weight if the search term is found
                    }
                    $search_array = explode(' ', $search);
                    if (Str::lower($i->first_name) == Str::lower($search_array[0])) {
                        $weight = $weight + 100;
                    }
                    return $weight;
                });
            }
        }

        if (!$isPaginated && $request->has('page')) {
            $clients = $clients->paginate($request->input('per_page'));
        }

        if (!$isPaginated && !$request->has('page')) {
            $clients = $clients->get();
        }

        if ($request->append && count($request->append)) {
            $clients->append($request->append);
        }

        if ($isPaginated && $request->has('page')) {
            $clients = $this->paginate($clients);
        }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => 'Clients return successfully',
                    'status' => '1',
                ])->merge($clients)
            );
        }

        return response()->json([
            'data' => $clients,
            'message' => 'Clients return successfully',
            'status' => '1',
        ]);
    }

    public function newFilter(ClientNewFilterRequest $request)
    {
        $user = Auth::user();

        $company = $user->company;

        $client_fields = $request->client_filter ?? [];

        if ($request->search) {
            $client_fields[] = [
                'value' => $request->search,
                'key' => 'equal',
                'type' => 'search',
            ];
        }

        $clients = $company->filterClients($client_fields, $request->has('page'), !!$request->count);

        if ($request->has('page') && !$request->count) {
            return response()->json(
                collect([
                    'message' => 'Clients return successfully',
                    'status' => '1',
                ])->merge($clients)
            );
        }

        return response()->json([
            'data' => $clients,
            'message' => 'Clients return successfully',
            'status' => '1',
        ]);
    }

    public function marketingUnsubscribe($id)
    {
        $client = Client::findOrFail($id);

        $client->unsubscribeMarketing();

        return response()->json([
            'message' => 'Unsubscribe successfully',
            'status' => '1',
        ]);
    }

    public function downloadClientData(ClientDataDownloadRequest $request, PDFServiceInterface $pdfService)
    {
        $user = Auth::user();
        $company = $user->company;

        $filename = $this->generateFilePath('client_data', $user, 'client', 'pdf');
        $file = null;

        if ($request->type == ClientTreatment::class) {
            $treatment = ClientTreatment::where('id', $request->id)->whereHas('client', function ($query) use ($company) {
                return $query->where('company_id', $company->id);
            })->firstOrFail();

            $file = $pdfService->client($treatment->client)->procedure($treatment)->saveFile($user, $filename);
        }

        if ($request->type == GeneralNote::class) {
            $note = GeneralNote::where('id', $request->id)->whereHas('client', function ($query) use ($company) {
                return $query->where('company_id', $company->id);
            })->firstOrFail();

            $file = $pdfService->client($note->client)->generalNote($note)->saveFile($user, $filename);
        }

        if ($request->type == ClientLetterOfConsent::class) {
            $letterOfConsent = ClientLetterOfConsent::where('id', $request->id)->whereHas('client', function ($query) use ($company) {
                return $query->where('company_id', $company->id);
            })->firstOrFail();

            $file = $pdfService->client($letterOfConsent->client)->letterOfConsent($letterOfConsent)->saveFile($user, $filename);
        }

        if ($request->type == CompanyBooking::class) {
            $booking = CompanyBooking::where('id', $request->id)->where('company_id', $company->id)->firstOrFail();

            $file = $pdfService->client($booking->client)->booking($booking)->saveFile($user, $filename);
        }

        if ($request->type == CompanyBookingClient::class) {
            $clientBooking = CompanyBookingClient::where('id', $request->id)->whereHas('client', function ($query) use ($company) {
                return $query->where('company_id', $company->id);
            })->firstOrFail();

            $file = $pdfService->client($clientBooking->client)->booking($clientBooking->booking, $clientBooking)->saveFile($user, $filename);
        }

        if ($request->type == CompanyBookingClient::class) {
            $clientBooking = CompanyBookingClient::where('id', $request->id)->whereHas('client', function ($query) use ($company) {
                return $query->where('company_id', $company->id);
            })->firstOrFail();

            return $pdfService->client($clientBooking->client)->booking($clientBooking->booking, $clientBooking)->download();
        }

        if ($request->type == ClientPrescription::class) {
            $prescription = ClientPrescription::where('id', $request->id)->whereHas('client', function ($query) use ($company) {
                return $query->where('company_id', $company->id);
            })->firstOrFail();

            $file = $pdfService->client($prescription->client)->prescription($prescription)->saveFile($user, $filename);
        }

        if ($file) {
            $stream = Storage::readStream($file->filename);
            $mimeType = Storage::mimeType($file->filename) ?? 'application/pdf';

            return new StreamedResponse(function () use ($stream, $file) {
                try {
                    fpassthru($stream);
                } finally {
                    // Delete the file after streaming is complete
                    $file->delete();
                }
            }, 200, [
                'Content-Type'        => $mimeType,
                'Content-Disposition' => 'inline; filename="client.pdf"',
            ]);
        }

        return response()->json([
            'message' => 'Invalid request',
            'status' => '0',
        ]);
    }
}
