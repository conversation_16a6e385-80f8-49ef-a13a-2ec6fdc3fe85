<?php

namespace App\Http\Controllers\Api\v3;

use App\GeneralTemplate;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\ListTemplateQuestionRequest;
use App\Http\Requests\v3\Managment\UpdateTemplateQuestionRequest;
use App\Http\Requests\v3\StoreTemplateQuestionRequest;
use App\Models\GeneralTemplateQuestion;
use Illuminate\Http\Request;

class TemplateQuestionController extends Controller
{
    public function index(GeneralTemplate $template, ListTemplateQuestionRequest $request)
    {
        $template_questions = $template->questions();

        if ($request->has('search')) {
            $template_questions = $template_questions->whereLike(['question', 'options'], $request->search);
        }

        if ($request->has('orderBy') && $request->has('orderDirection')) {
            $template_questions = $template_questions->orderBy($request->orderBy, $request->orderDirection);
        } else {
            $template_questions = $template_questions->latest();
        }

        if ($request->has('page')) {
            return response()->json(collect([
                'message' => __('management_strings.template_questions_returned_successfully'),
                'status' => '1'
            ])->merge($template_questions->paginate($request->input('per_page'))));
        }

        return response()->json([
            'data' => $template_questions->get(),
            'message' => __('management_strings.template_questions_returned_successfully'),
            'status' => '1'
        ]);
    }

    public function store(StoreTemplateQuestionRequest $request, GeneralTemplate $template)
    {
        $this->authorize('viewAny', [GeneralTemplateQuestion::class]);

        $file = null;
        if (in_array($request->type, config('general_template.type_with_image')) && $request->hasFile('options')) {
            $file = $this->saveFile($request->file('options'), 'general_template_questions');
        }

        $template_question = $template->questions()->create([
            'question' => $request->question,
            'type' => $request->type,
            'required' => $request->boolean('required'),
            'default' => $request->default,
            'options' => $file ? [$file->filename] : (in_array($request->type, array_keys(config('general_template.type_with_options'))) ? $request->options : null),
        ]);

        return response()->json([
            'data' => $template_question->refresh(),
            'message' => __('management_strings.template_questions_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(UpdateTemplateQuestionRequest $request, GeneralTemplate $template, GeneralTemplateQuestion $template_question)
    {
        $this->authorize('viewAny', [GeneralTemplateQuestion::class]);
       
        if ($request->has('question')) {
            $template_question->question = $request->question;
        }

        if ($request->has('type')) {
            $template_question->type = $request->type;
        }

        if ($request->has('required')) {
            $template_question->required = $request->required;
        }

        if ($request->has('default')) {
            $template_question->default = $request->default;
        }

        if ($request->has('options')) {
            if (in_array($request->type, config('general_template.type_with_image')) && $request->hasFile('options')) {
                $file = $this->saveFile($request->file('options'), 'general_template_questions');
                $template_question->options = [$file->filename];
            } else {
                $template_question->options = $request->options;
            }
        }

        if ($template_question->isDirty()) {
            $template_question->save();
        }

        return response()->json([
            'data' => $template_question->refresh(),
            'message' => __('management_strings.template_questions_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function destroy(GeneralTemplate $template, GeneralTemplateQuestion $template_question)
    {
        $this->authorize('viewAny', [GeneralTemplateQuestion::class]);

        $template_question->delete();

        return response()->json([
            'data' => $template_question,
            'message' => __('management_strings.template_questions_deleted_successfully'),
            'status' => '1'
        ]);
    }

    public function shift(GeneralTemplate $template, GeneralTemplateQuestion $template_question, Request $request)
    {
        $this->authorize('viewAny', [GeneralTemplateQuestion::class]);

        $up = $request->boolean('dragUp');
        $next_question = GeneralTemplateQuestion::findOrFail($request->input("nextIndex"));

        $template_question->move(!$up ? 'moveAfter' : 'moveBefore', $next_question);

        return response()->json([
            'data' => $template_question,
            'message' => __('management_strings.template_questions_updated_successfully'),
            'status' => '1'
        ]);
    }
}
