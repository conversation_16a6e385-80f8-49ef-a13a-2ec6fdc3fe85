<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyReceipt;
use App\CompanyReceiptRefund;
use App\Contracts\Services\POS\RefundServiceInterface;
use App\Contracts\Services\POS\RefundReceiptServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Export\GetRefundReceiptExportTypeRequest;
use App\Http\Requests\v3\Refund\RefundCompanyReceiptRequest;
use App\Http\Requests\v3\Refund\RetryCompanyReceiptRefundRequest;
use App\ReceiptExport;
use App\ReceiptType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CompanyReceiptRefundController extends Controller
{
    function __construct(private readonly RefundServiceInterface $refundService, private readonly RefundReceiptServiceInterface $refundReceiptService)
    {
    }

    public function refund(CompanyReceipt $companyReceipt, RefundCompanyReceiptRequest $request)
    {
        $user = Auth::user();
        $this->authorize('create', [CompanyReceiptRefund::class, $companyReceipt]);

        $refund = $this->refundService->processRefund($request, $user, $companyReceipt);

        return response()->json([
            'data' => $refund->loadMissing('gift_card'),
            'message' => __('pos_strings.refund_request_has_been_sent'),
            'status' => '1'
        ]);
    }

    public function show(CompanyReceiptRefund $companyReceiptRefund, Request $request)
    {
        $this->authorize('view', [$companyReceiptRefund]);

        $with = [];

        $companyReceiptRefund = $this->refundReceiptService->updateStatus($companyReceiptRefund);

        if ($request->has('receipt')) {
            $with[] = 'receipt';
        }

        if ($request->has('items')) {
            $with[] = 'items';
        }

        if ($request->has('gift_card')) {
            $with[] = 'gift_card';
        }

        if ($request->has('terminal')) {
            $with[] = 'terminal';
        }

        $companyReceiptRefund = $companyReceiptRefund->loadMissing($with);

        return response()->json([
            'data' => $companyReceiptRefund,
            'message' => __('pos_strings.company_receipt_refund_returned_success'),
            'status' => '1'
        ]);
    }

    public function retry(CompanyReceiptRefund $companyReceiptRefund, RetryCompanyReceiptRefundRequest $request)
    {
        $companyReceiptRefund = $this->refundReceiptService->updateStatus($companyReceiptRefund);

        $this->authorize('retry', $companyReceiptRefund);

        $receipt = $companyReceiptRefund->receipt;

        $companyReceiptRefund = $this->refundService->retryRefund($request, auth()->user(), $receipt, $companyReceiptRefund);

        return response()->json([
            'data' => $companyReceiptRefund,
            'message' => __('pos_strings.company_receipt_refund_processed_for_retry'),
            'status' => '1'
        ]);
    }

    public function abort(CompanyReceiptRefund $companyReceiptRefund)
    {
        $companyReceiptRefund = $this->refundReceiptService->updateStatus($companyReceiptRefund);

        $this->authorize('abort', [$companyReceiptRefund]);

        return DB::transaction(function () use ($companyReceiptRefund) {
            $companyReceiptRefund = $this->refundService->abortRefund($companyReceiptRefund);

            $companyReceiptRefund?->company?->getCurrentOrCreateBatch();

            return response()->json([
                'data' => $companyReceiptRefund,
                'message' => __('pos_strings.company_receipt_refund_aborted'),
                'status' => '1'
            ]);
        });
    }

    public function export(CompanyReceiptRefund $companyReceiptRefund, GetRefundReceiptExportTypeRequest $request)
    {

        $this->authorize('export', $companyReceiptRefund);
        $user = Auth::user();

        $type = $request->type;
        $receipt_type = !!$companyReceiptRefund->downloaded ? ReceiptType::kopia : ReceiptType::normal;

        $user_downloads = $companyReceiptRefund->user_downloads;
        $user_downloads["user_$user->id"] =  isset($user_downloads["user_$user->id"]) ? $user_downloads["user_$user->id"] + 1 : 1;
        $companyReceiptRefund->user_downloads = $user_downloads;
        $companyReceiptRefund->downloaded += 1;
        $companyReceiptRefund->save();

        if($type == "mail") {
            $this->refundReceiptService->exportRefund($companyReceiptRefund, ReceiptExport::MAIL, $receipt_type);

            return response()->json([
                'message' => __('pos_strings.mail.mail_sent_successfully'),
                'status' => '1'
            ]);
        }

        return $this->refundReceiptService->exportRefund($companyReceiptRefund, ReceiptExport::PDF_DOWNLOAD, $receipt_type);
    }

    public function view(CompanyReceiptRefund $companyReceiptRefund)
    {
        $this->authorize('export', $companyReceiptRefund);

        return $this->refundReceiptService->exportRefund($companyReceiptRefund, ReceiptExport::PDF_STREAM , ReceiptType::normal);
    }
}
