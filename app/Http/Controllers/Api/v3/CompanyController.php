<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\Setting;
use App\Contracts\Services\POS\InfrasecEnrollmentServiceInterface;
use App\Contracts\Services\Subscription\SubscriptionServiceInterface;
use App\FutureSubscription;
use App\Http\Controllers\Controller;
use App\Http\Integrations\POSInfrasec\Exceptions\RegisterIDAlreadyExistException;
use App\Http\Requests\v3\Company\UpdateCompanyRequest;
use App\Http\Requests\v3\Infrasec\UpdateMasterCompanyRequest;
use App\Jobs\QMSZipDownloadJob;
use App\Mail\POS\SendMerchantIdActivationMail;
use App\Traits\ApiResponser;
use App\Traits\ColorGenerator;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use App\UserNotification;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;

class CompanyController extends Controller
{
    use SaveFile, VerifiesEmails, ApiResponser, ColorGenerator, GetEncryptedFile;

    function __construct(private readonly InfrasecEnrollmentServiceInterface $infrasecEnrollService) {}

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCompanyRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $this->authorize('update', $company);

        return DB::transaction(function () use ($request, $user, $company) {
            if ($request->has('unit')) {
                $company->unit = $request->input('unit');

                $company->save();

                return response()->json([
                    'data' => Auth::user(),
                    'message' => __('strings.Company_currency_updated_successfully'),
                    'status' => '1'
                ]);
            }

            if ($request->has('profile_photo')) {
                $file = $this->saveFile($request->file('profile_photo'), 'company_photo', null, true);

                // if ($company->file) {
                //     $company->file->delete();
                // }
                if ($company->files && $company->profile_photo) {
                    foreach ($company->files as $f) {
                        $temp_f_name =  str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($f->filename));
                        if ($temp_f_name  == $company->profile_photo) {
                            $f->delete();
                            break;
                        }
                    }
                }

                $company->file()->save($file);
                $company->profile_photo = $file->filename;
            }

            if ($request->has('cover_image_deleted') && $company->cover_image) {
                if (Storage::disk('s3')->exists($company->cover_image)) {
                    Storage::disk('s3')->delete($company->cover_image);
                }
                $company->cover_image = "";
            }

            if ($request->has('cover_image')) {
                $file = $this->saveFile($request->file('cover_image'), 'company_photo', null, false);

                // if ($company->file) {
                //     $company->file->delete();
                // }
                if ($company->files && $company->cover_image) {
                    foreach ($company->files as $f) {
                        $temp_f_name =  str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($f->filename));
                        if ($temp_f_name  == $company->cover_image) {
                            $f->delete();
                            break;
                        }
                    }
                }

                $company->file()->save($file);
                $company->cover_image = $file->filename;
            }

            if ($request->has('company_name')) {
                $company->company_name = $request->input('company_name');

                if (!$user->title) {
                    $user->title = $request->company_name;
                }
            }

            if ($request->has('theme')) {
                $company->theme = $request->input('theme');
            }

            if ($request->has('street_address')) {
                $company->street_address = $request->input('street_address', '') ?? '';
            }

            if ($request->has('city')) {
                $company->city = $request->input('city', '') ?? '';
            }

            if ($request->has('state')) {
                $company->state = $request->input('state', '') ?? '';
            }

            if ($request->has('zip_code')) {
                $company->zip_code = $request->input('zip_code', '') ?? '';
            }
            if ($request->has('is_black_text')) {
                $company->is_black_text = $request->input('is_black_text', '1') ?? '';
            }

            // if ($request->has('country')) {
            //     $company->country = $request->input('country');
            // }

            if ($request->has('mobile_number')) {
                $company->mobile_number = $request->input('mobile_number', '') ?? '';

                if (!$user->mobile_number) {
                    $user->mobile_number = $request->mobile_number;
                }
            }

            if ($request->has('country_code')) {
                $company->country_code = $request->input('country_code', '') ?? '';

                if (!$user->country_code) {
                    $user->country_code = $request->country_code;
                }
            }

            if ($request->has('timezone')) {
                $company->timezone = $request->input('timezone', '') ?? '';
            }

            if ($request->has('organization_number') && !$company->ccu_register_id) {
                $company->organization_number = $request->input('organization_number', '');
            }

            if ($request->has('first_name')) {
                $company->first_name = $request->first_name;

                if (!$user->first_name) {
                    $user->first_name = $request->first_name;
                }
            }

            if ($request->has('last_name')) {
                $company->last_name = $request->last_name;

                if (!$user->last_name) {
                    $user->last_name = $request->last_name;
                }
            }

            $dirty = $company->isDirty();
            $userDirty = $user->isDirty();

            if ($dirty) {
                $company->save();
            }

            if ($userDirty) {
                $user->save();
            }

            if ($company->ccu_register_id && $request->hasAny(['city', 'state', 'zip_code', 'street_address', 'mobile_number']) && $dirty) {
                // dispatch(function () use ($company) {
                $this->infrasecEnrollService->update($company);
                // });
            }

            return response()->json([
                'data' => Auth::user(),
                'message' => __('strings.Company_updated_successfully'),
                'status' => '1'
            ]);
        });
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function masterUpdate(UpdateMasterCompanyRequest $request, Company $company, SubscriptionServiceInterface $subscriptionService)
    {
        $this->authorize('masterUpdate', [Company::class]);

        return DB::transaction(function () use ($request, $company, $subscriptionService) {
            $sent_mail = false;
            if (!empty($company->viva_merchant_id)) {
                $sent_mail = true;
            }
            if ($request->has('email')) {
                $user = $company->users()->where('email', $company->email)->firstOrFail();
                $user->email = $request->input('email');
                $user->save();
                $company->email = $request->input('email');
                $company->save();
            }

            if ($request->has('street_address')) {
                $company->street_address = $request->input('street_address');
            }

            if ($request->has('city')) {
                $company->city = $request->input('city');
            }

            if ($request->has('company_name')) {
                $company->company_name = $request->input('company_name');
            }

            if ($request->has('state')) {
                $company->state = $request->input('state');
            }

            if ($request->has('zip_code')) {
                $company->zip_code = $request->input('zip_code');
            }

            if ($request->has('country_code')) {
                $company->country_code = $request->input('country_code');
            }

            if ($request->has('timezone')) {
                $company->timezone = $request->input('timezone');
            }

            if ($request->has('organization_number')) {
                $company->organization_number = $request->input('organization_number', '');
            }

            if ($request->has('isv_percentage')) {
                $company->isv_percentage = $request->isv_percentage;
            }

            if ($request->has('viva_merchant_id')) {
                $language = Setting::getSetting($company, Setting::LANGUAGE)->value;
                $email = $company->email;

                if (empty($company->ccu_register_id)) {
                    try {
                        $company = $this->infrasecEnrollService->store($company);
                    } catch (RegisterIDAlreadyExistException $th) {
                        report($th);
                    }
                }

                if ($email && $company->viva_merchant_id != $request->viva_merchant_id) {
                    Mail::to($email)->locale($language)->send(new SendMerchantIdActivationMail($company, $sent_mail));
                }

                $company->viva_merchant_id = $request->input('viva_merchant_id');
            }

            // if ($request->has('country')) {
            //     $company->country = $request->input('country');
            // }

            if ($request->has('is_blocked')) {
                if ($request->is_blocked == '1') {
                    //TODO: remove all users token.
                    foreach ($company->users as $user) {
                        $user->tokens()->delete();
                    }
                }
                $company->is_blocked = $request->input('is_blocked', 0);
            }

            if ($request->has('is_read_only')) {
                $company->is_read_only = $request->input('is_read_only', 0);
            }

            if ($request->has('verification')) {
                $company->verification = $request->input('verification');
            }

            if ($request->has('verification_notes')) {
                $company->verification = $request->input('verification');

                Setting::updateOrCreate([
                    'key' => Setting::COMPANY_VERIFICATION_NOTES,
                    'company_id' => $company->id,
                ], [
                    'value' => $request->verification_notes,
                ]);
            }



            $message = __('strings.Company_updated_successfully');

            // if ($company->isDirty()) {
            //     $company->save();
            // }

            $dirty = $company->isDirty();

            $isMerchantChange = $company->isDirty('viva_merchant_id');

            if ($dirty) {
                $company->save();
            }

            if ($isMerchantChange && $company->viva_merchant_id && !$company->is_pos_on) {
                $subscriptionService->subscribeToFutureSubscription($company);
            }

            return response()->json([
                'data' => $company,
                'message' => $message,
                'status' => '1'
            ]);
        });
    }

    function qms_generate(Company $company)
    {
        $this->authorize('qms_generate', $company);

        $cacheKey = 'qms_zip_' . $company->id;

        $user = $company->users()->where('email', $company->email)->firstOrFail();

        Cache::put($cacheKey, 'queue', 86400);
        UserNotification::create([
            'user_id' => $user->id,
            'title' => __('zip.zip_generating'),
            'description' => __('zip.zip_in_queue'),
            'is_read' => 0
        ]);

        $language = Setting::getSetting($company, Setting::LANGUAGE)?->value;

        QMSZipDownloadJob::dispatch($company, $language);

        return response()->json([
            'message' => __('zip.zip_accepted'),
            'status' => '1'
        ]);
    }
}
