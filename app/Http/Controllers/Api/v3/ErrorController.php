<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use App\Mail\ReportBugMail;
use Error;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class ErrorController extends Controller
{
    public function report(Request $request)
    {
        $slack_token = env("SLACK_TOKEN");
        $slack_channel = env("SLACK_CHANNEL");        

        try {
            if($slack_token && $slack_channel) {
                $url = "https://slack.com/api/chat.postMessage";
                $client =  Http::withHeaders([
                    "Authorization" => "Bearer " . $slack_token,
                    "Content-Type" => $request->header('Content-Type'),
                ]);
                $message = "New bug report \n";
                foreach ($request->all() as $key => $val) {
                    $message = $message . "$key:- \n$val\n";
                }

                $response = $client->post($url, [
                    'channel' => $slack_channel,
                    'text' => $message,
                ]);

                if(!$response->successful()) {
                    throw new Error('Not Successfull');
                } else {
                    return response()->json([
                        'message' => $response->object(),
                        'status' => '1',
                    ], 500);
                }
            }

            // Mail::to($mail)->send(new ReportBugMail($request->all()));
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ], 500);
        }

        return response()->json([
            'message' => "Issue reported",
            'status' => '1',
        ]);
    }
}
