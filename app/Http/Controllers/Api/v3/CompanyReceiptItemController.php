<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\CompanyProduct;
use App\CompanyReceipt;
use App\CompanyService;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Receipt\GetCompanyReceiptItemRequest;
use App\Setting;
use App\Traits\ApiResponser;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CompanyReceiptItemController extends Controller
{
    use ApiResponser;

    public function index(GetCompanyReceiptItemRequest $request)
    {
        $this->authorize('viewAny', [CompanyReceipt::class]);

        /**
         * @var User $user
         */
        $user = Auth::user();
        $company = $user->company;

        if ($user->isMasterAdmin()) {
            $company = Company::findOrFail($request->input('company_id'));
        }

        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        $items = $company->receipt_items();

        $items = $items->with('receipt');

        if ($request->has('client_id')) {
            $items = $items->whereHas('receipt', function ($query) use ($request) {
                $query->where('client_id', $request->client_id);
            });
        }

        if ($request->type === 'product') {
            $items = $items->where('receiptable_type', CompanyProduct::class);
        }

        if ($request->type === 'service') {
            $items = $items->where('receiptable_type', CompanyService::class);
        }

        if ($start_date && $end_date) {
            $items = $items
                ->whereDate('created_at', '>=', $start_date)
                ->whereDate('created_at', '<=', $end_date);
        }

        $isPaginated = $request->has('search') || in_array($request->orderBy, [
            'receipt.viva_receipt_id',
            'total',
            "name",
            "selling_price",
            "quantity",
            "tax_information",
            "refunded_quantity",
            "discount_percentage",
            "status",
        ]);

        if ($isPaginated) {
            $items = $items->lazy();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);
            //CURRENT TIME-DATE BASED ON COMPANY TIMEZONE
            $dateFormat = Setting::getDateTimeFormat($company);

            $items = $items->filter(function ($query) use ($search, $dateFormat) {
                $fields = [
                    $query->receipt->viva_receipt_id,
                    $query->name,
                    Carbon::parse($query->created_at)->format($dateFormat),
                    $query->selling_price,
                    $query->quantity,
                    $query->total,
                    $query->status,
                ];

                return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');
        $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

        // Collection level sorting
        if ($isPaginated && $request->has('orderBy')) {
            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

            $items =  $items->sortBy($orderBy, $sortExtra, $isDescOrder);
        }
        if ($isPaginated && !$request->has('orderBy')) {
            $items =  $items->sortBy('created_at', $sortExtra, $orderDirection);
        }

        // Database level sorting
        if (!$isPaginated && $request->has('orderBy')) {
            $items = $items->orderBy($orderBy, $orderDirection);
        }
        if (!$isPaginated && !$request->has('orderBy')) {
            $items = $items->latest();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('pos_strings.receipt_items_returned_successfully'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($items) : $items->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $isPaginated ? $items->values() : $items->lazy(),
            'message' => __('pos_strings.receipt_items_returned_successfully'),
            'status' => '1'
        ]);
    }
}
