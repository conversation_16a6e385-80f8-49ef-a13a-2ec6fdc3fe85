<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyNote;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\ListCompanyNoteRequest;
use App\Http\Requests\v3\StoreCompanyNoteRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CompanyNoteController extends Controller
{
    public function index(ListCompanyNoteRequest $request)
    {
       $this->authorize('viewAny',CompanyNote::class);
       $company_notes = CompanyNote::where('company_id',$request->company_id)->latest()->get();
       return response()->json([
        'data'=> $company_notes,
        'message' => __('strings.company_notes_returned'),
        'status' => '1',
        ]); 
    }

    public function store(StoreCompanyNoteRequest $request)
    {
        $this->authorize('create',CompanyNote::class);
        $user = Auth::user();
        CompanyNote::create([
            'user_id' => $user->id, 
            'company_id' => $request->company_id, 
            'title' => $request->title, 
            'details' => $request->details, 
        ]);

        return response()->json([
            'message' => __('strings.company_note_created'),
            'status' => '1',
        ]);        
    }
    public function update(CompanyNote $note,Request $request)
    {
        $this->authorize('update',CompanyNote::class);
        if ($request->has('title')) {
            $note->title = $request->title; 
        }
        if ($request->has('details')) {
            $note->details = $request->details; 
        }

        $note->save();
        
        return response()->json([
            'message' => __('strings.company_note_updated'),
            'status' => '1',
        ]);        
    }

    public function destroy(CompanyNote $note)
    {
        $this->authorize('delete',[CompanyNote::class,$note]);

        $note->delete();

        return response()->json([
            'message' => __('strings.company_note_delete'),
            'status' => '1',
        ]);     
    }
}