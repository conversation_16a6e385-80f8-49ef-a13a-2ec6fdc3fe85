<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use App\Http\Requests\v3\LeadNote\GetLeadNoteRequest;
use App\Http\Requests\v3\LeadNote\StoreLeadNoteRequest;
use App\Http\Requests\v3\LeadNote\UpdateLeadNoteRequest;
use App\Models\Lead;
use App\Models\LeadNote;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class LeadNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(GetLeadNoteRequest $request)
    {
        $this->authorize('viewAny', [LeadNote::class]);

        $user = Auth::user();

        $notes = LeadNote::query()->with('lead');

        if ($request->has('lead_id')) {
            $notes = $notes->where('lead_id', $request->lead_id);
        }

        if ($request->has('filter') && ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed')) {
            $notes = $notes->{$request->filter}();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $notes = $notes->whereLike([
                'title',
                'note',
                'remind_at'
            ], $search);
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');

        // Database level sorting
        if ($request->has('orderBy')) {
            $notes = $notes->orderBy($orderBy, $orderDirection);
        }
        if (!$request->has('orderBy')) {
            $notes = $notes->latest();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('strings.lead_notes_returned_successfully'),
                    'status' => '1',
                ])->merge($notes->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $notes->lazy(),
            'message' => __('strings.lead_notes_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  StoreLeadNoteRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreLeadNoteRequest $request, Lead $lead)
    {
        $this->authorize('create', [LeadNote::class]);

        $user = Auth::user();

        $leadNote = $lead->notes()->create($request->validated());

        return response()->json([
            'data' => $leadNote,
            'message' => __('strings.lead_notes_created_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Lead  $lead
     * @param  \App\Models\LeadNote  $leadNote
     * @return \Illuminate\Http\Response
     */
    public function show(Lead $lead, LeadNote $leadNote)
    {
        $this->authorize('view', $leadNote);

        return response()->json([
            'data' => $leadNote,
            'message' => __('strings.lead_notes_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  UpdateLeadNoteRequest  $request
     * @param  \App\Models\Lead  $lead
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateLeadNoteRequest $request, Lead $lead, LeadNote $leadNote)
    {
        $this->authorize('update', $leadNote);

        $leadNote->update($request->validated());

        return response()->json([
            'data' => $leadNote,
            'message' => __('strings.lead_notes_updated_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Lead  $lead
     * @return \Illuminate\Http\Response
     */
    public function destroy(Lead $lead, LeadNote $leadNote)
    {
        $this->authorize('delete', $leadNote);

        $leadNote->delete();

        return response()->json([
            'message' => __('strings.lead_notes_deleted_successfully'),
            'status' => '1'
        ]);
    }
}
