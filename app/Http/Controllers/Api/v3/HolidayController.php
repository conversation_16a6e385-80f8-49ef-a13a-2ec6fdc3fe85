<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use om\IcalParser;

class HolidayController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            /**
             * @var \App\Models\Company $company
             */
            $company = Auth::user()->company;

            $setting = Setting::getSetting($company, Setting::HOLIDAY_CALENDARS);
            $lang = Setting::getSetting($company, Setting::LANGUAGE)?->value ?? 'en';

            $calendarIds = json_decode($setting?->value);

            if (!$calendarIds || empty($calendarIds)) {
                return response()->json([
                    'message' => __('strings.calendar_not_set'),
                    'status' => '0'
                ]);
            }

            $data = [];

            foreach ($calendarIds as $key => $calendarData) {
                $calendarId = $lang . "." . $calendarData->calendarId;

                if (!$calendarId) {
                    continue;
                }

                $color = $calendarData?->color ?? '';
                $name = $calendarData?->name ?? '';

                $newData = Cache::remember("holiday-$calendarId", 60 * 60 * 24 * 30, function () use ($calendarId, $color) {
                    $calendarId = urlencode($calendarId);

                    $icalUrl = "https://calendar.google.com/calendar/ical/$calendarId/public/basic.ics";

                    $cal = new IcalParser();
                    $cal->parseFile($icalUrl);

                    $events = $cal->getEvents()->sorted();
                    $data = [];

                    foreach ($events as $event) {
                        $data[] = [
                            'start' => $event['DTSTART']->format('Y-m-d'),
                            'end' => $event['DTEND']->format('Y-m-d'),
                            'summary' => $event['SUMMARY'],
                        ];
                    }

                    return $data;
                });

                $newData = array_map(function ($item) use ($color, $name) {
                    $item['color'] = $color;
                    $item['name'] = $name;

                    return $item;
                }, $newData);

                $data = array_merge($data, $newData);
            }

            // sort by start date
            usort($data, function ($a, $b) {
                return strtotime($a['start']) - strtotime($b['start']);
            });

            return response()->json([
                'data' => $data,
                'message' => __('strings.holidays_fetched'),
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            //throw $th;

            report($th);

            return response()->json([
                'data' => [],
                'message' => __('strings.error_occurred'),
                'status' => '1'
            ]);
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function calendars(Request $request)
    {
        $holiday = collect(config('holiday'));

        $data = $holiday;

        return response()->json([
            'data' => $data,
            'message' => __('strings.calendars_fetched'),
            'status' => '1'
        ]);
    }
}
