<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\CompanyReceipt;
use App\Contracts\Services\POS\PaymentServiceInterface;
use App\Contracts\Services\POS\ReceiptServiceInterface;
use App\Contracts\Services\POS\RefundReceiptServiceInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Export\GetReceiptExportTypeRequest;
use App\Http\Requests\v3\Receipt\GetCompanyReceiptRequest;
use App\Http\Requests\v3\Receipt\RetryCompanyReceiptRequest;
use App\Http\Requests\v3\Receipt\ShowCompanyReceiptRequest;
use App\Http\Requests\v3\Receipt\StatusCompanyReceiptRequest;
use App\Http\Requests\v3\Receipt\StoreCompanyReceiptRequest;
use App\ReceiptExport;
use App\ReceiptType;
use App\Setting;
use App\Traits\ApiResponser;
use App\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CompanyReceiptController extends Controller
{
    use ApiResponser;

    function __construct(private readonly PaymentServiceInterface $paymentService, private readonly ReceiptServiceInterface $receiptService, private readonly RefundReceiptServiceInterface $refundReceiptService) {}

    public function index(GetCompanyReceiptRequest $request)
    {
        $this->authorize('viewAny', [CompanyReceipt::class]);

        /**
         * @var User $user
         */
        $user = Auth::user();
        $company = $user->company;

        if ($user->isMasterAdmin()) {
            $company = Company::findOrFail($request->input('company_id'));
        }

        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        $receipts = $company->receipts();

        if ($request->has('client_id')) {
            $receipts = $receipts->where('client_id', $request->input('client_id'));
        }

        if ($request->has('with')) {
            $receipts = $receipts->with($request->input('with', []));
        }

        $receipts = $receipts->withExists('paid_for_gift_card');

        if ($start_date && $end_date) {
            $receipts = $receipts
                ->whereDate('created_at', '>=', $start_date)
                ->whereDate('created_at', '<=', $end_date);
        }

        $isPaginated = $request->has('search') || in_array($request->orderBy, ['total_formatted', 'payment_mode']);

        if ($isPaginated) {
            $receipts = $receipts->lazy();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);
            //CURRENT TIME-DATE BASED ON COMPANY TIMEZONE
            $dateFormat = Setting::getDateTimeFormat($company);

            $receipts = $receipts->filter(function ($query) use ($search, $dateFormat) {
                $fields = [
                    $query->created_at->format($dateFormat),
                    $query->paid_amount,
                    (string) __("pos_strings.receipt.status.{$query->status}"),
                    $query->payment_mode,
                    $query->viva_receipt_id,
                ];

                return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');
        $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);

        // Collection level sorting
        if ($isPaginated && $request->has('orderBy')) {
            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

            $receipts =  $receipts->sortBy($orderBy, $sortExtra, $isDescOrder);
        }
        if ($isPaginated && !$request->has('orderBy')) {
            $receipts =  $receipts->sortBy('created_at', $sortExtra, $orderDirection);
        }

        // Database level sorting
        if (!$isPaginated && $request->has('orderBy')) {
            $receipts = $receipts->orderBy($orderBy, $orderDirection);
        }
        if (!$isPaginated && !$request->has('orderBy')) {
            $receipts = $receipts->latest();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('pos_strings.company_product_receipt_list_return'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($receipts) : $receipts->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $isPaginated ? $receipts->values() : $receipts->lazy(),
            'message' => __('pos_strings.company_product_receipt_list_return'),
            'status' => '1'
        ]);
    }

    public function store(StoreCompanyReceiptRequest $request)
    {
        $user = Auth::user();

        $this->authorize('create', [CompanyReceipt::class, $request]);

        $receipt = $this->paymentService->processPayment($request, $user);

        return response()->json([
            'data' => $receipt,
            'message' => __('pos_strings.payment_request_has_been_sent'),
            'status' => '2'
        ]);
    }

    public function show(CompanyReceipt $companyReceipt, ShowCompanyReceiptRequest $request)
    {
        $this->authorize('view', [$companyReceipt]);

        $with = [];

        $companyReceipt = $this->receiptService->updatePaymentStatus($companyReceipt);

        if ($request->has('client')) {
            $with[] = 'client';
        }

        if ($request->has('company')) {
            $with[] = 'company';
        }

        if ($request->has('gift_card')) {
            $with[] = 'gift_card';
        }

        if ($request->has('items')) {
            $with[] = 'items.receiptable';
            $with[] = 'items.user';
        }

        if ($request->has('terminal')) {
            $with[] = 'terminal';
        }

        if ($request->has('relatable')) {
            $with[] = 'relatable';
        }

        if ($request->has('refunds')) {
            $with[] = 'refunds.items';
            $with[] = 'refunds.gift_card';
        }

        $companyReceipt = $companyReceipt->loadMissing($with)->loadExists('paid_for_gift_card');

        if ($request->has('refunds')) {
            foreach ($companyReceipt->refunds as $key => $refund) {
                $companyReceipt->refunds[$key] = $this->refundReceiptService->updateStatus($refund);
            }
        }

        return response()->json([
            'data' => $companyReceipt,
            'message' => __('pos_strings.company_receipt_returned_success'),
            'status' => '1'
        ]);
    }

    public function retry(RetryCompanyReceiptRequest $request, CompanyReceipt $companyReceipt)
    {
        $companyReceipt = $this->receiptService->updatePaymentStatus($companyReceipt);

        $companyReceipt->loadExists('paid_for_gift_card');

        $this->authorize('retry', $companyReceipt);

        $user = Auth::user();

        $receipt = $this->paymentService->retryPayment($request, $companyReceipt, $user);

        return response()->json([
            'data' => $receipt,
            'message' => __('pos_strings.company_receipt_processed_for_retry'),
            'status' => '1'
        ]);
    }

    public function abort(CompanyReceipt $companyReceipt)
    {
        $companyReceipt = $this->receiptService->updatePaymentStatus($companyReceipt);

        $this->authorize('abort', [$companyReceipt]);

        $companyReceipt->company->getCurrentOrCreateBatch();

        $receipt = $this->paymentService->abortPayment($companyReceipt);

        return response()->json([
            'data' => $receipt,
            'message' => __('pos_strings.company_receipt_aborted'),
            'status' => '1'
        ]);
    }

    public function export(CompanyReceipt $companyReceipt, GetReceiptExportTypeRequest $request)
    {
        $this->authorize('export', $companyReceipt);

        return DB::transaction(function () use ($request, $companyReceipt) {
            $user = Auth::user();

            $type = $request->type;

            $receipt_type = !!$companyReceipt->downloaded ? ReceiptType::kopia : ReceiptType::normal;

            $user_downloads = $companyReceipt->user_downloads;
            $user_downloads["user_$user->id"] =  isset($user_downloads["user_$user->id"]) ? $user_downloads["user_$user->id"] + 1 : 1;
            $companyReceipt->user_downloads = $user_downloads;
            $companyReceipt->downloaded += 1;
            $companyReceipt->save();

            if ($type == "mail") {
                $this->receiptService->exportPayment($companyReceipt, ReceiptExport::MAIL, $receipt_type);

                return response()->json([
                    'message' => __('pos_strings.mail.mail_sent_successfully'),
                    'status' => '1'
                ]);
            }

            return $this->receiptService->exportPayment($companyReceipt, ReceiptExport::PDF_DOWNLOAD, $receipt_type);
        });
    }

    public function view(CompanyReceipt $companyReceipt)
    {
        $this->authorize('export', $companyReceipt);

        return $this->receiptService->exportPayment($companyReceipt, ReceiptExport::PDF_STREAM, ReceiptType::normal);
    }

    public function status(StatusCompanyReceiptRequest $request)
    {
        $orderCode = $request->input('s');
        $transactionId = $request->input('t');

        $companyReceipt = CompanyReceipt::where('session_id', $orderCode)->firstOrFail();

        $companyReceipt = $this->receiptService->updatePaymentStatus($companyReceipt, true, $transactionId);

        return response()->json([
            'data' => [
                'company_id' => $companyReceipt->company->encrypted_id,
                'booking_id' => encrypt($companyReceipt->relatable?->booking_id ? $companyReceipt->relatable?->booking_id : $companyReceipt->relatable?->id),
                'client_id' => $companyReceipt->relatable?->booking_id ? encrypt($companyReceipt->relatable->id) : null,
            ],
            'message' => __('pos_strings.company_receipt_returned_success'),
            'status' => '1'
        ]);
    }
}
