<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyProduct;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Lead\GetLeadRequest;
use App\Http\Requests\v3\Lead\StoreLeadRequest;
use App\Http\Requests\v3\Lead\UpdateLeadRequest;
use App\Models\Lead;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LeadController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(GetLeadRequest $request)
    {
        $this->authorize('viewAny', [Lead::class]);

        $user = Auth::user();

        $leads = Lead::query();

        if ($request->has('filter') && ($request->filter == 'withTrashed' || $request->filter == 'onlyTrashed')) {
            $leads = $leads->{$request->filter}();
        } else if ($request->filter) {
            $leads = $leads->where($request->filter, $request->filter_type, $request->filter_value);
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $leads = $leads->whereLike([
                'company_name',
                'client_name',
                'email',
                'mobile_number',
                'country_code',
                'type',
                'status',
                'industry',
                'size'
            ], $search);
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'asc');

        // Database level sorting
        if ($request->has('orderBy')) {
            $leads = $leads->orderBy($orderBy, $orderDirection);
        }
        if (!$request->has('orderBy')) {
            $leads = $leads->latest();
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('strings.leads_returned_successfully'),
                    'status' => '1',
                ])->merge($leads->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $leads->lazy(),
            'message' => __('strings.leads_returned_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  StoreLeadRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreLeadRequest $request)
    {
        $this->authorize('create', [Lead::class]);

        $user = Auth::user();

        try {
            $lead = Lead::create($request->validated());
        } catch (QueryException $th) {
            $errorCode = $th->errorInfo[1];
            if ($errorCode == 1062) {
                throw new Exception(__('strings.lead_must_have_unique_email_and_mobile'), 500);
            }
            throw $th;
        }

        return response()->json([
            'data' => $lead,
            'message' => __('strings.leads_created_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Lead  $lead
     * @return \Illuminate\Http\Response
     */
    public function show(Lead $lead)
    {
        $this->authorize('view', $lead);

        return response()->json([
            'data' => $lead,
            'message' => __('strings.leads_created_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  UpdateLeadRequest  $request
     * @param  \App\Models\Lead  $lead
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateLeadRequest $request, Lead $lead)
    {
        $this->authorize('update', $lead);

        try {
            $lead->update($request->validated());
        } catch (QueryException $th) {
            $errorCode = $th->errorInfo[1];
            if ($errorCode == 1062) {
                throw new Exception(__('strings.lead_must_have_unique_email_and_mobile'), 500);
            }
            throw $th;
        }

        return response()->json([
            'data' => $lead,
            'message' => __('strings.leads_updated_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Lead  $lead
     * @return \Illuminate\Http\Response
     */
    public function destroy(Lead $lead)
    {
        $this->authorize('delete', $lead);

        $lead->delete();

        return response()->json([
            'message' => __('strings.leads_deleted_successfully'),
            'status' => '1'
        ]);
    }
}
