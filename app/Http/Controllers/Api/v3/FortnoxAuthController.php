<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\v3;

use App\Contracts\Services\Fortnox\FortnoxServiceInterface;
use App\Exceptions\Fortnox\FortnoxInvalidStateException;
use App\Exceptions\Fortnox\FortnoxNotAuthenticatedException;
use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FortnoxAuthController extends Controller
{
    public function __construct(
        private FortnoxServiceInterface $fortnoxService
    ) {}

    /**
     * Get Fortnox authorization URL for OAuth flow
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function connect(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            // Authorize access to this company
            $this->authorize('update', $company);

            // Get scopes from request or use defaults
            $scopes = $request->get('scopes', ['customer', 'invoice']);

            $authorizationUrl = $this->fortnoxService->getAuthorizationUrl($company, $scopes);

            return response()->json([
                'data' => $authorizationUrl,
                'message' => __('strings.fortnox_connect_message'),
                'status' => '1'
            ]);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_connect_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    /**
     * Handle OAuth callback from Fortnox
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function callback(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            // Authorize access to this company
            $this->authorize('update', $company);

            $authorizationCode = $request->get('code');

            if (!$authorizationCode) {
                return response()->json([
                    'message' => __('strings.fortnox_authorization_code_required'),
                    'status' => '0'
                ], 400);
            }

            // Check for error from Fortnox
            if ($request->has('error')) {
                $error = $request->get('error');
                $errorDescription = $request->get('error_description', 'Unknown error');

                Log::warning('Fortnox OAuth error', [
                    'company_id' => $company->id,
                    'error' => $error,
                    'error_description' => $errorDescription
                ]);

                return response()->json([
                    'message' => __('strings.fortnox_authorization_failed') . ": {$errorDescription}",
                    'status' => '0'
                ], 400);
            }

            $this->fortnoxService->handleOAuthCallback($company, $authorizationCode, $request->get('state'));

            return response()->json([
                'data' => true,
                'message' => __('strings.fortnox_connected_successfully'),
                'status' => '1'
            ]);
        } catch (FortnoxInvalidStateException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'status' => '0'
            ], 400);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_callback_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    /**
     * Disconnect/revoke Fortnox authentication
     *
     * @return JsonResponse
     */
    public function disconnect(): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            // Authorize access to this company
            $this->authorize('update', $company);

            DB::transaction(function () use ($company) {
                $company->fortnox_auth = null;

                $clients = $company->clients()->where('fortnox_customer_id', '!=', null)->lazy();

                $clients->each->update(['fortnox_customer_id' => null]);

                $company->save();
            });

            return response()->json([
                'data' => false,
                'message' => __('strings.fortnox_disconnected_successfully'),
                'status' => '1'
            ]);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_disconnect_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    /**
     * Check Fortnox authentication status
     *
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            // Authorize access to this company
            $this->authorize('view', $company);

            $isConnected = !empty($company->fortnox_auth);
            $tokenExpired = false;

            if ($isConnected && $company->fortnox_auth) {
                $tokenExpired = $company->fortnox_auth->hasExpired();
            }

            return response()->json([
                'data' => [
                    'connected' => $isConnected,
                    'token_expired' => $tokenExpired,
                    'expires_at' => $isConnected && $company->fortnox_auth ?
                        $company->fortnox_auth->getExpiresAt() : null
                ],
                'status' => '1'
            ]);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_status_check_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }

    /**
     * Refresh Fortnox access token
     *
     * @return JsonResponse
     */
    public function refresh(): JsonResponse
    {
        try {
            $user = Auth::user();
            $company = $user->company;

            // Authorize access to this companysdf
            $this->authorize('update', $company);

            if (!$company->fortnox_auth) {
                throw new FortnoxNotAuthenticatedException(__('strings.fortnox_company_not_connected'));
            }

            $this->fortnoxService->refreshToken($company);

            $company->refresh(); // Refresh the model to get latest data

            return response()->json($company->fortnox_auth->getAccessToken());

            return response()->json([
                'data' => [
                    'expires_at' => $company->fortnox_auth->getExpiresAt()
                ],
                'message' => __('strings.fortnox_token_refreshed_successfully'),
                'status' => '1'
            ]);
        } catch (FortnoxNotAuthenticatedException $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'status' => '0'
            ], 401);
        } catch (Exception $e) {
            report($e);

            return response()->json([
                'message' => __('strings.fortnox_token_refresh_failed') . ': ' . $e->getMessage(),
                'status' => '0'
            ], 500);
        }
    }
}
