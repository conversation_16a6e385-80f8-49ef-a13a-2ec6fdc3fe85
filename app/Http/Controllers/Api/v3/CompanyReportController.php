<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyZReport;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Report\GetCompanyReportListRequest;
use App\Http\Requests\v3\Report\GetDownloadCompanyReportRequest;
use App\Setting;
use App\Traits\ApiResponser;
use App\Traits\POS\HasPOSReport;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Traits\TimeZoneManager;
use Carbon\Carbon;

class CompanyReportController extends Controller
{
    use HasPOSReport, ApiResponser;

    public function index(GetCompanyReportListRequest $request)
    {
        $user = Auth::user();

        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        $company_report_z = CompanyZReport::where('company_id', $user->company_id)
            ->whereNotNull('start_datetime')->whereNotNull('end_datetime');

        if ($start_date) {
            $company_report_z = $company_report_z
                ->whereDate('end_datetime', '>=', $start_date);
        }

        if ($end_date) {
            $company_report_z = $company_report_z
                ->whereDate('end_datetime', '<=', $end_date);
        }

        $isPaginated = $request->has('search') || in_array($request->orderBy, ['closing_amount']);

        if ($isPaginated) {
            $company_report_z = $company_report_z->lazy();
        }

        // Collection level searching
        if ($request->has('search')) {
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $company = Auth::user()->company;
            $dateFormat = Setting::getDateTimeFormat($company);

            $company_report_z = $company_report_z->filter(function ($query) use ($search, $dateFormat) {
                $fields = [
                    $query->report_id,
                    $query->end_datetime->format($dateFormat),
                    $query->closing_amount,
                ];

                return collect($fields)->reduce(fn($o, $n) => $o ? true : Str::contains(Str::lower($n), $search), false);
            });
        }

        $orderBy = $request->orderBy;
        $orderDirection = $request->input('orderDirection', 'desc');
        $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
        $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

        // Collection level sorting
        if ($isPaginated && $request->has('orderBy')) {

            $company_report_z =  $company_report_z->sortBy($orderBy, $sortExtra, $isDescOrder);
        }
        if ($isPaginated && !$request->has('orderBy')) {
            $company_report_z =  $company_report_z->sortBy('end_datetime', $sortExtra, $isDescOrder);
        }

        // Database level sorting
        if (!$isPaginated && $request->has('orderBy')) {
            $company_report_z =  $company_report_z->orderBy($orderBy, $orderDirection);
        }
        if (!$isPaginated && !$request->has('orderBy')) {
            $company_report_z =  $company_report_z->orderBy('end_datetime', $orderDirection);
        }

        if ($request->has('page')) {
            $per_page = $request->input('per_page', 10);

            return response()->json(
                collect([
                    'message' => __('pos_strings.company_report_z_list_return'),
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($company_report_z) : $company_report_z->paginate($per_page))
            );
        }

        return response()->json([
            'data' => $isPaginated ? $company_report_z->values() : $company_report_z->lazy(),
            'message' => __('pos_strings.company_report_z_list_return'),
            'status' => '1'
        ]);
    }

    public function currentBatch()
    {
        $company = Auth::user()->company;

        return response()->json([
            'data' => $company->getCurrentOpenBatch(),
            'message' => __('pos_strings.current_batch_returned_successfully'),
            'status' => '1'
        ]);
    }

    public function closeBatch()
    {
        $company = Auth::user()->company;

        $z_report = $company->getCurrentOpenBatch();

        if (!$z_report) {
            throw new \Exception(__('pos_strings.no_batch_open'));
        };

        $z_report = $company->closeBatch();

        return response()->json([
            'data' => $z_report,
            'message' => __('pos_strings.batch_closed_successfully'),
            'status' => '1'
        ]);
    }

    public function download(GetDownloadCompanyReportRequest $request, CompanyZReport $company_z_report)
    {
        $this->authorize('view', $company_z_report);

        $end_datetime = null;

        if ($request->start_date && $request->end_date) {
            $company_z_report->start_datetime = Carbon::parse($request->start_date)->startOfDay();
            $end_datetime = Carbon::parse($request->end_date)->endOfDay();
        }

        $data = $this->getReportData($company_z_report, $end_datetime);

        return PDF::loadView('pdf.pos_report', $data)->stream();
    }
}
