<?php

namespace App\Http\Controllers\Api\v3;

use App\Client;
use App\CompanyProduct;
use App\CompanyReceipt;
use App\CompanyReceiptItem;
use App\CompanyService;
use App\Exports\Reports\Booking\BookingDataExport;
use App\Exports\Reports\POS\POSDataExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Report\GetCompanyBookingDataRequest;
use App\Http\Requests\v3\Report\GetCompanyPosDataRequest;
use App\Http\Requests\v3\Report\GetCompanyRecordDataRequest;
use App\QuestionaryData;
use App\Traits\ApiResponser;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Exports\Reports\Record\RecordDataExport;
use App\UserCompany;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    use ApiResponser;
    public function recordData(GetCompanyRecordDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;


        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();
        $period = $request->input('period', 'week');

        $periods = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$period}", Carbon::parse($request->end_date)->endOfDay());

        $per_page = $request->per_page ?? 5;

        switch ($request->type) {
            case 'client_statistics':
                $clients = $company->clients()->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('clients.user_id', $request->user_ids);
                })->whereBetween('created_at', [$start_date, $end_date])->count();
                $procedures = $company->procedures()->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('client_treatments.user_id', $request->user_ids);
                })->whereBetween('client_treatments.created_at', [$start_date, $end_date])->count();
                $prescriptions = $company->prescriptions()->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('client_prescriptions.sign_by_id', $request->user_ids);
                })->whereNotNull('client_prescriptions.sign_by_id')->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])->count();
                $client_sms = $company->client_sms()->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('client_sms.user_id', $request->user_ids);
                })->whereBetween('client_sms.created_at', [$start_date, $end_date])->count();

                return response()->json([
                    'data' => [
                        'total_clients' => $clients,
                        'total_procedures' => $procedures,
                        'prescriptions_signed' => $prescriptions,
                        'sms_sent' => $client_sms
                    ],
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
                break;
            case 'new_client_registrations':

                foreach ($periods as $date) {
                    if ($period == 'month') {
                        $key = $date->format("M y");
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    } elseif ($period == 'week') {
                        $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                        $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                    } elseif ($period == 'day') {
                        $key = $date->format("d M");
                        $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                    } else {
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    }

                    $clients = $company->clients()->when($request->user_ids, function ($query) use ($request) {
                        return $query->whereIn('clients.user_id', $request->user_ids);
                    })->whereBetween('created_at', [$start_date, $end_date])->count();

                    $data[] = ['name' => $key, 'value' => $clients];
                }

                return response()->json([
                    'data' => $data,
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
                break;
            case 'procedures_performed':

                foreach ($periods as $date) {
                    if ($period == 'month') {
                        $key = $date->format("M y");
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    } elseif ($period == 'week') {
                        $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                        $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                    } elseif ($period == 'day') {
                        $key = $date->format("d M");
                        $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                    } else {
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    }

                    $procedures = $company->procedures()->when($request->user_ids, function ($query) use ($request) {
                        return $query->whereIn('client_treatments.user_id', $request->user_ids);
                    })->whereBetween('client_treatments.created_at', [$start_date, $end_date])->count();


                    $data[] = ['name' => $key, 'value' => $procedures];
                }

                return response()->json([
                    'data' => $data,
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
                break;
            case 'most_procedures':
                $clients = $company->clients()->withOut('addresses')->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('clients.user_id', $request->user_ids);
                })->withCount(['treatments' => function ($query) use ($start_date, $end_date) {
                    $query->whereBetween('client_treatments.created_at', [$start_date, $end_date]);
                }])->whereHas('treatments', function ($query) use ($start_date, $end_date) {
                    $query->whereBetween('client_treatments.created_at', [$start_date, $end_date]);
                })->orderBy('treatments_count', 'desc');


                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($clients->paginate($per_page))
                );
                break;
            case 'most_notes':
                $clients = $company->clients()->withOut('addresses')->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('clients.user_id', $request->user_ids);
                })->withCount(['general_notes' => function ($query) use ($start_date, $end_date) {
                    $query->whereBetween('general_notes.created_at', [$start_date, $end_date]);
                }])->whereHas('general_notes', function ($query) use ($start_date, $end_date) {
                    $query->whereBetween('general_notes.created_at', [$start_date, $end_date]);
                })->orderBy('general_notes_count', 'desc');

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($clients->paginate($per_page))
                );
                break;

            case 'top_questionnaires':
                $question = QuestionaryData::with('modelable')
                    ->whereHas('client', function ($query) use ($request, $company) {
                        if ($request->user_ids) {
                            $query = $query->whereIn('user_id', $request->user_ids);
                        }
                        return $query->where('company_id', $company->id);
                    })
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->get()->append('title')->groupBy('modelable_type');

                $data = collect();

                $question = $question->map(function ($item, $key) use ($data) {
                    if ($key === 'App\\Questionary') {
                        return $item->map(function ($item, $key) use ($data) {
                            $count = $data[$item->modelable->title]['value'] ?? 0;
                            $data->put($item->modelable->title, ['name' => $item->modelable->title, 'value' => $count + 1]);
                        });
                    }
                    $data->put($key, ['name' => $item->first()->title, 'value' => $item->count()]);
                });

                $data =  $data->sortByDesc('value')->values();

                $data = array_slice($data->toArray(), 0, $per_page);
                return  response()->json([
                    'data' => $data,
                    'message' => "data returned successfully.",
                    'status' => '1'
                ]);
                break;

            case 'prescriptions_signed':
                $doctor_ids = $company->doctors()
                    ->where('user_companies.invite_status', UserCompany::ACCEPTED)
                    ->pluck('users.id')
                    ->toArray();

                $user_ids = $request->user_ids;
                if ($user_ids) {
                    $prescriber_ids = array_values(array_intersect($user_ids, $doctor_ids));
                    $user_ids = array_values(array_diff($user_ids, $prescriber_ids));

                    $prescriptions_by_own = $company->prescriptions()
                        ->whereIn('sign_by_id', $user_ids)
                        ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                        ->count();

                    $prescriptions_by_mal = $company->prescriptions()
                        ->whereIn('sign_by_id', $prescriber_ids)
                        ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                        ->count();
                } else {
                    $company_users = $company->users()->pluck('id')->toArray();
                    $prescriptions_by_own = $company->prescriptions()
                        ->whereIn('sign_by_id', $company_users)
                        ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                        ->count();

                    $prescriptions_by_mal = $company->prescriptions()
                        ->whereIn('sign_by_id', $doctor_ids)
                        ->whereBetween('client_prescriptions.created_at', [$start_date, $end_date])
                        ->count();
                }

                return response()->json([
                    'data' => [
                        'prescriptions_by_own' => $prescriptions_by_own,
                        'prescriptions_by_mal' => $prescriptions_by_mal
                    ],
                    'message' => "data returned successfully.",
                    'status' => '1'
                ]);
                break;
            case 'client_age':
                $clients = $company->clients()->withOut('addresses')->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('clients.user_id', $request->user_ids);
                })
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->cursor()
                    ->filter(function ($client) {
                        if ($client->personal_id != "" || $client->social_security_number != "" || $client->cpr_id != "") {
                            return true;
                        }
                    })->values()->toArray();

                return response()->json([
                    'data' => $clients,
                    'message' => "data returned successfully.",
                    'status' => '1'
                ]);
                break;
            default:
                # code...
                break;
        }
    }

    public function recordExport()
    {
        $user = Auth::user();
        $company = $user->company;

        return Excel::download(
            new RecordDataExport($company),
            'record_data.xlsx'
        );
    }
    public function bookingExport()
    {
        $user = Auth::user();
        $company = $user->company;

        return Excel::download(
            new BookingDataExport($company),
            'booking_data.xlsx'
        );
    }

    public function posExport()
    {
        $user = Auth::user();
        $company = $user->company;

        return Excel::download(
            new POSDataExport($company),
            'pos_data.xlsx'
        );
    }

    public function bookingData(GetCompanyBookingDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();
        $period = $request->input('period', 'week');

        $periods = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$period}", Carbon::parse($request->end_date)->endOfDay());

        $per_page = $request->per_page ?? 5;

        switch ($request->type) {
            case 'booking_statistics':
                $bookings = $company->bookings()
                    ->select('company_bookings.*')
                    ->with(['service.category:id,group_booking'])
                    ->withCount([
                        'clients' => function ($query) {
                            $query->whereNull('slot_released_at');
                        },
                        'clients as no_show_clients_count' => function ($query) {
                            $query->where('is_shown', 0)->whereNull('slot_released_at');
                        },
                        'clients as active_clients_count' => function ($query) {
                            $query->where(['is_cancelled' => 0, 'is_shown' => 1])->whereNull('slot_released_at');
                        },
                        'clients as cancelled_clients_count' => function ($query) {
                            $query->where('is_cancelled', 1)->whereNull('slot_released_at');
                        }
                    ])
                    ->when($request->user_ids, function ($query) use ($request) {
                        return $query->whereIn('company_bookings.user_id', $request->user_ids);
                    })
                    ->whereNull('slot_released_at')
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->get();

                $single_bookings = $bookings->filter(function ($booking) {
                    return (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                })->count();

                $group_bookings = $bookings->filter(function ($booking) {
                    return $booking->service?->category?->group_booking == 1 && $booking->clients_count > 0;
                })->count();

                $group_bookings_individual = $bookings->filter(function ($booking) {
                    return $booking->service?->category?->group_booking == 1;
                })->sum('clients_count');

                $bookings_revenue = $bookings->filter(function ($booking) {
                    return $booking->is_cancelled == 0 && $booking->is_shown == 1;
                })->sum(function ($booking) {
                    if ($booking->service?->category?->group_booking == 1) {
                        return $booking->active_clients_count > 0 ? $booking->price * $booking->active_clients_count : 0;
                    }
                    return $booking->price;
                });

                $single_booking_cancelled = $bookings->filter(function ($booking) {
                    return $booking->is_cancelled == 1 &&
                        (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                })->count();

                $group_booking_client_cancelled = $bookings->filter(function ($booking) {
                    return $booking->service?->category?->group_booking == 1;
                })->sum('cancelled_clients_count');

                $booking_cancelled = $single_booking_cancelled + $group_booking_client_cancelled;

                $single_booking_no_show = $bookings->filter(function ($booking) {
                    return $booking->is_shown == 0 &&
                        (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                })->count();

                $group_booking_client_no_shows = $bookings->filter(function ($booking) {
                    return $booking->service?->category?->group_booking == 1;
                })->sum('no_show_clients_count');

                $booking_no_show = $single_booking_no_show + $group_booking_client_no_shows;

                return response()->json([
                    'data' => [
                        'total_single_bookings' => $single_bookings,
                        'total_group_bookings' => $group_bookings,
                        'total_group_bookings_individual' => $group_bookings_individual,
                        'total_bookings_revenue' => round($bookings_revenue),
                        'total_cancelled_bookings' => $booking_cancelled,
                        'total_bookings_no_show' => $booking_no_show,
                    ],
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
            case 'company_bookings':

                foreach ($periods as $date) {
                    if ($period == 'month') {
                        $key = $date->format("M y");
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    } elseif ($period == 'week') {
                        $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                        $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                    } elseif ($period == 'day') {
                        $key = $date->format("d M");
                        $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                    } else {
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    }

                    $bookings = $company->bookings()->when($request->user_ids, function ($query) use ($request) {
                        return $query->whereIn('company_bookings.user_id', $request->user_ids);
                    })->whereBetween('created_at', [$start_date, $end_date])->count();

                    $data[] = ['name' => $key, 'value' => $bookings];
                }

                return response()->json([
                    'data' => $data,
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
                break;
            case 'booking_by_online_and_manual':
                $bookings_online = $company->bookings()->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('company_bookings.user_id', $request->user_ids);
                })->whereBetween('created_at', [$start_date, $end_date])->has('receipt')->count();

                $bookings_manual = $company->bookings()->when($request->user_ids, function ($query) use ($request) {
                    return $query->whereIn('company_bookings.user_id', $request->user_ids);
                })->whereBetween('created_at', [$start_date, $end_date])->doesntHave('receipt')->count();

                return response()->json([
                    'data' => ["bookings_online" => $bookings_online, "bookings_manual" => $bookings_manual],
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
                break;
            case 'top_practitioners':
                $users = $company->users()
                    ->when($request->user_ids, function ($query) use ($request) {
                        return $query->whereIn('users.id', $request->user_ids);
                    })
                    ->withOut('company')
                    ->with('bookings', function ($query) use ($request, $start_date, $end_date) {
                        $query->when($request->user_ids, function ($query) use ($request) {
                            return $query->whereIn('company_bookings.user_id', $request->user_ids);
                        })
                            ->whereNull('slot_released_at')
                            ->whereBetween('created_at', [$start_date, $end_date])
                            ->withCount([
                                'clients' => function ($query) {
                                    $query->whereNull('slot_released_at');
                                },
                                'clients as active_clients_count' => function ($query) {
                                    $query->where(['is_cancelled' => 0, 'is_shown' => 1])->whereNull('slot_released_at');
                                }
                            ]);
                    })
                    ->get()
                    ->map(function ($user) {
                        $total_price = $user->bookings->sum(function ($booking) {
                            if ($booking->is_cancelled == 0 && $booking->is_shown == 1) {
                                if ($booking->service?->category?->group_booking == 1) {
                                    return $booking->active_clients_count > 0 ? $booking->price * $booking->active_clients_count : 0;
                                }
                                return $booking->price;
                            }
                        });

                        $single_bookings = $user->bookings->filter(function ($booking) {
                            return (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                        })->count() ?? 0;

                        $group_bookings = $user->bookings->filter(function ($booking) {
                            return $booking->service?->category?->group_booking == 1 && $booking->clients_count > 0;
                        })->count() ?? 0;

                        $user->bookings_sum_price = $total_price;
                        $user->bookings_count = $single_bookings + $group_bookings;
                        return $user;
                    });

                $users = $users->sortByDesc('bookings_count')->values();

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($users))
                );
                break;
            case 'top_booking_services':
                $services = $company->company_services()
                    ->with('bookings', function ($query) use ($request, $start_date, $end_date) {
                        $query->when($request->user_ids, function ($query) use ($request) {
                            return $query->whereIn('company_bookings.user_id', $request->user_ids);
                        })
                            ->whereNull('slot_released_at')
                            ->whereBetween('created_at', [$start_date, $end_date])
                            ->withCount([
                                'clients' => function ($query) {
                                    $query->whereNull('slot_released_at');
                                },
                                'clients as active_clients_count' => function ($query) {
                                    $query->where(['is_cancelled' => 0, 'is_shown' => 1])->whereNull('slot_released_at');
                                }
                            ]);
                    })
                    ->get();

                $services->map(function ($service) {
                    $total_price = $service->bookings->sum(function ($booking) {
                        if ($booking->is_cancelled == 0 && $booking->is_shown == 1) {
                            if ($booking->service?->category?->group_booking == 1) {
                                return $booking->active_clients_count > 0 ? $booking->price * $booking->active_clients_count : 0;
                            }
                            return $booking->price;
                        }
                    });

                    $single_bookings = $service->bookings->filter(function ($booking) {
                        return (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                    })->count() ?? 0;

                    $group_bookings = $service->bookings->filter(function ($booking) {
                        return $booking->service?->category?->group_booking == 1 && $booking->clients_count > 0;
                    })->count() ?? 0;

                    $service->bookings_sum_price = $total_price;
                    $service->bookings_count = $single_bookings + $group_bookings;

                    return $service;
                });

                $services = $services->sortByDesc('bookings_count')->values();

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($services))
                );
                break;

            case 'top_booking_categories':
                $categories = $company->company_categories()
                    ->with('bookings', function ($query) use ($request, $start_date, $end_date) {
                        $query->when($request->user_ids, function ($query) use ($request) {
                            return $query->whereIn('company_bookings.user_id', $request->user_ids);
                        })
                            ->whereNull('slot_released_at')
                            ->whereBetween('company_bookings.created_at', [$start_date, $end_date])
                            ->withCount([
                                'clients' => function ($query) {
                                    $query->whereNull('slot_released_at');
                                },
                                'clients as active_clients_count' => function ($query) {
                                    $query->where(['is_cancelled' => 0, 'is_shown' => 1])->whereNull('slot_released_at');
                                }
                            ]);
                    })
                    ->get()
                    ->map(function ($category) {
                        $total_price = $category->bookings->sum(function ($booking) {
                            if ($booking->is_cancelled == 0 && $booking->is_shown == 1) {
                                if ($booking->service?->category?->group_booking == 1) {
                                    return $booking->active_clients_count > 0 ? $booking->price * $booking->active_clients_count : 0;
                                }
                                return $booking->price;
                            }
                        });

                        $single_bookings = $category->bookings->filter(function ($booking) {
                            return (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                        })->count() ?? 0;

                        $group_bookings = $category->bookings->filter(function ($booking) {
                            return $booking->service?->category?->group_booking == 1 && $booking->clients_count > 0;
                        })->count() ?? 0;

                        $category->bookings_sum_price = $total_price;
                        $category->bookings_count = $single_bookings + $group_bookings;

                        return $category;
                    });

                $categories = $categories->sortByDesc('bookings_count')->values();

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($categories))
                );

                break;

            case 'booking_cancelled_and_no_show':

                foreach ($periods as $date) {
                    if ($period == 'month') {
                        $key = $date->format("M y");
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    } elseif ($period == 'week') {
                        $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                        $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                    } elseif ($period == 'day') {
                        $key = $date->format("d M");
                        $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                    } else {
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    }

                    $bookings = $company->bookings()
                        ->select('company_bookings.*')
                        ->with(['service.category:id,group_booking'])
                        ->withCount([
                            'clients as cancelled_clients_count' => function ($query) {
                                $query->where('is_cancelled', 1);
                            },
                            'clients as no_show_clients_count' => function ($query) {
                                $query->where('is_shown', 0);
                            }
                        ])
                        ->when($request->user_ids, function ($query) use ($request) {
                            return $query->whereIn('company_bookings.user_id', $request->user_ids);
                        })
                        ->whereBetween('created_at', [$start_date, $end_date])
                        ->get();

                    $single_booking_cancelled = $bookings->filter(function ($booking) {
                        return $booking->is_cancelled == 1 &&
                            (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                    })->count();

                    $group_booking_client_cancelled = $bookings->filter(function ($booking) {
                        return $booking->slot_released_at === null &&
                            $booking->service?->category?->group_booking == 1;
                    })->sum('cancelled_clients_count');

                    $booking_cancelled = $single_booking_cancelled + $group_booking_client_cancelled;

                    $single_booking_no_show = $bookings->filter(function ($booking) {
                        return $booking->is_shown == 0 &&
                            (!$booking->service?->category || $booking->service?->category?->group_booking == 0);
                    })->count();

                    $group_booking_client_no_shows = $bookings->filter(function ($booking) {
                        return $booking->slot_released_at === null &&
                            $booking->service?->category?->group_booking == 1;
                    })->sum('no_show_clients_count');

                    $booking_no_show = $single_booking_no_show + $group_booking_client_no_shows;

                    $booking_data[] = ['name' => $key, 'value' => $booking_cancelled, 'value2' => $booking_no_show];
                }

                return response()->json([
                    'data' => $booking_data,
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);

            default:
                # code...
                break;
        }
    }

    public function posData(GetCompanyPosDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();
        $period = $request->input('period', 'week');
        $user_ids = $request->user_ids;
        $periods = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$period}", Carbon::parse($request->end_date)->endOfDay());

        $per_page = $request->per_page ?? 5;

        switch ($request->type) {
            case 'pos_statistics':
                return response()->json([
                    'data' => self::posStatistics($company, $start_date, $end_date, $user_ids),
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);

            case 'pos_revenue':
                $data = self::posRevenue($company, $start_date, $end_date, $user_ids, $periods, $period);

                return response()->json([
                    'data' => $data['data'],
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);

            case 'payment_gateway_preference':
                $data = self::posPaymentsGateway($company, $start_date, $end_date, $user_ids);

                return response()->json([
                    'data' => [
                        'swish_payment' => $data['swish_payment'],
                        'viva_payment' => $data['viva_payment'],
                    ],
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);

            case 'client_life_time':

                $clients = self::posClientLifetimeValue($company, $start_date, $end_date, $user_ids);

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($clients))
                );
                break;
            case 'practitioner_performance':
                $practitioners = self::posPractitionerPerformance($company, $start_date, $end_date, $user_ids);

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($practitioners))
                );
            case 'total_discount':
                $data = self::posTotalDiscount($company, $start_date, $end_date);

                return response()->json([
                    'data' => ["total_revenue" => $data['total_revenue'], "direct_discount" => $data['direct_discount']],
                    'message' => "data returned successfully.",
                    'status' => '1',
                ]);
            case 'top_selling_product':
                $products = self::posTopSellingProduct($company, $start_date, $end_date, $user_ids);

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($products))
                );
                break;
            case 'top_booked_services':
                $services = self::posTopBookedServices($company, $start_date, $end_date, $user_ids);

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($services))
                );
            case 'low_stock_products':
                $products =  self::posLowStockProduct($company, $start_date, $end_date);

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($products))
                );
                break;

            case 'refunds':
                $clients = self::posRefunds($company, $start_date, $end_date, $user_ids);

                return response()->json(
                    collect([
                        'message' => "data returned successfully.",
                        'status' => '1'
                    ])->merge($this->paginate($clients))
                );
                break;
            default:
                # code...
                break;
        }
    }

    public static function posStatistics($company, $start_date, $end_date, $user_ids)
    {
        $statuses = [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED];

        $users = $company->users()
            ->when($user_ids, fn($query) => $query->whereIn('users.id', $user_ids))
            ->without(['addresses', 'company'])
            ->with([
                'receipt_items' => function ($query) use ($start_date, $end_date, $statuses) {
                    $query->whereBetween('company_receipt_items.created_at', [$start_date, $end_date])
                        ->whereHas('receipt', fn($q) => $q->whereIn('status', $statuses));
                },
            ])
            ->get();

        $revenue = 0;
        $transactions = 0;
        $refunds = 0;

        if ($user_ids) {
            $revenue = $users->sum(
                fn($user) =>
                $user->receipt_items->where('status', '!=', CompanyReceiptItem::REFUNDED)->sum('remaining_amount')
            );

            $transactions = $users->flatMap(fn($user) => $user->receipt_items)
                ->pluck('receipt_id')
                ->unique()
                ->count();

            $refunds = $users->sum(
                fn($user) =>
                $user->receipt_items->where('status', CompanyReceiptItem::REFUNDED)->sum('total')
            );
        } else {
            $revenue = $company->receipts()
                ->whereIn('status', $statuses)
                ->whereBetween('created_at', [$start_date, $end_date])
                ->get()
                ->sum('remaining_amount');

            $transactions = $company->receipts()
                ->whereBetween('paid_at', [$start_date, $end_date])
                ->count();

            $refunds = $company->receipts()
                ->whereIn('status', [CompanyReceipt::REFUNDED, CompanyReceipt::PARTIALLY_REFUNDED])
                ->whereBetween('created_at', [$start_date, $end_date])
                ->get()
                ->sum('refund_amount');
        }

        $avg_transaction = $transactions > 0 ? ($revenue / $transactions) : 0;

        return [
            'total_revenue' => round($revenue),
            'no_of_transactions' => round($transactions),
            'avg_transaction' => round($avg_transaction),
            'total_refunds' => round($refunds),
        ];
    }
    public static function posRevenue($company, $start_date, $end_date, $user_ids, $periods, $period)
    {
        $total_products = 0;
        $total_services = 0;

        foreach ($periods as $date) {
            if ($period == 'month') {
                $key = $date->format("M y");
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            } elseif ($period == 'week') {
                $key = "{$date->weekOfMonth} week of {$date->format("M")}";
                $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
            } elseif ($period == 'day') {
                $key = $date->format("d M");
                $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
            } else {
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            }

            $products = $company->receipt_items()
                ->when($user_ids, function ($query) use ($user_ids) {
                    return $query->whereIn('company_receipt_items.user_id', $user_ids);
                })
                ->whereBetween('company_receipt_items.created_at', [$start_date, $end_date])
                ->whereHas('receipt', function ($query) {
                    return $query->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED]);
                })
                ->where('receiptable_type', CompanyProduct::class)->cursor()->sum('quantity');

            $services = $company->receipt_items()
                ->when($user_ids, function ($query) use ($user_ids) {
                    return $query->whereIn('company_receipt_items.user_id', $user_ids);
                })
                ->whereBetween('company_receipt_items.created_at', [$start_date, $end_date])
                ->whereHas('receipt', function ($query) {
                    return $query->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED]);
                })
                ->where('receiptable_type', CompanyService::class)->cursor()->sum('quantity');


            $total_products += $products;
            $total_services += $services;
            $data[] = ['name' => $key, 'products' => $products, 'services' => $services];
        }

        return [
            'data' => $data,
            'total_products' => $total_products,
            'total_services' => $total_services
        ];
    }

    public static function posPaymentsGateway($company, $start_date, $end_date, $user_ids)
    {
        $statuses = [
            CompanyReceipt::PAID,
            CompanyReceipt::REFUNDED,
            CompanyReceipt::PARTIALLY_REFUNDED,
        ];

        $base_query = $company->receipt_items()
            ->when($user_ids, fn($query) => $query->whereIn('company_receipt_items.user_id', $user_ids))
            ->whereBetween('company_receipt_items.created_at', [$start_date, $end_date]);

        $swish_payment = (clone $base_query)
            ->whereHas('receipt', function ($query) use ($statuses) {
                $query->where('company_receipts.payment_method', CompanyReceipt::PAYMENT_METHOD_SWISH)
                    ->whereIn('company_receipts.status', $statuses);
            })
            ->pluck('receipt_id')
            ->unique()
            ->count();

        $viva_payment = (clone $base_query)
            ->whereHas('receipt', function ($query) use ($statuses) {
                $query->whereIn('company_receipts.payment_method', [
                    CompanyReceipt::PAYMENT_METHOD_VIVA,
                    CompanyReceipt::PAYMENT_METHOD_VIVA_ONLINE,
                ])
                    ->whereIn('company_receipts.status', $statuses);
            })
            ->pluck('receipt_id')
            ->unique()
            ->count();

        return [
            'swish_payment' => $swish_payment,
            'viva_payment' => $viva_payment,
        ];
    }

    public static function posClientLifetimeValue($company, $start_date, $end_date, $user_ids)
    {
        $clients = $company->clients()
            ->withOut('addresses')
            ->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('clients.user_id', $user_ids);
            })
            ->with(['receipts' => function ($query) use ($start_date, $end_date) {
                $query->select('id', 'client_id', 'paid_amount')
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->where('status', CompanyReceipt::PAID);
            }])
            ->withCount(['receipts' => function ($query) use ($start_date, $end_date) {
                $query->whereBetween('created_at', [$start_date, $end_date]);
            }])
            ->get();

        $clients->each(function ($client) {
            $client->receipts_sum_paid_amount = $client->receipts->sum('paid_amount');
        });

        $clients = $clients->sortByDesc('receipts_sum_paid_amount')->values();

        return $clients;
    }

    public static function posPractitionerPerformance($company, $start_date, $end_date, $user_ids)
    {
        $practitioners = $company->users()
            ->withOut('addresses', 'company')
            ->when(
                $user_ids,
                fn($query) =>
                $query->whereIn('users.id', $user_ids)
            )
            ->with([
                'receipt_items' => function ($query)  use ($start_date, $end_date) {
                    return $query->whereBetween('company_receipt_items.created_at', [$start_date, $end_date])
                        ->whereHas('receipt', function ($query) {
                            return $query->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED]);
                        });
                },
            ])
            ->get()
            ->map(function ($practitioner) {
                $practitioner->receipts_sum_paid_amount =
                    $practitioner->receipt_items->filter(
                        function ($receipt_item) {
                            return $receipt_item->status != CompanyReceiptItem::REFUNDED;
                        }
                    )->sum('remaining_amount');

                return $practitioner;
            })
            ->sortByDesc('receipts_sum_paid_amount')
            ->values();

        return $practitioners;
    }

    public static function posTotalDiscount($company, $start_date, $end_date)
    {
        $receipts = $company->receipts()
            ->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED])
            ->whereBetween('created_at', [$start_date, $end_date])->get();


        $total_revenue = $receipts->sum('remaining_amount');
        $discount_amount = $receipts->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED])->sum('discount_amount_formatted');

        return ["total_revenue" => $total_revenue, "direct_discount" => $discount_amount];
    }

    public static function posTopSellingProduct($company, $start_date, $end_date, $user_ids)
    {
        $products = $company->products()
            ->with('receipt_items', function ($query) use ($user_ids, $start_date, $end_date) {
                return $query->whereBetween('created_at', [$start_date, $end_date])
                    ->whereHas('receipt', function ($query) {
                        return $query->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED]);
                    })
                    ->when($user_ids, function ($query) use ($user_ids) {
                        return $query->whereIn('company_receipt_items.user_id', $user_ids);
                    });
            })
            ->get();

        foreach ($products as $product) {
            $product->sold_quantity = $product->receipt_items->sum('quantity');
            $product->revenue = $product->receipt_items->filter(
                function ($receipt_item) {
                    return $receipt_item->status != CompanyReceiptItem::REFUNDED;
                }
            )->sum('remaining_amount');
        }
        $products = $products->sortByDesc('sold_quantity');

        return $products;
    }

    public static function posTopBookedServices($company, $start_date, $end_date, $user_ids)
    {
        $services = $company->company_services()
            ->with('receipt_items', function ($query) use ($user_ids, $start_date, $end_date) {
                return $query->whereBetween('created_at', [$start_date, $end_date])
                    ->whereHas('receipt', function ($query) {
                        return $query->whereIn('status', [CompanyReceipt::PAID, CompanyReceipt::PARTIALLY_REFUNDED, CompanyReceipt::REFUNDED]);
                    })->when($user_ids, function ($query) use ($user_ids) {
                        return $query->whereIn('company_receipt_items.user_id', $user_ids);
                    });
            })
            ->get();

        foreach ($services as $service) {
            $service->sold_quantity = $service->receipt_items->sum('quantity');
            $service->revenue = $service->receipt_items->filter(
                function ($receipt_item) {
                    return $receipt_item->status != CompanyReceiptItem::REFUNDED;
                }
            )->sum('remaining_amount');
        }

        $services = $services->sortByDesc('sold_quantity');

        return $services;
    }

    public static function posLowStockProduct($company, $start_date, $end_date)
    {
        $products = $company->products()->whereBetween('created_at', [$start_date, $end_date])->cursor()->sortBy('stock');

        return $products;
    }

    public static function posRefunds($company, $start_date, $end_date, $user_ids)
    {
        $clients =  $company->clients()
            ->withOut('addresses')
            ->when($user_ids, function ($query) use ($user_ids) {
                return $query->whereIn('clients.user_id', $user_ids);
            })
            ->whereHas('receipts', function ($query) use ($start_date, $end_date) {
                return $query->whereIn('status', [CompanyReceipt::REFUNDED, CompanyReceipt::PARTIALLY_REFUNDED])
                    ->whereBetween('created_at', [$start_date, $end_date]);
            })
            ->with(['receipts' => function ($query) use ($start_date, $end_date) {
                return  $query->whereBetween('created_at', [$start_date, $end_date])
                    ->whereIn('status', [CompanyReceipt::REFUNDED, CompanyReceipt::PARTIALLY_REFUNDED]);
            }])
            ->get();

        $clients->each(function ($client) {
            $client->receipts_sum_refund = $client->receipts->sum('refund_amount');
        });

        $clients = $clients->sortByDesc('receipts_sum_refund')->values();

        return $clients;
    }
    public function store(Request $request)
    {
        //
    }
}
