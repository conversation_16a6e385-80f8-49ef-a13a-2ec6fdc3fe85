<?php

namespace App\Http\Controllers\Api\v3;

use App\CompanyService;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\ListEmailTemplateRequest;
use App\Http\Requests\v3\StoreEmailTemplateRequest;
use App\Http\Requests\v3\UpdateEmailTemplateRequest;
use App\EmailTemplate;
use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EmailTemplateController extends Controller
{
    public function index(ListEmailTemplateRequest $request)
    {
        $email_templates = EmailTemplate::where('company_id', Auth::user()->company_id);
        $orderBy = 'id';
        $orderDirection = 'desc';
        if ($request->has('orderDirection')) {
            $orderDirection = $request->orderDirection;
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
        }
        if ($request->has('filter')) {
            $email_templates = $email_templates->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
        }

        if ($request->has('search')) {
            $email_templates = $email_templates->where('title', 'LIKE', "%{$request->search}%");
        }

        $email_templates = $email_templates->orderBy($orderBy, $orderDirection);
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('prescription_strings.template_list_returned'),
                    'status' => '1'
                ])->merge($email_templates->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $email_templates->get(),
                'message' => __('prescription_strings.template_list_returned'),
                'status' => '1'
            ]);
        }
    }

    public function store(StoreEmailTemplateRequest $request)
    {
        $user = Auth::user();
        $email_template = EmailTemplate::create([
            'company_id' => $user->company->id,
            'title' => $request->title,
            'description' => $request->description,
            'is_active' => $request->is_active ?? 1,
        ]);

        return response()->json([
            'data' => $email_template,
            'message' => __('strings.Template_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(EmailTemplate $email_template, UpdateEmailTemplateRequest $request)
    {
        if ($request->has('title')) {
            $email_template->title = $request->title;
        }

        if ($request->has('description')) {
            $email_template->description = $request->description;
        }

        if ($email_template->isDirty()) {
            $email_template->is_changed = 1;
            $email_template->save();
        }

        return response()->json([
            'data' => $email_template,
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function toggle(EmailTemplate $email_template)
    {
        if ($email_template->is_active) {
            $company = Auth::user()->company;
            $email_template_ids = collect([
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_SEND_AFTER_CARE_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_INTERNAL_BOOKING_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_REGISTRATION_TEMPLATE_ID)->value,
            ]);

            if ($email_template_ids->contains($email_template->id)) {
                return response()->json([
                    'message' => __('strings.you_cannot_disable_this_template'),
                    'status' => '0'
                ]);
            }
        }
        $email_template->is_active = !$email_template->is_active;
        $email_template->save();
        return response()->json([
            'data' => $email_template->refresh(),
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(EmailTemplate $email_template)
    {
        if ($email_template->is_active) {
            $company = Auth::user()->company;
            $email_template_ids = collect([
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_SEND_AFTER_CARE_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_INTERNAL_BOOKING_REMINDER_TEMPLATE_ID)->value,
                Setting::getSetting($company, Setting::EMAIL_CLIENT_REGISTRATION_TEMPLATE_ID)->value,
            ]);

            if ($email_template_ids->contains($email_template->id)) {
                return response()->json([
                    'message' => __('strings.you_cannot_delete_this_template'),
                    'status' => '0'
                ]);
            }

            if (CompanyService::where('finished_email_template_id', $email_template->id)->exists()) {
                return response()->json([
                    'message' => __('strings.you_cannot_delete_this_template'),
                    'status' => '0'
                ]);
            }
        }
        $email_template->delete();
        return response()->json([
            'message' => __('strings.Template_deleted_successfully'),
            'status' => '1'
        ]);
    }
}