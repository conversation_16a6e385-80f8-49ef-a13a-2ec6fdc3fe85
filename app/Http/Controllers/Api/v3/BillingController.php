<?php

namespace App\Http\Controllers\Api\v3;

use App\Company;
use App\CompanyPaymentMethod;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v2\UpdateCardInfoRequest;
use App\Models\Cashier\Subscription;
use Illuminate\Support\Facades\DB;

class BillingController extends Controller
{
    public function update(UpdateCardInfoRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $company = Auth::user()->company;

            $this->authorize('updateBilling', Subscription::class);

            $company->createOrGetStripeCustomer();

            $data = [];

            if ($request->has('vat_number')) {
                $taxIds = $company->taxIds();

                $hasTaxId = false;

                foreach ($taxIds as $key => $taxId) {
                    if ($taxId->type == $request->vat_code && $taxId->value == $request->vat_numer) {
                        $hasTaxId = true;
                        break;
                    }

                    $company->deleteTaxId($taxId->id);
                }

                if ($request->vat_code && $request->vat_number) {
                    if (!$hasTaxId) {
                        try {
                            $taxId = $company->createTaxId($request->vat_code, $request->vat_number);
                        } catch (\Throwable $th) {
                            return response()->json([
                                'message' => __('strings.invalid_vat_number'),
                                'status' => '0'
                            ]);
                        }
                    }
                }

                $data['invoice_settings'] = [
                    'custom_fields' =>  $request->vat_number ? [
                        [
                            'name'  => 'VAT No',
                            'value' => $request->vat_number ?? '',
                        ]
                    ] : '',
                ];
            }

            if ($request->has('address.city')) {
                $data['address']['city'] = $request->address['city'];
            }
            if ($request->has('address.country')) {
                $data['address']['country'] = $company->country;
            }

            if ($request->has('address.line1')) {
                $data['address']['line1'] = $request->address['line1'];
            }
            if ($request->has('address.postal_code')) {
                $data['address']['postal_code'] = $request->address['postal_code'];
            }

            if ($request->has('phone')) {
                $data['phone'] = $request->phone;
            }
            if ($request->has('name')) {
                $data['name'] = $request->name;
            }

            if (count($data)) {
                $company->updateStripeCustomer($data);
            }

            if ($request->has('paymentMethod') && $request->paymentMethod) {
                $paymentMethod = $company->updateDefaultPaymentMethod($request->paymentMethod);

                if ($company->hasDefaultPaymentMethod()) {
                    $companyPaymentMethod = CompanyPaymentMethod::getPaymentMethodByLabel($company, CompanyPaymentMethod::RECORD);
                    if ($companyPaymentMethod) {
                        $companyPaymentMethod->payment_method_id = $paymentMethod->id;
                        $companyPaymentMethod->meta_data = [
                            'last4' => (string)$paymentMethod->card->last4,
                            'exp_year' => (string)$paymentMethod->card->exp_year,
                            'exp_month' => (string)$paymentMethod->card->exp_month,
                        ];
                        $companyPaymentMethod->save();
                    }
                }
            }

            return response()->json([
                'message' => __('strings.Payment_details_updated_successfully'),
                'status' => '1'
            ]);
        });
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show()
    {
        $company = Auth::user()->company;

        $this->authorize('viewBilling', Subscription::class);

        $customer = $company->createOrGetStripeCustomer();
        $has_payment_method = $company->hasDefaultPaymentMethod();
        $payment_method = $has_payment_method ? $company->defaultPaymentMethod() : null;

        $taxIds =  $company?->taxIds();

        $required_billing_detail = !($has_payment_method && $payment_method && $customer->name && $customer->address && $customer->phone);

        $data = [
            "required_billing_detail" => $required_billing_detail,
            "vat_number" => $taxIds?->first()?->value ?? collect($customer?->invoice_settings?->custom_fields ?? [])?->where("name", "VAT No")?->first()?->value,
            "vat_code" => $taxIds?->first()?->type,
            "address" => $customer->address,
            "phone" => $customer->phone,
            "name" => $customer->name,
            "card" => [
                'brand' => $payment_method?->card?->brand ?? $payment_method?->brand,
                'exp_month' => $payment_method?->card?->exp_month ?? $payment_method?->exp_month,
                'exp_year' => $payment_method?->card?->exp_year ?? $payment_method?->exp_year,
                'last4' => $payment_method?->card?->last4 ?? $payment_method?->last4,
            ]
        ];

        return response()->json([
            'message' => 'customer data returned successfully.',
            'status' => '1',
            'data' => $data,
        ]);
    }
}
