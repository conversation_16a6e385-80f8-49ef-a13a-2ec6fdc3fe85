<?php

namespace App\Http\Controllers\Api\v1;

use App\Plan;
use Exception;
use App\Company;
use App\CompanyBooking;
use App\CompanyCoupon;
use App\CompanyPlatform;
use Carbon\Carbon;
use App\Stripe\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use App\Http\Resources\InvoiceResource;
use Laravel\Cashier\Exceptions\IncompletePayment;
use App\Http\Requests\v1\StoreSubscriptionRequest;
use App\Http\Requests\v1\SubscriptionUpdateRequest;
use App\Http\Requests\v2\CreateFreeTrailRequest;
use App\Http\Requests\v2\GetCancelSubscriptionDataRequest;
use App\Http\Requests\v2\GetPromocodeRequest;
use App\Http\Requests\v2\UpdateCardInfoRequest;
use App\Http\Requests\v2\UpdateFreeTrailRequest;
use App\Mail\CancelSubscriptionRequestMail;
use App\Mail\SubscriptionCancelConfirmation;
use App\Mail\SubscriptionCancellationStartMail;
use App\Setting;
use App\SubscriptionCancellationData;
use App\Traits\ApiResponser;
use App\Traits\PromocodeManager;
use App\User;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\Crypt;

class SubscriptionController extends Controller
{
    use  ApiResponser, PromocodeManager;
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function createFreeTrail(CreateFreeTrailRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        if ($request->has('quantity')) {
            $company->user_count = $request->quantity;
        }
        if ($company->is_free_trail_finished) {
            return response()->json([
                'message' => __('strings.free_trail_is_already_used'),
                'status' => '0'
            ]);
        }
        if ($company->free_trail_start_date != null) {
            return response()->json([
                'message' => __('strings.free_trail_is_already_in_use'),
                'status' => '0'
            ]);
        }
        $company->free_trail_start_date = Carbon::now()->format('Y-m-d');
        $company->free_trail_end_date = Carbon::now()->addDay(env('FREE_TRAIL_DAYS', 14))->format('Y-m-d');
        $company->is_free_trail_finished = 0;
        $company->free_trail_plan_id = $request->plan;
        $company->free_trail_quantity = $request->quantity;
        $company->free_trail_type = $request->is_yearly ? 'yearly' : 'monthly';

        if ($request->has('code_name')) {
            $company->free_trail_coupon = $request->code_name;
        }
        $company->save();
        return response()->json([
            'message' => __('strings.free_trail_started'),
            'status' => '1'
        ]);
    }

    public function updateFreeTrail(UpdateFreeTrailRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if ($company->is_free_trail_finished) {
            return response()->json([
                'message' => __('strings.free_trail_is_already_used'),
                'status' => '0'
            ]);
        }

        if ($request->has('quantity')) {
            $company->user_count = $request->quantity;
            $company->free_trail_quantity = $request->quantity;
        }
        if ($request->has('plan')) {
            $company->free_trail_plan_id = $request->plan;
        }
        if ($request->has('is_yearly')) {
            $company->free_trail_type = $request->is_yearly ? 'yearly' : 'monthly';
        }
        if ($request->has('code_name')) {
            $company->free_trail_coupon = $request->code_name;
        }
        if ($company->free_trail_coupon) {
            $coupon_to_check = $this->getPromocode($company->free_trail_coupon);
            $coupon_type = 'monthly';
            if ($coupon_to_check && $coupon_to_check->metadata) {
                if (isset($coupon_to_check->metadata->type)) {
                    $coupon_type = $coupon_to_check->metadata->type;
                }
            }
            if ($coupon_type != $company->free_trail_type) {
                // return response()->json([
                //     'message' => __('strings.invalid_promocode'),
                //     'status' => '0'
                // ]);
                $company->free_trail_coupon = null;
            }
        }
        $company->save();

        return response()->json([
            'message' => __('strings.free_trail_update'),
            'status' => '1'
        ]);
    }


    public function updateCardInfo(UpdateCardInfoRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $company->createOrGetStripeCustomer();
        if ($request->input('payment_method.metadata.vat_number') && $request->input('payment_method.metadata.vat_code')) {
            $vat_number = $request->input('payment_method.metadata.vat_number');
            $vat_code = $request->input('payment_method.metadata.vat_code');
            try {
                $taxIds = $company->taxIds();
                foreach ($taxIds as $key => $taxId) {
                    $company->deleteTaxId($taxId->id);
                }
                $taxId = $company->createTaxId($vat_code, $vat_number);
            } catch (\Throwable $th) {
                return response()->json([
                    'message' => __('strings.invalid_vat_number'),
                    'status' => '0'
                ]);
            }
        }

        try {
            if ($request->has('payment_method.billing_details')) {
                $billing_details = $request->payment_method['billing_details'];
                //code...
                $company->updateStripeCustomer(array_merge([
                    'address' => array_merge($billing_details['address'], ['country' => $company->country]),
                    // 'email' => $billing_details['email'],
                    'phone' => $billing_details['phone'],
                    'name' => $request->payment_method['metadata']['company_name'],
                    // 'tax_id_data' =>  [
                    //     'type' => 'eu_vat',
                    //     'value' => $request->payment_method['metadata']['vat_number'],
                    // ],
                    'invoice_settings' => [
                        'custom_fields' =>  $request->input('payment_method.metadata.vat_number') ? [
                            [
                                'name'  => 'VAT No',
                                'value' => $request->payment_method['metadata']['vat_number']
                            ],
                        ] : ''
                    ]
                ], $request->refercode ? ['metadata' => [
                    'Samarbete' => $request->input('refercode') ?? ''
                ]] : []));

                $billing_details = [
                    $billing_details
                ];
            } else {
                $billing_details = [];
            }
        } catch (\Throwable $th) {
            //throw $th;
            $billing_details = [];
        }
        return response()->json([
            'message' => __('strings.card_updated'),
            'status' => '1'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $company = Auth::user()->company;
        if (!$company) {
            return response()->json([
                'message' => 'Please login',
                'status' => '0'
            ]);
        }

        $this->authorize('subscriptionPermission', [Company::class, $company]);

        try {
            if (!$company->shouldConnectToStripe()) {
                return response()->json([
                    'data' => [
                        'intent' => null,
                        'plans' => $company->plans(),
                        'yearly_plans' => $company->plans(true),
                    ],
                    'message' => "plans \ setup intent returned successfully.",
                    'status' => '1'
                ]);
            }
            $company->createOrGetStripeCustomer();

            $intent = $company->createSetupIntent();

            return response()->json([
                'data' => [
                    'intent' => $intent->client_secret,
                    'plans' => $company->plans(),
                    'yearly_plans' => $company->plans(true),
                ],
                'message' => "plans \ setup intent returned successfully.",
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'error' => $th->getMessage(),
                'message' => 'please try again, something happen.',
                'status' => '0'
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreSubscriptionRequest $request)
    {
        $company = Auth::user()->company;

        $this->authorize('subscriptionPermission', [Company::class, $company]);

        if ($company->shouldConnectToStripe()) {
            $company->createOrGetStripeCustomer();
        }
        if ($request->has('code_name')) {
            $company_coupon = $company->coupons()->where('code_name', $request->code_name)->first();
            if ($company_coupon) {
                return response()->json([
                    'message' => __('strings.promocode_already_used'),
                    'status' => '0'
                ]);
            }
        }


        $plans = Cache::rememberForever('plans-key', function () {
            return Plan::get();
        });
        $plan = $plans->where('plan_id', $request->plan)->first();

        if ($plan->isLicensed()) {
            $plan->client = $plan->client * $request->quantity;
            $plan->users = $plan->users * $request->quantity;
        }

        if (!$plan) {
            throw new Exception("Plan not found.");
        }

        if ($request->has('remove_free_trial') && $request->remove_free_trial) {
            $company->free_trail_plan_id = null;
            $company->free_trail_quantity = null;
            $company->free_trail_coupon = null;
            $company->free_trail_type = null;
            $company->save();
        }


        if ($company->card_last_four && $company->is_subscribed && $company->activeSubscription()) {
            if (
                $plan->users < $company->users()->count()
            ) {
                return response()->json([
                    'message' => __('strings.cant_downgrade_plan'),
                    'status' => '0'
                ]);
            }
            $stripe_id =  $company->activeSubscription()->stripe_id;
            $subscription = $this->getSubscription($stripe_id);
            $plan = Plan::get()->where('plan_id', $request->plan)->first();
            $should_remove_coupon = false;
            if (isset($subscription->discount) && isset($subscription->discount->coupon) && isset($subscription->discount->coupon->metadata->type)) {
                switch ($subscription->discount->coupon->metadata->type) {
                    case 'yearly':
                        if (!$plan->is_yearly) {
                            $should_remove_coupon = true;
                        }
                        break;
                    case 'monthly':
                        if ($plan->is_yearly) {
                            $should_remove_coupon = true;
                        }
                        break;

                    default:
                        if ($plan->is_yearly) {
                            $should_remove_coupon = true;
                        }
                        break;
                }
            }

            if ($should_remove_coupon) {
                $this->removePromocodeFromSubscription($stripe_id);
            }

            try {
                $subscription_name = 'Subscription';
                if ($company->activeSubscription()) {
                    $subscription_name = $company->activeSubscription()->name;
                }
                $company->subscription($subscription_name)->swap([
                    $request->plan => $plan->isFree() ? [] : [
                        'quantity' => $request->quantity,
                    ]
                ]);
            } catch (IncompletePayment $th) {
                $invoice = $company->findInvoice($th->payment->invoice);
                if ($invoice) {
                    $invoice->void();
                }
                Log::error($th);
                return response()->json([
                    'message' => __('strings.payment_failure_with_stripe'),
                    'status' => '2'
                ]);
            }

            if ($request->refercode) {
                $company->updateStripeCustomer([
                    'metadata' => [
                        'Samarbete' => $request->input('refercode') ?? ''
                    ]
                ]);
            }
            if ($request->has('code_name')) {
                $company_coupon = $company->coupons()->where('code_name', $request->code_name)->first();
                if (!$company_coupon) {
                    if ($company->activeSubscription()) {
                        $stripe_id =  $company->activeSubscription()->stripe_id;
                        $this->applyPromocodeToSubscription($request->code_name, $stripe_id);
                        $subscription = $this->getSubscription($stripe_id);
                        $coupon_expiration_date = null;
                        if ($subscription->discount && $subscription->discount->end) {
                            $coupon_expiration_date =  Carbon::createFromTimestamp($subscription->discount->end);
                        }
                        CompanyCoupon::create([
                            'company_id' => $company->id,
                            'code_name' => $request->code_name,
                            'expire_at' => $coupon_expiration_date,
                            'meta_data' => $subscription->discount,
                        ]);
                    }
                } else {
                    return response()->json([
                        'message' => __('strings.promocode_already_used'),
                        'status' => '0'
                    ]);
                }
            }

            // if($company->activeSubscription()){
            $company->is_record_on = 1;
            $company->save();
            $user = Auth::user();
            $user->is_record_on = 1;
            $user->save();
            // }
            return response()->json([
                'message' => __('strings.Plan_changed_successfully'),
                'status' => '1'
            ]);
        } else {
            if ($request->input('payment_method.metadata.vat_number') && $request->input('payment_method.metadata.vat_code')) {
                $vat_number = $request->input('payment_method.metadata.vat_number');
                $vat_code = $request->input('payment_method.metadata.vat_code');
                try {
                    $taxIds = $company->taxIds();
                    foreach ($taxIds as $key => $taxId) {
                        $company->deleteTaxId($taxId->id);
                    }
                    $taxId = $company->createTaxId($vat_code, $vat_number);
                } catch (\Throwable $th) {
                    return response()->json([
                        'message' => __('strings.invalid_vat_number'),
                        'status' => '0'
                    ]);
                }
            }

            try {
                if ($request->has('payment_method.billing_details')) {
                    $billing_details = $request->payment_method['billing_details'];
                    //code...
                    $company->updateStripeCustomer(array_merge([
                        'address' => array_merge($billing_details['address'], ['country' => $company->country]),
                        // 'email' => $billing_details['email'],
                        'phone' => $billing_details['phone'],
                        'name' => $request->payment_method['metadata']['company_name'],
                        // 'tax_id_data' =>  [
                        //     'type' => 'eu_vat',
                        //     'value' => $request->payment_method['metadata']['vat_number'],
                        // ],
                        'invoice_settings' => [
                            'custom_fields' =>  $request->input('payment_method.metadata.vat_number') ? [
                                [
                                    'name'  => 'VAT No',
                                    'value' => $request->payment_method['metadata']['vat_number']
                                ],
                            ] : ''
                        ]
                    ], $request->refercode ? ['metadata' => [
                        'Samarbete' => $request->input('refercode') ?? ''
                    ]] : []));

                    $billing_details = [
                        $billing_details
                    ];
                } else {
                    $billing_details = [];
                }
            } catch (\Throwable $th) {
                //throw $th;
                $billing_details = [];
            }

            try {
                if ($company->is_subscribed && $company->activeSubscription()) {
                    $subscription_name = 'Subscription';
                    if ($company->activeSubscription()) {
                        $subscription_name = $company->activeSubscription()->name;
                    }
                    $company->subscription($subscription_name)->cancelNow();
                }
            } catch (\Throwable $th) {
                //throw $th;
            }

            // if (!$company->subscriptions->count()) {
            //     try {
            //         Mail::to($company->email)->locale(app()->getLocale())->send(new NewAccountUserMail());
            //     } catch (\Throwable $th) {
            //         //throw $th;
            //     }
            // }
            if ($company->activeSubscription()) {
                $stripe_id =  $company->activeSubscription()->stripe_id;
                $subscription = $this->getSubscription($stripe_id);
                $plan = Plan::get()->where('plan_id', $request->plan)->first();
                $should_remove_coupon = false;
                if (isset($subscription->discount) && isset($subscription->discount->coupon) && isset($subscription->discount->coupon->metadata->type)) {
                    switch ($subscription->discount->coupon->metadata->type) {
                        case 'yearly':
                            if (!$plan->is_yearly) {
                                $should_remove_coupon = true;
                            }
                            break;
                        case 'monthly':
                            if ($plan->is_yearly) {
                                $should_remove_coupon = true;
                            }
                            break;

                        default:
                            if ($plan->is_yearly) {
                                $should_remove_coupon = true;
                            }
                            break;
                    }
                }

                if ($should_remove_coupon) {
                    $this->removePromocodeFromSubscription($stripe_id);
                }
            }


            if (
                !($company->is_subscribed && $company->activeSubscription())
                &&
                $company->is_cancelled
                &&
                ($company->clients()->count() > $plan->client || $company->users()->count() > $plan->users)
            ) {
                return response()->json([
                    'message' => __('strings.cant_downgrade_plan_1'),
                    'status' => '0'
                ]);
            }

            try {
                if ($request->has('code_name')) {
                    $company_coupon = $company->coupons()->where('code_name', $request->code_name)->first();
                    if (!$company_coupon) {
                        $subscription = $company
                            ->newSubscription('Subscription', $request->plan)
                            ->withCoupon($request->code_name)
                            ->quantity($plan->isFree() ? 1 : $request->quantity)
                            ->create($request->has('payment_method.paymentMethod') ? $request->payment_method['paymentMethod'] : null, $billing_details, [
                                'metadata' => $request->has('payment_method.metadata') ? $request->payment_method['metadata'] : '',
                                'default_tax_rates' => $company->subscriptionTaxRates(),
                            ]);
                        $coupon_expiration_date = null;
                        if ($company->activeSubscription()) {
                            $stripe_id =  $company->activeSubscription()->stripe_id;
                            $subscription = $this->getSubscription($stripe_id);
                            $coupon_expiration_date = null;
                            if ($subscription->discount && $subscription->discount->end) {
                                $coupon_expiration_date =  Carbon::createFromTimestamp($subscription->discount->end);
                            }
                        }
                        CompanyCoupon::create([
                            'company_id' => $company->id,
                            'code_name' => $request->code_name,
                            'expire_at' => $coupon_expiration_date,
                            'meta_data' => $subscription->discount,
                        ]);
                    } else {
                        return response()->json([
                            'message' => __('strings.promocode_already_used'),
                            'status' => '0'
                        ]);
                    }
                } else {
                    $subscription = $company
                        ->newSubscription('Subscription', $request->plan)
                        ->quantity($plan->isFree() ? 1 : $request->quantity)
                        ->create($request->has('payment_method.paymentMethod') ? $request->payment_method['paymentMethod'] : null, $billing_details, [
                            'metadata' => $request->has('payment_method.metadata') ? $request->payment_method['metadata'] : '',
                            'default_tax_rates' => $company->subscriptionTaxRates(),
                        ]);
                }
            } catch (IncompletePayment $th) {
                $invoice = $company->findInvoice($th->payment->invoice);
                if ($invoice) {
                    $invoice->void();
                }
                Log::error($th);
                return response()->json([
                    'message' => __('strings.payment_failure_with_stripe'),
                    'status' => '2'
                ]);
            }
            $company->is_free_trail_finished = 1;
            $company->save();

            // if ($company->activeSubscription()) {
            $company->is_record_on = 1;
            $company->save();
            $user = Auth::user();
            $user->is_record_on = 1;
            $user->save();
            // }


            return response()->json([
                'message' => __('strings.Successfully_subscribed_to_plan'),
                'status' => '1'
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        //asStripeCustomer
        $company = Auth::user()->company;

        $this->authorize('subscriptionPermission', [Company::class, $company]);
        $customer = null;
        $payment_method = null;
        if ($company->shouldConnectToStripe()) {
            try {
                $company->createOrGetStripeCustomer();
                $customer = $company->asStripeCustomer();
                $payment_method = $company->defaultPaymentMethod();
            } catch (\Throwable $th) {
                //throw $th;
            }
        }

        $data = [];

        if ($customer) {
            if (isset($customer->invoice_settings->custom_fields) && $vat_number = collect($customer->invoice_settings->custom_fields)->where("name", "VAT No")->first()) {
                $data['vat_number'] = $vat_number['value'];
            }
            $taxIds = $company->taxIds();
            if ($taxIds->count()) {
                $data['vat_number'] = $taxIds->first()->value;
                $data['vat_code'] = $taxIds->first()->type;
            }
            $data['address'] = $customer->address;
            $data['phone'] = $customer->phone;
            $data['name'] = $customer->name;
        }
        if ($payment_method && $payment_method->card) {
            $data['card'] = [
                'brand' => $payment_method->card->brand,
                'exp_month' => $payment_method->card->exp_month,
                'exp_year' => $payment_method->card->exp_year,
                'last4' => $payment_method->card->last4,
            ];
        } elseif ($payment_method && $payment_method->brand && $payment_method->exp_month && $payment_method->exp_year && $payment_method->last4) {
            $data['card'] = [
                'brand' => $payment_method->brand,
                'exp_month' => $payment_method->exp_month,
                'exp_year' => $payment_method->exp_year,
                'last4' => $payment_method->last4,
            ];
        }

        return response()->json([
            'message' => 'customer data returned successfully.',
            'status' => '1',
            'data' => count($data) > 0 ? $data : null,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(SubscriptionUpdateRequest $request)
    {
        $company = Auth::user()->company;

        $this->authorize('subscriptionPermission', [Company::class, $company]);

        $company->createOrGetStripeCustomer();
        $data = [];
        if ($request->has('vat_number')) {
            $taxIds = $company->taxIds();
            $hasSaveTax = false;
            foreach ($taxIds as $key => $taxId) {
                if ($taxId->type == $request->vat_code && $taxId->value == $request->vat_numer) {
                    $hasSaveTax = true;
                    break;
                }
                $company->deleteTaxId($taxId->id);
            }

            if ($request->vat_code && $request->vat_number) {
                if (!$hasSaveTax) {
                    try {
                        $taxId = $company->createTaxId($request->vat_code, $request->vat_number);
                    } catch (\Throwable $th) {
                        return response()->json([
                            'message' => __('strings.invalid_vat_number'),
                            'status' => '0'
                        ]);
                    }
                }
            }

            // $company->updateStripeCustomer([
            //     'metadata' => [
            //         'VAT No' => $request->input('vat_number') ?? ''
            //     ]
            // ]);

            $data['invoice_settings'] = [
                'custom_fields' =>  $request->vat_number ? [
                    [
                        'name'  => 'VAT No',
                        'value' => $request->vat_number ?? '',
                    ]
                ] : '',
            ];
        }

        if ($request->has('address.city')) {
            $data['address']['city'] = $request->address['city'];
        }
        if ($request->has('address.country')) {
            // $data['address']['country'] = $request->address['country'];
            $data['address']['country'] = $company->country;
        }

        if ($request->has('address.line1')) {
            $data['address']['line1'] = $request->address['line1'];
        }
        if ($request->has('address.postal_code')) {
            $data['address']['postal_code'] = $request->address['postal_code'];
        }

        if ($request->has('phone')) {
            $data['phone'] = $request->phone;
        }
        if ($request->has('name')) {
            $data['name'] = $request->name;
        }

        if (count($data)) {
            $company->updateStripeCustomer($data);
        }

        if ($request->has('paymentMethod') && $request->paymentMethod) {
            $company->updateDefaultPaymentMethod($request->paymentMethod);
        }

        return response()->json([
            'message' => __('strings.Payment_details_updated_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy()
    {
        $company = Auth::user()->company;
        $this->authorize('subscriptionPermission', [Company::class, $company]);

        // if ($company->is_subscribed) {
        $subscription_name = 'Subscription';
        if ($company->activeSubscription()) {
            $subscription_name = $company->activeSubscription()->name;
        }
        $company->subscription($subscription_name)->cancel();

        $countryDetail = collect(config('stripe.country'))->filter(function ($country) use ($company) {
            return  in_array($company->country, $country['countries']);
        })->first();

        if (!$countryDetail) {
            $countryDetail = collect(config('stripe.country'))->where('default', true)->first();
        }

        if ($company->asStripeCustomer()->currency != strtolower($countryDetail['currency'])) {
            $company->stripe_id = null;
            $company->card_brand = null;
            $company->card_last_four = null;
            $company->save();

            $company->createAsStripeCustomer();
        }
        // }

        return response()->json([
            'message' => __('strings.Subscription_cancelled_successfully'),
            'status' => '1',
        ]);
    }


    public function requestToCancel(GetCancelSubscriptionDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company->loadCount(['users', 'clients', 'procedures']);
        $this->authorize('subscriptionPermission', [Company::class, $company]);

        Mail::to('<EMAIL>')->locale(app()->getLocale())->send(new CancelSubscriptionRequestMail($user, $company, $request->data));

        return response()->json([
            'message' => __('strings.cancel_request_sent'),
            'status' => '1',
        ]);
    }
    public function cancelConfirmation($id)
    {
        $subscription_cancellation_data = SubscriptionCancellationData::findOrFail(Crypt::decrypt($id));

        $company  = $subscription_cancellation_data->company;
        $super_user = null;
        foreach ($company->users as $user) {
            if ($user->email == $company->email) {
                $super_user = $user;
            }
        }

        if (!$subscription_cancellation_data->is_from_free_trail) {
            if (!$subscription_cancellation_data->subscription) {
                return response()->json([
                    'message' => __('strings.subscription_not_found'),
                    'status' => '0',
                ]);
            }

            $subscription = $subscription_cancellation_data->subscription;
            if (!$subscription->active()) {
                return response()->json([
                    'message' => __('strings.subscription_not_active'),
                    'status' => '0',
                ]);
            }

            if ($subscription->ends_at) {
                return response()->json([
                    'message' => __('strings.subscription_already_cancelled'),
                    'status' => '2',
                ]);
            }
            $subscription->cancel();
            $subscription_cancellation_data->should_delete_data = 1;
            $subscription_cancellation_data->save();
        } else {
            $subscription_cancellation_data->should_delete_data = 1;
            $subscription_cancellation_data->save();
            $company->is_free_trail_cancelled = 1;
            $company->save();
        }
        $language = Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)?->value;
        Mail::to($super_user->email)
            ->locale($language ?? app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, false));
        Mail::to("<EMAIL>")
            ->locale(app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, true));

        return response()->json([
            'message' => __('strings.Subscription_cancelled_successfully'),
            'status' => '1',
        ]);
    }

    public function delete(GetCancelSubscriptionDataRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $is_in_free_trail = false;

        if ($company->isFreeTrailActive()) {
            $is_in_free_trail = true;
        }
        $subscription_cancellation_data = null;
        if ($is_in_free_trail) {
            $expiration_date = Carbon::parse($company->free_trail_end_date)->format('Y-m-d');
            $subscription_cancellation_data = SubscriptionCancellationData::updateOrCreate([
                'company_id' => $company->id,
            ], [
                'data' => $request->data,
                'should_delete_data' => 1,
                'is_data_deleted' => 0,
                'data_deletion_date' => $expiration_date,
                'is_from_free_trail' => 1,
            ]);
            $company->is_free_trail_cancelled = 1;
            $company->save();
        } else {
            $subscription_to_cancel =  $user->company->activeSubscription();

            $subscription_id =  $user->company->activeSubscription()->stripe_id;
            \Stripe\Stripe::setApiKey(secret_env('STRIPE_SECRET'));

            $subscription = null;
            try {
                $subscription = \Stripe\Subscription::retrieve($subscription_id);
            } catch (\Throwable $th) {
                //throw $th;
            }

            if ($subscription) {
                $expiration_date =  Carbon::createFromTimestamp($subscription->current_period_end)->format('Y-m-d');
            }


            $subscription_cancellation_data = SubscriptionCancellationData::updateOrCreate([
                'company_id' => $company->id,
                'subscription_id' => $subscription_to_cancel->id,
            ], [
                'data' => $request->data,
                'should_delete_data' => 0,
                'is_data_deleted' => 0,
                'data_deletion_date' => $expiration_date,
                'is_from_free_trail' => 0,
            ]);
            $subscription = $subscription_cancellation_data->subscription;
            if ($subscription->ends_at) {
                return response()->json([
                    'message' => __('strings.subscription_already_cancelled'),
                    'status' => '0',
                ]);
            }
            $subscription->cancel();
            $subscription_cancellation_data->should_delete_data = 1;
            $subscription_cancellation_data->save();
        }
        // Mail::to($user->email)
        //     ->locale(app()->getLocale())
        //     ->send(new SubscriptionCancellationStartMail($subscription_cancellation_data));
        $super_user = null;
        foreach ($company->users as $user) {
            if ($user->email == $company->email) {
                $super_user = $user;
            }
        }
        $language = Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)?->value;
        Mail::to($super_user->email)
            ->locale($language ?? app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, false));
        Mail::to("<EMAIL>")
            ->locale(app()->getLocale())
            ->send(new SubscriptionCancelConfirmation($subscription_cancellation_data, true));


        return response()->json([
            'message' => __('strings.Subscription_cancelled_successfully'),
            'status' => '1',
        ]);
    }
    public function invoices(Request $request)
    {
        $company = Auth::user()->company;
        $this->authorize('invoices', [Company::class, $company]);

        $upcomming_invoice = $company->upcomingInvoice();
        $data = collect(array_merge($upcomming_invoice ? [$upcomming_invoice] : [], $company->invoices(true, [
            'limit' => 100,
            'created' => [
                'gt' => Carbon::now()->subYear()->timestamp
            ],
        ])->all()));

        $platform = CompanyPlatform::RECORD_SYSTEM;
        $product_to_use = null;
        foreach (config('stripe.products') as $product) {
            if ($product['platform'] == $platform) {
                $product_to_use = $product;
            }
        }
        if ($product_to_use) {
            $data = $data->filter(function ($invoice) use ($product_to_use) {
                if ($invoice->lines->data && is_array($invoice->lines->data) && count($invoice->lines->data) > 0) {
                    if (in_array($invoice->lines->data[0]->price->product, $product_to_use['stripe_ids'])) {
                        return true;
                    } else {
                        return false;
                    }
                }
            });
        }
        $data = InvoiceResource::collection($data);
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra =  in_array($orderBy, ['number', 'status']) ? (SORT_NATURAL | SORT_FLAG_CASE) : SORT_FLAG_CASE;
            $data =  $data->sortBy($orderBy, $sortExtra, $isDescOrder);
            // $data =  $data->sort(function ($a, $b) use ($orderBy) {
            //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
            // });
        }
        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.Invoices_returned_successfully'),
                    'status' => '1'
                ])->merge($this->paginate($data))
            );
        }

        return [
            'data' => $data,
            'message' => __('strings.Invoices_returned_successfully'),
            'status' => '1'
        ];
    }

    public function invoicePay($id, Request $request)
    {
        $company = Auth::user()->company;
        $this->authorize('invoicePay', [Company::class, $company]);

        $invoice  = $company->invoicePay($id);

        if ($invoice->paid) {
            $company->is_read_only = false;
            $company->is_blocked = false;
            $company->payment_failed_attempts = false;
            $company->save();

            return [
                'data' => $invoice ? new InvoiceResource($invoice) : null,
                'message' => __('strings.Invoice_payed_successfully'),
                'status' => '1'
            ];
        }

        return [
            'message' => __('strings.Invoice_payed_failed'),
            'status' => '0'
        ];
    }

    public function invoiceDownload($id, Request $request)
    {
        $company = Auth::user()->company;
        $this->authorize('invoiceDownload', [Company::class, $company]);

        $invoice = new Invoice($company, $company->findInvoiceOrFail($id)->asStripeInvoice());

        $response = Http::withoutVerifying()->get($invoice->invoice_pdf);

        $contentType = $response->header('Content-Type');

        $body = $response->getBody();

        $stream = new StreamedResponse(function () use ($body) {
            while (!$body->eof()) {
                echo $body->read(1024);
            }
        });

        $stream->headers->set('Content-Type', $contentType);

        return $stream;
    }


    public function getExpirationDate()
    {
        $user = Auth::user();
        $company = $user->company;

        if (!$company->activeSubscription()) {
            return response()->json([
                'message' => __('strings.you_dont_have_any_subscription'),
                'status' => '0',
            ]);
        }

        $subscription_id =  $company->activeSubscription()->stripe_id;
        \Stripe\Stripe::setApiKey(secret_env('STRIPE_SECRET'));
        $subscription = null;
        try {
            $subscription = \Stripe\Subscription::retrieve($subscription_id);
        } catch (\Throwable $th) {
            //throw $th;
        }
        $expiration_date = null;
        if ($subscription) {
            $expiration_date =  Carbon::createFromTimestamp($subscription->current_period_end)->format('Y-m-d');
        }

        return response()->json([
            'data' => $expiration_date,
            'message' => __('strings.expiration_date_returned'),
            'status' => '1',
        ]);
    }
    // public function billing_details(Request $request)
    // {
    //     $company = Auth::user()->company;
    //     $this->authorize('billing_details', [Company::class, $company]);


    //     return [
    //         'data' => $payment_method ? new BillingDetailResource($payment_method) : null,
    //         'message' => __('strings.Billing_Details_returned_successfully'),
    //         'status' => '1'
    //     ];
    // }

    public function getPromocodeData(GetPromocodeRequest $request)
    {
        $company = Auth::user()->company;
        if ($request->has('code_name')) {
            $company_coupon = $company->coupons()->where('code_name', $request->code_name)->first();
            if ($company_coupon) {
                return response()->json([
                    'message' => __('strings.promocode_already_used'),
                    'status' => '0'
                ]);
            }
        }
        try {
            $promocode = $this->getPromocode($request->code_name);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }

        if (!$promocode) {
            return response()->json([
                'message' => __('strings.invalid_promocode'),
                'status' => '0',
            ]);
        }

        if (!$promocode->percent_off) {
            return response()->json([
                'message' => __('strings.invalid_promocode'),
                'status' => '0',
            ]);
        }
        if ($promocode->metadata) {
            if (isset($promocode->metadata->type)) {
                switch ($promocode->metadata->type) {
                    case 'monthly':
                        if ($request->type != 'monthly') {
                            return response()->json([
                                'message' => __('strings.only_for_monthly'),
                                'status' => '0',
                            ]);
                        }
                        break;
                    case 'yearly':
                        if ($request->type != 'yearly') {
                            return response()->json([
                                'message' => __('strings.only_for_yearly'),
                                'status' => '0',
                            ]);
                        }
                        break;

                    default:
                        # code...
                        break;
                }
            }
        }
        return [
            'data' => $this->getPromocode($request->code_name),
            'message' => __('strings.promocode_return'),
            'status' => '1'
        ];
    }

    public function getAppliedPromocode()
    {
        $user = Auth::user();
        $company = $user->company;

        if ($company->free_trail_coupon != null) {
            return response()->json([
                'expiration_date' => null,
                'data' => $this->getPromocode($company->free_trail_coupon),
                'message' => __('strings.expiration_date_returned'),
                'status' => '1',
            ]);
        }

        if (!$company->activeSubscription()) {
            return response()->json([
                'message' => __('strings.you_dont_have_any_subscription'),
                'status' => '0',
            ]);
        }

        $subscription_stripe_id =  $company->activeSubscription()->stripe_id;
        $subscription = $this->getSubscription($subscription_stripe_id);
        $subscription_expiration_date = null;
        if ($subscription) {
            $subscription_expiration_date =  Carbon::createFromTimestamp($subscription->current_period_end);
        } else {
            return response()->json([
                'message' => __('strings.you_dont_have_any_subscription'),
                'status' => '0',
            ]);
        }

        if (!$subscription->discount) {
            return response()->json([
                'message' => __('strings.no_promocode_applied'),
                'status' => '0',
            ]);
        }
        $coupon_expiration_date = null;
        if ($subscription->discount->end) {
            $coupon_expiration_date =  Carbon::createFromTimestamp($subscription->discount->end);
            if ($coupon_expiration_date->lessThan($subscription_expiration_date)) {
                return response()->json([
                    'message' => __('strings.no_promocode_applied'),
                    'status' => '0',
                ]);
            }
        }
        return response()->json([
            'expiration_date' => $coupon_expiration_date ? $coupon_expiration_date->format('Y-m-d H:i:s') : null,
            'data' => $subscription->discount->coupon,
            'message' => __('strings.expiration_date_returned'),
            'status' => '1',
        ]);
    }
}
