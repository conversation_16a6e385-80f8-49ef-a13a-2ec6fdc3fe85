<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\StoreClientAccessRequest;
use App\User;
use App\UserCompany;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientAccessController extends Controller
{
    public function index(User $user)
    {
        return response()->json([
            'data' => $user->company->clients()->get(),
            'client_access' => $user->accesses,
            'message' => __('strings.Client_access_returned_successfully'),
            'status' => '1'
        ]);
    }

    public function userAccess(User $user)
    {
        return response()->json([
            'data' => $user->accesses->pluck('id'),
            'message' => __('strings.Client_access_returned_successfully'),
            'status' => '1'
        ]);
    }

    public function clientsAccess(Client $client)
    {
        return response()->json([
            'data' => $client->accesses->pluck('id'),
            'message' => __('strings.Client_access_returned_successfully'),
            'status' => '1'
        ]);
    }

    public function indexWeb()
    {
        $user = Auth::user();

        if ($user->user_role === User::ADMIN || $user->company->email === $user->email) {
            $clients = Auth::user()->company;
        } else {
            $clients = $user;
        }

        $clients = $clients->clients()->without([
            'addresses', 'letter_of_consents', 'treatments', 'general_questions', 'health_questionaries', 'aesthetic_insterests'
        ])->select([
            'id',
            'first_name',
            'last_name',
            'profile_picture'
        ]);

        if (request()->has('order_by_recent')) {
            $clients = $clients->latest();
        }

        $clients = $clients->with('accesses')->get();

        if (request()->has('order_by_az')) {
            $clients = $clients->sortBy('last_name', SORT_NATURAL | SORT_FLAG_CASE);
        }

        if (request()->has('order_by_za')) {
            $clients = $clients->sortByDesc('last_name', SORT_NATURAL | SORT_FLAG_CASE);
        }

        return response()->json([
            'data' => $clients->values(),
            'messages' => __('strings.Client_access_returned_successfully'),
            'status' => '1'
        ]);
    }

    public function updateAccess(Client $client, Request $request)
    {
        $auth_user = Auth::user();
        if ($request->has('remove_all') && $request->remove_all) {
            $admin_user = User::where('email', $auth_user->company->email)->first();

            $client->accesses()->sync([$admin_user->id]);
        }
        if ($request->has('allow_all') && $request->allow_all) {
            $user = Auth::user();
            $company = $user->company;
            $user_ids = User::where(function ($query) use ($company) {
                $query
                    ->where('company_id', $company->id)
                    ->orWhereHas('companies', function ($q) use ($company) {
                        $q->where('companies.id', $company->id)->whereNotIn('invite_status', [UserCompany::STOPPED, UserCompany::DELETED]);
                    });
            })
                ->with(['company_connections' => function ($query) use ($company) {
                    $query->where('user_companies.company_id', $company->id);
                }])->get()->pluck('id')->toArray();


            $client->accesses()->sync($user_ids);
        }
        return response()->json([
            'message' => __('strings.client_access_changed_successfully'),
            'status' => '1'
        ]);
    }

    public function store(StoreClientAccessRequest $request, User $user)
    {
        $this->authorize('clientAccessStore', [User::class]);

        foreach ($request->input('client_ids', []) as $index => $client_id) {
            $user->accesses()->toggle([$client_id]);
        }

        return response()->json([
            'message' => __('strings.client_access_changed_successfully'),
            'status' => '1'
        ]);
    }
}
