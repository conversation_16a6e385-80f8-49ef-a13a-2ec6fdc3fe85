<?php

namespace App\Http\Controllers\Api\v1;

use App\Company;
use Illuminate\Http\Request;
use App\Traits\ApiResponser;
use App\Http\Controllers\Controller;

class CompanyClientController extends Controller
{

    use ApiResponser;
    public function index(Company $company){
        $this->authorize('viewAny', [Company::class]);

        if (request()->has('search')) {
            $search = request()->input('search');

            $clients = $company->clients->filter(function ($client) use ($search) {
                        return (
                            mb_stristr($client->first_name, $search) ||
                            mb_stristr($client->last_name, $search) ||
                            mb_stristr($client->first_name . $client->last_name, str_replace(" ", "", $search)) ||
                            mb_stristr($client->social_security_number, $search) ||
                            mb_stristr($client->email, $search) ||
                            mb_stristr($client->phone_number, $search)
                            || ($client->addresses->filter(function ($address) use ($search) {

                                return (mb_stristr($address->street_address, $search) ||
                                    mb_stristr($address->state, $search) ||
                                    mb_stristr($address->zip_code, $search) ||
                                    mb_stristr($address->city, $search));
                            })->count() > 0) || ($client->letter_of_consents->filter(function ($letter_of_consents) use ($search) {

                                return (mb_stristr($letter_of_consents->consent_title, $search) ||
                                    mb_stristr($letter_of_consents->letter, $search));
                            })->count() > 0) || ($client->treatments->filter(function ($treatments) use ($search) {

                                return (mb_stristr($treatments->name, $search) ||
                                    mb_stristr($treatments->description, $search));
                            })->count() > 0)
                            );
                    });

            $clients = $this->paginate($clients);
        } else {
            // $companies = Company::get();
            $clients = $company->clients()->paginate();

            // $companies = $companies->where('email','!=','<EMAIL>');
            // $companies = $this->paginate($companies);
        }

        return response()->json(collect([
            'status' => '1',
            'new_chack' => request()->has('search') ? request()->input('search') : false,
            'message' => 'Clients returned successfully',
        ])->merge($clients));
    }
}
