<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\QuestionaryData;
use App\Traits\SaveFile;
use App\HealthQuestionary;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\HealthQuestionnaireRequest;
use App\Http\Requests\v1\HealthQuestionnaireNewRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class HealthQuestionnaireController extends Controller
{
    use SaveFile;

    // HealthQuestionnaireRequest $request
    public function store(Request $request, Client $client)
    {
        return response()->json([
            'message' => __('strings.Please_update_your_app'),
            'status' => '0'
        ]);

        $this->authorize('update', [Client::class, $client]);

        HealthQuestionary::updateOrCreate([
            'client_id' => $client->id
        ], [
            'data' => json_encode($request->input('health_questions'))
        ]);

        return response()->json([
            'message' => __('strings.Health_Questionnaire_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function storeNew(HealthQuestionnaireNewRequest $request, Client $client, PDFServiceInterface $pdfService)
    {
        $this->authorize('update', [Client::class, $client]);

        DB::transaction(function () use ($client, $request, $pdfService) {
            $healthQuestionary = HealthQuestionary::updateOrCreate([
                'client_id' => $client->id
            ], [
                'data_new' => json_encode($request->input('health_questions'))
            ]);

            $user = Auth::user();
            $created_at = now();
            $timezone = $client->company->timezone ?? 'Europe/Stockholm';

            if ($request->is_custom_date_selected == 1) {
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time", $timezone)->utc();
            }

            $data = ['datas' => $request->input('health_questions'), 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_health_questionary', 'clients/' . md5($client->id) . '/health_questionary/pdf');
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/health_questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->health($request->input('health_questions'), $created_at)->saveFile($user, $filename);

            $questionaryData = QuestionaryData::create([
                'client_id' => $client->id,
                'modelable_type' => HealthQuestionary::class,
                'modelable_id' => $healthQuestionary->id,
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);

            if ($request->is_custom_date_selected == 1) {
                $questionaryData->created_at = $created_at;
                $questionaryData->save();
            }
        });

        return response()->json([
            'message' => __('strings.Health_Questionnaire_updated_successfully'),
            'status' => '1'
        ]);
    }
}
