<?php

namespace App\Http\Controllers\Api\v1;

use App\Company;
use App\CompanyPlatform;
use Illuminate\Http\Request;
use App\Mail\ReadOnlyAccountMail;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Mail\BlockAccountMail;
use Illuminate\Support\Facades\Mail;
use App\Mail\InvoicePaymentSuccessfull;
use App\Setting;
use Illuminate\Support\Facades\DB;
use Laravel\Cashier\Http\Controllers\WebhookController as CashierController;

class WebhookController extends CashierController
{
    /**
     * Handle invoice payment succeeded.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handleInvoicePaymentSucceeded($payload)
    {
        Log::info("Invoice sending");
        $payload = json_decode(json_encode((object) $payload), FALSE);

        try {
            $data = $payload->data->object;

            $company = Company::where('stripe_id', $data->customer)->firstOrFail();
            $company->payment_failed_attempts = 0;
            $company->save();

            $invoice = $company->findInvoiceOrFail($data->id);
            //code...
            // Mail::to($company->email ?? '<EMAIL>')
            //     ->locale(app()->getLocale())
            //     ->send(new InvoicePaymentSuccessfull(__('strings.Your_receipt_from') . ' ' . $data->account_name . '‬ #' . $data->number, $invoice));
            Log::info("Invoice send successfully");
        } catch (\Throwable $th) {
            Log::info("Error sending invoice mail:     " . $th);
        }
    }

    /**
     * Handle invoice payment failed.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handleInvoicePaymentFailed($payload)
    {
        Log::info("invoice.payment_failed");
        $payload = json_decode(json_encode((object) $payload), FALSE);

        try {
            Log::info("invoice.payment_failed called");
            $data = $payload->data->object;

            DB::transaction(function () use ($data) {
                $company = Company::where('stripe_id', $data->customer)->firstOrFail();
                $invoice = $company->findInvoiceOrFail($data->id);

                $is_from_sms = 0;
                if (isset($invoice->lines->data) && is_array($invoice->lines->data) && count($invoice->lines->data) > 0) {
                    if ($invoice->lines->data[0]->price) {
                        foreach (config('stripe.prices') as $price) {
                            if ($price['platform'] == CompanyPlatform::SMS) {
                                foreach ($price['prices'] as $price_item) {
                                    if ($invoice->lines->data[0]->price['id'] == $price_item['stripe_id']) {
                                        $is_from_sms = 1;
                                    }
                                }
                            }
                        }
                    }
                }

                if ($is_from_sms == 1) {
                    return null;
                }

                $company->payment_failed_attempts = $company->payment_failed_attempts + 1;
                if ($company->payment_failed_attempts == 4) {
                    $company->is_read_only = true;
                    try {
                        $language = Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)?->value;
                        Mail::to($company->email)
                            ->locale($language ?? app()->getLocale())
                            ->send(new ReadOnlyAccountMail($company->name));
                    } catch (\Throwable $th) {
                    }
                }
                if ($company->payment_failed_attempts == 6) {
                    $company->is_blocked = true;

                    foreach ($company->users as $user) {
                        $user->tokens()->delete();
                    }
                    try {
                        $language = Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)?->value;
                        Mail::to($company->email)
                            ->locale($language ?? app()->getLocale())
                            ->send(new BlockAccountMail($company->name));
                    } catch (\Throwable $th) {
                        //throw $th;
                    }
                }
                $company->save();
            });
        } catch (\Throwable $th) {
            Log::info("Error invoice.payment_failed event:     " . $th);
        }
    }
}
