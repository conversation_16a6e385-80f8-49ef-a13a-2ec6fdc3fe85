<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\LetterOfConsent;
use App\Traits\SaveFile;
use App\ClientLetterOfConsent;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\ClientLetterOfConsentRequest;
use App\Http\Requests\v1\ClientLetterOfConsentIndexRequest;
use App\Http\Requests\v1\ClientLetterOfConsentUpdateRequest;
use App\Traits\ApiResponser;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ClientLetterOfConsentController extends Controller
{
    use SaveFile, ApiResponser;

    /**
     * Display a listing of the resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function index(ClientLetterOfConsentIndexRequest $request, Client $client)
    {
        $isPaginated = false;

        $clientLetterOfConsents = $client->letter_of_consents()->with(['cancelled_by']);


        if ($request->has('search')) {
            if (!$isPaginated) {
                $clientLetterOfConsents = $clientLetterOfConsents->get();
                $isPaginated = true;
            }

            $search = $request->search;
            $clientLetterOfConsents = $clientLetterOfConsents->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->consent_title), Str::lower($search));
            });
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');


            if ($orderBy == 'consent_title') {
                if (!$isPaginated) {
                    $clientLetterOfConsents = $clientLetterOfConsents->get();
                    $isPaginated = true;
                }

                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = $orderBy == 'consent_title' ? (SORT_NATURAL | SORT_FLAG_CASE) : SORT_FLAG_CASE;
                $clientLetterOfConsents =  $clientLetterOfConsents->sortBy($orderBy, $sortExtra, $isDescOrder);
                // $clientLetterOfConsents =  $clientLetterOfConsents->sort(function ($a, $b) use ($orderBy) {
                //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
                // });
            } else {
                $clientLetterOfConsents = $clientLetterOfConsents->orderBy($orderBy, $orderDirection);
            }
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => 'client letter of consents return successfully',
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($clientLetterOfConsents) : $clientLetterOfConsents->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $clientLetterOfConsents : $clientLetterOfConsents->get(),
                'message' => 'client letter of consents return successfully',
                'status' => '1'
            ]);
        }
    }

    public function store(ClientLetterOfConsentRequest $request, Client $client)
    {
        $this->authorize('create', [ClientLetterOfConsent::class, $client]);

        $clientLetterOfConsent = DB::transaction(function () use ($request, $client) {
            $input = $request->validated();

            $file = null;
            $file1 = null;
            $input['client_id'] = $client->id;
            if ($request->has('signature')) {
                $file = $this->saveFile($request->file('signature'), 'clients/' . md5($client->id) . '/letter_of_consents');

                $input['signature'] = $file->filename;
            }

            if ($request->input('consent_id')) {
                $consent = LetterOfConsent::find($request->input('consent_id'));

                $input['consent_title'] = $consent->consent_title;
                $input['letter'] = $consent->letter_html ?: ($consent->letter ?? "");
                $input['version'] = $consent->version;
            } else {
                $input['consent_title'] = "";
                $input['letter'] = "";
                $input['version'] = "0";
            }

            $input['is_publish_before_after_pictures'] = $request->input('is_publish_before_after_pictures');
            $input['is_bad_allergic_shock'] = 'no';

            $clientLetterOfConsent = ClientLetterOfConsent::create($input);

            if ($file) {
                $clientLetterOfConsent->files()->save($file);
            }
            if ($file1) {
                $clientLetterOfConsent->files()->save($file1);
            }

            $activity = activity()
                ->performedOn($clientLetterOfConsent);

            $activity = $activity->by($client);
            $activity->log("{$clientLetterOfConsent->client->first_name} {$clientLetterOfConsent->client->last_name} client 's letter of consent (:subject.consent_title) has been signed by patient - :causer.first_name :causer.last_name");

            return $clientLetterOfConsent;
        });

        return response()->json([
            'data' => $clientLetterOfConsent,
            'message' => __('strings.Client_Letter_of_Consent_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(ClientLetterOfConsentUpdateRequest $request, ClientLetterOfConsent $clientLetterOfConsent)
    {
        $this->authorize('update', [ClientLetterOfConsent::class, $clientLetterOfConsent]);

        DB::transaction(function () use ($request, $clientLetterOfConsent) {
            if ($request->has('verified_sign')) {
                $file1 = $this->saveFile($request->file('verified_sign'), 'clients/' . md5($clientLetterOfConsent->client_id) . '/letter_of_consents');

                $clientLetterOfConsent->verified_sign = $file1->filename;
                $clientLetterOfConsent->verified_signed_at = now();
                $clientLetterOfConsent->verified_sign_by_id = Auth::id();
                $clientLetterOfConsent->timestamps = false;

                $activity = activity()
                    ->performedOn($clientLetterOfConsent);

                $activity = $activity->by(Auth::user());
                $activity->log("{$clientLetterOfConsent->client->first_name} {$clientLetterOfConsent->client->last_name} client 's letter of consent (:subject.consent_title) had verified sign with bank-id by :causer.first_name :causer.last_name");
            }

            if ($request->has('is_signed_by_bank_id') && $request->is_signed_by_bank_id) {
                $user = Auth::user();

                if (!$user->is_bankid_verified) {
                    return response()->json([
                        'message' => __('prescription_strings.please_verify_bank_id'),
                        'status' => '0'
                    ]);
                }
                $clientLetterOfConsent->is_signed_by_bank_id = 1;
                $clientLetterOfConsent->signed_by_bank_id = $user->personal_id;
                $clientLetterOfConsent->verified_signed_at = now();
                $clientLetterOfConsent->verified_sign_by_id = Auth::id();
                $clientLetterOfConsent->timestamps = false;

                $activity = activity()
                    ->performedOn($clientLetterOfConsent);

                $activity = $activity->by(Auth::user());
                $activity->log("{$clientLetterOfConsent->client->first_name} {$clientLetterOfConsent->client->last_name} client 's letter of consent (:subject.consent_title) had verified sign with bank-id by :causer.first_name :causer.last_name");
            }

            if ($request->has('is_cancelled') && $request->is_cancelled) {
                $clientLetterOfConsent->is_cancelled = $request->is_cancelled;
                $clientLetterOfConsent->cancel_note = $request->cancel_note;
                $clientLetterOfConsent->cancelled_by_id = Auth::id();
                $clientLetterOfConsent->cancelled_at = Carbon::now();


                $activity = activity()
                    ->performedOn($clientLetterOfConsent);

                $activity = $activity->by(Auth::user());
                $activity->log("{$clientLetterOfConsent->client->first_name} {$clientLetterOfConsent->client->last_name} client 's letter of consent (:subject.consent_title) had cancelled sign by :causer.first_name :causer.last_name");
            }

            if ($clientLetterOfConsent->isDirty()) {
                $clientLetterOfConsent->save();
            }
        });

        return response()->json([
            'message' => __('strings.Client_Letter_of_Consent_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(ClientLetterOfConsent $clientLetterOfConsent)
    {
        $this->authorize('delete', [$clientLetterOfConsent]);

        $activity = activity()
            ->performedOn($clientLetterOfConsent);

        $activity = $activity->by(Auth::user());
        $activity->log("{$clientLetterOfConsent->client->first_name} {$clientLetterOfConsent->client->last_name} client 's letter of consent (:subject.consent_title) has been deleted by :causer.first_name :causer.last_name");

        if ($clientLetterOfConsent->delete()) {
            return response()->json([
                'message' => __('strings.Client_letter_of_consent_deleted_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Client_letter_of_consent_deletion_failed'),
                'status' => '0'
            ]);
        }
    }
}