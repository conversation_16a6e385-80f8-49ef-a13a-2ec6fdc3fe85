<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\ClientTreatment;
use App\User;
use App\Setting;
use App\Company;
use App\CompanyLead;
use App\CompanyPlatform;
use Carbon\Carbon;
use App\Traits\SaveFile;
use Illuminate\Support\Str;
use App\Traits\ApiResponser;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Rap2hpoutre\FastExcel\FastExcel;
use Illuminate\Support\Facades\Cache;
use Psr\Http\Message\ServerRequestInterface;
use App\Http\Requests\v1\CompanyIndexRequest;
use App\Http\Requests\v1\StoreCompanyRequest;
use App\Http\Requests\v1\UpdateCompanyRequest;
use Illuminate\Foundation\Auth\VerifiesEmails;
use App\Http\Requests\v1\UpdateMasterCompanyRequest;
use App\Http\Requests\v3\ShowCompanyRequest;
use App\Traits\ColorGenerator;
use App\Traits\GetEncryptedFile;
use App\Treatment;
use App\UserCompany;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Laravel\Passport\Http\Controllers\AccessTokenController;

class CompanyController extends Controller
{
    use SaveFile, VerifiesEmails, ApiResponser, ColorGenerator, GetEncryptedFile;

    /**
     * Verify account.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(CompanyIndexRequest $request)
    {
        $this->authorize('viewAny', [Company::class]);
        $isPaginated = false;

        $companies = Company::query()->where('email', '!=', '<EMAIL>');

        $companies = $companies->withCount([
            'clients',
            'users as booking_users_count' => function ($query) {
                $query->forBooking();
            },
            'users as record_users_count' => function ($query) {
                $query->forRecord();
            }
        ]);

        $companies = $companies->with([
            'lead',
            'platforms' => function ($query) {
                $query = $query->select([
                    'platform',
                    'company_id',
                ]);
            },
            'future_subscriptions',
            'subscriptions',
            'subscriptions.items',
        ]);

        $companies = $companies->addSelect([
            'last_login_at' => User::select('last_login_at')
                ->whereColumn('company_id', 'companies.id')
                ->orderByDesc('last_login_at')
                ->limit(1)
        ]);

        if ($request->has('super_user')) {
            $companies = $companies->with(['super_user' => function ($query) {
                return $query->select([
                    'id',
                    'company_id',
                    'email',
                    'first_name',
                    'last_name',
                ])->without('company')->setEagerLoads([]);
            }]);
        }

        if ($request->has('is_booking_on')) {
            $companies = $companies->where('is_booking_on', $request->is_booking_on);
        }

        // Subscription filter
        if ($request->has('subscription_type')) {
            match ($request->subscription_type) {
                'booking' => $companies->hasBookingPlan(),
                'record' => $companies->hasRecordPlan(),
                'pos' => $companies->hasPosPlan(),
                'qms' => $companies->hasManagementPlan(),
                default => null
            };
        }

        if ($request->has('filter')) {
            switch ($request->filter) {
                case "PRESCRIBERS":
                    $companies = $companies->whereHas('platforms', function ($query) {
                        $query->where('platform', CompanyPlatform::PRESCRIPTION);
                    })
                        //TODO::Try to do by hasManyThrough
                        ->with([
                            'users' => function ($query) use ($request) {
                                $query->whereExists(function ($query) {
                                    $query->select(DB::raw(1))
                                        ->from('companies')
                                        ->whereColumn('companies.email', 'users.email');
                                })
                                    ->setEagerLoads([])
                                    ->withCount([
                                        'accepted_companies',
                                        'assigned_prescriptions as signed_prescription_count' => function ($query) use ($request) {
                                            $query->whereColumn('client_prescriptions.sign_by_id', 'users.id')
                                                ->when($request->start_date && $request->end_date, function ($query) use ($request) {
                                                    $query->whereBetween('client_prescriptions.signed_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()]);
                                                });
                                        }
                                    ]);
                            }
                        ]);
                    break;
                case "COMPANY_DELETE":
                    $reminder_days = min(explode(',', env('COMPANY_DELETE_REMINDER_DAYS')));
                    $start_date = Carbon::parse(env("COMPANY_DELETE_START_DATE"));
                    $beforeDate = "";
                    $now = Carbon::now();
                    if ($start_date->addDays($reminder_days)->lte($now)) {
                        $beforeDate = $now->subDays($reminder_days)->format('Y-m-d');
                    }

                    $companies = $companies->has('clients', '<=', 1)
                        ->has('subscriptions', '<=', 0)
                        ->has('bookings', '<=', 0)
                        ->whereHas('platforms', function ($query) {
                            $query->where('platform',  CompanyPlatform::PRESCRIPTION);
                        }, '<=', 0)
                        ->whereNull('stripe_id')
                        ->whereDate('created_at', '<=', $beforeDate)
                        ->addSelect([
                            'is_delete_company' => Setting::select('value')
                                ->whereColumn('company_id', 'companies.id')
                                ->where('key', Setting::DELETE_COMPANY)
                                ->limit(1)
                        ])->orderBy('is_delete_company', 'ASC');


                default:
            }
        }

        if ($request->has('verification')) {
            $companies = $companies->where('verification', $request->verification === 'pending' ? null : $request->verification);
        }

        if ($request->has('contacted')) {
            $companies = $companies->where(function ($query) use ($request) {
                if ($request->contacted != 'true') {
                    $query = $query->doesntHave('lead');
                }

                $query->orWhereHas('lead', fn($q) => $q->where('contacted', $request->contacted == 'true'));
            });
        }

        if ($request->orderBy == 'contacted') {
            $companies = $companies->addSelect([
                'contacted' => CompanyLead::select('contacted')
                    ->whereColumn('company_id', 'companies.id')
                    ->limit(1)
            ]);
        }

        if ($request->has('search')) {
            $companies = Company::search($request->search)->constrain($companies);
        }

        if ($request->has('orderBy')) {
            if (in_array($request->orderBy, ['company_name', 'email', 'subscription', 'storage_usage', 'mobile_number'])) {
                if (!$isPaginated) {
                    $companies = $request->has('search') ? $companies->get() : $companies->lazy();
                    $isPaginated = true;
                }

                if ($request->orderBy == "subscription") {
                    $companies = $companies->sortBy(function ($company) {
                        $subscriptions = $company->active_subscriptions;
                        if (count($subscriptions)) {
                            return strtolower("Licenced");
                        }
                        if (!$company->isFreeTrailUsed() && $company->free_trail_end_date) {
                            return strtolower("Free Trial");
                        }
                        return strtolower("Not Subscribed");
                    }, SORT_REGULAR, !in_array($request->input('orderDirection', 'asc'), ['asc', 'ASC']));
                } else {
                    $companies = $companies->sortBy(function ($company) use ($request) {
                        return $company->{$request->orderBy} ? strtolower($company->{$request->orderBy}) : PHP_INT_MAX;
                    }, SORT_REGULAR, !in_array($request->input('orderDirection', 'asc'), ['asc', 'ASC']));
                }
            } else {
                $companies = $companies->orderBy($request->orderBy, $request->input('orderDirection', 'asc'));
            }
        }

        if ($isPaginated) {
            $companies = $this->paginate($companies);
        } else {
            $companies = $companies->paginate();
        }

        $companies->each(function ($company) {
            $company->append('is_subscribed')->makeHidden('doctors', 'all_users', 'future_subscriptions');
        });

        return response()->json(collect([
            'status' => '1',
            'message' => 'Companies returned successfully.'
        ])->merge($companies));
    }

    static private function companiesGenerator($companies)
    {
        foreach ($companies->cursor() as $company) {
            if ($company->email != '<EMAIL>') {
                yield [
                    'Name' => $company->company_name,
                    'E-mail' => $company->email,
                    'Mobile Number' => $company->country_code ? '+' . $company->country_code . $company->mobile_number : $company->mobile_number,
                    'Country' => $company->country_code ? (isset(config('phone_country')[$company->country_code]) ? config('phone_country')[$company->country_code] : '-') : '-',
                    'SUBSCRIPTION' => $company->record_plan || $company->pos_plan ? 'Licenced' : 'Not Subscribed',
                    'Booking System' => $company->is_booking_on ? 'true' : 'false',
                    'Record System' => $company->is_record_on ? 'true' : 'false',
                    'POS System' => $company->is_pos_on ? 'true' : 'false',
                    'QMS System' => $company->is_management_on ? 'true' : 'false',
                    'Users' => $company->users_count,
                    'Clients' => $company->clients_count,
                    'Treatments' => $company->procedures_count,
                    'Bookings' => $company->bookings_count,
                    'Storage Usage' => $company->storage_usage,
                    'Created At' => Carbon::parse($company->created_at)->format('Y-m-d H:i:s'),
                    'last login at' => $company->last_login_at,
                    'IsBlocked' => $company->is_blocked ? 'true' : 'false',
                    'IsReadOnly' => $company->is_read_only ? 'true' : 'false',
                ];
            }
        }
    }

    /**
     * login into user account.
     *
     */
    public function export(Request $request)
    {
        $companies = Company::with(['lead']);

        $companies = $companies->addSelect([
            'last_login_at' => User::select('last_login_at')
                ->whereColumn('company_id', 'companies.id')
                ->orderByDesc('last_login_at')
                ->limit(1)
        ]);

        $companies = $companies->withCount('users', 'clients', 'procedures', 'bookings');

        $companies = $companies->with([
            'platforms' => function ($query) {
                $query = $query->select([
                    'platform',
                    'company_id',
                ]);
            }
        ]);

        if ($request->has('subscription_type')) {
            if ($request->subscription_type == 'booking') {
                $companies = $companies->hasBookingPlan();
            }

            if ($request->subscription_type == 'record') {
                $companies = $companies->hasRecordPlan();
            }

            if ($request->subscription_type == 'pos') {
                $companies = $companies->hasPosPlan();
            }

            if ($request->subscription_type == 'qms') {
                $companies = $companies->hasManagementPlan();
            }
        }

        return (new FastExcel(self::companiesGenerator($companies)))->download('companies.xlsx');
    }

    /**
     * login into user account.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {}

    public function clientsCount(Request $request)
    {
        return response()->json([
            'data' => [
                'clients' => Auth::user()->company->clients()->count(),
                'users' => Auth::user()->company->users()->count(),
                'booking_users' => Auth::user()->company->users()->forBooking()->count(),
                'record_users' => Auth::user()->company->users()->forRecord()->count(),
                'pos_users' => Auth::user()->company->users()->forPos()->count(),
            ],
            'message' => 'Clients count return successfully',
            'status' => "1",
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreCompanyRequest $request, ServerRequestInterface $server_request, AccessTokenController $accessTokenController)
    {
        return DB::transaction(function () use ($request, $server_request, $accessTokenController) {
            $company = Company::create([
                'company_name' => $request->company_name,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'language_id' => 1,
                'email' => $request->email,
                'mobile_number' => $request->has('mobile_number') ? $request->mobile_number ?? "" : "",
                'country_code' => $request->has('country_code') ? $request->country_code ?? "" : "",
                'street_address' => $request->input('street_address', '') ?? '',
                'zip_code' => $request->input('zip_code', '') ?? '',
                'city' => $request->input('city', '') ?? '',
                'state' => $request->input('state', '') ?? '',
                'country' => $request->country,
                'password' => bcrypt($request->password),
                'unit' => 'usd',
            ]);

            // 'company_photo' => 'required|image|mimes:jpeg,jpg,png',

            dispatch(function () use ($company) {
                activity()
                    ->performedOn($company)
                    ->by($company)
                    ->log('New Company has been registered.');
            });

            $user = User::create([
                'title' => $request->company_name,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'company_id' => $company->id,
                'user_role' => User::ADMIN,
                'email' => $request->email,
                'mobile_number' => $request->has('mobile_number') ? $request->mobile_number ?? "" : "",
                'country_code' => $request->has('country_code') ? $request->country_code ?? "" : "",
                'password' => bcrypt($request->password),
                'street_address' => $request->input('street_address', '') ?? '',
                'zip_code' => $request->input('zip_code', '') ?? '',
                'city' => $request->input('city', '') ?? '',
                'state' => $request->input('state', '') ?? '',
                'country' => $request->country ?? '',
                'email_verified_at' => now(),
            ]);

            $user->last_login_at = now();
            $user->save();

            if ($request->has('company_photo')) {
                $company_file = $this->saveFile($request->company_photo, 'company_photo', $user, true);
                $company_photo = $company_file->filename;
                $company->file()->save($company_file);
                $company->profile_photo = $company_photo;
                $company->save();
            }

            if ($request->has('profile_photo')) {
                $user_file = $this->saveFile($request->profile_photo, 'user_photo', $user, true);
                $profile_photo = $user_file->filename;
                $user->file()->save($user_file);
                $user->profile_photo = $profile_photo;
                $user->save();
            }

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('New User has been created.');
            });

            // Changed status for verify account from 0 to 2
            if (Hash::check($request->password, $user->password)) {
                $user->tokens()->delete();
            }

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('User has logged in to system.');
            });

            $company->createOrGetStripeCustomer();

            $company->updateStripeCustomer([
                'metadata' => [
                    'Samarbete' => $request->input('refercode')
                ]
            ]);

            $client = Client::create([
                'user_id' => $user->id,
                'company_id' => $company->id,
                'profile_picture' => '',
                'first_name' => 'Test',
                'last_name' => 'Patient',
                'social_security_number' => now()->subMonth()->format('Y-m-d'),
                'email' => '<EMAIL>',
                'phone_number' => '**********',
            ]);

            $treatment = Treatment::create([
                'name' => 'Fillers',
                'description' => 'Test Fillers',
                'cost' => 1500,
                'color' => $this->generate(),
                'unit' => $company->unit ?? 'eur',
                'notes' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
                'type' => Treatment::TYPE_TREATMENT,
                'company_id' => $company->id,
            ]);

            $client_treatment_images = [];

            $clientTreatment = ClientTreatment::create([
                'client_id' => $client->id,
                'name' => 'Test Fillers',
                'description' => 'Test Fillers',
                'cost' => 1500,
                'color' => $this->generate(),
                'date' => Carbon::now()->format('Y-m-d'),
                'notes' => '',
                'notes_html' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
                'images' => json_encode($client_treatment_images),
                'unit' => $company->unit ?? 'eur',
                'treatment_cost' => 1500,
                'treatment_id' => null,
                'user_id' => $user->id,
            ]);

            $clientTreatmentDetail = $clientTreatment->details()->create([
                'name' => 'Fillers',
                'description' => 'Test Fillers',
                'cost' => 1500,
                'unit' => $company->unit ?? 'eur',
                'color' => $this->generate(),
                'notes' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
                'type' => Treatment::TYPE_TREATMENT,
                'actual_cost' => 1500,
                'actual_unit' => $company->unit ?? 'eur',
                'treatment_id' => $treatment->id,
            ]);

            event(new Registered($user));

            Auth::login($user);
            $request->session()->regenerate();

            return response()->json([
                // 'message' => __('strings.verify_email'),
                'message' => __('strings.Company_registered_successfully_next'),
                'status' => '1'
            ]);
        });
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Company $company, ShowCompanyRequest $request)
    {
        $this->authorize('viewAny', [Company::class]);
        $statistics = [];
        if ($request->has('statistic')) {
            switch ($request->statistic) {
                case 'PRESCRIBERS':
                    $user = User::where('email', $company->email)
                        ->without('company')
                        ->verifiedEmail()
                        ->withCount([
                            'accepted_companies',
                            'assigned_prescriptions as signed_prescription_count' => function ($query) use ($request) {
                                $query->whereColumn('client_prescriptions.sign_by_id', 'users.id');
                                if ($request->start_date && $request->end_date) {
                                    $query->whereBetween('client_prescriptions.signed_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()]);
                                }
                            }
                        ])
                        ->withSum([
                            'assigned_prescriptions as earning' => function ($query) use ($request) {
                                $query->whereColumn('client_prescriptions.sign_by_id', 'users.id');
                                if ($request->start_date && $request->end_date) {
                                    $query->whereBetween('client_prescriptions.signed_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()]);
                                }
                            }
                        ], 'fees')
                        ->withSum([
                            'assigned_prescriptions as fees' => function ($query) use ($request) {
                                $query->whereColumn('client_prescriptions.sign_by_id', 'users.id');
                                if ($request->start_date && $request->end_date) {
                                    $query->whereBetween('client_prescriptions.signed_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()]);
                                }
                            }
                        ], 'platform_fees')
                        ->firstOrFail();
                    $statistics['signed_prescription_count'] = (string) $user->signed_prescription_count;
                    $statistics['earning'] = (string) number_format($user->earning, 0, "", "");
                    $statistics['fees'] = (string) number_format($user->fees, 0, "", "");
                    break;
                case 'COMPANIES':
                    $statistics['clients'] = (string) $company->clients()->count();
                    $statistics['users'] = (string) $company->users()->count();
                    $statistics['bookings'] = (string) $company->bookings()->count();
                    $statistics['no_show'] = (string) $company->bookings()->where('is_shown', 0)->count();
                    break;

                default:
                    break;
            }
        }
        $company = $company->loadMissing([
            'all_users' => fn($query) => $query->setEagerLoads([]),
            'upcoming_platform_fees',
            'platforms'
        ])->append([
            'is_subscribed',
            'is_cancelled',
            'has_pending_payment',
            'active_subscriptions',
            'record_plan',
            'pos_plan',
            'management_plan',
            'booking_plan',
            'has_system',
        ])->makeHidden('all_users');


        $yearlyStripeSubscription =  $company->activeSubscription(true)?->asStripeSubscription();
        $monthlyStripeSubscription =  $company->activeSubscription(false)?->asStripeSubscription();


        $company->expiration_date = [
            "monthly" => $monthlyStripeSubscription ? Carbon::createFromTimestamp($monthlyStripeSubscription->current_period_end) : null,
            "yearly" => $yearlyStripeSubscription ? Carbon::createFromTimestamp($yearlyStripeSubscription->current_period_end) : null
        ];

        $company->user_count = $company?->booking_plan?->users ?? 0;
        $company->promo_code = $company->getActiveCoupon();
        $company->users = $company->all_users;

        return response()->json([
            'data' => $company,
            'statistics' => $statistics,
            'status' => '1',
            'message' => 'Company returned successfully',
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCompanyRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $message = "";
        $this->authorize('update', $company);

        if ($request->has('unit')) {
            $company->unit = $request->input('unit');
            $message = __('strings.Company_currency_updated_successfully');
        } else {
            if ($request->has('profile_photo')) {
                $file = $this->saveFile($request->file('profile_photo'), 'company_photo', null, true);

                // if ($company->file) {
                //     $company->file->delete();
                // }
                if ($company->files && $company->profile_photo) {
                    foreach ($company->files as $f) {
                        $temp_f_name = str_replace(substr(Storage::url('/'), 0, -1), "", (string) $this->getS3SignedUrl($f->filename));
                        if ($temp_f_name == $company->profile_photo) {
                            $f->delete();
                            break;
                        }
                    }
                }

                $company->file()->save($file);
                $company->profile_photo = $file->filename;
            }

            if ($request->has('email')) {
                if ($company->email != $request->email) {
                    if (Company::where('email', $request->email)->where('id', '!=', $company->id)->exists()) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_email'),
                            'status' => '0'
                        ]);
                    }
                    if ($user) {
                        $user->email = $request->email;
                        $user->save();
                    }
                    $company->email  = $request->email;
                    $company->save();
                }
            }
            if ($request->has('personal_id')) {
                if ($user) {
                    if ($user->personal_id != $request->personal_id) {

                        //CHECKING FOR PERSONAL ID
                        $count = User::get()->filter(function ($user) use ($request) {
                            if ($user->personal_id) {
                                return strtolower($user->personal_id) === strtolower($request->personal_id);
                            }

                            return false;
                        })->count();

                        if ($count > 0) {
                            return response()->json([
                                'message' => __('strings.Please_enter_unique_personal_id'),
                                'status' => '0'
                            ]);
                        }

                        $user->personal_id = $request->personal_id;
                        $user->is_bankid_verified = 0;
                        $user->save();
                    }
                }
            }
            if ($request->has('cover_image_deleted') && $company->cover_image) {
                if (Storage::disk('s3')->exists($company->cover_image)) {
                    Storage::disk('s3')->delete($company->cover_image);
                }
                $company->cover_image = "";
            }

            if ($request->has('cover_image')) {
                $file = $this->saveFile($request->file('cover_image'), 'company_photo', null, false);

                // if ($company->file) {
                //     $company->file->delete();
                // }
                if ($company->files && $company->cover_image) {
                    foreach ($company->files as $f) {
                        $temp_f_name = str_replace(substr(Storage::url('/'), 0, -1), "", (string) $this->getS3SignedUrl($f->filename));
                        if ($temp_f_name == $company->cover_image) {
                            $f->delete();
                            break;
                        }
                    }
                }

                $company->file()->save($file);
                $company->cover_image = $file->filename;
            }

            if ($request->has('company_name')) {
                $company->company_name = $request->input('company_name');

                if (!$user->title) {
                    $user->title = $request->company_name;
                }
            }

            if ($request->has('theme')) {
                $company->theme = $request->input('theme');
            }

            if ($request->has('street_address')) {
                $company->street_address = $request->input('street_address', '') ?? '';
            }

            if ($request->has('city')) {
                $company->city = $request->input('city', '') ?? '';
            }

            if ($request->has('state')) {
                $company->state = $request->input('state', '') ?? '';
            }

            if ($request->has('zip_code')) {
                $company->zip_code = $request->input('zip_code', '') ?? '';
            }
            if ($request->has('is_black_text')) {
                $company->is_black_text = $request->input('is_black_text', '1') ?? '';
            }

            // if ($request->has('country')) {
            //     $company->country = $request->input('country');
            // }

            if ($request->has('mobile_number')) {
                $company->mobile_number = $request->input('mobile_number', '') ?? '';

                if (!$user->mobile_number) {
                    $user->mobile_number = $request->mobile_number;
                }
            }

            if ($request->has('country_code')) {
                $company->country_code = $request->input('country_code', '') ?? '';

                if (!$user->country_code) {
                    $user->country_code = $request->country_code;
                }
            }

            if ($request->has('timezone')) {
                $company->timezone = $request->input('timezone', '') ?? '';
            }

            if ($request->has('organization_number')) {
                $company->organization_number = $request->input('organization_number', '');
            }

            if ($request->has('first_name')) {
                $company->first_name = $request->first_name;

                if (!$user->first_name) {
                    $user->first_name = $request->first_name;
                }
            }
            if ($request->has('last_name')) {
                $company->last_name = $request->last_name;

                if (!$user->last_name) {
                    $user->last_name = $request->last_name;
                }
            }

            $message = __('strings.Company_updated_successfully');
        }

        if ($company->isDirty()) {
            $company->save();
        }

        return response()->json([
            'data' => Auth::user(),
            'message' => $message,
            'status' => '1'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function masterUpdate(UpdateMasterCompanyRequest $request, Company $company)
    {
        $this->authorize('masterUpdate', [Company::class]);

        return DB::transaction(function () use ($request, $company) {
            if ($request->has('email')) {
                $user = $company->users()->where('email', $company->email)->firstOrFail();
                $user->email = $request->input('email');
                $user->save();
                $company->email = $request->input('email');
                $company->save();
            }

            if ($request->has('street_address')) {
                $company->street_address = $request->input('street_address');
            }

            if ($request->has('city')) {
                $company->city = $request->input('city');
            }

            if ($request->has('company_name')) {
                $company->company_name = $request->input('company_name');
            }

            if ($request->has('state')) {
                $company->state = $request->input('state');
            }

            if ($request->has('zip_code')) {
                $company->zip_code = $request->input('zip_code');
            }

            if ($request->has('country_code')) {
                $company->country_code = $request->input('country_code');
            }

            if ($request->has('timezone')) {
                $company->timezone = $request->input('timezone');
            }
            if ($request->has('mobile_number')) {
                $company->mobile_number = $request->mobile_number;
            }

            if ($request->has('organization_number')) {
                $company->organization_number = $request->input('organization_number', '');
            }

            if ($request->has('isv_percentage')) {
                $company->isv_percentage = $request->isv_percentage;
            }

            // if ($request->has('country')) {
            //     $company->country = $request->input('country');
            // }

            if ($request->has('is_blocked')) {
                if ($request->is_blocked == '1') {
                    //TODO: remove all users token.
                    foreach ($company->users as $user) {
                        $user->tokens()->delete();
                    }
                }
                $company->is_blocked = $request->input('is_blocked', 0);
            }

            if ($request->has('is_read_only')) {
                $company->is_read_only = $request->input('is_read_only', 0);
            }

            $message = __('strings.Company_updated_successfully');

            if ($company->isDirty()) {
                $company->save();
            }

            return response()->json([
                'data' => $company,
                'message' => $message,
                'status' => '1'
            ]);
        });
    }

    public function getUsage()
    {
        $value = Cache::rememberForever("storage-usage-" . Auth::user()->company->id, function () {
            $value = round(Auth::user()->company->files()->sum('size') / 1000000);

            if ($value > 1024) {
                $value = ($value / 1000) . " GB";
            } else {
                $value = $value . " MB";
            }
            return $value;
        });

        return response()->json([
            'data' => $value,
            'status' => '1'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Company $company)
    {
        $this->authorize('delete', [Company::class, $company]);

        $isDeleted = DB::transaction(function () use ($company) {
            $company->devices()->delete();
            $isDeleted = $company->delete();
            if ($company->is_subscribed && $isDeleted) {
                try {

                    $subscription_name = 'Subscription';
                    if ($company->activeSubscription()) {
                        $subscription_name = $company->activeSubscription()->name;
                    }
                    $company->subscription($subscription_name)->cancelNow();
                } catch (\Throwable $th) {
                }
            }
            return $isDeleted;
        });
        if ($isDeleted) {
            return response()->json([
                'message' => __('strings.company_removed_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.company_removal_failed'),
                'status' => '0'
            ]);
        }
    }
}
