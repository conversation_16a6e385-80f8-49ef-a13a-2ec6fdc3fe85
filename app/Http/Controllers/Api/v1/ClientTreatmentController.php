<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\ClientTreatment;
use App\Traits\SaveFile;
use App\Traits\ColorGenerator;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\ClientTreatmentRequest;
use App\Http\Requests\v1\ClientTreatmentUpdateRequest;
use App\Http\Requests\v1\ClientTreatmentDeleteMobileRequest;
use App\Http\Requests\v1\ClientTreatmentIndexRequest;
use App\Http\Requests\v1\ClientTreatmentUpdateMobileRequest;
use App\Traits\ApiResponser;
use Illuminate\Support\Str;

class ClientTreatmentController extends Controller
{
    use ColorGenerator, SaveFile, ApiResponser;

    public function index(ClientTreatmentIndexRequest $request, $client)
    {
        $isPaginated = false;

        $clientProcedures = ClientTreatment::where('client_id', $client)
            ->latest()->with('details', 'signed_by')
            ->with(['user', 'prescriptions' => fn($q) => $q->plucked(), 'cancelled_by']);

        if ($request->has('search')) {
            if (!$isPaginated) {
                $clientProcedures = $clientProcedures->get();
                $isPaginated = true;
            }

            $search = $request->search;
            $clientProcedures = $clientProcedures->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->name), Str::lower($search));
                // ||
                //     Str::contains(Str::lower($value->description), $search);
            });
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $clientProcedures = $clientProcedures->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);

            $clientProcedures = $clientProcedures->sortBy($orderBy, SORT_NATURAL | SORT_FLAG_CASE, $isDescOrder);
        }

        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => 'client treatments return successfully',
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($clientProcedures) : $clientProcedures->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $clientProcedures : $clientProcedures->get(),
                'message' => 'client treatments return successfully',
                'status' => '1'
            ]);
        }
    }

    public function store(ClientTreatmentRequest $request, Client $client)
    {
        $this->authorize('update', [Client::class, $client]);

        $clientTreatment['client_id'] = $client->id;
        $clientTreatment['name'] = $request->input('name');
        $clientTreatment['description'] = $request->input('description', "") ?? "";
        $clientTreatment['cost'] = $request->input('cost', "") ?? "";
        $clientTreatment['unit'] = $request->input('unit');
        $clientTreatment['treatment_cost'] = $request->input('treatment_cost', "") ?? "";
        $clientTreatment['treatment_id'] = $request->input('treatment_id', "") ?? null;
        $clientTreatment['color'] = $request->input('color');
        $clientTreatment['date'] = $request->input('date');
        $clientTreatment['notes'] = $request->input('notes', "") ?? "";

        $client_treatment_images = array();

        $files = [];
        foreach ($request->file('images', []) as $index => $image) {
            $file = $this->saveFile($image, 'clients/' . md5($client->id) . '/treatments');
            $files[] = $file;

            array_push($client_treatment_images, $file->filename);
        }

        $clientTreatment['images'] = json_encode($client_treatment_images);

        $clientTreatment = Auth::user()->client_treatments()->create($clientTreatment);

        $clientTreatment->files()->saveMany($files);

        return response()->json([
            'message' => __('strings.Client_Treatment_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(ClientTreatmentUpdateRequest $request, ClientTreatment $client_treatment)
    {
        $this->authorize('update', [ClientTreatment::class, $client_treatment]);

        DB::transaction(function () use ($request, $client_treatment) {
            if ($request->has("name")) {
                $client_treatment->name = $request->input('name', "") ?? "";
            }
            if ($request->has("date")) {
                $client_treatment->date = $request->input('date', null) ?? null;
            }
            if ($request->has("treatment_id")) {
                $client_treatment->treatment_id = $request->input('treatment_id', null) ?? null;
            }
            if ($request->has("cost")) {
                $client_treatment->cost = $request->input('cost', "") ?? "";
            }
            if ($request->has("unit")) {
                $client_treatment->unit = $request->input('unit', "") ?? "";
            }
            if ($request->has("notes")) {
                $client_treatment->notes = $request->input('notes', "") ?? "";
            }

            if ($request->has("images")) {
                $client_treatment_images = array();

                $files = [];
                foreach ($request->file('images', []) as $index => $image) {
                    $file = $this->saveFile($image, 'clients/' . md5($client_treatment->client_id) . '/treatments');
                    $files[] = $file;

                    array_push($client_treatment_images, $file->filename);
                }

                if (count($files) && count($client_treatment_images)) {
                    $client_treatment->files->each->delete();
                    $client_treatment->images = json_encode($client_treatment_images);
                    $client_treatment->files()->saveMany($files);
                }
            }

            if ($request->hasFile("sign")) {
                $file = $this->saveFile($request->file('sign'), 'clients/' . md5($client_treatment->client_id) . '/treatments/sign');
                $client_treatment->sign = $file->filename;
                $client_treatment->signed_at = now();
                $client_treatment->signed_by_id = Auth::id();
                $client_treatment->timestamps = false;

                $activity = activity()
                    ->performedOn($client_treatment);

                $activity = $activity->by(Auth::user());
                $activity->log("{$client_treatment->client->first_name} {$client_treatment->client->last_name} client 's procedure (:subject.name) had be signed by :causer.first_name :causer.last_name");
            }

            if ($request->has('is_signed_by_bank_id') && $request->is_signed_by_bank_id) {
                $user = Auth::user();

                if (!$user->is_bankid_verified) {
                    return response()->json([
                        'message' => __('prescription_strings.please_verify_bank_id'),
                        'status' => '0'
                    ]);
                }
                $client_treatment->is_signed_by_bank_id = 1;
                $client_treatment->signed_by_bank_id = $user->personal_id;
                $client_treatment->signed_by_id = Auth::id();
                $client_treatment->signed_at = now();
                $client_treatment->timestamps = false;

                $activity = activity()
                    ->performedOn($client_treatment);

                $activity = $activity->by(Auth::user());
                $activity->log("{$client_treatment->client->first_name} {$client_treatment->client->last_name} client 's procedure (:subject.title) had be signed with bank id by :causer.first_name :causer.last_name");
            }

            if ($client_treatment->isDirty()) {
                $client_treatment->user_id = Auth::id();
                $client_treatment->save();
            }
        });

        return response()->json([
            'message' => __('strings.Client_Treatment_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function updateMobile(ClientTreatmentUpdateMobileRequest $request, ClientTreatment $client_treatment)
    {
        $this->authorize('update', [ClientTreatment::class, $client_treatment]);

        if ($request->has("images")) {
            $client_treatment_files = $client_treatment->files;
            $client_treatment_images = $client_treatment_files->map(function ($file) {
                return $file->filename;
            })->toArray();

            $files = [];
            foreach ($request->file('images', []) as $index => $image) {
                $file = $this->saveFile($image, 'clients/' . md5($client_treatment->client_id) . '/treatments');
                $files[] = $file;


                $treatment_file = $client_treatment_files->where('id', $request->input("files.$index", -1))->first();
                if ($treatment_file) {
                    $key = array_search($treatment_file->filename, $client_treatment_images); // $key = 2;
                    $treatment_file->delete();
                    if ($key != false) {
                        $client_treatment_images[$index] = $file->filename;
                    } else {
                        array_push($client_treatment_images, $file->filename);
                    }
                } else {
                    array_push($client_treatment_images, $file->filename);
                }
            }

            if (count($files)) {
                $client_treatment->files()->saveMany($files);

                $client_treatment_images = ClientTreatment::with('files')->find($client_treatment->id)->files->map(function ($file) {
                    return $file->filename;
                });

                $client_treatment->images = json_encode($client_treatment_images);
            }
        }

        if ($client_treatment->isDirty()) {
            $client_treatment->user_id = Auth::id();
            $client_treatment->save();
        }
        return response()->json([
            'message' => __('strings.Client_Treatment_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function deleteImageMobile(ClientTreatmentDeleteMobileRequest $request, ClientTreatment $client_treatment)
    {
        $this->authorize('update', [ClientTreatment::class, $client_treatment]);

        if ($request->has("files")) {
            $client_treatment_files = $client_treatment->files;

            $files = [];
            foreach ($request->input('files', []) as $index => $image) {
                $treatment_file = $client_treatment_files
                    ->where('id', $request->input("files.$index", -1))
                    ->first();

                if ($treatment_file) {
                    $treatment_file->delete();
                }
            }

            $client_treatment_images = ClientTreatment::with('files')->find($client_treatment->id)->files->map(function ($file) {
                return $file->filename;
            });

            $client_treatment->images = json_encode($client_treatment_images);
        }

        if ($client_treatment->isDirty()) {
            $client_treatment->user_id = Auth::id();
            $client_treatment->save();
        }
        return response()->json([
            'message' => __('strings.Client_Treatment_images_deleted_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(ClientTreatment $clientTreatment)
    {
        $this->authorize('delete', [ClientTreatment::class, $clientTreatment]);
        $activity = activity()
            ->performedOn($clientTreatment);

        $activity = $activity->by(Auth::user());
        $activity->log("{$clientTreatment->client->first_name} {$clientTreatment->client->last_name} client 's procedure (:subject.name) had be deleted by :causer.first_name :causer.last_name");

        if ($clientTreatment->delete()) {
            return response()->json([
                'message' => __('strings.Client_Treatment_deleted_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Client_Treatment_deletion_failed'),
                'status' => '0'
            ]);
        }
    }
}
