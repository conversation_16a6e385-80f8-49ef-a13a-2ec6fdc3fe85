<?php

namespace App\Http\Controllers\Api\v1;

use App\File;
use App\Template;
use App\Traits\SaveFile;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\TemplateRequest;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Storage;

class TemplateController extends Controller
{
    use SaveFile;

    public function index()
    {
        $user = Auth::user();

        $templates = $user->company
            ->templates()
            ->where('is_editable', true)
            ->get();

        if ($templates->count() > 0) {
            return response()->json([
                'data' => $templates,
                'message' => 'Template returned successfully.',
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.No_Templates_found'),
                'status' => '0'
            ]);
        }
    }

    public function store(TemplateRequest $request)
    {
        $this->authorize('create', [Template::class]);

        $input = $request->validated();

        $file = null;
        if ($request->hasFile('image')) {
            $file = $this->saveFile($request->file('image'), 'template');

            $image = $file->filename;
        } else {
            $image = "";
        }

        $input['image'] = $image;
        $input['company_id'] = Auth::user()->company->id;

        $template = Template::create($input);

        if ($file) {
            $template->file()->save($file);
        }

        return response()->json([
            'message' => __('strings.Template_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(TemplateRequest $request, Template $template)
    {
        if (!$template->is_editable) {
            throw (new ModelNotFoundException())->setModel(Template::class);
        }

        $this->authorize('update', [Template::class, $template]);

        if ($request->has('image')) {
            $file = $this->saveFile($request->file('image'), 'template');

            try {
                if($template->file) {
                    $template->file->delete();
                }
            } catch (\Throwable $th) {
                //throw $th;
            }
            $template->file()->save($file);
            $template->image = $file->filename;
        }

        if ($request->has('name')) {
            $template->name = $request->input('name');
        }

        if ($template->isDirty()) {
            $template->save();
        }

        return response()->json([
            'message' => __('strings.Template_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function delete(Template $template)
    {
        if (!$template->is_editable) {
            throw (new ModelNotFoundException())->setModel(Template::class);
        }

        $this->authorize('delete', [Template::class, $template]);

        if ($template->forceDelete()) {
            return response()->json([
                'message' => __('strings.Template_deleted_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Template_deletion_failed'),
                'status' => '0'
            ]);
        }
    }
}
