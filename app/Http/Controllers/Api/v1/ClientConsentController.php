<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\Setting;
use App\ClientConsent;
use App\Company;
use Illuminate\Http\Request;
use App\Mail\ClientConsentMail;
use App\CompanyClientExtraField;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\StoreClientConsentRequest;
use Carbon\Carbon;

class ClientConsentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function index(Client $client)
    {
        //
    }

    static public function getClientFields(Client $client)
    {
        $firstList = Setting::where(function ($query) {
            return $query
                ->where('key', 'SHOW_HEALTH_QUESTIONNAIRE')
                ->orWhere('key', 'SHOW_AESTHETIC_INTEREST')
                ->orWhere('key', 'SHOW_COVID_19')
                ->orWhere('key', 'SHOW_LETTER_OF_CONSENT');
        })->where('value', true)->where('company_id', $client->company_id)->get()->pluck('key')->map(function ($data) {
            switch ($data) {
                case 'SHOW_HEALTH_QUESTIONNAIRE':
                    return [
                        'en' => __('strings.settings_HealthQuestionnaire', [], 'en'),
                        'sv' => __('strings.settings_HealthQuestionnaire', [], 'sv'),
                    ];
                case 'SHOW_AESTHETIC_INTEREST':
                    return [
                        'en' => __('strings.settings_Aestethicinterest', [], 'en'),
                        'sv' => __('strings.settings_Aestethicinterest', [], 'sv'),
                    ];
                case 'SHOW_COVID_19':
                    return [
                        'en' => __('strings.settings_Covid19Questionnaire', [], 'en'),
                        'sv' => __('strings.settings_Covid19Questionnaire', [], 'sv'),
                    ];
                case 'SHOW_LETTER_OF_CONSENT':
                    return [
                        'en' => __('strings.settings_LettersofConsents', [], 'en'),
                        'sv' => __('strings.settings_LettersofConsents', [], 'sv'),
                    ];
            }
        });

        $secondList = Setting::where(function ($query) {
            return $query
                ->where('key', 'PORTAL_VIEW_OCCUPATION')
                ->orWhere('key', 'PORTAL_VIEW_DATE_OF_BIRTH')
                ->orWhere('key', 'PORTAL_VIEW_CITY')
                ->orWhere('key', 'PORTAL_VIEW_PHONE')
                ->orWhere('key', 'PORTAL_VIEW_STREET_ADDRESS')
                ->orWhere('key', 'PORTAL_VIEW_ZIPCODE')
                ->orWhere('key', 'PORTAL_VIEW_STATE')
                ->orWhere('key', 'PORTAL_VIEW_COUNTRY')
                ->orWhere('key', 'PORTAL_VIEW_PROFILE');
        })->where('value', true)->where('company_id', $client->company_id)->get()->pluck('key')->map(function ($data) {
            switch ($data) {
                case 'PORTAL_VIEW_OCCUPATION':
                    return [
                        'en' => __('strings.setting_occupation', [], 'en'),
                        'sv' => __('strings.setting_occupation', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_DATE_OF_BIRTH':
                    return [
                        'en' => __('strings.setting_date_of_birth', [], 'en'),
                        'sv' => __('strings.setting_date_of_birth', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_CITY':
                    return [
                        'en' => __('strings.setting_city', [], 'en'),
                        'sv' => __('strings.setting_city', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_PHONE':
                    return [
                        'en' => __('strings.setting_phone', [], 'en'),
                        'sv' => __('strings.setting_phone', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_STREET_ADDRESS':
                    return [
                        'en' => __('strings.setting_street_address', [], 'en'),
                        'sv' => __('strings.setting_street_address', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_ZIPCODE':
                    return [
                        'en' => __('strings.setting_zip_code', [], 'en'),
                        'sv' => __('strings.setting_zip_code', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_STATE':
                    return [
                        'en' => __('strings.setting_state', [], 'en'),
                        'sv' => __('strings.setting_state', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_COUNTRY':
                    return [
                        'en' => __('strings.setting_country', [], 'en'),
                        'sv' => __('strings.setting_country', [], 'sv'),
                    ];
                case 'PORTAL_VIEW_PROFILE':
                    return [
                        'en' => __('strings.setting_profile', [], 'en'),
                        'sv' => __('strings.setting_profile', [], 'sv'),
                    ];
            }
        });

        $thirdList = CompanyClientExtraField::where('company_id', $client->company_id)->where('view', true)->get()->pluck('name')->values()->all();

        $fourthList = collect([
            [
                'en' => __('strings.setting_name', [], 'en'),
                'sv' => __('strings.setting_name', [], 'sv'),
            ],
            [
                'en' => __('strings.setting_email', [], 'en'),
                'sv' => __('strings.setting_email', [], 'sv'),
            ],
            [
                'en' => __('strings.setting_health_information', [], 'en'),
                'sv' => __('strings.setting_health_information', [], 'sv'),
            ],
        ]);

        return [
            'en' => array_merge(
                $fourthList->map(function ($data) {
                    return $data['en'];
                })->values()->all(),
                $secondList->map(function ($data) {
                    return $data['en'];
                })->values()->all(),
                $thirdList,
                $firstList->map(function ($data) {
                    return $data['en'];
                })->values()->all(),
            ),
            'sv' => array_merge(
                $fourthList->map(function ($data) {
                    return $data['sv'];
                })->values()->all(),
                $secondList->map(function ($data) {
                    return $data['sv'];
                })->values()->all(),
                $thirdList,
                $firstList->map(function ($data) {
                    return $data['sv'];
                })->values()->all(),
            ),
        ];
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function store(Client $client)
    {
        $this->authorize('create', [ClientConsent::class, $client]);

        $client_fields = self::getClientFields($client);

        $myCompanyConsent = ClientConsentController::getConsent($client->company);

        $message = __('strings.consent_body', [
            'company_name' => $client->company->company_name,
            'fields' => implode('', collect($client_fields[app()->getLocale()])->map(function ($data) {
                return "•    $data<br>";
            })->values()->all()),
        ]);

        if ($myCompanyConsent) {
            $message = $myCompanyConsent;
        }

        $client->consent()->updateOrCreate([
            'client_id' => $client->id,
        ], [
            'fields' => json_encode($client_fields),
            'message' => $message,
            'verified_at' => now(),
        ]);

        $activity = activity()
            ->performedOn($client);

        $activity = $activity->by(Auth::user());
        $activity->log($client->first_name . " " . $client->last_name . "'s consent has been created by :causer.first_name :causer.last_name from panel");

        return response()->json([
            'message' => __('strings.client_consent_verified_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Client  $client
     * @param  \App\ClientConsent  $clientConsent
     * @return \Illuminate\Http\Response
     */
    public function show(Client $client, ClientConsent $clientConsent)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Client  $client
     * @param  \App\ClientConsent  $clientConsent
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Client $client, ClientConsent $clientConsent)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @param  \App\ClientConsent  $clientConsent
     * @return \Illuminate\Http\Response
     */
    public function destroy(Client $client)
    {
        $this->authorize('delete', [ClientConsent::class, $client]);

        if ($client->consent) {
            $client->consent->delete();

            $activity = activity()
                ->performedOn($client);

            $activity = $activity->by(Auth::user());
            $activity->log($client->first_name . " " . $client->last_name . "'s consent has been deleted by :causer.first_name :causer.last_name");
        }

        return response()->json([
            'message' => __('strings.client_consent_deleted_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * send email consent to client
     *
     * @param  \App\Client  $client
     * @param  \App\ClientConsent  $clientConsent
     * @return \Illuminate\Http\Response
     */
    public function sendMail(Client $client)
    {
        $this->authorize('sendMail', [ClientConsent::class, $client]);

        $language = Setting::getSetting($client->company, Setting::CUSTOMER_LANGUAGE)?->value;

        Mail::to($client->email)->locale($language ?? app()->getLocale())->send(new ClientConsentMail($client, $language));

        return response()->json([
            'message' => __('strings.client_consent_mail_send_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * verify email consent to client
     *
     * @param  \App\Client  $client
     * @param  \App\ClientConsent  $clientConsent
     * @return \Illuminate\Http\Response
     */
    public function verifyMail(Request $request, Client $client)
    {
        if (!$request->hasValidSignature()) {
            abort(401);
        }

        $this->authorize('verifyMail', [ClientConsent::class, $client]);

        $client_fields = self::getClientFields($client);

        $myCompanyConsent = ClientConsentController::getConsent($client->company);

        if ($myCompanyConsent) {
            $message = $myCompanyConsent;
        } else {
            $message = __('strings.consent_body', [
                'company_name' => $client->company->company_name,
                'fields' => implode('', collect($client_fields[app()->getLocale()])->map(function ($data) {
                    return "•    $data<br>";
                })->values()->all()),
            ]);
        }

        $clientConsent = $client->consent()->updateOrCreate([
            'client_id' => $client->id,
        ], [
            'fields' => json_encode($client_fields),
            'message' => $message,
            'verified_at' => now(),
        ]);

        $activity = activity()->performedOn($client);

        $activity->log($client->first_name . " " . $client->last_name . "(" . $client->email . ")'s consent has been created from email consent");

        return view('mails.client_consent_success', [
            'date' => Setting::formateDate($client->company, $clientConsent->created_at),
            'fields' => implode('', collect($client_fields[app()->getLocale()])->map(function ($data) {
                return "•    $data<br>";
            })->values()->all()),
            'company_name' => $client->company->company_name,
            'company_consent' => $message,
            'company_email' => $client->company->email,
        ]);
    }

    public const hippa_terms = [
        "en" => "<p>We value your privacy and are committed to protecting your personal data. Below is our policy for personal data that we ask you to read carefully and approve before your patient visit.</p><h2>1. Data Controller</h2><p>[Clinic name] is responsible for the processing of your personal data.</p><h2>2. Collection and Processing of Personal Data</h2><p>To provide you with the best possible care, we collect and process the following personal data:</p><ul><li><p>Name</p></li><li><p>Personal identification number</p></li><li><p>Contact details (address, phone number, email)</p></li><li><p>Medical history and records</p></li><li><p>Visit and treatment information</p></li></ul><h2>3. Purpose of Processing</h2><p>Your personal data is collected and processed for the following purposes:</p><ul><li><p>To provide medical care and treatment</p></li><li><p>To administer patient visits and manage bookings</p></li><li><p>To communicate with you about your health status and treatments</p></li><li><p>To fulfil legal obligations according to laws and regulations</p></li></ul><h2>4. Legal Basis for Processing</h2><p>The processing of your personal data is based on:</p><ul><li><p>Contract: To fulfil our agreement with you as a patient.</p></li><li><p>Legal obligation: To comply with our obligations under healthcare laws.</p></li><li><p>Consent: For specific treatments requiring your explicit consent.</p></li></ul><h2>5. Sharing of Personal Data</h2><p>If the law allows we may share your personal data with:</p><ul><li><p>Other healthcare providers to ensure coordinated care</p></li><li><p>Authorities and regulatory bodies as required by law</p></li><li><p>IT providers supporting our systems, under strict confidentiality</p></li></ul><h2>6. Your Rights</h2><p>You have the right to:</p><ul><li><p>Access your personal data and receive a copy of it</p></li><li><p>Request correction of inaccurate or incomplete data</p></li><li><p>Request deletion of your personal data in certain circumstances</p></li><li><p>Object to processing based on our legitimate interest</p></li><li><p>Request restriction of processing under certain conditions</p></li><li><p>Have your personal data transferred to another healthcare provider (data portability)</p></li></ul><h2>7. Data Retention</h2><p>Your personal data is stored only as long as necessary to fulfil the purposes for which it was collected and in accordance with applicable legal requirements.</p><h2>8. Security</h2><p>We take appropriate technical and organizational measures to protect your personal data against unauthorized access, loss, destruction, or alteration.</p><h2>9. Contact Information</h2><p>If you have questions about this policy or your personal data, please contact us at:</p><ul><li><p>[Clinics Super User]</p></li><li><p>[Clinics phone number]</p></li><li><p>[Clinics email-address]</p></li><li><p>[Clinics Adress]</p></li></ul><h2>10. Complaints</h2><p>If you are dissatisfied with how we process your personal data, you have the right to file a complaint with the authorities in your country.</p><hr><p>By approving this policy, you consent to the processing of your personal data as described above.</p>",
        "sv" => "<p>Vi värnar om din integritet och strävar efter att skydda dina personuppgifter. Nedan följer vår policy om personuppgifter som vi ber dig att läsa noggrant och godkänna innan ditt patientbesök.</p><h2>1. Ansvarig för personuppgifter</h2><p>[Klinikens namn] är ansvarig för behandlingen av dina personuppgifter.</p><h2>2. Insamling och behandling av personuppgifter</h2><p>För att kunna ge dig bästa möjliga vård samlar vi in och behandlar följande personuppgifter:</p><ul><li><p>Namn</p></li><li><p>Personnummer</p></li><li><p>Kontaktuppgifter (ex: adress, telefonnummer, e-post)</p></li><li><p>Medicinsk historik och journalinformation</p></li><li><p>Besöks- och behandlingsinformation</p></li></ul><h2>3. Ändamål med behandlingen</h2><p>Dina personuppgifter samlas in och behandlas för följande ändamål:</p><ul><li><p>För att erbjuda medicinsk vård och behandling</p></li><li><p>För att administrera patientbesök och hantera bokningar</p></li><li><p>För att kommunicera med dig om ditt hälsotillstånd och dina behandlingar</p></li><li><p>För att uppfylla rättsliga skyldigheter enligt lag och regler</p></li></ul><h2>4. Rättslig grund för behandlingen</h2><p>Behandlingen av dina personuppgifter grundar sig på:</p><ul><li><p>Avtal: För att fullgöra vårt avtal med dig som patient.</p></li><li><p>Rättslig förpliktelse: För att uppfylla våra skyldigheter enligt hälso- och sjukvårdslagen.</p></li><li><p>Samtycke: För specifika behandlingar som kräver ditt uttryckliga samtycke.</p></li></ul><h2>5. Delning av personuppgifter</h2><p>Om lagen tillåter kan vi komma att dela dina personuppgifter med:</p><ul><li><p>Andra vårdgivare för att säkerställa samordnad vård</p></li><li><p>Myndigheter och tillsynsorgan enligt lagkrav</p></li><li><p>IT-leverantörer som stödjer våra system, under strikt sekretess</p></li></ul><h2>6. Dina rättigheter</h2><p>Du har rätt att:</p><ul><li><p>Få tillgång till dina personuppgifter och få en kopia av dem</p></li><li><p>Begära rättelse av felaktiga eller ofullständiga uppgifter</p></li><li><p>Begära radering av dina personuppgifter i vissa fall</p></li><li><p>Invända mot behandling som grundar sig på vårt berättigade intresse</p></li><li><p>Begära begränsning av behandlingen under vissa omständigheter</p></li><li><p>Få dina personuppgifter överförda till en annan vårdgivare (dataportabilitet)</p></li></ul><h2>7. Lagring av personuppgifter</h2><p>Dina personuppgifter lagras endast så länge som det är nödvändigt för att uppfylla de ändamål som de samlades in för och i enlighet med gällande lagkrav.</p><h2>8. Säkerhet</h2><p>Vi vidtar lämpliga tekniska och organisatoriska åtgärder för att skydda dina personuppgifter mot obehörig åtkomst, förlust, förstörelse eller ändring.</p><h2>9. Kontaktinformation</h2><p>Om du har frågor om denna policy eller dina personuppgifter, kontakta oss på:</p><ul><li><p>[Klinikens superanvändare]</p></li><li><p>[Klinikens telefonnummer]</p></li><li><p>[Klinikens e-postadress]</p></li><li><p>[Klinikens adress]</p></li></ul><h2>10. Klagomål</h2><p>Om du är missnöjd med hur vi behandlar dina personuppgifter, har du rätt att lämna in ett klagomål till Datainspektionen.</p><h2>Godkännande</h2><p>Genom att godkänna denna policy samtycker du till att vi behandlar dina personuppgifter enligt ovanstående beskrivning.</p><hr><p>Jag har läst och förstått [Klinikens namn] policy om personuppgifter och samtycker till behandlingen av mina personuppgifter.</p>",
    ];

    public static function getConsent(Company $company)
    {
        $consent_lang = Setting::getSetting($company, Setting::CUSTOMER_LANGUAGE)?->value;
        $consent_EN = Setting::getSetting($company, Setting::HIPPA_TERMS)?->value;
        $consent_SV = Setting::getSetting($company, Setting::HIPPA_TERMS_SV)?->value;

        return $consent_lang == 'en'
            ? ($consent_EN ? $consent_EN : ClientConsentController::hippa_terms['en'])
            : ($consent_SV ? $consent_SV : ClientConsentController::hippa_terms['sv']);
    }
}
