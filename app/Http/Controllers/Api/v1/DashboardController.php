<?php

namespace App\Http\Controllers\Api\v1;

use App\Plan;
use App\User;
use App\Client;
use App\ClientPrescription;
use App\ClientSMS;
use App\Company;
use Carbon\Carbon;
use App\ClientTreatment;
use App\CompanyBooking;
use App\CompanyPlatform;
use App\CompanyService;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\v1\DashboardBoxDataRequest;
use App\Http\Requests\v1\DashboardGraphDataRequest;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function userGraphData(Request $request)
    {

        $user = Auth::user();
        $company = $user->company;
        $user_ids = [];
        if ($request->has('user_ids')) {
            $user_ids = $request->user_ids;
        }


        //BOOKING DATA
        $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
        $data = [];
        $data['data']['title']['text'] = "Bookings";
        $data['data']['chart']['type'] = "line";



        if (count($user_ids) > 0) {
            $data['data']["series"] = [];

            $users = User::whereIn('id', $user_ids)->get();

            foreach ($period as $date) {
                if ($request->input('filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                } elseif ($request->input('filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                } elseif ($request->input('filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                }
            }
            foreach ($users as $user) {
                $user_data = [];
                foreach ($period as $date) {
                    if ($request->input('filter', 'month') == 'month') {
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    } elseif ($request->input('filter', 'month') == 'week') {
                        $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                    } elseif ($request->input('filter', 'month') == 'day') {
                        $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                    } else {
                        $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                        $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    }


                    $user_bookings_counts = CompanyBooking::where('is_verified', 1)
                        ->where('company_id', $company->id)
                        ->whereBetween('created_at', [$start_date, $end_date])
                        ->where('user_id', $user->id);

                    // $user_data = $user_bookings_counts->count();
                    array_push($user_data, $user_bookings_counts->count());
                }



                $data['data']["series"][] = [
                    "name" => $user->first_name . ' ' . $user->last_name,
                    "data" => $user_data,
                ];
            }
        } else {
            $data['data']["series"][0]["name"] = "Count";
            foreach ($period as $date) {
                if ($request->input('filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }


                $bookings_counts = CompanyBooking::where('is_verified', 1)->where('company_id', $company->id)->whereBetween('created_at', [$start_date, $end_date]);
                if (count($user_ids) > 0) {
                    $bookings_counts = $bookings_counts->whereIn('user_id', $user_ids);
                }

                $data['data']["series"][0]["data"][] = $bookings_counts->count();
            }
        }


        $start_date = Carbon::parse($request->start_date);
        $end_date = Carbon::parse($request->end_date);
        if ($request->input('filter', 'month') == 'month') {
            $start_date = $start_date->startOfMonth()->format("Y-m-d H:i:s");
            $end_date = $end_date->endOfMonth()->format("Y-m-d H:i:s");
        } elseif ($request->input('filter', 'month') == 'week') {
            $start_date = $start_date->startOfWeek()->format("Y-m-d H:i:s");
            $end_date = $end_date->endOfWeek()->format("Y-m-d H:i:s");
        } elseif ($request->input('filter', 'month') == 'day') {
            $start_date = $start_date->startOfDay()->format("Y-m-d H:i:s");
            $end_date = $end_date->endOfDay()->format("Y-m-d H:i:s");
        } else {
            $start_date = $start_date->startOfMonth()->format("Y-m-d H:i:s");
            $end_date = $end_date->endOfMonth()->format("Y-m-d H:i:s");
        }

        $cancelled_booking = CompanyBooking::where('is_verified', 1)->where('company_id', $company->id)->whereBetween('created_at', [$start_date, $end_date])->where('is_cancelled', 1);
        if (count($user_ids) > 0) {
            $cancelled_booking = $cancelled_booking->whereIn('user_id', $user_ids);
        }

        $no_show_booking = CompanyBooking::where('is_verified', 1)->where('company_id', $company->id)->whereBetween('created_at', [$start_date, $end_date])->where('is_shown', 0);
        if (count($user_ids) > 0) {
            $no_show_booking = $no_show_booking->whereIn('user_id', $user_ids);
        }

        $total_bookings = CompanyBooking::where('is_verified', 1)->where('company_id', $company->id)->whereBetween('created_at', [$start_date, $end_date]);
        if (count($user_ids) > 0) {
            $total_bookings = $total_bookings->whereIn('user_id', $user_ids);
        }

        $data['data']["counts"]["cancelled_booking"] = $cancelled_booking->count();
        $data['data']["counts"]["no_show_booking"] = $no_show_booking->count();
        $data['data']["counts"]["total_booking"] = $total_bookings->count();
        $booking_data = $data;







        //client DATA
        $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
        $data = [];
        $data['data']['title']['text'] = "Clients";
        $data['data']['chart']['type'] = "bar";
        $data['data']["series"][0]["name"] = "Count";

        foreach ($period as $date) {
            if ($request->input('filter', 'month') == 'month') {
                $data['data']["xaxis"]["categories"][] = $date->format("M y");
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            } elseif ($request->input('filter', 'month') == 'week') {
                $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
            } elseif ($request->input('filter', 'month') == 'day') {
                $data['data']["xaxis"]["categories"][] = $date->format("d M");
                $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
            } else {
                $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
            }

            $clients_counts = Client::where('company_id', $company->id)
                ->whereBetween('created_at', [$start_date, $end_date]);


            if (count($user_ids) > 0) {
                $clients_counts->whereHas('accesses', function ($query) use ($user_ids) {
                    $query = $query->whereIn('user_client.user_id', $user_ids);
                });
            }

            $data['data']["series"][0]["data"][] = $clients_counts->count();
        }
        $client_data = $data;



        $final_data = [];
        $final_data[0] = $client_data;
        $final_data[1] = $booking_data;


        return response()->json([
            'data' => $final_data,
            'message' => 'graph data returned successfully.',
            'status' => '1',
        ]);
    }

    public function clientGraphData(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $user_ids = [];


        $clients = Client::where('company_id', $company->id);

        $start_date = Carbon::parse($request->start_date);
        $end_date = Carbon::parse($request->end_date);

        $clients = $clients->withCount(['bookings' => function ($query) use ($start_date, $end_date, $request) {
            $query = $query
                ->whereDate('start_at', '>=', $start_date->format('Y-m-d'))
                ->whereDate('start_at', '<=', $end_date->format('Y-m-d'));

            switch ($request->filter) {
                case 'most_booked':
                    $query = $query->where('is_cancelled', 0)->where('is_shown', 1);
                    break;
                case 'most_cancelled':
                    $query = $query->where('is_cancelled', 1);
                    break;
                case 'most_no_show':
                    $query = $query->where('is_shown', 0);
                    break;

                default:
                    $query = $query->where('is_cancelled', 0)->where('is_shown', 1);
                    break;
            }

            if ($request->has('user_ids')) {
                $user_ids = $request->user_ids;

                $query = $query->whereIn('user_id', $user_ids);
            }
        }]);


        $clients = $clients->orderBy('bookings_count', 'desc')->limit(10);

        return response()->json([
            'data' => $clients->get(),
            'message' => 'graph data returned successfully.',
            'status' => '1',
        ]);
    }

    public function serviceGraphData(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $user_ids = [];

        $services = CompanyService::where('company_id', $company->id)->where('is_active', 1);

        $start_date = Carbon::parse($request->start_date);
        $end_date = Carbon::parse($request->end_date);

        $services = $services->withCount(['bookings' => function ($query) use ($start_date, $end_date, $request) {
            $query = $query
                ->whereDate('start_at', '>=', $start_date->format('Y-m-d'))
                ->whereDate('start_at', '<=', $end_date->format('Y-m-d'));

            $query = $query->where('is_cancelled', 0)->where('is_shown', 1);

            if ($request->has('user_ids')) {
                $user_ids = $request->user_ids;

                $query = $query->whereIn('user_id', $user_ids);
            }
        }]);

        if ($request->has('filter')) {
            switch ($request->filter) {
                case 'most_booked':
                    $services = $services->orderBy('bookings_count', 'desc')->limit(10);
                    break;
                case 'least_booked':
                    $services = $services->orderBy('bookings_count', 'asc')->limit(10);
                    break;
            }
        }

        return response()->json([
            'data' => $services->get(),
            'message' => 'graph data returned successfully.',
            'status' => '1',
        ]);
    }
    public function graphData(DashboardGraphDataRequest $request)
    {
        $data = [];

        $booking_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-booking-{$request->input('data.4.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.4.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "Bookings";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "Count";

            foreach ($period as $date) {
                if ($request->input('data.4.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.4.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.4.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] = CompanyBooking::query()
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->count();
            }

            return $data;
        });


        $treatment_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-treatment-{$request->input('data.3.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.3.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "Treatments";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "Count";

            foreach ($period as $date) {
                if ($request->input('data.3.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.3.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.3.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] = ClientTreatment::query()
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->count();
            }

            return $data;
        });

        $client_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-client-{$request->input('data.2.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.2.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "Clients";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "Count";

            foreach ($period as $date) {
                if ($request->input('data.2.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.2.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.2.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] = Client::query()
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->count();
            }

            return $data;
        });

        $user_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-user-{$request->input('data.1.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.1.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "Users";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "Count";


            foreach ($period as $date) {
                if ($request->input('data.1.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.1.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.1.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] =
                    User::where('email', '!=', '<EMAIL>')
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->count();
            }

            return $data;
        });

        $company_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-company-{$request->input('data.0.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.0.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "Companies";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "Booking";
            $data['data']["series"][1]["name"] = "Record";
            $data['data']["series"][2]["name"] = "MAL";
            $data['data']["series"][3]["name"] = "POS";

            // $plans = Cache::rememberForever('plans-key', function () {
            //     return Plan::get();
            // });

            // foreach ($plans as $index => $plan) {
            //     $data['data']["series"][$index + 1]["name"] = $plan->name;
            //     $data['data']["series"][$index + 1]["name"] = $plan->name;
            //     $data['data']["series"][$index + 1]["name"] = $plan->name;
            //     $data['data']["series"][$index + 1]["name"] = $plan->name;
            // }

            foreach ($period as $date) {
                if ($request->input('data.0.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.0.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.0.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] =
                    Company::whereDoesntHave('users', function ($query) {
                        $query->where('email', '<EMAIL>');
                    })
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->where('is_booking_on', 1)
                    ->count();
                $data['data']["series"][1]["data"][] =
                    Company::whereDoesntHave('users', function ($query) {
                        $query->where('email', '<EMAIL>');
                    })
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->where('is_record_on', 1)
                    ->count();
                $data['data']["series"][2]["data"][] =
                    Company::whereDoesntHave('users', function ($query) {
                        $query->where('email', '<EMAIL>');
                    })
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->whereHas('platforms', function ($query) {
                        $query->where('platform', CompanyPlatform::PRESCRIPTION);
                    })
                    ->count();
                $data['data']["series"][3]["data"][] = 0;
                // $data['data']["series"][3]["data"][] =
                // Company::whereDoesntHave('users', function ($query) {
                //     $query->where('email', '<EMAIL>');
                // })
                // ->whereBetween('created_at', [$start_date, $end_date])
                // ->where('is_pos_on',1)
                // ->count();
                // foreach ($plans as $index => $plan) {
                //     $data['data']["series"][1 + $index]["data"][] =
                //         Company::whereDoesntHave('users', function ($query) {
                //             $query->where('email', '<EMAIL>');
                //         })
                //         ->whereHas('subscriptions', function ($query) use ($plan) {
                //             $query->active()->where('stripe_plan', $plan->plan_id);
                //         })
                //         ->whereBetween('created_at', [$start_date, $end_date])
                //         ->count();
                // }
            }

            return $data;
        });

        $prescription_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-prescriptions-{$request->input('data.5.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.5.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "Prescriptions";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "All";
            $data['data']["series"][1]["name"] = "Signed";

            foreach ($period as $date) {
                if ($request->input('data.5.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.5.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.5.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] =
                    ClientPrescription::whereBetween('created_at', [$start_date, $end_date])->count();
                $data['data']["series"][1]["data"][] =
                    ClientPrescription::whereNotNull('signed_at')->whereBetween('created_at', [$start_date, $end_date])->count();
            }

            return $data;
        });

        $sms_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-sms-{$request->input('data.6.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.6.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "SMS";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "SMS";

            foreach ($period as $date) {
                if ($request->input('data.6.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.6.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.6.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] = ClientSMS::whereBetween('created_at', [$start_date, $end_date])->sum('total_message_count');
            }

            return $data;
        });

        $mal_data = Cache::remember("{$request->start_date}-{$request->end_date}-graph-data-mal-{$request->input('data.7.filter', 'month')}-" . App::getLocale(), 600, function () use ($request) {
            $period = CarbonPeriod::create(Carbon::parse($request->start_date)->startOfDay(), "1 {$request->input('data.7.filter', 'month')}", Carbon::parse($request->end_date)->endOfDay());
            $data = [];

            $data['data']['title']['text'] = "MAL";
            $data['data']['chart']['type'] = "bar";
            $data['data']["series"][0]["name"] = "Companies";

            foreach ($period as $date) {
                if ($request->input('data.7.filter', 'month') == 'month') {
                    $data['data']["xaxis"]["categories"][] = $date->format("M y");
                    $start_date = $date->startOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.7.filter', 'month') == 'week') {
                    $data['data']["xaxis"]["categories"][] = "{$date->weekOfMonth} week of {$date->format("M")}";
                    $start_date = $date->startOfWeek()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfWeek()->format("Y-m-d H:i:s");
                } elseif ($request->input('data.7.filter', 'month') == 'day') {
                    $data['data']["xaxis"]["categories"][] = $date->format("d M");
                    $start_date = $date->startOfDay()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfDay()->format("Y-m-d H:i:s");
                } else {
                    $start_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                    $end_date = $date->endOfMonth()->format("Y-m-d H:i:s");
                }

                $data['data']["series"][0]["data"][] =  Company::whereDoesntHave('users', function ($query) {
                    $query->where('email', '<EMAIL>');
                })
                    ->whereBetween('created_at', [$start_date, $end_date])
                    ->whereHas('platforms', function ($query) {
                        $query->where('platform', CompanyPlatform::PRESCRIPTION);
                    })
                    ->count();
            }

            return $data;
        });

        $data[0] = $company_data;
        $data[1] = $user_data;
        $data[2] = $client_data;
        $data[3] = $treatment_data;
        $data[4] = $booking_data;
        $data[5] = $prescription_data;
        $data[6] = $sms_data;
        $data[7] = $mal_data;

        return response()->json([
            'data' => $data,
            'message' => 'graph data returned successfully.',
            'status' => '1',
        ]);
    }

    public function boxData(DashboardBoxDataRequest $request)
    {
        return response()->json([
            'data' => [
                'total_company' => Company::whereDoesntHave('users', function ($query) {
                    $query->where('email', '<EMAIL>');
                })
                    ->whereBetween('created_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()])
                    ->count(),
                'total_users' => User::where('email', '!=', '<EMAIL>')
                    ->whereBetween('created_at', [Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()])
                    ->count(),
                'total_clients' => Client::whereBetween('created_at', [
                    Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()
                ])->count(),
                'total_treatment' => ClientTreatment::whereBetween('created_at', [
                    Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()
                ])->count(),
                'total_booking' => CompanyBooking::whereBetween('created_at', [
                    Carbon::parse($request->start_date)->startOfDay(), Carbon::parse($request->end_date)->endOfDay()
                ])->count(),
            ],
            'message' => "data returned successfully.",
            'status' => '1',
        ]);
    }
}
