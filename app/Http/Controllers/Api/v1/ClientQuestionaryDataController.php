<?php

namespace App\Http\Controllers\Api\v1;

use App\AestheticInterest;
use App\Client;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Covid19;
use App\HealthQuestionary;
use App\Questionary;
use App\QuestionaryData;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\IndexClientQuestionaryDataRequest;
use App\Http\Requests\v2\IndexClientQuestionaryAllDataRequest;
use App\Traits\SaveFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ClientQuestionaryDataController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function index(Client $client, IndexClientQuestionaryDataRequest $request)
    {
        $this->authorize('view', [Client::class, $client]);
        // authorize
        $questionaries = $client->questionary_data()
            ->where('modelable_id', $request->questionary_id)
            ->where('modelable_type', $request->questionary_type)
            ->with(["modelable", "files"]);

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            $questionaries = $questionaries->orderBy($orderBy, $orderDirection);
        } else {
            $questionaries = $questionaries->latest();
        }
        if (request()->has('page')) {
            return response()->json(collect([
                'message' => 'Questionaries returned successfully.',
                'status' => '1'
            ])->merge($questionaries->paginate()));
        }

        return response()->json([
            'data' => $questionaries->get(),
            'message' => 'Questionaries returned successfully.',
            'status' => '1'
        ]);
    }

    public function indexAll(Client $client, IndexClientQuestionaryAllDataRequest $request)
    {
        $this->authorize('view', [Client::class, $client]);
        // authorize
        $questionaries = $client->questionary_data()->with(["modelable", 'files']);

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            $questionaries = $questionaries->orderBy($orderBy, $orderDirection);
        } else {
            $questionaries = $questionaries->latest();
        }
        if (request()->has('page')) {
            return response()->json(collect([
                'message' => 'Questionaries returned successfully.',
                'status' => '1'
            ])->merge($questionaries->paginate($request->per_page)));
        }

        return response()->json([
            'data' => $questionaries->get(),
            'message' => 'Questionaries returned successfully.',
            'status' => '1'
        ]);
    }

    public function update(Client $client, QuestionaryData $questionary, Request $request)
    {
        if ($request->hasFile('sign')) {
            $file = $this->saveFile($request->file('sign'), 'clients/' . md5($client->id) . '/questionaries/sign');
            $questionary->sign = $file->filename;
            $questionary->signed_at = now();
            $questionary->signed_by_id = Auth::id();
            $activity = activity()
                ->performedOn($questionary);
            $activity = $activity->by(Auth::user());
            $activity->log("{$questionary->client->first_name} {$questionary->client->last_name} client 's Questionary had be signed by :causer.first_name :causer.last_name");
            $questionary->save();
        }
        return response()->json([
            'message' => __('strings.client_questionaries_signed'),
            'status' => '1',
        ]);
    }

    public function regenPdf(Client $client, QuestionaryData $questionaryData, PDFServiceInterface $pdfService)
    {
        $this->authorize('regenPdf', $questionaryData);

        return DB::transaction(function () use ($client, $questionaryData, $pdfService) {
            activity()->disableLogging();

            $user = Auth::user();

            $filename = $questionaryData->getRawOriginal('pdf');

            $qdData = (isset($questionaryData->formatted_response[0]) ? $questionaryData->formatted_response[0] : null);

            if ($questionaryData->modelable_type == AestheticInterest::class) {
                $aestheticInterest = $questionaryData->modelable;
                $file = $pdfService->client($aestheticInterest->client)->questionary()
                    ->aesthethicInterest(
                        $aestheticInterest?->data_new['aesthetic_interest'] ?? $qdData,
                        $questionaryData->created_at
                    )
                    ->saveFile($user, $filename);
            }

            if ($questionaryData->modelable_type == HealthQuestionary::class) {
                $health_questionary = $questionaryData->modelable;
                $file = $pdfService->client($client)->questionary()
                    ->health(
                        $health_questionary?->data_new ?? $qdData,
                        $questionaryData->created_at
                    )
                    ->saveFile($user, $filename);
            }

            if ($questionaryData->modelable_type == Covid19::class) {
                $covid19 = $questionaryData->modelable;
                $file = $pdfService->client($client)->questionary()
                    ->covid19(
                        $covid19?->data_new ?? $qdData,
                        $questionaryData->created_at
                    )
                    ->saveFile($user, $filename);
            }

            if ($questionaryData->modelable_type == Questionary::class) {
                $questionary = $questionaryData->modelable;
                $file = $pdfService->client($client)->questionary()
                    ->custom($questionary, $questionaryData->formatted_response->toArray(), $questionaryData->created_at)
                    ->saveFile($user, $filename);
            }

            $questionaryData->updated_at = now();

            $questionaryData->save();

            return response()->json([
                'data' => $questionaryData->refresh()->loadMissing(["modelable"]),
                'message' => 'Questionaries returned successfully.',
                'status' => '1'
            ]);
        });
    }
}
