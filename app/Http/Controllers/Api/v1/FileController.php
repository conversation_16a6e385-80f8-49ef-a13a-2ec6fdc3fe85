<?php

namespace App\Http\Controllers\Api\v1;

use App\ClientTreatment;
use App\File;
use App\Http\Controllers\Controller;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    use GetEncryptedFile, SaveFile;

    public function store(Request $request)
    {
        if ($request->hasFile('image')) {
            $path = $this->saveFile($request->file('image'), 'user/public/');
            // $path = $request->file('image')->store('images/public', 's3');

            $clientTreatment = ClientTreatment::first();

            $file = $clientTreatment->file()->create([
                'filename' => \basename($path),
                'url' => Storage::disk('s3')->url($path),
                'user_id' => '1',
                'size' => Storage::disk('s3')->size($path)
            ]);

            return response()->json($file);
        }
        return response()->json('error');
    }

    public function getSignedUrl(Request $request)
    {
        if ($request->has('path')) {
            return response()->json([
                'data' => (string)$this->getS3SignedUrl($request->path),
                'message' => 'Signed url returned successfully',
                'status' => '1'
            ]);
        }
        return response()->json([
            'message' => 'please provide path to sign',
            'status' => '0'
        ]);
    }
}
