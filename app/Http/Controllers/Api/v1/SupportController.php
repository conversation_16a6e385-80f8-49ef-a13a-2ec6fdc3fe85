<?php

namespace App\Http\Controllers\Api\v1;

use App\Notifications\SupportMail;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\StoreSupportRequest;
use App\Traits\SaveFile;
use Illuminate\Support\Facades\Notification;

class SupportController extends Controller
{
    use SaveFile;

    public function store(StoreSupportRequest $request)
    {
        $input = [
            'email' => $request->email,
            'message' => $request->message,
            'subject' => $request->subject
        ];

        $original_filename = null;
        $file = null;
        if ($request->hasFile('file')) {
            $original_filename =  $request->file('file')->getClientOriginalName();
            $file = $this->saveFile($request->file('file'), 'users/' . md5(Auth::id()) . '/support');

            $input['file'] = $file->filename;
        }

        $support = Auth::user()->supports()->create($input);

        if ($file) {
            $support->file()->save($file);
        }

        Notification::route('mail', env('SUPPORT_EMAIL', '<EMAIL>'))
            ->notify(new SupportMail($support, $original_filename));

        return response()->json([
            'message' => __('strings.Support_send_successfully'),
            'status' => '1'
        ]);
    }
}
