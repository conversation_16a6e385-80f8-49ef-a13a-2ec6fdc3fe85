<?php

namespace App\Http\Controllers\Api\v1;

use App\Company;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CompanyUserController extends Controller
{
    public function index(Company $company){
        $this->authorize('viewAny', [Company::class]);

        return response()->json([
            'data' => $company->users,
            'status' => '1',
            'message' => 'Company returned successfully',
        ]);
    }
}
