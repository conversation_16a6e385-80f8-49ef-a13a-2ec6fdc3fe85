<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\ClientMedia;
use App\File;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\CommonIndexRequest;
use App\Http\Requests\v1\StoreClientMediaRequest;
use App\Traits\ApiResponser;
use App\Traits\SaveFile;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ClientMediaController extends Controller
{
    use SaveFile;
    use ApiResponser;
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function index(CommonIndexRequest $request, Client $client)
    {
        $this->authorize('view', [ClientMedia::class, $client]);

        $media = $client->media;
        if (!$media) {
            return response()->json([
                'message' => 'Clients return successfully',
                'status' => '1',
                'data' => [],
            ]);
        }

        if (request()->has('noGroupBy')) {
            $media = $media->files()->orderBy('created_at', 'desc')->cursor();
            return response()->json(collect([
                'message' => 'Clients return successfully',
                'status' => '1',
            ])->merge($this->paginate($media)));
        }

        $media = $media
            ->files()
            ->cursor()
            ->groupBy(function ($item, $key) {
                return Carbon::parse($item->created_at)->format('Y-m-d');
            })->map(function ($item, $key) {
                return [
                    'date' => $key,
                    'data' => $item,
                ];
            })
            ->sortByDesc('date')
            ->values();

        if (request()->has('page')) {
            return response()->json(collect([
                'message' => 'Clients return successfully',
                'status' => '1',
            ])->merge($this->paginate($media)));
        }

        return response()->json([
            'message' => 'Clients return successfully',
            'status' => '1',
            'data' => $media,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function store(StoreClientMediaRequest $request, Client $client)
    {
        $this->authorize('create', [ClientMedia::class, $client]);
        ini_set('max_execution_time', 900);
        DB::transaction(function () use ($request, $client) {
            $media = ClientMedia::updateOrCreate([
                'client_id' => $client->id,
            ]);

            foreach ($request->file('files') as $index => $data) {
                $file = $this->saveFile($data, 'clients/' . md5($client->id) . '/media', null, false, true);
                $media->files()->save($file);
            }
        });

        return response()->json([
            'message' => __('strings.client_media_saved'),
            'status' => '1',
        ]);
    }

    // /**
    //  * Display the specified resource.
    //  *
    //  * @param  \App\Client  $client
    //  * @param  \App\File  $file
    //  * @return \Illuminate\Http\Response
    //  */
    // public function show(Client $client, File $file)
    // {
    //     //
    // }

    // /**
    //  * Update the specified resource in storage.
    //  *
    //  * @param  \Illuminate\Http\Request  $request
    //  * @param  \App\Client  $client
    //  * @param  \App\File  $file
    //  * @return \Illuminate\Http\Response
    //  */
    // public function update(Request $request, Client $client, File $file)
    // {
    //     //
    // }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @param  \App\File  $file
     * @return \Illuminate\Http\Response
     */
    public function destroy(Client $client, File $file)
    {
        $this->authorize('delete', [ClientMedia::class, $client]);

        $media = $client->media()->firstOrFail();
        $file = $media->files()->where('files.id', $file->id)->firstOrFail();

        $file->delete();

        return response()->json([
            'message' => 'media file deleted successfully.',
            'status' => '1',
        ]);
    }

    public function multiDelete(Client $client, Request $request)
    {
        $this->authorize('delete', [ClientMedia::class, $client]);

        $ids = $request->input('ids', []);

        if (count($ids)) {
            try {
                File::destroy($ids);
            } catch (\Throwable $th) {
            }
        }

        return response()->json([
            'message' => 'Media deleted successfully.',
            'status' => '1',
        ]);
    }
}
