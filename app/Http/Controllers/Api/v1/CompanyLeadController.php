<?php

namespace App\Http\Controllers\Api\v1;

use App\Company;
use App\CompanyLead;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\StoreUpdateCompanyLeadRequest;

class CompanyLeadController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreUpdateCompanyLeadRequest $request)
    {
        $this->authorize('create', [CompanyLead::class]);

        $companyLead = CompanyLead::updateOrCreate([
            'company_id' => $request->company_id,
        ], [
            'status' => $request->status,
            'notes' => $request->notes,
        ] + ($request->has('contacted') ? [
            'contacted' => $request->boolean('contacted', false),
        ] : []));

        return response()->json([
            'data' => $companyLead,
            'status' => '1',
            'message' => 'Company Lead saved successfully',
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\CompanyLead  $companyLead
     * @return \Illuminate\Http\Response
     */
    public function show(CompanyLead $companyLead)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\CompanyLead  $companyLead
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CompanyLead $companyLead)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\CompanyLead  $companyLead
     * @return \Illuminate\Http\Response
     */
    public function destroy(CompanyLead $companyLead)
    {
        //
    }
}
