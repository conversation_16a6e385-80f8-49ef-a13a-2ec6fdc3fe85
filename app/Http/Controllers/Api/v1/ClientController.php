<?php

namespace App\Http\Controllers\Api\v1;

use App\Activity;
use App\AestheticInterest;
use App\Client;
use App\ClientAddress;
use App\ClientConsent;
use App\ClientLetterOfConsent;
use App\ClientPrescription;
use App\ClientTreatment;
use App\Company;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\ZIP\ZipServiceInterface;
use App\Covid19;
use App\Exports\ClientExportEXCEL;
use App\Exports\ClientExportPDF;
use App\File;
use App\GeneralNote;
use App\HealthQuestionary;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\ClientAfterCareLogIndexRequest;
use App\Http\Requests\v1\GetLogListRequest;
use App\Http\Requests\v1\StoreClientRequest;
use App\Http\Requests\v1\StorePublicClientRequest;
use App\Http\Requests\v1\UpdateClientRequest;
use App\Jobs\ClientZipDownloadJob;
use App\Jobs\NewClientCreated;
use App\Jobs\RegPortalLogs;
use App\LetterOfConsent;
use App\Mail\SuperUserWhenClientRegister;
use App\Models\VideoCall;
use App\QuestionaryData;
use App\QuestionaryQuestion;
use App\Setting;
use App\Traits\ApiResponser;
use App\Traits\GeneratePDF;
use App\Traits\SaveFile;
use App\User;
use App\UserNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\Facades\FastExcel;
use Symfony\Component\HttpKernel\Exception\HttpException;
use VerumConsilium\Browsershot\Facades\PDF;

class ClientController extends Controller
{
    use SaveFile;
    use ApiResponser;
    use GeneratePDF;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, Client $client)
    {
        if ($client->id != null) {
            $this->authorize('view', [Client::class, $client]);
            $user = Auth::user();

            if ($request->has('download')) {
                dispatch(function () use ($user, $client) {
                    activity()
                        ->performedOn($client)
                        ->by($user)
                        ->log("{$user->first_name} {$user->last_name} exported patient {$client->first_name} {$client->last_name} record.");
                });

                if ($request->download == 'excel') {
                    return Excel::download(
                        new ClientExportEXCEL($client),
                        $client->first_name . ' ' . $client->last_name . '.xlsx'
                    );
                } else {
                    return PDF::loadView('exports.client', ['client' => $client])
                        ->waitUntilNetworkIdle()
                        ->format('A4')
                        ->margins(15, 0, 15, 0)
                        ->download();
                    // return Excel::download(
                    //     new ClientExportPDF($client),
                    //     $client->first_name . " " . $client->last_name . '.pdf',
                    //     \Maatwebsite\Excel\Excel::DOMPDF
                    // );
                }
            } else {
                dispatch(function () use ($user, $client) {
                    activity()
                        ->performedOn($client)
                        ->by($user)
                        ->log("{$user->first_name} {$user->last_name} opened patient {$client->first_name} {$client->last_name} record.");
                });

                return response()->json([
                    'data' => $client->loadExists([
                        'general_notes as general_notes_important_exists' => function ($query) {
                            $query->where('important', true);
                        },
                        'media' => function ($query) {
                            $query->withCount('files');
                        },
                        'general_notes as general_notes_unsigned_exists' => function ($query) {
                            $query->where('signed_at', null);
                        },
                        'treatments as treatments_unsigned_exists' => function ($query) {
                            $query->where('signed_at', null);
                        },
                        'letter_of_consents as letter_of_consents_unsigned_exists' => function ($query) {
                            $query->where('verified_signed_at', null);
                        },
                    ])->loadMissing([
                        'extra_fields',
                        'consent',
                    ]),
                    'message' => 'Client return successfully',
                    'status' => '1',
                ]);
            }
        }

        $user = Auth::user();

        if ($user->user_role == User::ADMIN && $user->company->email == $user->email) {
            $clients = $user->company->clients();
        } else {
            $clients = $user->accesses();
        }

        if ($request->boolean('withTrashed', false)) {
            $clients = $clients;
        } elseif ($request->boolean('onlyTrashed', false)) {
            $clients = $clients->where('deleted_at', '!=', null);
        } else {
            $clients = $clients->where('deleted_at', null);
        }

        if ($request->has('page')) {
            if ($request->has('search')) {
                $clients = $clients->with('addresses')->get();

                $search = Str::lower($request->input('search'));
                $search = preg_replace('/\s\s+/', ' ', $search);
                // $search = explode(" ", $search);

                $clients = $clients->filter(function ($value) use ($search) {
                    return Str::contains(Str::lower($value->first_name), $search) ||
                        Str::contains(Str::lower($value->last_name), $search) ||
                        // Str::contains(Str::lower($value->full_name), $search) ||
                        Str::contains(Str::lower($value->email), $search) ||
                        Str::contains(Str::lower($value->phone_number), $search) ||
                        $value->addresses->filter(function ($address) use ($search) {
                            return Str::contains(Str::lower($address->street_address), $search) ||
                                Str::contains(Str::lower($address->state), $search) ||
                                Str::contains(Str::lower($address->zip_code), $search) ||
                                Str::contains(Str::lower($address->city), $search);
                        })->count() > 0;
                    // ||
                    // $value->letter_of_consents->filter(function ($letter_of_consents) use ($search) {

                    //     return (mb_stristr($letter_of_consents->consent_title, $search) ||
                    //         mb_stristr($letter_of_consents->letter, $search));
                    // })->count() > 0
                    // ||
                    // $value->treatments->filter(function ($treatments) use ($search) {

                    //     return (mb_stristr($treatments->name, $search) ||
                    //         mb_stristr($treatments->description, $search));
                    // })->count() > 0
                });

                $clients = $clients->sortByDesc(function ($i, $k) use ($search) {
                    $props = [
                        // 'full_name' => 10,
                        'first_name' => 5,
                        'last_name' => 5,
                        'email' => 2,
                        'phone_number' => 1,
                    ];
                    // The bigger the weight, the higher the record
                    $weight = 0;
                    // Iterate through search terms
                    // foreach($search as $searchTerm) {
                    // Iterate through properties (address1, address2...)
                    foreach ($props as $prop => $acquire_weight) {
                        // Use strpos instead of %value% (cause php)
                        if (strpos(Str::lower($i->{$prop}), $search) !== false) {
                            $weight += $acquire_weight;
                        } // Increase weight if the search term is found
                    }
                    // }

                    return $weight;
                });
            } else {
                if ($request->has('filter_all')) {
                    if ($user->user_role == User::ADMIN && $user->company->email == $user->email) {
                        $clients = $user->company->clients();
                    }
                }

                if ($request->has('filter_my')) {
                    $clients = $user->accesses();
                }

                if ($request->has('order_by_recent')) {
                    $clients = $clients->latest();
                }

                if ($request->has('my_clients')) {
                    $clients = $clients->where('clients.user_id', $user->id);
                }

                if ($request->has('pending_sign')) {
                    $clients = $clients->where(function ($query) {
                        $query->whereHas('treatments', function ($query) {
                            $query->where('signed_at', null);
                        })->orWhereHas('letter_of_consents', function ($query) {
                            $query->where('verified_signed_at', null);
                        })->orWhereHas('general_notes', function ($query) {
                            $query->where('signed_at', null);
                        });
                    });
                }

                $clients = $clients->get();

                if ($request->has('order_by_az')) {
                    $clients = $clients->sortBy('last_name', SORT_NATURAL | SORT_FLAG_CASE);
                }

                if ($request->has('order_by_za')) {
                    $clients = $clients->sortByDesc('last_name', SORT_NATURAL | SORT_FLAG_CASE);
                }
            }

            return response()->json(
                collect(
                    [
                        'message' => 'Clients return successfully',
                        'status' => '1',
                    ]
                )->merge($this->paginate($clients))
            );
        } else {
            return response()->json(
                [
                    'data' => $clients->get(),
                    'message' => 'Clients return successfully',
                    'status' => '1',
                ]
            );
        }
    }

    public function dispatchClientExportedLog(Client $client, User $user)
    {
        dispatch(function () use ($user, $client) {
            $msg = "{$user->first_name} {$user->last_name} exported patient {$client->first_name} {$client->last_name} record.";
            activity()->performedOn($client)->by($user)->log($msg);
        });
    }

    public function getClientQuestionnaries($client, $id, $type)
    {
        return $client->questionary_data()->where('modelable_type', $type)->latest()->get();
    }

    public function download(Client $client)
    {
        $user = Auth::user();
        $storage_url = substr(Storage::url('/'), 0, -1);
        $this->dispatchClientExportedLog($client, $user);

        $zip_data = collect([]);

        $company = $client->company;
        $is_twelve_hours = Setting::getSetting($company, Setting::IS_TWELVE_HOURS)->value;

        // Client Info
        $zip_data->push([
            'file_content' => Client::downloadPDF($client, $is_twelve_hours),
            'filename' => "{$client->full_name}.pdf",
        ]);

        // Procedures
        foreach ($client->treatments as $index => $treatment) {
            $date = $treatment->date ? Carbon::parse($treatment->date)->format('Y-m-d') : '';
            $name = ($treatment->name || $date) ? "{$treatment->name}-{$date}" : 'Procedure ' . ($index + 1) . "-{$date}";
            $zip_data->push([
                'file_content' => ClientTreatment::downloadPDF($treatment, $is_twelve_hours),
                'filename' => "Procedures/{$name}.pdf",
            ]);
        }

        // Letter of consents
        foreach ($client->letter_of_consents as $index => $letter_of_consent) {
            $date = Carbon::parse($letter_of_consent->created_at)->format('Y-m-d G-i-s');
            $name = "{$letter_of_consent->consent_title}-{$date}";
            $zip_data->push([
                'file_content' => ClientLetterOfConsent::downloadPDF($letter_of_consent, $is_twelve_hours),
                'filename' => "Letter of Consents/{$name}.pdf",
            ]);
        }

        // General Notes
        foreach ($client->general_notes as $note) {
            $date = Carbon::parse($note->created_at)->format('Y-m-d G-i-s');
            $name = "{$note->title}-{$date}";
            $zip_data->push([
                'file_content' => GeneralNote::downloadPDF($note, $is_twelve_hours),
                'filename' => "Notes & Files/{$name}.pdf",
            ]);
        }

        // Bookings
        foreach ($client->bookings as $booking) {
            $date = Carbon::parse($booking->created_at)->format('Y-m-d G-i-s');
            $name = "{$client->full_name}-$date";
            $zip_data->push([
                'file_content' => CompanyBooking::downloadPDF($booking, $is_twelve_hours),
                'filename' => "Bookings/{$name}.pdf",
            ]);
        }

        // Health Questionaries
        foreach ($this->getClientQuestionnaries($client, '1', HealthQuestionary::class) as $index => $questionary) {
            $date = Carbon::parse($questionary->created_at)->format('Y-m-d G-i-s');
            $name = "{$client->full_name}-$date";
            $zip_data->push([
                'url' => $storage_url . $questionary->pdf,
                'filename' => "Health Questionnaires/{$name}.pdf",
            ]);
        }

        // Aesthetic Interests
        foreach ($this->getClientQuestionnaries($client, '1', AestheticInterest::class) as $index => $questionary) {
            $date = Carbon::parse($questionary->created_at)->format('Y-m-d G-i-s');
            $name = "{$client->full_name}-$date";
            $zip_data->push([
                'url' => $storage_url . $questionary->pdf,
                'filename' => "Aesthetic Interests/{$name}.pdf",
            ]);
        }

        // Covid-19 Questionnaires
        foreach ($this->getClientQuestionnaries($client, '1', Covid19::class) as $index => $questionary) {
            $date = Carbon::parse($questionary->created_at)->format('Y-m-d G-i-s');
            $name = "{$client->full_name}-$date";
            $zip_data->push([
                'url' => $storage_url . $questionary->pdf,
                'filename' => "Covid-19 Questionnaires/{$name}.pdf",
            ]);
        }

        // Custom Questionnaires
        foreach ($this->getClientQuestionnaries($client, '1', "App\Questionary") as $index => $questionary) {
            $date = Carbon::parse($questionary->created_at)->format('Y-m-d G-i-s');
            $name = "{$client->full_name}-$date";
            $zip_data->push([
                'url' => $storage_url . $questionary->pdf,
                'filename' => "Custom Questionnaires/{$name}.pdf",
            ]);
        }

        // Send Information / After care
        $zip_data->push([
            'file_content' => AfterCareTreatmentController::generatePDF($client, $is_twelve_hours),
            'filename' => "Sent Information/{$client->full_name}.pdf",
        ]);

        return $this->createZip(['files' => $zip_data->toArray()]);
    }

    public function download2(Client $client, Request $request)
    {
        $this->authorize('download2', $client);

        return DB::transaction(function () use ($request, $client) {

            $cacheKey = 'client_zip_' . $client->id;
            $cache_exists = Cache::get($cacheKey);

            if ($cache_exists === 'working') {
                throw new HttpException(208, __('zip.zip_generating'));
            }

            if ($cache_exists) {
                throw new HttpException(208, __('zip.zip_in_queue'));
            }

            $force = $request->input('force');

            if (!$force) {
                $zip_already_exists = File::where('fileable_type', 'client_zip')->where('fileable_id', $client->id)->first();

                if ($zip_already_exists) {
                    return response()->json([
                        'status' => '0',
                        'data' => $zip_already_exists,
                        'message' => __('zip.zip_already_present'),
                    ], 210);
                }
            }

            Cache::put($cacheKey, 'queue', 86400);
            UserNotification::create([
                'user_id' => Auth::user()->id,
                'title' => __('zip.zip_generating'),
                'description' => __('zip.zip_in_queue'),
                'is_read' => 0
            ]);
            ClientZipDownloadJob::dispatch($client, Auth::user(), app()->getLocale(), false, true);

            return response()->json([
                'status' => '1',
                'message' => __('zip.zip_accepted'),
            ]);
        });
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreClientRequest $request)
    {
        $this->authorize('create', [Client::class]);

        $client = DB::transaction(function () use ($request) {
            $user = Auth::user()->loadMissing(['company.clients', 'company.users']);

            if ($request->has('email')) {
                $count = $user->company->clients
                    ->filter(function ($client) use ($request) {
                        if ($request->email) {
                            return strtolower($client->email) === strtolower($request->email);
                        }
                        return false;
                    })->count();

                if ($count > 0) {
                    throw new HttpException(200, __('strings.Please_enter_unique_email'));
                }
            }


            //CHECKING FOR PERSONAL ID
            if ($request->has('personal_id')) {
                $count = $user->company->clients
                    ->filter(function ($client) use ($request) {
                        if ($client->personal_id) {
                            return strtolower($client->personal_id) === strtolower($request->personal_id);
                        }

                        return false;
                    })->count();

                if ($count > 0) {
                    throw new HttpException(200, __('strings.Please_enter_unique_personal_id'));
                }
            }

            if ($request->has('cpr_id')) {
                $count = $user->company->clients
                    ->filter(function ($client) use ($request) {
                        if ($client->cpr_id) {
                            return strtolower($client->cpr_id) === strtolower($request->cpr_id);
                        }

                        return false;
                    })->count();

                if ($count > 0) {
                    throw new HttpException(200, __('strings.Please_enter_unique_cpr_id'));
                }
            }

            $file = null;
            if ($request->has('profile_picture')) {
                $file = $this->saveFile($request->file('profile_picture'), 'client', null, true);
            }

            $client = Client::create([
                'user_id' => $user->id,
                'company_id' => $user->company->id,
                'profile_picture' => $file ? $file->filename : '',
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'personal_id' => $request->input('personal_id'),
                'cpr_id' => $request->input('cpr_id'),
                'is_personal_id_verified' => $request->input('is_personal_id_verified'),
                'occupation' => $request->input('occupation'),
                'social_security_number' => $request->input('social_security_number', '') ?? '',
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number', '') ?? '',
                'country_code' => $request->input('country_code', '') ?? '',
            ]);

            if ($request->has('extra') && count($request->input('extra', [])) > 0) {
                $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
                    return [
                        'company_client_extra_field_id' => $value['id'],
                        'value' => $value['value'] ?? '',
                    ];
                }));
            }

            if ($file) {
                $client->file()->save($file);
            }

            foreach ($request->input('addressess', []) ?? [] as $index => $addressessInput) {
                $client->addresses()->create([
                    'street_address' => array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '',
                    'zip_code' => array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '',
                    'city' => array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '',
                    'state' => array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '',
                    'country' => array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '',
                ]);
            }

            foreach ($user->company->users as $index => $user_in) {
                $user_in->accesses()->toggle([$client->id]);
            }

            NewClientCreated::dispatch($client, $user->company, false);

            return $client;
        });

        return response()->json([
            'data' => $client,
            'message' => __('strings.Client_created_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function createPublic(Request $request)
    {
        $request->merge([
            'company_id' => Company::decryptId($request->company_id),
        ]);

        $validatedData = Validator::make($request->all(), [
            'company_id' => 'required|exists:companies,id',
            'email' => 'required|email',
        ]);

        $company = Company::findOrFail($request->company_id);

        if ($company->is_read_only) {
            throw new HttpException(403, __('strings.This_Account_is_readonly_please_contact_admin'));
        }

        if (!$company->record_plan) {
            throw new AuthorizationException(__('strings.Please_subscribe_to_a_plan'), 403);
        }

        $value = [];

        if ($request->has('email') && $request->email) {
            $count = $company->clients
                ->filter(function ($client) use ($request) {
                    return strtolower($client->email) === strtolower($request->email);
                })->count();

            if ($count > 0) {
                $value['email'] = __('strings.Please_enter_unique_email');
            }
        }


        if ($request->has('personal_id')) {
            $count = $company->clients
                ->filter(function ($client) use ($request) {
                    if ($client->personal_id) {
                        return strtolower($client->personal_id) === strtolower($request->personal_id);
                    }

                    return false;
                })->count();

            if ($count > 0) {
                $value['personal_id'] = __('strings.Please_enter_unique_personal_id');
            }
        }

        if ($request->has('cpr_id')) {
            $count = $company->clients
                ->filter(function ($client) use ($request) {
                    if ($client->cpr_id) {
                        return strtolower($client->cpr_id) === strtolower($request->cpr_id);
                    }

                    return false;
                })->count();

            if ($count > 0) {
                $value['cpr_id'] = __('strings.Please_enter_unique_cpr_id');
            }
        }

        return response()->json([
            'data' => $value,
            'status' => '1',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storePublic(StorePublicClientRequest $request, PDFServiceInterface $pdfService, ZipServiceInterface $zipService)
    {
        $client = DB::transaction(function () use ($request, $pdfService, $zipService) {
            activity()->disableLogging();

            $company = Company::findOrFail($request->company_id);

            if ($company->is_read_only) {
                throw new HttpException(403, __('strings.This_Account_is_readonly_please_contact_admin'));
            }

            if (!$company->record_plan) {
                throw new AuthorizationException(__('strings.Please_subscribe_to_a_plan'), 403);
            }

            if ($request->has('email')) {
                //CHECKING FOR EMAIL
                $count = $company->clients->filter(function ($client) use ($request) {
                    if ($client->email) {
                        return strtolower($client->email) === strtolower($request->email);
                    }
                    return false;
                })->count();

                if ($count > 0) {
                    throw new Exception(__('strings.Please_enter_unique_email'), 500);
                }
            }


            if ($request->has('personal_id')) {
                //CHECKING FOR PERSONAL ID
                $count = $company->clients->filter(function ($client) use ($request) {
                    if ($client->personal_id) {
                        return strtolower($client->personal_id) === strtolower($request->personal_id);
                    }

                    return false;
                })->count();

                if ($count > 0) {
                    throw new Exception(__('strings.Please_enter_unique_personal_id'), 500);
                }
            }

            if ($request->has('cpr_id')) {
                //CHECKING FOR PERSONAL ID
                $count = $company->clients->filter(function ($client) use ($request) {
                    if ($client->cpr_id) {
                        return strtolower($client->cpr_id) === strtolower($request->cpr_id);
                    }

                    return false;
                })->count();

                if ($count > 0) {
                    throw new Exception(__('strings.Please_enter_unique_cpr_id'), 500);
                }
            }

            $user = $company->users()->first();

            $client = Client::create([
                'user_id' => $request->input('user_id'),
                'company_id' => $company->id,
                'profile_picture' => '',
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'personal_id' => $request->input('personal_id'),
                'cpr_id' => $request->input('cpr_id'),
                'is_personal_id_verified' => $request->input('is_personal_id_verified'),
                'occupation' => $request->input('occupation'),
                'social_security_number' => $request->input('social_security_number', '') ?? '',
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number', '') ?? '',
                'country_code' => $request->input('country_code', '') ?? '',
            ]);

            if ($request->has('extra') && count($request->input('extra', [])) > 0) {
                $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
                    return [
                        'company_client_extra_field_id' => $value['id'],
                        'value' => $value['value'] ?? '',
                    ];
                })->values()->all());
            }

            if ($request->has('profile_picture')) {
                $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), $user, true);
                $client->profile_picture = $file->filename;
                $client->save();
            }

            NewClientCreated::dispatch($client, $company);

            foreach ($request->input('addressess', []) ?? [] as $index => $addressessInput) {
                $client->addresses()->create([
                    'client_id' => $client->id,
                    'street_address' => array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '',
                    'zip_code' => array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '',
                    'city' => array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '',
                    'state' => array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '',
                    'country' => array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '',
                ]);
            }
            $aestheticInterest = null;
            $created_at = now();
            if ($request->has('aesthetic_interest')) {
                $inputs = $request->all();
                if ($request->hasFile('aesthetic_interest.5.image')) {
                    $file = $this->saveFile($request->file('aesthetic_interest.5.image'), 'aesthetic_interest', $user);
                    $inputs['aesthetic_interest'][5]['image'] = $file->url;
                }

                $aestheticInterest = AestheticInterest::updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'data_new' => json_encode(['aesthetic_interest' => $inputs['aesthetic_interest']]),
                ]);

                $data = ['datas' => $inputs['aesthetic_interest'], 'client' => $client, 'created_at' => $created_at];

                // $file = $this->generateStoreQuestionary($data, 'exports.client_aesthethic_interest_questionary', 'clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->aesthethicInterest($inputs['aesthetic_interest'], $created_at)->saveFile($user, $filename);

                QuestionaryData::updateOrcreate([
                    'client_id' => $client->id,
                    'modelable_type' => AestheticInterest::class,
                    'modelable_id' => $aestheticInterest->id,
                ], [
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                ]);

                if ($request->hasFile('aesthetic_interest.5.image')) {
                    $aestheticInterest->file()->save($file);
                }
            }

            $healthQuestionary = null;
            if ($request->has('health_questions')) {
                $healthQuestionary = HealthQuestionary::updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'data_new' => json_encode($request->input('health_questions')),
                ]);

                $data = ['datas' => $request->input('health_questions'), 'client' => $client, 'created_at' => $created_at];

                // $file = $this->generateStoreQuestionary($data, 'exports.client_health_questionary', 'clients/' . md5($client->id) . '/health_questionary/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/health_questionary/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->health($request->input('health_questions'), $created_at)->saveFile($user, $filename);

                QuestionaryData::updateOrcreate([
                    'client_id' => $client->id,
                    'modelable_type' => HealthQuestionary::class,
                    'modelable_id' => $healthQuestionary->id,
                ], [
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                ]);
            }

            $covid19 = null;
            if ($request->has('covid19')) {
                $covid19 = Covid19::updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'data' => json_encode($request->input('covid19')),
                ]);

                $data = ['datas' => $request->input('covid19'), 'client' => $client, 'created_at' => $created_at];

                // $file = $this->generateStoreQuestionary($data, 'exports.client_covid19_questionary', 'clients/' . md5($client->id) . '/covid19_questionary/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/covid19_questionary/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->covid19($request->input('covid19'), $created_at)->saveFile($user, $filename);

                $questionaryData = QuestionaryData::updateOrcreate([
                    'client_id' => $client->id,
                    'modelable_type' => Covid19::class,
                    'modelable_id' => $covid19->id,
                ], [
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                ]);
            }

            $letterOfConsents = [];
            if ($request->has('letter_of_consents')) {
                foreach ($request->input('letter_of_consents', []) as $index => $letter_of_consent) {
                    $file = null;
                    $file1 = null;
                    $letter_of_consent['client_id'] = $client->id;

                    if ($request->hasFile("letter_of_consents.$index.signature")) {
                        $file = $this->saveFile($request->file("letter_of_consents.$index.signature"), 'clients/' . md5($client->id) . '/letter_of_consents', $user);

                        $letter_of_consent['signature'] = $file->filename;
                    }
                    if ($request->has("letter_of_consents.$index.signed_file")) {
                        $file1 = $this->saveFile($request->file("letter_of_consents.$index.signed_file"), 'clients/' . md5($client->id) . '/letter_of_consents', $user);

                        $letter_of_consent['signed_file'] = $file1->filename;
                    }

                    if ($request->input("letter_of_consents.$index.consent_id")) {
                        $consent = LetterOfConsent::find($letter_of_consent['consent_id']);
                        if ($consent) {
                            $letter_of_consent['consent_title'] = $consent->consent_title;
                            $letter_of_consent['letter'] = $consent->letter_html ?? $consent->letter ?? '';
                            $letter_of_consent['version'] = $consent->version;
                        } else {
                            $letter_of_consent['consent_title'] = '';
                            $letter_of_consent['letter'] = '';
                            $letter_of_consent['version'] = '0';
                        }
                    } else {
                        $letter_of_consent['consent_title'] = '';
                        $letter_of_consent['letter'] = '';
                        $letter_of_consent['version'] = '0';
                    }

                    $letter_of_consent['is_bad_allergic_shock'] = 'no';

                    $clientLetterOfConsent = ClientLetterOfConsent::create($letter_of_consent);

                    $letterOfConsents[$index] = $clientLetterOfConsent;

                    if ($file) {
                        $clientLetterOfConsent->files()->save($file);
                    }
                    if ($file1) {
                        $clientLetterOfConsent->files()->save($file1);
                    }
                }
            }

            if ($request->has('questionary')) {
                $questionaries = $company->questionaries()->active()->get();

                foreach ($questionaries as $questionaryIndex => $questionary) {
                    $data = $request->input("questionary.$questionaryIndex.data");

                    $questions = [];
                    $questionaryData = $questionary->datas()->create([
                        'client_id' => $client->id,
                        'pdf' => "",
                        'response' => ""
                    ]);

                    $zip_data = collect();

                    foreach ($questionary->questions as $index => $question) {
                        array_push($questions, (object) [
                            'question' => $question->question,
                            'type' => $question->type,
                            'properties' => $question->properties,
                            'options' => $question->options,
                        ]);

                        if ($question->type == QuestionaryQuestion::IMAGE) {
                            $file = $this->saveFile($request->file("questionary.$questionaryIndex.data.$index"), 'questionary/uploads', $user);
                            $data[$index] = $file->filename;
                        }
                        if ($question->type == QuestionaryQuestion::FILE_UPLOAD) {
                            $uploaded_files = $request->file("questionary.$questionaryIndex.data.$index");
                            if ($uploaded_files) {
                                $files = [];
                                foreach ($uploaded_files['files'] as $i => $uploaded_file) {
                                    $file = $this->saveFile($uploaded_file, "questionary_files");
                                    $questionaryData->files()->save($file);
                                    array_push($files, (object) ['file_id' => $file->id, 'file_name' => $request->input("questionary.$questionaryIndex.data.$index.file_names.$i") ?? '']);

                                    $zip_data->push([
                                        'path' => $file->filename,
                                        'zip_path' => "Files/Q" . ($index + 1) . "/" . $request->input("questionary.$questionaryIndex.data.$index.file_names.$i") ?? '',
                                        "delete" => false,
                                    ]);
                                }
                                $data[$index] = (object) ['files' => $files];
                            }
                        }
                    }

                    $fileData = ['questionary' => $questionary, 'data' => $data, 'client' => $client, 'created_at' => $created_at];

                    // $file = $this->generateStoreQuestionary($fileData, 'exports.client_questionaries', 'clients/' . md5($client->id) . '/questionary/pdf', $user);
                    $filename = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, 'pdf');
                    $file = $pdfService->client($client)->questionary()->custom($questionary, $data, $created_at, $questions)->saveFile($user, $filename);

                    if ($zip_data->count()) {
                        $zip_path = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, "zip");

                        $zip_data->push([
                            'path' => $file->filename,
                            'zip_path' => "{$questionary->title}.pdf",
                            "delete" => false,
                        ]);

                        // Generate and store zip
                        $file = $zipService->make($zip_data)->saveFile($user, $zip_path);
                    }


                    $questionaryData->pdf = $file->filename;
                    $questionaryData->response = collect($data)->values();
                    $questionaryData->questions = collect($questions)->values();
                    $questionaryData->save();
                }
            }

            if ($request->boolean('verify', false)) {
                $client_fields = ClientConsentController::getClientFields($client);

                $client->consent()->updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'fields' => json_encode($client_fields),
                    'message' => __('strings.consent_body', [
                        'company_name' => $client->company->company_name,
                        'fields' => implode('', collect($client_fields[app()->getLocale()])->map(function ($data) {
                            return "•    $data<br>";
                        })->values()->all()),
                    ]),
                    'verified_at' => now(),
                ]);

                // activity()->enableLogging();
                // $activity = activity()->performedOn($client);

                // if ($request->has('user_id')) {
                //     $activity = $activity->by(User::find($request->user_id));
                //     $activity->log($client->first_name . " " . $client->last_name . "'s consent has been created by :causer.first_name :causer.last_name from registration portal");
                // } else {
                //     $activity = $activity->by($company);
                //     $activity->log($client->first_name . " " . $client->last_name . "'s consent has been created from registration portal");
                // }
                // activity()->disableLogging();
            }

            RegPortalLogs::dispatch(
                $client,
                $company,
                $request->input('user_id'),
                $aestheticInterest,
                $healthQuestionary,
                $covid19,
                $letterOfConsents
            );

            $hasSendMessageToSuperUser = $company->settings()->where('key', 'SUPER_USER_MAIL_WHEN_CLIENT_REGISTER')->where('value', 1)->exists();
            if (!$hasSendMessageToSuperUser) {
                return $client;
            }

            try {
                $LANGUAGE = Setting::getSetting($user->company, Setting::LANGUAGE)->value;
                Mail::to($company->email)->locale($LANGUAGE ?? app()->getLocale())->send(new SuperUserWhenClientRegister($client));
            } catch (\Throwable $th) {
            }

            return $client;
        });

        return response()->json([
            'data' => [
                'id' => $client->id,
            ],
            'message' => __('strings.Client_created_successfully'),
            'status' => '1',
        ]);
    }


    public function getCommonLogsQuery(Client $client, GetLogListRequest $request)
    {
        return Activity::where(function ($query) use ($client, $request) {
            if ($request->missing('filter')) {
                $query = $query->where(function ($query) use ($client) {
                    $query->where('subject_type', Client::class)->where('subject_id', $client->getKey());
                });

                if ($client->aesthetic_insterests->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', AestheticInterest::class)->where('subject_id', $client->aesthetic_insterests[0]->getKey());
                    });
                }

                if ($client->covid19s->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', Covid19::class)->where('subject_id', $client->covid19s[0]->getKey());
                    });
                }

                if ($client->consent) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', ClientConsent::class)->where('subject_id', $client->consent->id);
                    });
                }

                if ($client->health_questionaries->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', HealthQuestionary::class)->where('subject_id', $client->health_questionaries[0]->getKey());
                    });
                }

                if ($client->treatments->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', ClientTreatment::class)->whereIn('subject_id', $client->treatments()->withTrashed()->pluck('id')->all());
                    });
                }

                if ($client->letter_of_consents->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', ClientLetterOfConsent::class)->whereIn('subject_id', $client->letter_of_consents()->withTrashed()->pluck('id')->all());
                    });
                }

                if ($client->general_notes->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', GeneralNote::class)->whereIn('subject_id', $client->general_notes()->withTrashed()->pluck('id')->all());
                    });
                }

                if ($client->addresses->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', ClientAddress::class)->whereIn('subject_id', $client->addresses->pluck('id')->all());
                    });
                }

                if ($client->questionary_data->count()) {
                    $query = $query->orWhere(function ($query) use ($client) {
                        $query->where('subject_type', QuestionaryData::class)->whereIn('subject_id', $client->questionary_data->pluck('id')->all());
                    });
                }

                $query = $query->orWhere(function ($query) use ($client) {
                    $query
                        ->where('subject_type', ClientPrescription::class)
                        ->whereIn('subject_id', ClientPrescription::withTrashed()->where('client_id', $client->id)->get()->pluck('id')->all());
                });

                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', CompanyBooking::class)->whereIn('subject_id', CompanyBooking::where('client_id', $client->id)->get()->pluck('id')->all());
                });

                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', CompanyBookingClient::class)->whereIn('subject_id', CompanyBookingClient::where('client_id', $client->id)->get()->pluck('id')->all());
                });

                $query = $query->orWhere(function ($query) use ($client) {
                    $query
                        ->where('subject_type', VideoCall::class)
                        ->whereIn('subject_id', VideoCall::where('company_id', $client->company_id)
                            ->whereHas('other_members', function ($query) use ($client) {
                                $query->where('user_id', $client->id);
                            })
                            ->get()->pluck('id')->all());
                });
            } else {
                switch ($request->input('filter')) {
                    case 'general_note':
                        $query = $query->where(function ($query) use ($client) {
                            $query->where('subject_type', GeneralNote::class)->whereIn('subject_id', $client->general_notes()->withTrashed()->pluck('id')->all());
                        });
                        break;
                    case 'loc':
                        $query = $query->where(function ($query) use ($client) {
                            $query->where('subject_type', ClientLetterOfConsent::class)->whereIn('subject_id', $client->letter_of_consents()->withTrashed()->pluck('id')->all());
                        });
                        break;
                    case 'procedure':
                        $query = $query->where(function ($query) use ($client) {
                            $query->where('subject_type', ClientTreatment::class)->whereIn('subject_id', $client->treatments()->withTrashed()->pluck('id')->all());
                        });
                        break;
                    case 'prescription':

                        $query = $query->where('subject_type', ClientPrescription::class)->whereIn('subject_id', ClientPrescription::withTrashed()->where('client_id', $client->id)->get()->pluck('id')->all());
                        break;

                    case 'video_call':
                        $query = $query->where(function ($query) use ($client) {
                            $query
                                ->where('subject_type', VideoCall::class)
                                ->whereIn('subject_id', VideoCall::where('company_id', $client->company_id)
                                    ->whereHas('other_members', function ($query) use ($client) {
                                        $query->where('user_id', $client->id);
                                    })
                                    ->get()->pluck('id')->all());
                        });
                        break;
                    case 'booking':
                        $query = $query->where(function ($query) use ($client) {
                            $query->where('subject_type', CompanyBooking::class)->whereIn('subject_id', CompanyBooking::where('client_id', $client->id)->get()->pluck('id')->all());
                        });

                        $query = $query->orWhere(function ($query) use ($client) {
                            $query->where('subject_type', CompanyBookingClient::class)->whereIn('subject_id', CompanyBookingClient::where('client_id', $client->id)->get()->pluck('id')->all());
                        });

                        break;

                    default:
                        # code...
                        break;
                }
            }
        });
    }

    public function logs(Client $client, GetLogListRequest $request)
    {
        $this->authorize('logs', [Client::class, $client]);

        $client_prescriptions = ClientPrescription::where('client_id', $client->id)
            ->doesntHave('logs')
            ->whereNotNull('signed_at')
            ->get();

        foreach ($client_prescriptions as $client_prescription) {
            if (!$client_prescription->sign_by_id) {
                continue;
            }
            if (!$client_prescription->signed_by) {
                continue;
            }

            $msg = "{$client->first_name} {$client->last_name} client's Prescription " . $client_prescription->title . " signed by {$client_prescription->signed_by->first_name} {$client_prescription->signed_by->last_name}";
            $activity = activity()
                ->performedOn($client_prescription)
                ->by($client_prescription->signed_by)
                ->log($msg);
            $activity->created_at = $client_prescription->signed_at;
            $activity->save();
        }

        $activities = $this->getCommonLogsQuery($client, $request);

        $activities = $activities->with('sms');

        if (request()->has('orderBy')) {
            $activities = $activities->orderBy(request()->input('orderBy'), request()->input('orderDirection', 'asc'));
        } else {
            $activities = $activities->latest();
        }

        return response()->json(collect([
            'message' => 'Client Logs returned successfully.',
            'status' => '1',
        ])->merge($activities->paginate()));
    }



    public function logsDownload(Client $client, GetLogListRequest $request)
    {
        $this->authorize('logs', [Client::class, $client]);

        $logs = $this->getCommonLogsQuery($client, $request)->latest()->get();

        // return PDF::loadView('exports.logs', ['client' => $client, 'logs' => $logs])
        //     ->waitUntilNetworkIdle()
        //     ->format('A4')
        //     ->margins(15, 0, 15, 0)
        //     ->download();
        return self::downloadFromView('exports.logs', ['client' => $client, 'logs' => $logs]);
    }


    public function clientAfterCareLogs(Client $client, ClientAfterCareLogIndexRequest $request)
    {
        $this->authorize('logs', [Client::class, $client]);

        $isPaginated = false;
        $activities = Activity::where(function ($query) use ($client) {
            $query = $query->where(function ($query) use ($client) {
                $query->where('subject_type', Client::class)->where('subject_id', $client->getKey());
            })
                ->inLog('aftercare_treatment_mail', 'aftercare_sms');
        })->with(["user" => fn($query) => $query->without('company'), "sms", 'video_call.members']);
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if ($orderBy == 'description') {
                if (!$isPaginated) {
                    $activities = $activities->get();
                    $isPaginated = true;
                }
                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $activities = $activities->sortBy($orderBy, SORT_NATURAL | SORT_FLAG_CASE, $isDescOrder);
                // Fix: sorting
                // if ($activities->contains($orderBy, null)) {
                //     $activities = $activities->sort(function ($prev, $val) use ($orderBy) {
                //         if ($prev["$orderBy"] == null || $prev["$orderBy"] == "") {
                //             return 1;
                //         }
                //         if ($val["$orderBy"] == null || $val["$orderBy"] == "") {
                //             return -1;
                //         }
                //         return 0;
                //     });
                // }
            } else {
                $activities = $activities->orderBy($orderBy, $orderDirection);
            }
        } else {
            $activities = $activities->latest();
        }

        if ($request->has('search')) {
            if (!$isPaginated) {
                $activities = $activities->get();
                $isPaginated = true;
            }

            $search = $request->search;
            $activities = $activities->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->description), Str::lower($search));
            });
        }

        // $activities = $activities->paginate();

        return response()->json(collect([
            'message' => 'Client Logs returned successfully.',
            'status' => '1',
        ])->merge($isPaginated ? $this->paginate($activities) : $activities->paginate($request->per_page ?? 15)));
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function show(Client $client)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function edit(Client $client)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateClientRequest $request, Client $client)
    {
        $this->authorize('update', [Client::class, $client]);

        $user = Auth::user();

        if ($request->hasFile('profile_picture')) {
            $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), null, true);

            try {
                if ($client->file) {
                    $client->file->delete();
                }
            } catch (\Throwable $th) {
                //throw $th;
            }
            $client->file()->save($file);
            $client->profile_picture = $file->filename;
        }

        if ($client->first_name != $request->first_name || $client->last_name != $request->last_name) {
            $clientFullName = $client->first_name . ' ' . $client->last_name;
            $requestFullName = $request->first_name . ' ' . $request->last_name;
            $causer = ' :causer.first_name :causer.last_name ';
            $message = "$clientFullName's name has been changed to $requestFullName by $causer";
            activity()->performedOn($client)->by($user)->log($message);
        }

        if ($request->has('first_name')) {
            $client->first_name = $request->input('first_name');
        }

        if ($request->has('last_name')) {
            $client->last_name = $request->input('last_name');
        }

        $client->social_security_number = $request->input('social_security_number', '') ?? '';

        if ($request->has('email')) {
            // $count = $user->company->clients
            //     ->where('email', $request->input('email'))
            //     ->where('email', '!=', $client->email)
            //     ->count();

            $count = $user->company->clients
                ->filter(function ($filter_client) use ($request, $client) {
                    return strtolower($filter_client->email) === strtolower($request->email) && strtolower($filter_client->email) !== strtolower($client->email);
                })->count();

            if ($count > 0) {
                return response()->json([
                    'message' => __('strings.Please_enter_unique_email'),
                    'status' => '0',
                ]);
            }
            $client->email = $request->input('email');
        }

        if ($request->has('phone_number')) {
            $client->phone_number = $request->input('phone_number', '') ?? '';
        }
        if ($request->has('country_code')) {
            $client->country_code = $request->input('country_code', '') ?? '';
        }

        if ($request->has('occupation')) {
            $client->occupation = $request->input('occupation', '') ?? '';
        }

        if ($request->has('important_note')) {
            $client->important_note = $request->input('important_note', '') ?? '';
        }

        if ($request->has('personal_id')) {
            $count = $user->company->clients()->cursor()
                ->filter(function ($filter_client) use ($request, $client) {
                    if ($filter_client->personal_id) {
                        return strtolower($filter_client->personal_id) === strtolower($request->personal_id) && strtolower($filter_client->id) !== strtolower($client->id);
                    }

                    return false;
                })->count();

            if ($count > 0) {
                return response()->json([
                    'message' => __('strings.Please_enter_unique_personal_id'),
                    'status' => '0',
                ]);
            }

            $client->personal_id = $request->input('personal_id', '') ?? '';
            $client->is_personal_id_verified = false;
        }

        if ($request->has('cpr_id')) {
            $count = $user->company->clients()->cursor()
                ->filter(function ($filter_client) use ($request, $client) {
                    if ($filter_client->cpr_id) {
                        return strtolower($filter_client->cpr_id) === strtolower($request->cpr_id) && strtolower($filter_client->id) !== strtolower($client->id);
                    }

                    return false;
                })->count();

            if ($count > 0) {
                return response()->json([
                    'message' => __('strings.Please_enter_unique_cpr_id'),
                    'status' => '0',
                ]);
            }

            $client->cpr_id = $request->input('cpr_id', '') ?? '';
        }

        $company = $client->company;
        $is_bank_id_enabled = Setting::getSetting($company, Setting::BANK_ID_VERIFICATION_ENABLED)->value;

        if ($is_bank_id_enabled && $request->has('is_personal_id_verified')) {
            $client->is_personal_id_verified = $request->input('is_personal_id_verified');
        }

        if ($request->has('extra') && count($request->input('extra', [])) > 0) {
            $extra_fields = $client->extra_fields;
            foreach ($request->extra as $index => $field) {
                if ($data = $extra_fields->where('company_client_extra_field_id', $field['id'])->first()) {
                    $data->value = $field['value'] ?? '';
                    $data->save();
                } else {
                    $data = $client->extra_fields()->create([
                        'value' => $field['value'] ?? '',
                        'company_client_extra_field_id' => $field['id'],
                    ]);
                }
            }
        }

        if ($client->isDirty()) {
            $client->save();
        }

        if ($request->has('addressess')) {
            if (count($request->input('addressess', []) ?? []) < $client->addresses->count()) {
                $client_addresses = $client->addresses
                    ->take($client->addresses->count() - count($request->input('addressess', []) ?? []));

                foreach ($client_addresses as $addresses) {
                    $addresses->delete();
                }
            }

            $client = $client->fresh();

            foreach ($request->input('addressess', []) ?? [] as $index => $addressessInput) {
                if (($index + 1) <= $client->addresses->count()) {
                    $client_addresses = $client->addresses[$index];

                    $client_addresses->street_address = array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '';
                    $client_addresses->zip_code = array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '';
                    $client_addresses->city = array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '';
                    $client_addresses->state = array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '';
                    $client_addresses->country = array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '';
                    $client_addresses->save();
                } else {
                    $client->addresses()->create([
                        'street_address' => array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '',
                        'zip_code' => array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '',
                        'city' => array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '',
                        'state' => array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '',
                        'country' => array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '',
                    ]);
                }
            }
        }

        return response()->json([
            'message' => __('strings.Client_updated_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function destroy(Client $client)
    {
        $this->authorize('delete', [$client]);

        $client->deleted_at = now();
        $client->save();
        // $client->delete();

        return response()->json([
            'message' => __('strings.client_inactivated_success'),
            'status' => '1',
        ]);
    }

    public function delete(Client $client)
    {
        $this->authorize('delete', [$client]);

        $client->forceDelete();

        return response()->json([
            'message' => __('strings.client_inactivated_success'),
            'status' => '1',
        ]);
    }

    /**
     * Restore the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function restore(Client $client)
    {
        $this->authorize('delete', [$client]);

        $client->deleted_at = null;
        $client->save();
        // $client->delete();

        return response()->json([
            'message' => __('strings.client_activated_success'),
            'status' => '1',
        ]);
    }

    public function exportExcel()
    {
        $this->authorize('exportExcel', Auth::user());

        function clientsGenerator()
        {
            foreach (
                Client::with('company:id,first_name,last_name')->without([
                    'addresses',
                    'letter_of_consents',
                    'general_notes',
                    'treatments',
                    'health_questionaries',
                    'general_questions',
                    'aesthetic_insterests',
                    'covid19s',
                ])->cursor() as $client
            ) {
                yield $client;
            }
        }

        return (new FastExcel(clientsGenerator()))->export('clients.xlsx');
    }
}
