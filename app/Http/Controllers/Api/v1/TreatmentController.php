<?php

namespace App\Http\Controllers\Api\v1;

use App\ClientTreatmentDetail;
use App\Company;
use App\Treatment;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\v1\TreatmentRequest;
use App\Traits\ApiResponser;
use App\Traits\ColorGenerator;
use Exception;

class TreatmentController extends Controller
{
    use ColorGenerator, ApiResponser;

    public function index(Request $request)
    {
        $treatments = Auth::user()->company->treatments();

        if ($request->input('page')) {
            if ($request->has('order_by_recent')) {
                $treatments = $treatments->latest();
            }

            if ($request->has('filter')) {
                if ($request->input('filter') == "all") {
                    $treatments = $treatments->withTrashed();
                } else if ($request->input('filter') == "inactive") {
                    $treatments = $treatments->onlyTrashed();
                }
            }

            $treatments = $treatments->get();

            if ($request->has('order_by_az')) {
                $treatments = $treatments->sortBy('name');
            }

            if ($request->has('order_by_za')) {
                $treatments = $treatments->sortByDesc('name');
            }

            return response()->json(
                collect(
                    [
                        'message' => 'Treatments returned successfully',
                        'status' => '1'
                    ]
                )->merge($this->paginate($treatments))
            );
        } else {
            return response()->json(
                [
                    'data' => $treatments->latest()->get(),
                    'message' => 'Treatments returned successfully',
                    'status' => '1'
                ]
            );
        }
    }

    public function store(TreatmentRequest $request)
    {
        $this->authorize('create', [Treatment::class]);

        $company = Auth::user()->company;

        if (!$company) {
            return response()->json([
                'message' => __('strings.Invalid_company'),
                'status' => '0'
            ]);
        }

        $input = $request->validated();

        $input['company_id'] = $company->id;
        $input['unit'] = $request->input('unit', $company->unit ?? 'usd');
        $input['color'] = $this->generate();

        Treatment::create($input);

        return response()->json([
            'message' => __('strings.Treatment_created_successfully'),
            'status' => '1'
        ]);
    }

    public function update(TreatmentRequest $request, Treatment $treatment)
    {
        $this->authorize('update', [Treatment::class, $treatment]);

        if ($request->has('name')) {
            $treatment->name = $request->input('name');
        }

        if ($request->has('description')) {
            $treatment->description = $request->input('description', "");
        }

        if ($request->has('cost')) {
            $treatment->cost = $request->input('cost', 0);
        }

        if ($request->has('unit')) {
            $treatment->unit = $request->input('unit');
        }

        if ($request->has('notes')) {
            $treatment->notes = $request->input('notes');
        }
        if ($treatment->isDirty()) {
            $treatment->is_changed = 1;
            $treatment->save();
        }

        return response()->json([
            'message' => __('strings.Treatment_Updated_successfully'),
            'status' => '1'
        ]);
    }

    public function forceDelete(Treatment $treatment)
    {
        $this->authorize('delete', [Treatment::class, $treatment]);

        $detail = ClientTreatmentDetail::where('treatment_id', $treatment->id)->first();

        if ($detail) {
            throw new Exception(__('strings.cannot_delete_template'));
        }

        if ($treatment->forceDelete()) {
            return response()->json([
                'message' => __('strings.Treatment_deleted_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Treatment_removal_failed'),
                'status' => '0'
            ]);
        }
    }
    public function delete(Treatment $treatment)
    {
        $this->authorize('delete', [Treatment::class, $treatment]);

        if ($treatment->delete()) {
            return response()->json([
                'message' => __('strings.Treatment_removed_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.Treatment_removal_failed'),
                'status' => '0'
            ]);
        }
    }

    public function restore()
    {

        $treatment = Treatment::withTrashed()->find(request('treatment_id'));
        $this->authorize('restore', [Treatment::class, $treatment]);
        if ($treatment) {
            if ($treatment->restore()) {
                return response()->json([
                    'message' => __('strings.Treatment_restored_successfully'),
                    'status' => '1'
                ]);
            } else {
                return response()->json([
                    'message' => __('strings.Treatment_restoration_failed'),
                    'status' => '0'
                ]);
            }
        } else {
            return response()->json([
                'message' => __('strings.Does_not_exists_treatment'),
                'status' => '0'
            ]);
        }
    }
}
