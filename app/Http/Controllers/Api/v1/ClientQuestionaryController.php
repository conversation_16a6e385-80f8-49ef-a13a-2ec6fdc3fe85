<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\ClientQuestionaryIndexRequest;
use App\Questionary;
use App\QuestionaryData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientQuestionaryController extends Controller
{
    //
    public function index(Client $client, ClientQuestionaryIndexRequest $request)
    {
        $this->authorize('view', [Client::class, $client]);

        $user = Auth::user();

        $questionaries = $user->company
            ->questionaries()
            ->withCount(['datas' => function ($query) use ($client) {
                $query->where('questionary_data.client_id', $client->id);
            }])
            ->with(['data' => function ($query) use ($client) {
                $query->where('questionary_data.client_id', $client->id)->latest();
            }])
            ->where(function ($query) use ($client) {
                $query->where(function ($query) use ($client) {
                    $query
                        ->whereHas('datas', function ($query) use ($client) {
                            $query->where('questionary_data.client_id', $client->id);
                        });
                })->orWhere(function ($query) {
                    $query->withoutTrashed();
                });
            })->withTrashed();

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            $questionaries = $questionaries->orderBy($orderBy, $orderDirection);
        }
        if (request()->has('page') || request()->has('per_page')) {
            return response()->json(collect([
                'message' => 'Questionaries returned successfully.',
                'status' => '1'
            ])->merge($questionaries->paginate(request()->input('per_page'))));
        }

        return response()->json([
            'data' => $questionaries->get(),
            'message' => 'Questionaries returned successfully.',
            'status' => '1'
        ]);
    }

    public function show(Client $client, $type)
    {
        $this->authorize('view', [Client::class, $client]);
        $questionary = null;

        if($type === 'health_questionary') {
            $questionary = $client->health_questionaries()->get();
        } else if ($type === 'aesthetic_insterest') {
            $questionary = $client->aesthetic_insterests()->get();
        } else if ($type === 'covid19') {
            $questionary = $client->covid19s()->get();
        }

        return response()->json([
            'data' => $questionary,
            'message' => 'Questionary returned successfully.',
            'status' => '1'
        ]);
    }

    public function download(Client $client, Questionary $questionary, QuestionaryData $questionaryData)
    {
    }
}
