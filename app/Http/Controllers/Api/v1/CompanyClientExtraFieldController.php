<?php

namespace App\Http\Controllers\Api\v1;

use App\Company;
use App\CompanyClientExtraField;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\StoreCompanyClientExtraFieldRequest;
use App\Http\Requests\v1\UpdateCompanyClientExtraFieldRequest;
use Illuminate\Support\Facades\Auth;

class CompanyClientExtraFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $extraFields = Auth::user()->company->client_extra_fields()->get();

        return response()->json([
            'data' => $extraFields,
            'status' => '1',
            'message' => __('strings.company_client_extra_fields_returned_successfully'),
        ]);
    }

    public function indexPublic($id)
    {
        try {
            $id = Company::decryptId($id);
        } catch (\Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $company = Company::findOrFail($id);

        return response()->json([
            'data' => $company->client_extra_fields()->where('view', true)->get(),
            'message' => __('strings.company_client_extra_fields_returned_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreCompanyClientExtraFieldRequest $request)
    {
        $this->authorize('create', CompanyClientExtraField::class);

        $extraField = CompanyClientExtraField::create(array_merge($request->validated(), [
            'company_id' => Auth::user()->company->id,
        ]));

        $activity = activity()
        ->performedOn(Auth::user()->company);

        $activity = $activity->by(Auth::user());
        $activity->log("Company setting's registration portal field has been created by :causer.first_name :causer.last_name");
     

        return response()->json([
            'data' => $extraField,
            'status' => '1',
            'message' => __('strings.company_client_extra_field_created_successfully'),
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\CompanyClientExtraField  $companyClientExtraField
     * @return \Illuminate\Http\Response
     */
    public function show(CompanyClientExtraField $companyClientExtraField)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\CompanyClientExtraField  $companyClientExtraField
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateCompanyClientExtraFieldRequest $request, CompanyClientExtraField $companyClientExtraField)
    {
        $this->authorize('update', $companyClientExtraField);

        if ($request->has('name')) {
            $companyClientExtraField->name = $request->name;
        }

        if ($request->has('view')) {
            $companyClientExtraField->view = $request->boolean('view');
        }

        if ($request->has('required')) {
            $companyClientExtraField->required = $request->boolean('required');
        }

        if ($companyClientExtraField->isDirty()) {
            

            
            $companyClientExtraField->save();

            $activity = activity()
            ->performedOn(Auth::user()->company);
    
            $activity = $activity->by(Auth::user());
            $activity->log("Company setting's registration portal field has been updated by :causer.first_name :causer.last_name");
         
        }

        return response()->json([
            'data' => $companyClientExtraField,
            'status' => '1',
            'message' => __('strings.company_client_extra_field_updated_successfully'),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\CompanyClientExtraField  $companyClientExtraField
     * @return \Illuminate\Http\Response
     */
    public function destroy(CompanyClientExtraField $companyClientExtraField)
    {
        $this->authorize('delete', $companyClientExtraField);

        $companyClientExtraField->delete();
        $activity = activity()
            ->performedOn(Auth::user()->company);
    
            $activity = $activity->by(Auth::user());
            $activity->log("Company setting's registration portal field has been deleted by :causer.first_name :causer.last_name");

        return response()->json([
            'status' => '1',
            'message' => __('strings.company_client_extra_field_deleted_successfully'),
        ]);
    }
}
