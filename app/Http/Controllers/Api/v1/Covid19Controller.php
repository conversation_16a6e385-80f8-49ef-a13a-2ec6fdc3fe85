<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Covid19;
use App\QuestionaryData;
use App\Traits\SaveFile;
use App\HealthQuestionary;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\Covid19StoreUpdateRequest;
use App\Http\Requests\v1\Covid19StoreUpdateNewRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class Covid19Controller extends Controller
{
    use SaveFile;

    public function store(Covid19StoreUpdateRequest $request, Client $client)
    {
        $this->authorize('update', [Client::class, $client]);

        Covid19::updateOrCreate([
            'client_id' => $client->id
        ], [
            'data' => json_encode($request->input('covid19'))
        ]);

        return response()->json([
            'message' => __('strings.Covid19_questionarries_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function storeNew(Covid19StoreUpdateNewRequest $request, Client $client, PDFServiceInterface $pdfService)
    {
        $this->authorize('update', [Client::class, $client]);
        DB::transaction(function () use ($client, $request, $pdfService) {
            $created_at = now();
            $user = Auth::user();

            $timezone = $user->company->timezone ?? 'Europe/Stockholm';
            if ($request->is_custom_date_selected == 1) {
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time", $timezone)->utc();
            }

            $covid19 = Covid19::updateOrCreate([
                'client_id' => $client->id
            ], [
                'data' => json_encode($request->input('covid19'))
            ]);

            $data = ['datas' => $request->input('covid19'), 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_covid19_questionary', 'clients/' . md5($client->id) . '/covid19_questionary/pdf');
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/covid19_questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->covid19($request->input('covid19'), $created_at)->saveFile($user, $filename);

            $questionaryData = QuestionaryData::create([
                'client_id' => $client->id,
                'modelable_type' => Covid19::class,
                'modelable_id' => $covid19->id,
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);
            if ($request->is_custom_date_selected == 1) {
                $questionaryData->created_at = $created_at;
                $questionaryData->save();
            }
        });

        return response()->json([
            'message' => __('strings.Covid19_questionarries_updated_successfully'),
            'status' => '1'
        ]);
    }
}
