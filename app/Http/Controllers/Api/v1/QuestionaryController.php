<?php

namespace App\Http\Controllers\Api\v1;

use App\Company;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\ClientQuestionaryDownloadRequest;
use App\Http\Requests\v1\IndexQuestionaryRequest;
use App\Http\Requests\v1\PublicIndexQuestionaryRequest;
use App\Http\Requests\v1\StoreQuestionaryRequest;
use App\Http\Requests\v1\UpdateQuestionaryRequest;
use App\Questionary;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use VerumConsilium\Browsershot\Facades\PDF;

class QuestionaryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(IndexQuestionaryRequest $request)
    {
        $this->authorize('viewAny', [Questionary::class]);

        $user = Auth::user();

        $questionaries = $user->company
            ->questionaries();

        if (request()->has('search')) {
            $questionaries = $questionaries->whereLike(['title'], request()->search);
        }

        if (!$request->has('withTrashed')) {
            $questionaries = $questionaries->active();
        }

        if (request()->has('orderBy') && request()->has('orderDirection')) {
            $questionaries = $questionaries->orderBy(request()->orderBy, request()->orderDirection);
        } else {
            $questionaries = $questionaries->latest();
        }

        if (request()->has('page')) {
            return response()->json(collect([
                'message' => 'Questionaries returned successfully.',
                'status' => '1',
            ])->merge($questionaries->paginate()));
        }

        return response()->json([
            'data' => $questionaries->get(),
            'message' => 'Questionaries returned successfully.',
            'status' => '1',
        ]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function indexPublic(PublicIndexQuestionaryRequest $request)
    {
        $company = Company::findOrFail($request->company_id);

        $questionaries = $company
            ->questionaries()->has('questions')->with('questions')->active();

        return response()->json([
            'data' => $questionaries->get(),
            'message' => 'Questionaries returned successfully.',
            'status' => '1',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\v1\StoreQuestionaryRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreQuestionaryRequest $request)
    {
        $this->authorize('create', [Questionary::class]);

        $questionary = DB::transaction(function () use ($request) {
            $user = Auth::user();
            $questionary = $user->company->questionaries()->create([
                'title' => $request->title,
            ]);

            return $questionary;
        });

        return response()->json([
            'data' => $questionary,
            'message' => 'Questionary created successfully.',
            'status' => '1',
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function show(Questionary $questionary)
    {
        $this->authorize('view', [Questionary::class, $questionary]);

        return response()->json([
            'data' => $questionary->loadMissing(['questions']),
            'message' => 'Questionary returned successfully.',
            'status' => '1',
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\v1\UpdateQuestionaryRequest  $request
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateQuestionaryRequest $request, Questionary $questionary)
    {
        $this->authorize('update', [Questionary::class, $questionary]);
        $questionary = DB::transaction(function () use ($questionary, $request) {
            if ($request->has('title')) {
                $questionary->title = $request->title;
            }

            if ($request->has('is_active')) {
                $questionary->is_active = $request->boolean('is_active');
            }

            if ($questionary->isDirty()) {
                $questionary->save();
            }
        });

        return response()->json([
            'data' => $questionary,
            'message' => __('strings.Questionary_updated_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function destroy(Questionary $questionary)
    {
        $this->authorize('delete', [Questionary::class, $questionary]);

        $questionary->delete();

        return response()->json([
            'data' => $questionary,
            'message' => 'Questionary deleted successfully.',
            'status' => '1',
        ]);
    }

    public function download(ClientQuestionaryDownloadRequest $request)
    {
        if ($request->type === 'App\AestheticInterest') {
            return PDF::loadView('exports.example.client_aesthethic_interest_questionary', [])
                ->waitUntilNetworkIdle()
                ->format('A4')
                ->margins(15, 0, 15, 0)
                ->download();
        }

        if ($request->type === 'App\HealthQuestionary') {
            return PDF::loadView('exports.example.client_health_questionary', [])
                ->waitUntilNetworkIdle()
                ->format('A4')
                ->margins(15, 0, 15, 0)
                ->download();
        }

        if ($request->type === 'App\Covid19') {
            return PDF::loadView('exports.example.client_covid19_questionary', [])
                ->waitUntilNetworkIdle()
                ->format('A4')
                ->margins(15, 0, 15, 0)
                ->download();
        }

        if ($request->type === 'App\Custom') {
            $questionary = Questionary::find($request->id);
            $this->authorize('view', [Questionary::class, $questionary]);

            return PDF::loadView('exports.example.client_questionaries', ['questionary' => $questionary])
                ->waitUntilNetworkIdle()
                ->format('A4')
                ->margins(15, 0, 15, 0)
                ->download();
        }
    }
}