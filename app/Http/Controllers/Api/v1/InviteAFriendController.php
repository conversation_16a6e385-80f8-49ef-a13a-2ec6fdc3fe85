<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\v1\InviteAFriendRequest;
use App\Mail\InviteAFriend;
use App\Setting;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class InviteAFriendController extends Controller
{
    public function invite(InviteAFriendRequest $request)
    {
        if (Auth::user()->company->is_read_only) {
            return $this->deny(__('strings.account_read_only_message', ['message' => '<EMAIL>']), 403);
        }

        try {
            App::setLocale($request->lang);

            $user = Auth::user();

            $email = $request['email'] ?? '<EMAIL>';

            $language = Setting::getSetting($user->company, Setting::CUSTOMER_LANGUAGE)?->value;
            Mail::to($email)->locale($language ?? app()->getLocale())->send(new InviteAFriend(
                __('strings.iaf_1') . " " . $user->first_name . " " . $user->last_name . " " . __('strings.iaf_2'),
                [
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                ]
            ));

            return response()->json([
                'message' => __('strings.Invite_email_send'),
                'status' => '1'
            ]);
        } catch (\Throwable $th) {
            Log::info("Error sending invite a friend mail:     " . $th);

            return response()->json([
                'message' => __('strings.Error_sending_invite_a_friend_mail'),
                'status' => '0'
            ]);
        }
    }
}
