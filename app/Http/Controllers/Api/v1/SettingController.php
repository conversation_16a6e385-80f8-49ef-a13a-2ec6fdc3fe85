<?php

namespace App\Http\Controllers\Api\v1;

use Exception;
use App\Company;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use App\Http\Requests\v1\StoreSettingRequest;
use App\Http\Requests\v2\UpdateSettingArrayRequest;
use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SettingController extends Controller
{
    public function store(StoreSettingRequest $request)
    {
        $company = Auth::user()->company;
        Auth::user()->company->settings()->updateOrCreate([
            'key' => $request->key
        ], [
            'value' => $request->value ?? ''
        ]);

        Cache::forget("company_settings_$company->id");

        //make sure at least 1 of unique check should be active
        if (!$company->settings()->where('key', Setting::PORTAL_REQUIRED_EMAIL)->exists()) {
            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_REQUIRED_EMAIL
            ], [
                'value' => 1
            ]);

            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_VIEW_EMAIL
            ], [
                'value' => 1
            ]);
        } else {
            if (!$company
                ->settings()
                ->whereIn('key', [Setting::PORTAL_REQUIRED_PERSONAL_ID, Setting::PORTAL_REQUIRED_CPR_ID, Setting::PORTAL_REQUIRED_EMAIL])
                ->where('value', 1)
                ->exists()) {
                return response()->json([
                    'message' => __('strings.at_least_one_unique_check_should_be_active'),
                    'status' => '0',
                ]);
            };
        }


        return response()->json([
            'message' => __('strings.Setting_saved_successfully'),
            'status' => '1',
        ]);
    }

    public function update(Company $company, StoreSettingRequest $request)
    {
        $company->settings()->updateOrCreate([
            'key' => $request->key
        ], [
            'value' => $request->value ?? ''
        ]);
        Cache::forget("company_settings_$company->id");
        //make sure at least 1 of unique check should be active
        if (!$company->settings()->where('key', Setting::PORTAL_REQUIRED_EMAIL)->exists()) {
            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_REQUIRED_EMAIL
            ], [
                'value' => 1
            ]);

            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_VIEW_EMAIL
            ], [
                'value' => 1
            ]);
        } else {
            if (!$company
                ->settings()
                ->whereIn('key', [Setting::PORTAL_REQUIRED_PERSONAL_ID, Setting::PORTAL_REQUIRED_CPR_ID, Setting::PORTAL_REQUIRED_EMAIL])
                ->where('value', 1)
                ->exists()) {
                return response()->json([
                    'message' => __('strings.at_least_one_unique_check_should_be_active'),
                    'status' => '0',
                ]);
            };
        }
        return response()->json([
            'message' => __('strings.Setting_saved_successfully'),
            'status' => '1',
        ]);
    }

    public function storeArray(UpdateSettingArrayRequest $request)
    {
        $company = Auth::user()->company;

        DB::beginTransaction();
        foreach ($request->settings as $setting) {
            Auth::user()->company->settings()->updateOrCreate([
                'key' => $setting['key']
            ], [
                'value' => $setting['value'] ?? ''
            ]);
        }
        Cache::forget("company_settings_$company->id");
        //make sure at least 1 of unique check should be active
        if (!$company->settings()->where('key', Setting::PORTAL_REQUIRED_EMAIL)->exists()) {
            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_REQUIRED_EMAIL
            ], [
                'value' => 1
            ]);

            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_VIEW_EMAIL
            ], [
                'value' => 1
            ]);
        } else {
            if (!$company
                ->settings()
                ->whereIn('key', [Setting::PORTAL_REQUIRED_PERSONAL_ID, Setting::PORTAL_REQUIRED_CPR_ID, Setting::PORTAL_REQUIRED_EMAIL])
                ->where('value', 1)
                ->exists()) {
                DB::rollBack();
                return response()->json([
                    'message' => __('strings.at_least_one_unique_check_should_be_active'),
                    'status' => '0',
                ]);
            };
        }
        DB::commit();
        return response()->json([
            'message' => __('strings.Setting_saved_successfully'),
            'status' => '1',
        ]);
    }

    public function index()
    {
        $user = Auth::user();

        $company = $user->company;
        if (!$company->settings()->where('key', Setting::PORTAL_REQUIRED_EMAIL)->exists()) {
            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_REQUIRED_EMAIL
            ], [
                'value' => 1
            ]);

            Auth::user()->company->settings()->updateOrCreate([
                'key' => Setting::PORTAL_VIEW_EMAIL
            ], [
                'value' => 1
            ]);
            Cache::forget("company_settings_$company->id");
        }

        $settings = Cache::remember("company_settings_$company->id", 60 * 24 * 60, function () use ($user, $company) {
            $settings = $user->company->settings();

            if ($user->company->verification != Company::COMPANY_REJECTED) {
                $settings->where('key', '!=', Setting::COMPANY_VERIFICATION_NOTES);
            }

            $settings->where('key', '!=', Setting::SMS_VERIFICATION_CODE);

            $settings_query = $settings;

            $settings = $settings->get();

            if (!$settings->where('key', Setting::ONLINE_PAYMENT)->first() && $company->viva_merchant_id) {
                Setting::updateOrCreate([
                    'key' => Setting::ONLINE_PAYMENT,
                    'company_id' => $company->id,
                ], [
                    'value' => Setting::getDefaultValue(Setting::ONLINE_PAYMENT),
                ]);

                $settings = $settings_query->get();
            }

            if (!$settings->where('key', Setting::VIVA_WALLET_SOURCE_CODE)->first() && $company->viva_merchant_id) {
                $company->getOrCreatePaymentSource();

                $settings = $settings_query->get();
            }

            if (!$settings->where('key', Setting::CUSTOMER_LANGUAGE)->where('company_id', $user->company_id)->first()) {
                if ($user->company->stripe_id) {
                    $customer = $company->createOrGetStripeCustomer();
                    if ($customer->currency) {
                        if ($customer->currency == 'kr') {
                            Setting::updateOrCreate([
                                'key' => Setting::CUSTOMER_LANGUAGE,
                                'company_id' => $company->id,
                            ], [
                                'value' => 'sv',
                            ]);
                        } else {
                            Setting::updateOrCreate([
                                'key' => Setting::CUSTOMER_LANGUAGE,
                                'company_id' => $company->id,
                            ], [
                                'value' => 'en',
                            ]);
                        }
                    } else {
                        Setting::updateOrCreate([
                            'key' => Setting::CUSTOMER_LANGUAGE,
                            'company_id' => $company->id,
                        ], [
                            'value' => 'en',
                        ]);
                    }
                } else {
                    if ($company->country && $company->country == 'Sweden') {
                        Setting::updateOrCreate([
                            'key' => Setting::CUSTOMER_LANGUAGE,
                            'company_id' => $company->id,
                        ], [
                            'value' => 'sv',
                        ]);
                    } else {
                        Setting::updateOrCreate([
                            'key' => Setting::CUSTOMER_LANGUAGE,
                            'company_id' => $company->id,
                        ], [
                            'value' => 'en',
                        ]);
                    }

                    $settings = $settings_query->get();
                }
            }


            return $settings;
        });


        return response()->json([
            'data' => $settings,
            'message' => 'settings returned successfully.',
            'status' => '1',
        ]);
    }

    public function indexPublic($id)
    {

        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $company = Company::findOrFail($id);

        $settings = $company->settings();

        if ($company->verification != Company::COMPANY_REJECTED) {
            $settings->where('key', '!=', Setting::COMPANY_VERIFICATION_NOTES);
        }

        $settings->where('key', '!=', Setting::SMS_VERIFICATION_CODE);

        return response()->json([
            'data' => $settings->get(),
            'message' => 'settings returned successfully.',
            'status' => '1',
        ]);
    }

    public function getKey($key)
    {
        $company = Auth::user()->company;
        $setting = Setting::getSetting($company, $key);

        return response()->json([
            'data' => $setting,
            'message' => 'settings returned successfully.',
            'status' => '1',
        ]);
    }
}