<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\ClientKind;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\StoreClientKindRequest;
use App\Http\Requests\v1\UpdateClientKindRequest;
use App\Http\Requests\v1\UpdateClientRequest;
use Illuminate\Http\Request;

class ClientKindController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function index(Client $client)
    {
        $this->authorize('viewAny', [ClientKind::class, $client]);

        return response()->json([
            'message' => __('strings.client_kind_returned_successs'),
            'status' => '1',
            'data' => $client->kind,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function store(StoreClientKindRequest $request, Client $client)
    {
        $this->authorize('create', [ClientKind::class, $client]);

        $kind = $client->kind()->updateOrCreate([
            'client_id' => $client->id,
        ], $request->validated());

        return response()->json([
            'message' => __('strings.client_kind_saved_successs'),
            'status' => '1',
            'data' => $kind,
        ]);
    }
}
