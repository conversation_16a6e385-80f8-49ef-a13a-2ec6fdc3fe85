<?php

namespace App\Http\Controllers\Api\v1;

use App\File;
use App\Client;
use App\QuestionaryData;
use App\Traits\SaveFile;
use App\AestheticInterest;
use App\Contracts\Services\PDF\PDFServiceInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Request;
use App\Http\Requests\v1\AestheticInterestRequest;
use App\Http\Requests\v1\AestheticInterestNewRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class AestheticInterestController extends Controller
{
    use SaveFile;


    // AestheticInterestRequest $request
    public function store(Request $request, Client $client)
    {
        return response()->json([
            'message' => __('strings.Please_update_your_app'),
            'status' => '0'
        ]);

        $this->authorize('update', [Client::class, $client]);

        $inputs = $request->all();
        if ($request->hasFile('aesthetic_interest.10.image')) {
            $file = $this->saveFile(Request::file('aesthetic_interest')[10]['image'], 'aesthetic_interest', $client->company->users()->first());
            $inputs['aesthetic_interest'][10]['image'] = $file->url;
        }

        $aestheticInterest = AestheticInterest::updateOrCreate([
            'client_id' => $client->id
        ], [
            'data' => json_encode($inputs)
        ]);

        if ($request->hasFile('aesthetic_interest.10.image')) {
            if ($aestheticInterest->file) {
                $aestheticInterest->file->delete();
            }
            $aestheticInterest->file()->save($file);
        }

        return response()->json([
            'message' => __('strings.Aesthetic_Interest_updated_successfully'),
            'status' => '1'
        ]);
    }

    public function storeNew(AestheticInterestNewRequest $request, Client $client, PDFServiceInterface $pdfService)
    {
        $this->authorize('update', [Client::class, $client]);

        DB::transaction(function () use ($client, $request, $pdfService) {
            $inputs = $request->validated();
            $user = Auth::user();

            if ($request->hasFile('aesthetic_interest.5.image')) {
                $file = $this->saveFile(Request::file('aesthetic_interest')[5]['image'], 'aesthetic_interest');
                $inputs['aesthetic_interest'][5]['image'] = $file->url;
            }

            if ($this->isAssoc($inputs['aesthetic_interest'])) {
                $inputs['aesthetic_interest'] = collect($inputs['aesthetic_interest'])->sortBy(function ($data, $key) {
                    // Being sure the string is actually a number
                    if (is_numeric($key)) {
                        $number = $key + 0;
                    } else { // Let the number be 0 if the string is not a number
                        $number = 0;
                    }
                    return $number;
                })->values()->toArray();
            }

            $aestheticInterest = AestheticInterest::updateOrCreate([
                'client_id' => $client->id
            ], [
                'data_new' => json_encode($inputs)
            ]);

            if ($request->hasFile('aesthetic_interest.5.image')) {
                if ($aestheticInterest->file) {
                    $aestheticInterest->file->delete();
                }
                $aestheticInterest->file()->save($file);
            }

            $created_at = now();
            $timezone = $user->company->timezone ?? 'Europe/Stockholm';
            if ($request->is_custom_date_selected == 1) {
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time", $timezone)->utc();
            }

            $data = ['datas' => $inputs['aesthetic_interest'], 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_aesthethic_interest_questionary', 'clients/' . md5($client->id) . '/aesthetic_interest/pdf');
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->aesthethicInterest($inputs['aesthetic_interest'], $created_at)->saveFile($user, $filename);

            $questionaryData = QuestionaryData::create([
                'client_id' => $client->id,
                'modelable_type' => AestheticInterest::class,
                'modelable_id' => $aestheticInterest->id,
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);

            if ($request->is_custom_date_selected == 1) {
                $questionaryData->created_at = $created_at;
                $questionaryData->save();
            }
        });
        return response()->json([
            'message' => __('strings.Aesthetic_Interest_updated_successfully'),
            'status' => '1'
        ]);
    }

    protected function isAssoc(array $arr)
    {
        if (array() === $arr) {
            return false;
        }
        return array_keys($arr) !== range(0, count($arr) - 1);
    }
}
