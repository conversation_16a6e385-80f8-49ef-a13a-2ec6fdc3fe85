<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\ClientVerification;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\AddClientVerificationRequest;
use Illuminate\Http\Request;

class ClientVerificationController extends Controller
{
    public function store(Client $client, AddClientVerificationRequest $request)
    {
        $this->authorize('create', [ClientVerification::class, $client]);
        ClientVerification::updateOrCreate([
            'client_id' => $client->id
        ], [
            'has_id' => $request->has_id ?? 0,
            'has_driving_license' => $request->has_driving_license ?? 0,
            'has_passport' => $request->has_passport ?? 0,
            'other' => $request->other ?? 0,
            'note' => $request->note ?? null,
        ]);

        return response()->json([
            'message' => __('strings.client_verified_successfully'),
            'status' => '1',
        ]);
    }
}
