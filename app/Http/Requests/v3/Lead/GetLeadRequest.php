<?php

namespace App\Http\Requests\v3\Lead;

use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetLeadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            'orderBy' => 'in:company_name,client_name,email,mobile_number,country_code,company_id,type,status,industry,size',
            'orderDirection'  => 'in:asc,desc,ASC,DESC',
            'filter' => [
                Rule::in([
                    'onlyTrashed',
                    'withTrashed',
                    'status',
                    'type',
                ]),
            ],
            'filter_type' => [
                Rule::in(['=', '!='])
            ],
            'filter_value' => [
                Rule::in([
                    'true',
                    true,
                    false,
                    '1',
                    '0',
                    'false',
                    Lead::STATUS_CLOSE,
                    Lead::STATUS_FOLLOW_UP,
                    Lead::STATUS_ON_GOING,
                    Lead::STATUS_NEW,
                    Lead::STATUS_WON,
                    Lead::STATUS_LOST,
                    Lead::TYPE_COLD,
                    Lead::TYPE_WARM,
                ])
            ],
        ];
    }
}
