<?php

namespace App\Http\Requests\v3\Lead;

use App\Company;
use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLeadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_name' => '',
            'client_name' => '',
            'email' => 'email',
            'mobile_number' => 'integer',
            'country_code' => '',
            'type' => [
                Rule::in([
                    Lead::TYPE_COLD,
                    Lead::TYPE_WARM,
                ]),
            ],
            'status' => [
                Rule::in([
                    Lead::STATUS_CLOSE,
                    Lead::STATUS_FOLLOW_UP,
                    Lead::STATUS_NEW,
                    Lead::STATUS_ON_GOING,
                    Lead::STATUS_WON,
                    Lead::STATUS_LOST,
                ]),
            ],
            'industry' => "",
            'size' => "",
            'about' => "",
            'outcome_note' => '',
            "company_id" => [
                Rule::exists(Company::class, 'id'),
            ],
        ];
    }
}
