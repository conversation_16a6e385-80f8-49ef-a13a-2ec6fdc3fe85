<?php

namespace App\Http\Requests\v3\Lead;

use App\Company;
use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLeadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_name' => 'required',
            'client_name' => 'required',
            'email' => 'email|required_without:mobile_number',
            'mobile_number' => 'integer|required_without:email',
            'country_code' => '',
            'type' => [
                'required',
                Rule::in([
                    Lead::TYPE_COLD,
                    Lead::TYPE_WARM,
                ]),
            ],
            'status' => [
                'required',
                Rule::in([
                    Lead::STATUS_CLOSE,
                    Lead::STATUS_FOLLOW_UP,
                    Lead::STATUS_NEW,
                    Lead::STATUS_ON_GOING,
                    Lead::STATUS_LOST,
                    Lead::STATUS_WON,
                ]),
            ],
            "company_id" => [
                Rule::exists(Company::class, 'id'),
            ],
            'industry' => "",
            'size' => "",
            'about' => "",
            'outcome_note' => '',
        ];
    }
}
