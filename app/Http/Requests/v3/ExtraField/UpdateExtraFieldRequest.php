<?php

namespace App\Http\Requests\v3\ExtraField;

use Illuminate\Foundation\Http\FormRequest;

class UpdateExtraFieldRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'max:50|nullable',
            'sub_text' => 'max:100|nullable',
            'opening_hours' => 'array|nullable',
            'mode_of_payment' => 'max:1000|nullable',
            'about' => 'nullable',
            'facebook' => 'active_url|nullable',
            'instagram' => 'active_url|nullable',
            'youtube' => 'active_url|nullable',
            'twitter' => 'active_url|nullable',
            'whatsapp' => 'nullable',
            'clear_google_place' => 'boolean',
        ];
    }
}
