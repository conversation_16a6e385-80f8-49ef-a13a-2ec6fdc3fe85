<?php

namespace App\Http\Requests\v3;

use Illuminate\Foundation\Http\FormRequest;

class StoreMedicalDeviceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'product_name'  => 'required',
            'brand'         => 'required',
            'model'         => 'required',
            'supplier'      => '',
            'performed_maintenance' => '',
            'upcoming_maintenance' => '',
            'serial_number' => '',
            'compliance_declared' => 'in:0,1',
        ];
    }
}
