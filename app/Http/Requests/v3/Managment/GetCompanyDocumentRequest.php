<?php

namespace App\Http\Requests\v3\Managment;

use Illuminate\Foundation\Http\FormRequest;

class GetCompanyDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'view_company_document' => 'boolean|in:0,1',
        ];
    }
}
