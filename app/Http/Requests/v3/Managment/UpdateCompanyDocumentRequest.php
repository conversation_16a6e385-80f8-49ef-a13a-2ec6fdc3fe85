<?php

namespace App\Http\Requests\v3\Managment;

use App\GeneralTemplate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCompanyDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $company_document = $this->route('company_document');

        $questions = collect($company_document?->version?->questions) ?? collect([]);

        $formated_validations = [];
        foreach ($questions as $index => $question) {
            foreach (config('questionary.type_with_answer_validation') as $type => $validations) {
                if ($type == $question->type) {
                    if (optional($question)->required) {
                        $validations = $validations['required'];
                    } else {
                        $validations = $validations['normal'];
                    }
                    foreach ($validations as $field => $validation) {
                        $field = str_replace("_index", $index, $field);
                        if (optional($question)->options && count(optional($question)->options)) {
                            $field = str_replace("_options", implode(",", optional($question)->options), $field);
                        }

                        $validation = str_replace("_index", $index, $validation);
                        if (optional($question)->options && count(optional($question)->options)) {
                            $validation = str_replace("_options", implode(",", optional($question)->options), $validation);
                        }

                        $formated_validations[$field] = $validation;
                    }
                }
            }
        }

        if ($questions->count()) {
            $formated_validations['data'] = "required|size:{$questions->count()}|array";
        } else {
            $formated_validations['data'] = "required|size:1|array";
            $formated_validations['data.0.text'] = "max:10000|required";
        }

        $formated_validations["signature"] = [
            "required",
            "image",
            "mimes:jpeg,jpg,png",
        ];

        $formated_validations["process"] = [
            Rule::in([GeneralTemplate::PATIENT_SAFETY, GeneralTemplate::DEVIATION_MGMT , GeneralTemplate::THE_BUSINESS]),
        ];

        return $formated_validations;
    }
}
