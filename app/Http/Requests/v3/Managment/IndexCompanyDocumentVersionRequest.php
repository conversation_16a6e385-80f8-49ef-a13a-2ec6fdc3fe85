<?php

namespace App\Http\Requests\v3\Managment;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexCompanyDocumentVersionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            'orderBy' => 'in:title,version,created_at',
            'orderDirection'  => 'in:asc,desc,ASC,DESC',
            "search" => "",
        ];
    }
}
