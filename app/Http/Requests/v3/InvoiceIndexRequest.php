<?php

namespace App\Http\Requests\v3;

use App\CompanyPlatform;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class InvoiceIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'platforms' => 'array',
            'platforms.*' => Rule::in(CompanyPlatform::ALL_PLATFORMS),
        ];
    }
}
