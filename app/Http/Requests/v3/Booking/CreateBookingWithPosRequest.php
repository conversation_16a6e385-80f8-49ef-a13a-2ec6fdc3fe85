<?php

namespace App\Http\Requests\v3\Booking;

use App\CompanyBooking;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateBookingWithPosRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'recaptcha' => 'recaptcha',

            'is_bankid_verified' => "in:1,0",
            'full_name' => '',
            'first_name' => '',
            'last_name' => '',
            'email' => 'email|required_if:type,' . CompanyBooking::PAY_FOR_SLOT,
            'country_code' => '',
            'phone_number' => '',
            'address' => '',
            'city' => '',
            'zipcode' => '',
            'country' => '',
            'state' => '',
            'special_request' => '',

            'user_id' => 'required_if:type,' . CompanyBooking::BOOK_SLOT,
            'service_id' => 'required_if:type,' . CompanyBooking::BOOK_SLOT,
            'start_at' => 'required_if:type,' . CompanyBooking::BOOK_SLOT,

            'booking_id' => 'required_if:type,' . CompanyBooking::RELEASE_SLOT,

            'type' => [
                'required',
                Rule::in([
                    CompanyBooking::BOOK_SLOT,
                    CompanyBooking::RELEASE_SLOT,
                    CompanyBooking::PAY_FOR_SLOT,
                ])
            ]
        ];
    }
}
