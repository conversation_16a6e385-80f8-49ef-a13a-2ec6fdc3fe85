<?php

namespace App\Http\Requests\v3\Company;

use App\Rules\OrganizationNumber;
use App\Rules\UniqueCompanyName;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            // 'old_password' => 'required_with:password',
            // 'password' => 'sometimes|confirmed',
            'first_name' => 'sometimes',
            'last_name' => 'sometimes',
            'country_code' => 'sometimes',
            'mobile_number' => 'sometimes',
            'country' => 'sometimes',
            'state' => 'sometimes',
            'city' => 'sometimes',
            'zip_code' => 'sometimes',
            'street_address' => 'sometimes',
            'unit' => 'sometimes|in:eur,usd,sek,gbp',
            // 'date_of_birth' => 'sometimes|date|date_format:Y-m-d',
            // 'last_name' => 'sometimes',
            // 'first_name' => 'sometimes',
            'company_name' => [
                'sometimes',
                new UniqueCompanyName(Auth::user()->company_id),
            ],
            'theme' => '',
            'profile_photo' => 'sometimes|image|mimes:jpeg,jpg,png',
            'cover_image' => 'sometimes|image|mimes:jpeg,jpg,png',
            'organization_number' => [
                'sometimes',
                new OrganizationNumber(),
            ],
        ];
    }
}
