<?php

namespace App\Http\Requests\v3\Questionary;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class BulkQuestionaryQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'questions' => 'required|array',
            'questions.*.question' => 'required|max:1000',
            'questions.*.type' => [
                'required',
                'bail',
                Rule::in(config('questionary.type')),
            ],
            'questions.*.required' => 'required|boolean',
            'questions.*.options' => 'sometimes|array',
            'questions.*.properties' => 'sometimes',
            'questions.*.order' => [
                'sometimes',
                'integer',
                'min:0',
            ],
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->has('questions')) {
                foreach ($this->questions as $index => $question) {
                    $this->validateQuestionOptions($validator, $question, $index);
                }
            }
        });
    }

    private function validateQuestionOptions($validator, $question, $index)
    {
        if (!isset($question['type'])) {
            return;
        }

        $type = $question['type'];
        $options_validation = [];

        foreach (config('questionary.type_with_options') as $configType => $validation) {
            if ($type == $configType) {
                $options_validation = $validation['update'];
                break;
            }
        }

        if (count($options_validation) && (!isset($question['options']) || !is_array($question['options']))) {
            $validator->errors()->add("questions.{$index}.options", 'The options field is required for this question type.');
        } elseif (count($options_validation)) {
            // Validate options array based on type requirements
            $options = $question['options'];
            foreach ($options_validation as $rule) {
                if (str_contains($rule, 'min:') && count($options) < (int)str_replace('min:', '', $rule)) {
                    $validator->errors()->add("questions.{$index}.options", "The options field must have at least " . str_replace('min:', '', $rule) . " items.");
                }
                if (str_contains($rule, 'max:') && count($options) > (int)str_replace('max:', '', $rule)) {
                    $validator->errors()->add("questions.{$index}.options", "The options field must not have more than " . str_replace('max:', '', $rule) . " items.");
                }
            }
        }
    }
}
