<?php

namespace App\Http\Requests\v3\LeadNote;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class UpdateLeadNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'max:255',
            'note' => 'max:2000',
            'remind_at' => [
                'date',
            ],
        ];
    }
}
