<?php

namespace App\Http\Requests\v3\LeadNote;

use App\Models\Lead;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetLeadNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            'orderBy' => 'in:title,notes,remind_at',
            'orderDirection'  => 'in:asc,desc,ASC,DESC',
            'filter' => 'in:onlyTrashed,withTrashed',
            'lead_id' => [
                Rule::exists(Lead::class, 'id'),
            ],
        ];
    }
}
