<?php

namespace App\Http\Requests\v3\Report;

use Illuminate\Foundation\Http\FormRequest;

class GetCompanyPosDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'start_date' => [
                'required',
                'date_format:Y-m-d',
                'before_or_equal:end_date',
            ],
            'end_date' => [
                'required',
                'date_format:Y-m-d',
                'after_or_equal:start_date',
            ],
            'type' => 'required',
            'user_ids.*' => 'exists:users,id',
            'user_ids' => 'array',
            'page' => 'numeric|gt:0',
            'per_page' => 'numeric|gt:0',
        ];
    }
}
