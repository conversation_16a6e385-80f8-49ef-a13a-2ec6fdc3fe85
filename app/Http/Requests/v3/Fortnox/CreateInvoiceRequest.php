<?php

declare(strict_types=1);

namespace App\Http\Requests\v3\Fortnox;

use App\Client;
use App\CompanyProduct;
use App\CompanyService;
use App\User;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $products_data = $this->get('products');

        return [
            // Products validation
            'products.*.amount' => [
                'numeric',
                'min:0',
            ],
            'products.*.quantity' => [
                'required',
                'integer',
                'min:1',
                function (string $attribute, mixed $value, Closure $fail) use ($products_data) {
                    $index = array_search($value, array_column($products_data, 'quantity'));

                    if ($index !== false && isset($this->products[$index]['id'])) {
                        $product = CompanyProduct::find($this->products[$index]['id']);
                        if ($product && $product->stock < $value) {
                            $fail(__("pos_strings.quantity_more_than_available"));
                        }
                    }
                },
            ],
            'products.*.id' => [
                Rule::exists(CompanyProduct::class, 'id')->where('deleted_at', null)->using(function ($query) {
                    return $query->where('company_id', auth()->user()->company_id);
                }),
                'integer',
                'required'
            ],
            'products.*.user_id' => [
                Rule::exists(User::class, 'id')->where('deleted_at', null)->using(function ($query) {
                    return $query->where('company_id', auth()->user()->company_id);
                }),
                'integer',
            ],
            'products' => 'required_without:services|array|min:1',

            // Services validation
            'services.*.id' => [
                function (string $attribute, mixed $value, Closure $fail) {
                    $service = CompanyService::where('id', $value)->where('is_active', true)->where('deleted_at', null)->where(function ($query) {
                        return $query->where('company_id', auth()->user()->company_id)->where('price', '!=', null);
                    })->first();

                    if (!$service || $service->price <= 0) {
                        $fail(__("validation.exists", ['attribute' => $attribute]));
                    }
                },
                'integer',
                'required'
            ],
            'services.*.user_id' => [
                Rule::exists(User::class, 'id')->where('deleted_at', null)->using(function ($query) {
                    return $query->where('company_id', auth()->user()->company_id);
                }),
                'integer',
            ],
            'services.*.quantity' => 'required|integer|min:1',
            'services.*.amount' => [
                'numeric',
                'min:0',
            ],
            'services' => 'required_without:products|array|min:1',

            // Global discount
            'discount_value' => [
                'required_with:discount_type',
                $this->discount_type == 'percentage' ? 'integer' : 'regex:/^\d*\.?\d{0,2}$/',
                'min:1',
                $this->discount_type == 'percentage' ? 'max:100' : '',
            ],
            'discount_type' => 'required_with:discount_value|in:percentage,value',
            'client_id' => [
                Rule::exists(Client::class, 'id')->where('deleted_at', null)->where('company_id', auth()->user()->company_id),
                'integer',
                'required'
            ],
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $items = $this->input('items', []);

            foreach ($items as $index => $item) {
                // Validate discount_value and discount_type relationship for each item
                if (isset($item['discount_type']) && !isset($item['discount_value'])) {
                    $validator->errors()->add("items.{$index}.discount_value", 'The discount value field is required when discount type is present.');
                }

                if (isset($item['discount_value']) && !isset($item['discount_type'])) {
                    $validator->errors()->add("items.{$index}.discount_type", 'The discount type field is required when discount value is present.');
                }

                // Validate discount_value based on discount_type for each item
                if (isset($item['discount_type']) && isset($item['discount_value'])) {
                    $discountValue = $item['discount_value'];
                    $discountType = $item['discount_type'];

                    if ($discountType === 'percentage') {
                        if (!is_numeric($discountValue) || $discountValue < 0 || $discountValue > 100) {
                            $validator->errors()->add("items.{$index}.discount_value", 'The discount value must be between 0 and 100 for percentage discounts.');
                        }
                    } elseif ($discountType === 'value') {
                        if (!is_numeric($discountValue) || $discountValue < 0) {
                            $validator->errors()->add("items.{$index}.discount_value", 'The discount value must be a positive number for value discounts.');
                        }
                        // Check if discount value doesn't exceed item total
                        if (isset($item['price']) && isset($item['quantity'])) {
                            $itemTotal = $item['price'] * $item['quantity'];
                            if ($discountValue > $itemTotal) {
                                $validator->errors()->add("items.{$index}.discount_value", 'The discount value cannot exceed the item total.');
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'items.required' => __('strings.fortnox_items_required'),
            'items.array' => __('strings.fortnox_items_must_be_array'),
            'items.min' => __('strings.fortnox_items_min_one'),
            'items.*.description.required' => __('strings.fortnox_item_description_required'),
            'items.*.description.string' => __('strings.fortnox_item_description_must_be_string'),
            'items.*.description.max' => __('strings.fortnox_item_description_max_255'),
            'items.*.price.required' => __('strings.fortnox_item_price_required'),
            'items.*.price.numeric' => __('strings.fortnox_item_price_must_be_numeric'),
            'items.*.price.min' => __('strings.fortnox_item_price_cannot_be_negative'),
            'items.*.quantity.required' => __('strings.fortnox_item_quantity_required'),
            'items.*.quantity.integer' => __('strings.fortnox_item_quantity_must_be_numeric'),
            'items.*.quantity.min' => __('strings.fortnox_item_quantity_must_be_positive'),
            'items.*.unit.string' => __('strings.fortnox_item_unit_must_be_string'),
            'items.*.unit.max' => __('strings.fortnox_item_unit_max_10'),
            'items.*.vat.in' => __('strings.fortnox_item_vat_must_be_numeric'),

        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'items' => 'invoice items',
            'items.*.description' => 'item description',
            'items.*.price' => 'item price',
            'items.*.quantity' => 'item quantity',
            'items.*.unit' => 'item unit',
            'items.*.vat' => 'VAT percentage',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Set default values for optional fields in items
        if ($this->has('items')) {
            $items = $this->input('items');

            foreach ($items as $index => $item) {
                // Set default unit if not provided
                if (!isset($item['unit'])) {
                    $items[$index]['unit'] = 'st'; // Default Swedish unit
                }

                // Set default VAT if not provided
                if (!isset($item['vat'])) {
                    $items[$index]['vat'] = 25; // Default Swedish VAT
                }
            }

            $this->merge(['items' => $items]);
        }
    }
}
