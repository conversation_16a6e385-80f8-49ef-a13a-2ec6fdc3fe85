<?php

namespace App\Http\Requests\v3\Fortnox;

use Illuminate\Foundation\Http\FormRequest;

class GetInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            'orderBy' => 'in:created_at,total,fortnox_document_number,customer_name,user.full_name',
            'orderDirection'  => 'in:asc,desc,ASC,DESC',
            'date' => [
                'date_format:Y-m-d',
            ],
            'with' => 'array',
            'client_id' => 'integer|gt:0',
            'with.*' => 'in:items',
        ];
    }
}
