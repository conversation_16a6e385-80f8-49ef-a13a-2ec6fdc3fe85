<?php

namespace App\Http\Requests\v3\Client;

use App\Client;
use App\ClientLetterOfConsent;
use App\ClientPrescription;
use App\ClientTreatment;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\GeneralNote;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ClientDataDownloadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => [
                'required',
                Rule::in([
                    ClientTreatment::class,
                    GeneralNote::class,
                    ClientLetterOfConsent::class,
                    CompanyBooking::class,
                    CompanyBookingClient::class,
                    ClientPrescription::class,
                ]),
            ],
            'id' => 'required|integer|gt:0',
        ];
    }
}
