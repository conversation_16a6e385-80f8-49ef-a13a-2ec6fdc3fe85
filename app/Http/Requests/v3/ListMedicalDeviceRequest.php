<?php

namespace App\Http\Requests\v3;

use Illuminate\Foundation\Http\FormRequest;

class ListMedicalDeviceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'page' => 'numeric|gt:0',
            'per_page' => 'numeric|gt:0',
            'orderBy' => 'in:product_name,created_at',
            'orderDirection' => 'in:asc,desc,ASC,DESC',
        ];
    }
}
