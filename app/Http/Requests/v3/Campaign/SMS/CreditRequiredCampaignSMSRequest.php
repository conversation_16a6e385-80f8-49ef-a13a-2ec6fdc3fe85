<?php

namespace App\Http\Requests\v3\Campaign\SMS;

use App\Client;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreditRequiredCampaignSMSRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "content" => [
                'required',
            ],
            "client_filter" => [
                'array',
            ],
            "client_filter.*.type" => [
                'required',
                Rule::in(Client::CLIENT_FILTER_TYPES),
            ],
            "client_filter.*.key" => [
                'required',
                Rule::in(Client::CLIENT_FILTER_KEYS),
            ],
            "client_filter.*.value" => [
                'required',
                'min:0',
                'max:1000',
            ],
            'with_unsubscribe_link' => 'boolean',
        ];
    }
}
