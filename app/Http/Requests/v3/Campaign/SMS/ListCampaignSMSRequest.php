<?php

namespace App\Http\Requests\v3\Campaign\SMS;

use Illuminate\Foundation\Http\FormRequest;

class  ListCampaignSMSRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            'orderBy' => 'in:created_at,credit_used,client_count,name',
            'orderDirection'  => 'in:asc,desc,ASC,DESC',
            'start_date' => [
                'date_format:Y-m-d',
                'before_or_equal:end_date',
            ],
            'end_date' => [
                'date_format:Y-m-d',
                'after_or_equal:start_date',
            ]
        ];
    }
}
