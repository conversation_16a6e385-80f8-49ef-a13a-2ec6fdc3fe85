<?php

namespace App\Http\Requests\v3;

use Illuminate\Foundation\Http\FormRequest;

class ListTemplateQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'orderBy' => 'in:question,required,type,order,created_at',
            'orderDirection' => 'in:asc,desc',
            'page' => 'numeric|gt:0',
            'per_page' => 'numeric|gt:0',
            'search' => '',
        ];
    }
}
