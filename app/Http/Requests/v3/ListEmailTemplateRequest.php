<?php

namespace App\Http\Requests\v3;

use Illuminate\Foundation\Http\FormRequest;

class ListEmailTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            // 'filter' => 'in:ALL,ACTIVE,INACTIVE',
            'orderDirection'  => 'in:asc,desc,ASC,DESC',
            'orderBy' => 'in:title,created_at',
        ];
    }
}
