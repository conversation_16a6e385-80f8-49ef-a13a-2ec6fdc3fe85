<?php

namespace App\Http\Integrations\GooglePlaces\Authenticator;

use Saloon\Contracts\PendingRequest;
use Saloon\Contracts\Authenticator;

class QueryAuthenticator implements Authenticator
{
  public function __construct(
    public string $key,
    public string $apiKey,
  ) {
    //
  }

  public function set(PendingRequest $pendingRequest): void
  {
    $pendingRequest->query()->add($this->key, $this->apiKey);
  }
}
