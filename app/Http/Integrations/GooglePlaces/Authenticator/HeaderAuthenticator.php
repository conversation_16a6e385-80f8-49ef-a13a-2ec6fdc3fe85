<?php

namespace App\Http\Integrations\GooglePlaces\Authenticator;

use Saloon\Contracts\PendingRequest;
use Saloon\Contracts\Authenticator;

class HeaderAuthenticator implements Authenticator
{
  public function __construct(
    public string $header,
    public string $apiKey,
  ) {
    //
  }

  public function set(PendingRequest $pendingRequest): void
  {
    $pendingRequest->headers()->add($this->header, $this->apiKey);
  }
}
