<?php

namespace App\Http\Integrations\GooglePlaces\PlacesNew\Requests;

use App\Http\Integrations\GooglePlaces\PlacesNew\Endpoint;
use Saloon\Enums\Method;
use Saloon\Http\Request;

/**
 * @see https://developers.google.com/maps/documentation/places/web-service/place-details
 */
class PlaceDetails extends Request
{
    /**
     * @var \Saloon\Enums\Method
     */
    protected Method $method = Method::GET;

    /**
     * @param  string  $placeId
     * @param  array  $fields
     * @param  array  $params
     */
    public function __construct(
        private readonly string $placeId,
        private readonly array $fields = ['*'],
        private readonly array $params = [],
    ) {}

    /**
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return Endpoint::placeDetails($this->placeId);
    }

    /**
     * @return array|mixed[]
     */
    protected function defaultQuery(): array
    {
        return [
            'fields' => implode(',', $this->fields),
            ...$this->params,
        ];
    }
}
