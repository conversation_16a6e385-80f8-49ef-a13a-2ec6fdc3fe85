<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use App\Http\Integrations\VivaWallet\DataFactories\TransactionDetailFactory;
use Illuminate\Support\Collection;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class TransactionResponse implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public int $eventId,
        readonly public ?string $errorCode,
        readonly public ?int $errorText,
        readonly public string $timeStamp,
        readonly public ?int $correlationId,
        readonly public string $success,
        readonly public Collection $transactions,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            eventId: $data['EventId'],
            errorCode:$data['ErrorCode'],
            errorText:$data['ErrorText'],
            timeStamp:$data['TimeStamp'],
            correlationId:$data['CorrelationId'] ,
            success:$data['Success'],
            transactions: TransactionDetailFactory::collection($data['Transactions']),
        );
    }
}
