<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use App\Http\Integrations\VivaWallet\Enums\TransactionStatus;
use Carbon\Carbon;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class TransactionDetail implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $merchantTrns,
        readonly public string $transactionId,
        readonly public ?int $transactionTypeId,
        readonly public string $clearanceDate,
        readonly public Carbon $insDate,

        readonly public string $retrievalReferenceNumber,
        readonly public string $orderCode,

        readonly public ?string $email,
        readonly public ?string $phone,
        readonly public string $channelIc,
        readonly public ?string $fullName,

        readonly public string $token,
        readonly public string $number,
        readonly public string $countryCode,
        readonly public string $issuingBank,
        readonly public ?string $cardHolderName,
        readonly public string $expirationDate,

        readonly public string $cardType,
        readonly public string $CardTypeId,

        readonly public TransactionStatus $StatusId,
    ) {}

    public static function fromResponse(Response $response): self
    {
        $data = (array) $response->json();
        return static::fromArray($data);
    }

    public static function fromArray(array $attributes): self
    {
        return new static(
            merchantTrns: strval(data_get($attributes, 'MerchantTrns')),
            transactionId: strval(data_get($attributes, 'TransactionId')),
            clearanceDate: strval(data_get($attributes, 'ClearanceDate')),
            retrievalReferenceNumber: strval(data_get($attributes, 'RetrievalReferenceNumber')),
            orderCode: strval(data_get($attributes, 'Order.OrderCode')),
            StatusId: TransactionStatus::from($attributes['StatusId']),
            insDate: Carbon::parse($data['InsDate'] ?? now()),


            //Payment
            email: strval(data_get($attributes, 'Payment.Email')),
            phone: strval(data_get($attributes, 'Payment.Phone')),
            channelIc: strval(data_get($attributes, 'Payment.ChannelId')),
            fullName: strval(data_get($attributes, 'Payment.FullName')),

            //credit card
            token: strval(data_get($attributes, 'CreditCard.Token')),
            number: strval(data_get($attributes, 'CreditCard.Number')),
            countryCode: strval(data_get($attributes, 'CreditCard.CountryCode')),
            issuingBank: strval(data_get($attributes, 'CreditCard.IssuingBank')),
            cardHolderName: strval(data_get($attributes, 'CreditCard.CardHolderName')),
            expirationDate: strval(data_get($attributes, 'CreditCard.ExpirationDate')),

            //card type
            cardType: strval(data_get($attributes, 'CreditCard.CardType.Name')),
            CardTypeId: strval(data_get($attributes, 'CreditCard.CardType.CardTypeId')),

            transactionTypeId: strval(data_get($attributes, 'TransactionType.TransactionTypeId')),
        );
    }
}
