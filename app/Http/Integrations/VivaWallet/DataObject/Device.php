<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Device implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $merchantId,
        readonly public string $statusId,
        readonly public ?string $sourceCode,
        readonly public string $terminalId,
        readonly public string $virtualTerminalId,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            merchantId: $data['merchantId'],
            statusId: $data['statusId'],
            sourceCode: $data['sourceCode'],
            terminalId: $data['terminalId'],
            virtualTerminalId: $data['virtualTerminalId'],
        );
    }
}
