<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Session implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $sessionId,
        readonly public string $terminalId,
        readonly public string $cashRegisterId,

        readonly public int $amount,
        readonly public int $tipAmount,
        readonly public int $eventId,

        readonly public string $currencyCode,
        readonly public ?string $merchantReference,
        readonly public ?string $customerTrns,

        readonly public ?string $authorizationId,
        readonly public ?string $transactionId,
        readonly public ?int $transactionTypeId,
        readonly public ?int $retrievalReferenceNumber,

        readonly public ?string $panEntryMode,
        readonly public ?string $applicationLabel,
        readonly public ?string $primaryAccountNumberMasked,
        readonly public ?string $transactionDateTime,

        readonly public bool $abortOperation,
        readonly public ?string $abortAckTime,
        readonly public bool $abortSuccess,

        readonly public ?string $loyaltyInfo,
        readonly public ?string $verificationMethod,
        readonly public ?string $tid,
        readonly public ?string $shortOrderCode,
        readonly public ?string $orderCode,

        readonly public ?string $message,

        readonly public int $installments,
        readonly public int $referenceNumber,

        readonly public bool $preauth,
        readonly public bool $success,

        readonly public int $isvAmount,
        readonly public ?string $isvMerchantId,
        readonly public ?string $isvSourceCode,
        readonly public ?string $isvMerchantSourceCode,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            sessionId: $data['sessionId'],
            terminalId: $data['terminalId'],
            cashRegisterId: $data['cashRegisterId'],

            amount: $data['amount'] ?? 0,
            tipAmount: $data['tipAmount'] ?? 0,
            eventId: $data['eventId'] ?? 0,

            currencyCode: $data['currencyCode'] ?? null,
            merchantReference: $data['merchantReference'] ?? null,
            customerTrns: $data['customerTrns'] ?? null,

            authorizationId: $data['authorizationId'] ?? null,
            transactionId: $data['transactionId'] ?? null,
            transactionTypeId: $data['transactionTypeId'] ?? null,
            retrievalReferenceNumber: $data['retrievalReferenceNumber'] ?? null,

            panEntryMode: $data['panEntryMode'] ?? null,
            applicationLabel: $data['applicationLabel'] ?? null,
            primaryAccountNumberMasked: $data['primaryAccountNumberMasked'] ?? null,
            transactionDateTime: $data['transactionDateTime'] ?? null,

            abortOperation: $data['abortOperation'],
            abortAckTime: $data['abortAckTime'] ?? null,
            abortSuccess: $data['abortSuccess'] ?? false,

            loyaltyInfo: $data['loyaltyInfo'] ?? null,
            verificationMethod: $data['verificationMethod'] ?? null,
            tid: $data['tid'] ?? null,
            shortOrderCode: $data['shortOrderCode'] ?? null,
            orderCode: $data['orderCode'] ?? null,

            message: $data['message'] ?? null,

            preauth: !!($data['preauth'] ?? null),
            success: !!($data['success'] ?? null),

            installments: $data['installments'] ?? 0,
            referenceNumber: $data['referenceNumber'] ?? 0,

            isvAmount: $data['isvDetails']['isvAmount'] ?? 0,
            isvMerchantId: $data['isvDetails']['isvMerchantId'] ?? null,
            isvSourceCode: $data['isvDetails']['isvSourceCode'] ?? null,
            isvMerchantSourceCode: $data['isvDetails']['isvMerchantSourceCode'] ?? null,
        );
    }
}
