<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class RefundTransaction implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public ?string $emv,
        readonly public ?float $amount,
        readonly public string $statusId,
        readonly public ?int $currencyCode,
        readonly public ?string $transactionId,
        readonly public ?int $referenceNumber,
        readonly public ?string $authorizationId,
        readonly public ?int $retrievalReferenceNumber,
        readonly public ?float $conversionRate,
        readonly public ?int $transactionTypeId,
        readonly public ?int $threeDSecureStatusId,
        readonly public ?int $errorCode,
        readonly public ?string $errorText,
        readonly public string $timeStamp,
        readonly public ?string $correlationId,
        readonly public int $eventId,
        readonly public bool $success,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            emv: $data["Emv"],
            amount: $data["Amount"],
            statusId: $data["StatusId"],
            currencyCode: $data["CurrencyCode"],
            transactionId: $data["TransactionId"],
            referenceNumber: $data["ReferenceNumber"],
            authorizationId: $data["AuthorizationId"],
            retrievalReferenceNumber: $data["RetrievalReferenceNumber"],
            threeDSecureStatusId: $data["ThreeDSecureStatusId"],
            errorCode: $data["ErrorCode"],
            errorText: $data["ErrorText"],
            timeStamp: $data["TimeStamp"],
            correlationId: $data["CorrelationId"],
            eventId: $data["EventId"],
            success: $data["Success"],
            transactionTypeId: $data["TransactionTypeId"],
            conversionRate: $data["ConversionRate"],
        );
    }
}
