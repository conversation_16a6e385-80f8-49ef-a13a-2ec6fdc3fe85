<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use App\Http\Integrations\VivaWallet\ValueObject\Invitation;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Account implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $accountId,
        readonly public ?string $merchantId,

        readonly public bool $verified,
        readonly public bool $acquiringEnabled,

        readonly public ?string $email,
        readonly public ?string $created,

        readonly public Invitation $invitation,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            accountId: $data['accountId'],
            merchantId: $data['merchantId'] ?? null,
            verified: !!($data['verified'] ?? null),
            acquiringEnabled: !!($data['acquiringEnabled'] ?? null),
            email: $data['email'] ?? null,
            created: $data['created'] ?? null,
            invitation: Invitation::fromArray($data['invitation'], $data['accountId']),
        );
    }
}
