<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use App\Http\Integrations\VivaWallet\Enums\OrderStatus;
use App\Http\Integrations\VivaWallet\ValueObject\Invitation;
use Carbon\Carbon;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Order implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $orderCode,
        readonly public string $sourceCode,

        readonly public float $tipAmount,

        readonly public int $maxInstallments,

        readonly public float $requestAmount,

        readonly public Carbon $expirationDate,

        readonly public OrderStatus $status,

        readonly public ?array $tags = [],

        readonly public ?string $requestLang = null,
        readonly public ?string $merchantTrns = null,
        readonly public ?string $customerTrns = null,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            orderCode: $data['OrderCode'],
            sourceCode: $data['SourceCode'] ?? null,
            maxInstallments: $data['MaxInstallments'] ?? 0,
            requestAmount: $data['RequestAmount'] ?? null,
            expirationDate: Carbon::parse($data['ExpirationDate'] ?? now()),
            status: OrderStatus::from($data['StateId']),
            tipAmount: $data['TipAmount'] ?? null,
            tags: $data['Tags'] ?? [],
            requestLang: $data['RequestLang'] ?? null,
            merchantTrns: $data['MerchantTrns'] ?? null,
            customerTrns: $data['CustomerTrns'] ?? null,
        );
    }
}
