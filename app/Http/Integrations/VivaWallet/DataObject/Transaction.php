<?php

namespace App\Http\Integrations\VivaWallet\DataObject;

use App\Http\Integrations\VivaWallet\Enums\TransactionStatus;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Transaction implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public ?string $cardNumber,
        readonly public ?string $email,
        readonly public int $amount,
        readonly public int $orderCode,
        readonly public string $statusId,
        readonly public ?string $fullName,
        readonly public string $insDate,
        readonly public int $currencyCode,
        readonly public ?string $customerTrns,
        readonly public ?string $merchantTrns,
        readonly public int $transactionTypeId,
        readonly public ?string $recurringSupport,
        readonly public ?int $totalInstallments,
        readonly public ?string $cardCountryCode,
        readonly public ?string $cardIssuingBank,
        readonly public ?int $currentInstallment,
        readonly public ?int $cardTypeId,
        readonly public ?int $digitalWalletId,
        readonly public TransactionStatus $status,
    ) {}


    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            cardNumber: $data['cardNumber'],
            email: $data['email'],
            amount: $data['amount'],
            orderCode: $data['orderCode'],
            statusId: $data['statusId'],
            fullName: $data['fullName'],
            insDate: $data['insDate'],
            currencyCode: $data['currencyCode'],
            customerTrns: $data['customerTrns'],
            merchantTrns: $data['merchantTrns'],
            transactionTypeId: $data['transactionTypeId'],
            recurringSupport: $data['recurringSupport'],
            totalInstallments: $data['totalInstallments'],
            cardCountryCode: $data['cardCountryCode'],
            cardIssuingBank: $data['cardIssuingBank'],
            currentInstallment: $data['currentInstallment'],
            cardTypeId: $data['cardTypeId'],
            digitalWalletId: $data['digitalWalletId'],
            status: TransactionStatus::from($data['statusId']),
        );
    }
}
