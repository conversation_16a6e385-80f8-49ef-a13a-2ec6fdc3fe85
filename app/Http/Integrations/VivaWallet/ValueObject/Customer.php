<?php

namespace App\Http\Integrations\VivaWallet\ValueObject;

class Customer
{
    final public function __construct(
        public readonly ?string $email = null,
        public readonly ?string $fullName = null,
        public readonly ?string $phone = null,

        // The country code of the customer.
        // If left unspecified it is filled with the country code of the merchant.
        // Consists of two-letter ISO 3166-1 alpha-2 country code.
        public readonly ?string $countryCode = null,

        // string
        // The language (culture info) of the order, which determines the language that Smart Checkout will appear in, and so on.
        // If the requestLang parameter is empty, the language will be based on the countryCode of the merchant. For all possible values, please see the 'Allowed Values' list below
        // Consists of two-letter ISO 639-1 language code combined with two-letter ISO 3166-1 alpha-2 country code.
        // Allowed Values:
        // Bulgarian > bg-BG
        // Croatian > hr-HR
        // Czech (Czechia) > cs-CZ
        // Danish > da-DK
        // Dutch (Netherlands) > nl-NL
        // Dutch (Belgium)> nl-BE
        // English (United Kingdom) > en-GB
        // English (United States) > en-US
        // Finnish > fi-FI
        // French (Belgium)> fr-BE
        // French (France) > fr-FR
        // German > de-DE
        // Greek > el-GR
        // Hungarian > hu-HU
        // Italian > it-IT
        // Polish > pl-PL
        // Portuguese > pt-PT
        // Romanian > ro-RO
        // Russian > ru-RU
        // Spanish > es-ES
        // Swedish > sv-SE
        public readonly ?string $requestLang = null,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new static(
            email: isset($data['email']) ? $data['email'] : null,
            fullName: isset($data['fullName']) ? $data['fullName'] : null,
            phone: isset($data['phone']) ? $data['phone'] : null,
            countryCode: isset($data['countryCode']) ? $data['countryCode'] : null,
            requestLang: isset($data['requestLang']) ? $data['requestLang'] : null,
        );
    }
}
