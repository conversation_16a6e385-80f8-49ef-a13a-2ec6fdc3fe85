<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\DataObject\Order;
use App\Http\Integrations\VivaWallet\DataObject\RefundTransaction;
use App\Http\Integrations\VivaWallet\Requests\CancelPaymentOrder;
use App\Http\Integrations\VivaWallet\Requests\CreatePaymentOrder;
use App\Http\Integrations\VivaWallet\Requests\RefundPaymentOrder;
use App\Http\Integrations\VivaWallet\Requests\RetrievePaymentOrder;
use App\Http\Integrations\VivaWallet\ValueObject\Customer;
use App\Http\Integrations\VivaWallet\VivaWalletConnector;
use Saloon\Contracts\Connector;

/**
 * @property VivaWalletConnector $connector
 */
class PaymentOrderResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected string $merchantId,
    ) {
    }

    /**
     * Create a new transaction.
     *
     *
     * @return string
     */
    public function create(
        // integer <int64> >= 30
        // The amount associated with this payment order. Must be a positive, non-zero number.
        // The amount will be in the currency of the merchant account using the currency's smallest unit of measurement (e.g. cents of a euro).
        int $amount,

        // Customer
        // Information about the customer
        Customer $customer,


        // string [ 1 .. 2048 ]
        // Default: null
        // This can be either an ID or a short description that helps you uniquely identify the transaction in the viva banking app.
        // For example, this can be the customer order reference number.
        // After logging in, go to Sales > Sales Transactions and find the transaction.
        // Click on the Info button against the item to display the Transaction Details dialog box.
        // The contents of the merchantTrns field will be displayed on the line below the timestamp information.
        string $merchantTrns,


        // Default: "Gets the merchant currency"
        // The numeric code of the order's currency as defined in ISO 4712[https://en.wikipedia.org/wiki/ISO_4217].
        string $currencyCode,

        // integer or null <int64>
        // Default: null
        // The amount which will be paid out to the ISV partner.
        ?int $isvAmount = 0,

        // string <= 2048
        // This optional parameter adds a friendly description to the payment order that you want to display to
        // the customer on the payment form. It should be a short description of the items/services being purchased.
        ?string $customerTrns = '',

        // string <= 13
        // A descriptor tailored to the transaction, ensuring clarity and recognition for the buyer on their bank statement and 3DS verification page.
        // This parameter generates a dynamic descriptor for bank statements when processing a payment through smart checkout.
        ?string $dynamicDescriptor = '',

        // seconds
        // The time given to the customer to complete the payment.
        ?int $paymentTimeout = 1800,


        // Default: false
        // If you wish to create a payment order, and then send out an email to the customer to request payment,
        // rather than immediately redirect the customer to the payment page to pay now, set the value to true,
        ?bool $paymentNotification = false,

        // boolean
        // Default: false
        // This will hold the selected amount as unavailable (without the customer being charged) for a period of time.
        // No email receipt is sent out from us in this case as it is not a charge.
        ?bool $preauth = false,

        // Default: false
        // If this parameter is set to true, then any amount specified in the payment order is ignored (although still mandatory),
        // and the customer is asked to indicate the amount they will pay.
        // Note that if set to true, there will not be the option to pay with certain payment methods.
        ?bool $disableExactAmount = false,

        // Default: true
        // If this parameter is set to true, the customer will not have the option to pay in cash at a Viva Spot,
        // and the checkout page will not display the Cash (Viva Spot) and e-banking (ΔΙΑΣ) options.
        ?bool $disableCash = true,

        // Default: false
        // If this parameter is set to true, the customer will not have the option to pay using their Viva personal account (wallet),
        // and the checkout page will not display the Viva Wallet option.
        ?bool $disableWallet = true,

        // Default: 'Default', case-sensitive
        // This is the code of the payment source associated with the merchant.
        // If the merchant has defined multiple payment sources in their account,
        // you need to define the sourceCode parameter when creating the payment order,
        // so that the system selects the appropriate payment source.
        ?string $sourceCode = null,

        // Default: null, array<string>
        // This is the code of the payment source associated with the merchant.
        // If the merchant has defined multiple payment sources in their account,
        // you need to define the sourceCode parameter when creating the payment order,
        // so that the system selects the appropriate payment source.
        ?array $tags = null,

        // Default: 'Default', case-sensitive
        // This is the code of the payment source associated with the ISV partner (not with the merchant).
        // If the ISV has defined multiple payment sources(navigate to the STORES menu to see all payment sources) in their account,
        // you need to define the resellerSourceCode parameter when creating the payment order,
        // so that the system selects the appropriate payment source.
        ?string $resellerSourceCode = null,

        ?int $tipAmount = 0,

        ?string $color = '5551CE',
    ): string {
        $response = $this->connector
            ->send(new CreatePaymentOrder(
                merchantId: $this->merchantId,
                amount: $amount,
                currencyCode: $currencyCode,
                merchantTrns: $merchantTrns,
                customerTrns: $customerTrns ?? '',
                tipAmount: $tipAmount,
                customer: $customer,
                dynamicDescriptor: $dynamicDescriptor,
                paymentTimeout: $paymentTimeout,
                preauth: $preauth,
                paymentNotification: $paymentNotification,
                disableExactAmount: $disableExactAmount,
                disableCash: $disableCash,
                disableWallet: $disableWallet,
                isvAmount: $isvAmount,
                sourceCode: $sourceCode,
                tags: $tags,
                resellerSourceCode: $resellerSourceCode,
            ));

        $object = $response->dto();

        return $object->orderCode;
    }

    /**
     * Return  the payment order url.
     *
     *
     * @return string
     */
    public function returnOrderUrl(
        string $orderCode,
        ?string $color = '5551CE',
    ): string {
        $url = $this->connector->getUrl();

        return "$url/web2?ref={$orderCode}&color={$color}";
    }

    /**
     * Cancel the payment order.
     *
     *
     * @return string
     */
    public function cancel(
        string $orderCode,
    ): string {
        $response = $this->connector
            ->withBasicAuthCredentials("{$this->connector->resellerId}:{$this->merchantId}")
            ->authenticateWithBasicAuth()
            ->send((new CancelPaymentOrder(
                orderCode: $orderCode,
            ))->setBaseUrl($this->connector->getUrl()));

        return $response->dto();
    }

    /**
     * Refund the transaction.
     *
     *
     * @return RefundTransaction
     */
    public function refund(
        string $transactionId,
        int $amount,
    ): RefundTransaction {
        $response = $this->connector
            ->withBasicAuthCredentials("{$this->connector->resellerId}:{$this->merchantId}")
            ->authenticateWithBasicAuth()
            ->send((new RefundPaymentOrder(
                transactionId: $transactionId,
                amount: $amount,
            ))->setBaseUrl($this->connector->getUrl()));

        return $response->dto();
    }

    /**
     * Find Order.
     *
     *
     * @return Order
     */
    public function find(
        string $orderCode
    ): Order {
        $response = $this->connector
            ->withBasicAuthCredentials("{$this->connector->resellerId}:{$this->merchantId}")
            ->authenticateWithBasicAuth()
            ->send((new RetrievePaymentOrder(
                orderCode: $orderCode,
            ))->setBaseUrl($this->connector->getUrl()));

        return $response->dto();
    }
}
