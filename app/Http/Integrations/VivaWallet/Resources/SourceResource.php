<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\Requests\CreateSource;
use App\Http\Integrations\VivaWallet\VivaWalletConnector;
use Saloon\Contracts\Connector;

/**
 * @property VivaWalletConnector $connector
 */
class SourceResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected string $merchantId,
    ) {
    }

    /**
     * Create a new transaction.
     *
     *
     * @return string
     */
    public function create(
        string $domain,
        bool $isSecure,
        string $name,
        string $pathFail,
        string $pathSuccess,
        string $sourceCode,
    ): string {
        $response = $this->connector
            ->withBasicAuthCredentials("{$this->connector->resellerId}:{$this->merchantId}")
            ->authenticateWithBasicAuth()
            ->send((new CreateSource(
                domain: $domain,
                isSecure: $isSecure,
                name: $name,
                pathFail: $pathFail,
                pathSuccess: $pathSuccess,
                sourceCode: $sourceCode,
            ))->setBaseUrl($this->connector->getUrl()));

        return $response->dto();
    }
}
