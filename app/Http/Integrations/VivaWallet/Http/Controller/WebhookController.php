<?php

namespace App\Http\Integrations\VivaWallet\Http\Controller;

use App\Http\Controllers\Controller;
use App\Http\Integrations\POSPayment\Facades\POSPayment;
use App\Http\Integrations\VivaWallet\Events\AccountConnected;
use App\Http\Integrations\VivaWallet\Events\TransactionFailed;
use App\Http\Integrations\VivaWallet\Events\TransactionPaymentCreated;
use App\Http\Integrations\VivaWallet\Events\TransactionReversalCreated;
use App\Http\Integrations\VivaWallet\Events\WebhookEvent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    /**
     * Verify a webhook.
     *
     * @see https://developer.vivawallet.com/webhooks-for-payments/#generate-a-webhook-verification-key
     *
     * @throws GuzzleException
     * @throws VivaException
     */
    public function verify(): JsonResponse
    {
        $token = POSPayment::webhook()->getToken();

        return response()->json($token);
    }

    /**
     * Handle requests from Viva Wallet.
     *
     * @see https://developer.vivawallet.com/webhooks-for-payments/#handle-requests-from-viva-wallet
     */
    public function handle(Request $request): JsonResponse
    {
        Log::channel('slack')->critical("Viva Webhook Method: " .  $request->method());
        Log::channel('slack')->critical("Viva Webhook Payload: ",  $request->all());

        if ($request->isMethod('GET')) {
            return $this->verify();
        }

        /** @phpstan-ignore-next-line */
        $event = WebhookEvent::create($request->all());

        event($event);

        match ($event->EventData::class) {
            TransactionPaymentCreated::class => event($event->EventData),
            TransactionFailed::class => event($event->EventData),
            TransactionReversalCreated::class => event($event->EventData),
            AccountConnected::class => event($event->EventData),
            default => null,
        };

        return response()->json();
    }
}
