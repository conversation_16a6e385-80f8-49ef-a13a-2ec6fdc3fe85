<?php

namespace App\Http\Integrations\VivaWallet\Enums;

/**
 * @see https://developer.viva.com/apis-for-payments/payment-api/#tag/Payments/paths/~1api~1orders~1{orderCode}/get
 */
enum OrderStatus: int
{
    /** The order is in progress (PAYMENT PENDING) */
    case Pending = 0;

    /** The order was cancelled by the customer */
    case Cancelled = 2;

    /** The order has expired */
    case Expired = 1;

    /** The order has been paid */
    case Paid = 3;
}
