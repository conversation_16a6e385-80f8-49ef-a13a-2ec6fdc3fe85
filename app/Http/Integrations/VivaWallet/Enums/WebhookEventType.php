<?php

namespace App\Http\Integrations\VivaWallet\Enums;

enum WebhookEventType: int
{
    /** A customer’s payment has been successful */
    case TransactionPaymentCreated = 1796;

    /** A customer’s payment failed (but the customer may retry and the customer’s payment may - eventually - be successful) */
    case TransactionFailed = 1798;

    /** A commission payment has been withdrawn from your account by Viva Wallet */
    case TransactionPriceCalculated = 1799;

    /** A customer refund has been successfully actioned */
    case TransactionReversalCreated = 1797;

    /** A customer's account connected */
    case AccountConnected = 8193;

    /** A customer's account verification status changed */
    case CommandBankTransferCreated = 8194;
}
