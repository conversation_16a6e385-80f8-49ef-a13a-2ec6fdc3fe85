<?php

namespace App\Http\Integrations\VivaWallet\DataFactories;

use App\Http\Integrations\VivaWallet\DataObject\Device;
use Illuminate\Support\Collection;

final class DeviceFactory
{
    public static function new(array $attributes): Device
    {
        return (new self)->make(
            attributes: $attributes,
        );
    }

    public function make(array $attributes): Device
    {
        return new Device(
            merchantId: strval(data_get($attributes, 'merchantId')),
            statusId: strval(data_get($attributes, 'statusId')),
            sourceCode: strval(data_get($attributes, 'sourceCode')),
            terminalId: strval(data_get($attributes, 'terminalId')),
            virtualTerminalId: strval(data_get($attributes, 'virtualTerminalId')),
        );
    }

    public static function collection(array $devices): Collection
    {
        return (new Collection(
            items: $devices,
        ))->map(fn ($device): Device => self::new(attributes: $device));
    }
}
