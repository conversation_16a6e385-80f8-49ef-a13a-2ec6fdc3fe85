<?php

namespace App\Http\Integrations\VivaWallet\DataFactories;

use App\Http\Integrations\VivaWallet\DataObject\TransactionDetail;
use Illuminate\Support\Collection;

final class TransactionDetailFactory
{
    public static function new(array $attributes): TransactionDetail
    {
        return (new self)->make(
            attributes: $attributes,
        );
    }

    public function make(array $attributes): TransactionDetail
    {
        return TransactionDetail::fromArray($attributes);
    }

    public static function collection(array $transactions): Collection
    {
        return (new Collection(
            items: $transactions,
        ))->map(fn ($transaction): TransactionDetail => self::new(attributes: $transaction));
    }
}
