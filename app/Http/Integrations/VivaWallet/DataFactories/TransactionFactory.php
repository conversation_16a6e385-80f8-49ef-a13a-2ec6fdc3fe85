<?php

namespace App\Http\Integrations\VivaWallet\DataFactories;

use App\Http\Integrations\VivaWallet\DataObject\Transaction;
use Illuminate\Support\Collection;

final class TransactionFactory
{
    public static function new(array $attributes): Transaction
    {
        return (new self)->make(
            attributes: $attributes,
        );
    }

    public function make(array $attributes): Transaction
    {
        return new Transaction(
            cardNumber: strval(data_get($attributes, 'cardNumber')),
            email:strval(data_get($attributes,'email')),
            amount:strval(data_get($attributes,'amount')),
            orderCode:strval(data_get($attributes,'orderCode')),
            statusId:strval(data_get($attributes,'statusId')),
            fullName:strval(data_get($attributes,'fullName')),
            insDate:strval(data_get($attributes,'insDate')),
            currencyCode:strval(data_get($attributes,'currencyCode')),
            customerTrns:strval(data_get($attributes,'customerTrns')),
            merchantTrns:strval(data_get($attributes,'merchantTrns')),
            transactionTypeId:strval(data_get($attributes,'transactionTypeId')),
            recurringSupport:strval(data_get($attributes,'recurringSupport')),
            totalInstallments:strval(data_get($attributes,'totalInstallments')),
            cardCountryCode:strval(data_get($attributes,'cardCountryCode')),
            cardIssuingBank:strval(data_get($attributes,'cardIssuingBank')),
            currentInstallment:strval(data_get($attributes,'currentInstallment')),
            cardTypeId:strval(data_get($attributes,'cardTypeId')),
            digitalWalletId:strval(data_get($attributes,'digitalWalletId')),
        );
    }

    public static function collection(array $transactionDetails): Collection
    {
        return (new Collection(
            items: $transactionDetails,
        ))->map(fn ($transaction): Transaction => self::new(attributes: $transaction));
    }
}
