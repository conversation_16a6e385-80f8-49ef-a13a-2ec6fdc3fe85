<?php

namespace App\Http\Integrations\VivaWallet\Events;

use App\Http\Integrations\VivaWallet\Enums\TransactionStatus;
use App\Http\Integrations\VivaWallet\Enums\TransactionType;

/** @see https://developer.vivawallet.com/webhooks-for-payments/transaction-payment-created/ */
class TransactionPaymentCreated
{
    public function __construct(
        public readonly bool $Moto,
        public readonly ?string $Email,
        public readonly ?string $Phone,
        public readonly ?string $BankId,
        public readonly bool $Systemic,
        public readonly bool $Switching,
        public readonly ?string $ParentId,
        public readonly float $Amount,
        public readonly ?string $ChannelId,
        public readonly ?int $TerminalId,
        public readonly ?string $MerchantId,
        public readonly ?string $OrderCode,
        public readonly ?string $ProductId,
        public readonly ?string $FullName,
        public readonly ?string $ResellerId,
        public readonly ?string $InsDate,
        public readonly float $TotalFee,
        public readonly ?string $CardUniqueReference,
        public readonly ?string $CardToken,
        public readonly ?string $CardNumber,
        public readonly float $TipAmount,
        public readonly ?string $SourceCode,
        public readonly ?string $SourceName,
        public readonly ?float $Latitude,
        public readonly ?float $Longitude,
        public readonly ?string $CompanyName,
        public readonly ?string $CompanyTitle,
        public readonly ?string $PanEntryMode,
        public readonly ?string $ResponseCode,
        public readonly ?string $CurrencyCode,
        public readonly ?string $OrderCulture,
        public readonly ?string $CustomerTrns,
        public readonly bool $IsManualRefund,
        public readonly ?string $TargetPersonId,
        public readonly ?string $TargetWalletId,
        public readonly bool $LoyaltyTriggered,
        public readonly ?int $TotalInstallments,
        public readonly ?string $CardCountryCode,
        public readonly ?string $CardIssuingBank,
        public readonly ?int $RedeemedAmount,
        public readonly ?int $ClearanceDate,
        public readonly ?int $CurrentInstallment,
        /** @var string[] */
        public readonly array $Tags,
        public readonly ?string $BillId,
        public readonly ?string $ResellerSourceCode,
        public readonly ?string $ResellerSourceName,
        public readonly ?string $ResellerCompanyName,
        public readonly ?string $ResellerSourceAddress,
        public readonly ?string $CardExpirationDate,
        public readonly ?string $RetrievalReferenceNumber,
        /** @var string[] */
        public readonly array $AssignedMerchantUsers,
        /** @var string[] */
        public readonly array $AssignedResellerUsers,
        public readonly ?int $CardTypeId,
        public readonly ?int $DigitalWalletId,
        public readonly ?string $ResponseEventId,
        public readonly ?string $ElectronicCommerceIndicator,
        public readonly ?string $BinId,
        public readonly ?string $Ucaf,
        public readonly ?string $DualMessage,
        public readonly ?string $AcquirerApproved,
        public readonly ?string $AuthorizationId,
        public readonly ?string $OrderServiceId,
        public readonly ?string $ConnectedAccountId,
        public readonly ?string $MerchantCategoryCode,
        public readonly ?string $ServiceId,


        public readonly ?string $MerchantTrns,
        public readonly int $ReferenceNumber,
        public readonly string $TransactionId,
        public readonly TransactionStatus $StatusId,
        public readonly TransactionType $TransactionTypeId,

    ) {
    }

    /** @phpstan-param  TransactionPaymentCreatedArray  $attributes */
    public static function create(array $attributes): self
    {
        return new self(
            Moto: $attributes['Moto'] ?? null,
            Email: $attributes['Email'] ?? null,
            Phone: $attributes['Phone'] ?? null,
            BankId: $attributes['BankId'] ?? null,
            Systemic: $attributes['Systemic'] ?? null,
            Switching: $attributes['Switching'] ?? null,
            ParentId: $attributes['ParentId'] ?? null,
            Amount: $attributes['Amount'] ?? null,
            ChannelId: $attributes['ChannelId'] ?? null,
            TerminalId: $attributes['TerminalId'] ?? null,
            MerchantId: $attributes['MerchantId'] ?? null,
            OrderCode: $attributes['OrderCode'] ?? null,
            ProductId: $attributes['ProductId'] ?? null,
            FullName: $attributes['FullName'] ?? null,
            ResellerId: $attributes['ResellerId'] ?? null,
            InsDate: $attributes['InsDate'] ?? null,
            TotalFee: $attributes['TotalFee'] ?? null,
            CardUniqueReference: $attributes['CardUniqueReference'] ?? null,
            CardToken: $attributes['CardToken'] ?? null,
            CardNumber: $attributes['CardNumber'] ?? null,
            TipAmount: $attributes['TipAmount'] ?? null,
            SourceCode: $attributes['SourceCode'] ?? null,
            SourceName: $attributes['SourceName'] ?? null,
            Latitude: $attributes['Latitude'] ?? null,
            Longitude: $attributes['Longitude'] ?? null,
            CompanyName: $attributes['CompanyName'] ?? null,
            CompanyTitle: $attributes['CompanyTitle'] ?? null,
            PanEntryMode: $attributes['PanEntryMode'] ?? null,
            ResponseCode: $attributes['ResponseCode'] ?? null,
            CurrencyCode: $attributes['CurrencyCode'] ?? null,
            OrderCulture: $attributes['OrderCulture'] ?? null,
            CustomerTrns: $attributes['CustomerTrns'] ?? null,
            IsManualRefund: $attributes['IsManualRefund'] ?? null,
            TargetPersonId: $attributes['TargetPersonId'] ?? null,
            TargetWalletId: $attributes['TargetWalletId'] ?? null,
            LoyaltyTriggered: $attributes['LoyaltyTriggered'] ?? null,
            TotalInstallments: $attributes['TotalInstallments'] ?? null,
            CardCountryCode: $attributes['CardCountryCode'] ?? null,
            CardIssuingBank: $attributes['CardIssuingBank'] ?? null,
            RedeemedAmount: $attributes['RedeemedAmount'] ?? null,
            ClearanceDate: $attributes['ClearanceDate'] ?? null,
            CurrentInstallment: $attributes['CurrentInstallment'] ?? null,
            Tags: $attributes['Tags'] ?? null,
            BillId: $attributes['BillId'] ?? null,
            ResellerSourceCode: $attributes['ResellerSourceCode'] ?? null,
            ResellerSourceName: $attributes['ResellerSourceName'] ?? null,
            ResellerCompanyName: $attributes['ResellerCompanyName'] ?? null,
            ResellerSourceAddress: $attributes['ResellerSourceAddress'] ?? null,
            CardExpirationDate: $attributes['CardExpirationDate'] ?? null,
            RetrievalReferenceNumber: $attributes['RetrievalReferenceNumber'] ?? null,
            AssignedMerchantUsers: $attributes['AssignedMerchantUsers'] ?? null,
            AssignedResellerUsers: $attributes['AssignedResellerUsers'] ?? null,
            CardTypeId: $attributes['CardTypeId'] ?? null,
            DigitalWalletId: $attributes['DigitalWalletId'] ?? null,
            ResponseEventId: $attributes['ResponseEventId'] ?? null,
            ElectronicCommerceIndicator: $attributes['ElectronicCommerceIndicator'] ?? null,
            BinId: $attributes['BinId'] ?? null,
            Ucaf: $attributes['Ucaf'] ?? null,
            DualMessage: $attributes['DualMessage'] ?? null,
            AcquirerApproved: $attributes['AcquirerApproved'] ?? null,
            AuthorizationId: $attributes['AuthorizationId'] ?? null,
            OrderServiceId: $attributes['OrderServiceId'] ?? null,
            ConnectedAccountId: $attributes['ConnectedAccountId'] ?? null,
            MerchantCategoryCode: $attributes['MerchantCategoryCode'] ?? null,
            ServiceId: $attributes['ServiceId'] ?? null,
            MerchantTrns:  $attributes['MerchantTrns'] ?? null,
            ReferenceNumber: $attributes['ReferenceNumber'] ?? null,
            TransactionId: $attributes['TransactionId'] ?? null,
            StatusId: TransactionStatus::from($attributes['StatusId']),
            TransactionTypeId: TransactionType::from($attributes['TransactionTypeId']),
        );
    }
}
