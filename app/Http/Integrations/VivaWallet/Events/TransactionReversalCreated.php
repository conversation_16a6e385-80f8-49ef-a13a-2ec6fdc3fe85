<?php

namespace App\Http\Integrations\VivaWallet\Events;

use App\Http\Integrations\VivaWallet\Enums\TransactionStatus;
use App\Http\Integrations\VivaWallet\Enums\TransactionType;

/** @see https://developer.vivawallet.com/webhooks-for-payments/transaction-reversal-created/ */
class TransactionReversalCreated
{
    public function __construct(
        public readonly bool $Moto,
        public readonly ?string $Email,
        public readonly ?string $Phone,
        public readonly ?string $BankId,
        public readonly bool $Systemic,
        public readonly bool $Switching,
        public readonly ?string $ParentId,
        public readonly float $Amount,
        public readonly ?string $ChannelId,

        public readonly ?int $BinId,

        public readonly ?int $TerminalId,
        public readonly ?string $MerchantId,
        public readonly ?string $OrderCode,
        public readonly ?string $ProductId,
        public readonly TransactionStatus $StatusId,
        public readonly ?string $FullName,
        public readonly ?string $ResellerId,
        public readonly bool $DualMessage,
        public readonly ?string $InsDate,
        public readonly ?int $TotalFee,
        public readonly ?string $CardToken,
        public readonly ?string $CardNumber,
        public readonly ?int $DigitalWalletId,
        public readonly ?int $TipAmount,
        public readonly ?string $SourceCode,
        public readonly ?string $SourceName,

        public readonly ?string $Latitude,
        public readonly ?string $Longitude,
        public readonly ?string $CompanyName,
        public readonly string $TransactionId,
        public readonly ?string $CompanyTitle,
        public readonly ?string $PanEntryMode,
        public readonly int $ReferenceNumber,

        public readonly ?string $ResponseCode,
        public readonly ?string $CurrencyCode,
        public readonly ?string $OrderCulture,
        public readonly ?string $MerchantTrns,
        public readonly ?string $CustomerTrns,
        public readonly bool $IsManualRefund,
        public readonly ?int $TargetPersonId,
        public readonly ?int $TargetWalletId,
        public readonly bool $LoyaltyTriggered,
        public readonly TransactionType $TransactionTypeId,
        public readonly ?int $TotalInstallments,
        public readonly ?string $CardCountryCode,
        public readonly ?string $CardIssuingBank,
        public readonly ?int $RedeemedAmount,
        public readonly ?string $ClearanceDate,
        /** @var string[] */
        public readonly array $Tags,
        public readonly ?string $BillId,
        public readonly ?string $ResellerSourceCode,
        public readonly ?string $ResellerSourceName,
        public readonly ?string $ResellerCompanyName,
        public readonly ?string $ResellerSourceAddress,
        public readonly ?string $CardExpirationDate,
        public readonly ?string $RetrievalReferenceNumber,
        /** @var string[] */
        public readonly array $AssignedMerchantUsers,
        /** @var string[] */
        public readonly array $AssignedResellerUsers,
        public readonly ?int $CardTypeId,
        public readonly ?string $CardUniqueReference,
        public readonly ?string $ResponseEventId,
        public readonly ?string $ElectronicCommerceIndicator,
        public readonly ?string $ServiceId,

        public readonly ?string $Ucaf,
        public readonly bool $AcquirerApproved,
        public readonly ?string $AuthorizationId,
        public readonly ?int $ConnectedAccountId,
        public readonly ?int $CurrentInstallment,
        public readonly ?int $MerchantCategoryCode,
        public readonly ?int $OrderServiceId,
    ) {
    }

    public static function create(array $attributes): self
    {
        return new self(

            StatusId: TransactionStatus::from($attributes['StatusId']),
            TransactionTypeId: TransactionType::from($attributes['TransactionTypeId']),
            Moto: $attributes['Moto'] ?? null,
            Email: $attributes['Email'] ?? null,
            Phone: $attributes['Phone'] ?? null,
            BankId: $attributes['BankId'] ?? null,
            Systemic: $attributes['Systemic'] ?? null,
            Switching: $attributes['Switching'] ?? null,
            ParentId: $attributes['ParentId'] ?? null,
            Amount: $attributes['Amount'] ?? null,
            ChannelId: $attributes['ChannelId'] ?? null,
            BinId: $attributes['BinId'] ?? null,
            TerminalId: $attributes['TerminalId'] ?? null,
            MerchantId: $attributes['MerchantId'] ?? null,
            OrderCode: $attributes['OrderCode'] ?? null,
            ProductId: $attributes['ProductId'] ?? null,
            FullName: $attributes['FullName'] ?? null,
            ResellerId: $attributes['ResellerId'] ?? null,
            DualMessage: $attributes['DualMessage'] ?? null,
            InsDate: $attributes['InsDate'] ?? null,
            TotalFee: $attributes['TotalFee'] ?? null,



            CardToken: $attributes['CardToken'] ?? null,
            CardNumber: $attributes['CardNumber'] ?? null,
            DigitalWalletId: $attributes['DigitalWalletId'] ?? null,
            TipAmount: $attributes['TipAmount'] ?? null,
            SourceCode: $attributes['SourceCode'] ?? null,


            SourceName: $attributes['SourceName'] ?? null,
            Latitude: $attributes['Latitude'] ?? null,
            Longitude: $attributes['Longitude'] ?? null,
            CompanyName: $attributes['CompanyName'] ?? null,
            TransactionId: $attributes['TransactionId'] ?? null,
            CompanyTitle: $attributes['CompanyTitle'] ?? null,
            PanEntryMode: $attributes['PanEntryMode'] ?? null,
            ReferenceNumber: $attributes['ReferenceNumber'] ?? null,
            ResponseCode: $attributes['ResponseCode'] ?? null,
            CurrencyCode: $attributes['CurrencyCode'] ?? null,
            OrderCulture: $attributes['OrderCulture'] ?? null,
            MerchantTrns: $attributes['MerchantTrns'] ?? null,
            CustomerTrns: $attributes['CustomerTrns'] ?? null,
            IsManualRefund: $attributes['IsManualRefund'] ?? null,
            TargetPersonId: $attributes['TargetPersonId'] ?? null,
            TargetWalletId: $attributes['TargetWalletId'] ?? null,
            LoyaltyTriggered: $attributes['LoyaltyTriggered'] ?? null,
            TotalInstallments: $attributes['TotalInstallments'] ?? null,

            CardCountryCode: $attributes['CardCountryCode'] ?? null,
            CardIssuingBank: $attributes['CardIssuingBank'] ?? null,

            RedeemedAmount: $attributes['RedeemedAmount'] ?? null,
            ClearanceDate: $attributes['ClearanceDate'] ?? null,
            Tags: $attributes['Tags'] ?? null,
            BillId: $attributes['BillId'] ?? null,
            ResellerSourceCode: $attributes['ResellerSourceCode'] ?? null,
            ResellerSourceName: $attributes['ResellerSourceName'] ?? null,
            ResellerCompanyName: $attributes['ResellerCompanyName'] ?? null,
            ResellerSourceAddress: $attributes['ResellerSourceAddress'] ?? null,
            CardExpirationDate: $attributes['CardExpirationDate'] ?? null,
            RetrievalReferenceNumber: $attributes['RetrievalReferenceNumber'] ?? null,
            AssignedMerchantUsers: $attributes['AssignedMerchantUsers'] ?? null,
            AssignedResellerUsers: $attributes['AssignedResellerUsers'] ?? null,
            CardTypeId: $attributes['CardTypeId'] ?? null,
            CardUniqueReference: $attributes['CardUniqueReference'] ?? null,
            ResponseEventId: $attributes['ResponseEventId'] ?? null,
            ElectronicCommerceIndicator: $attributes['ElectronicCommerceIndicator'] ?? null,
            ServiceId: $attributes['ServiceId'] ?? null,
            Ucaf: $attributes['Ucaf'] ?? null,
            AcquirerApproved: $attributes['AcquirerApproved'] ?? null,
            AuthorizationId: $attributes['AuthorizationId'] ?? null,
            ConnectedAccountId: $attributes['ConnectedAccountId'] ?? null,
            CurrentInstallment: $attributes['CurrentInstallment'] ?? null,
            MerchantCategoryCode: $attributes['MerchantCategoryCode'] ?? null,
            OrderServiceId: $attributes['OrderServiceId'] ?? null,
        );
    }
}
