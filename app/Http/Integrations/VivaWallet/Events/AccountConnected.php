<?php

namespace App\Http\Integrations\VivaWallet\Events;


/** @see https://developer.vivawallet.com/webhooks-for-payments/account-connected/ */
class AccountConnected
{
    public function __construct(
        public readonly ?string $PersonId,
        public readonly ?int $WalletId,
        public readonly ?string $PlatformPersonId,
        public readonly string $ConnectedAccountId
    ) {
    }

    public static function create(array $attributes): self
    {
        return new self(
            PersonId: $attributes['PersonId'] ?? null,
            WalletId: $attributes['WalletId'] ?? null,
            PlatformPersonId: $attributes['PlatformPersonId'] ?? null,
            ConnectedAccountId: $attributes['ConnectedAccountId'] ?? null
        );
    }
}
