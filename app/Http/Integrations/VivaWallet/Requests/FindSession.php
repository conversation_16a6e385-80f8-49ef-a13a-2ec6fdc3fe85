<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\DataObject\Session;
use App\Http\Integrations\VivaWallet\Exceptions\SessionProcessingException;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\RequestException;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\NotFoundException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class FindSession extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/ecr/isv/v1/sessions/{$this->id}";
    }

    public function __construct(
        protected string $id,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function createDtoFromResponse(Response $response): Session
    {
        $exception = $this->getRequestException($response, null);
        if ($exception) {
            throw $exception;
        }

        return Session::fromResponse($response);
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {

        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, $response->json()['message'] ?? __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        if ($response->status() == 202) {
            return new SessionProcessingException($response, $response->json()['detail'] ?? __('pos_strings.viva.session_is_being_processed'), $response->status(), $senderException);
        }
        if ($response->status() == 400) {
            return new RequestException($response, $response->json()['message'] ?? __('pos_strings.viva.request_error'), $response->status(), $senderException);
        }
        if ($response->status() == 401) {
            return new UnauthorizedException($response, $response->json()['detail'] ?? __('pos_strings.viva.unauthorized'), $response->status(), $senderException);
        }
        if ($response->status() == 404) {
            return new NotFoundException($response, $response->json()['detail'] ?? __('pos_strings.viva.session_not_found'), $response->status(), $senderException);
        }
        return null;
    }
}
