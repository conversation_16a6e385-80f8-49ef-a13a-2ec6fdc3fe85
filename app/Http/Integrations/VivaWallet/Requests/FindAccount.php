<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\DataObject\Account;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class FindAccount extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/isv/v1/accounts/{$this->id}";
    }

    public function __construct(
        protected string $id,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function createDtoFromResponse(Response $response): Account
    {
        return Account::fromResponse($response);
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if($response->status() >= 500) {
            return new InternalServerErrorException($response, $response->json()['message'] ?? __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        return null;
    }
}
