<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\Exceptions\RefundInvalidParentSessionIdException;
use App\Http\Integrations\VivaWallet\Exceptions\RefundSessionIdAlreadyExistException;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Exceptions\Request\RequestException;

use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class RefundTransaction extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "ecr/isv/v1/transactions:refund";
    }

    public function __construct(
        protected string $merchantId,
        protected string $sessionId,
        protected string $terminalId,
        protected string $cashRegisterId,
        protected string $parentSessionId,
        protected int $amount,
        protected string $currencyCode,
        protected string $merchantReference,
        protected ?string $customerTrns = null,

    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {

        return [
            'sessionId' => $this->sessionId,
            'terminalId' => $this->terminalId,
            'cashRegisterId' => $this->cashRegisterId,
            'parentSessionId' => $this->parentSessionId,
            'amount' => $this->amount,
            'currencyCode' => $this->currencyCode,
            'merchantReference' => $this->merchantReference,
            'customerTrns' => $this->customerTrns,
             "isvDetails" => [
                "terminalMerchantId" => $this->merchantId,
            ],

        ];
    }

    public function createDtoFromResponse(Response $response): string
    {
        $exception = $this->getRequestException($response, null);
        if ($exception) {
            throw $exception;
        }

        return $response->status() == 200 ? __('pos_strings.viva.refund_completed') : __('pos_strings.viva.error');
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if ($response->status() == 400) {
            return new RequestException($response, $response->json()['message'] ?? __('pos_strings.viva.request_error'), $response->status(), $senderException);
        }
        if ($response->status() == 401) {
            return new UnauthorizedException($response, $response->json()['detail'] ?? __('pos_strings.viva.unauthorized'), $response->status(), $senderException);
        }
        if ($response->status() == 404) {
            return new RefundInvalidParentSessionIdException($response, $response->json()['message'] ?? __('pos_strings.viva.parent_session_not_exists_or_it_has_been_successfully_aborted'), $response->status(), $senderException);
        }
        if ($response->status() == 409) {
            return new RefundSessionIdAlreadyExistException($response, $response->json()['message'] ?? __('pos_strings.viva.session_with_this_id_already_exists_in_database'), $response->status(), $senderException);
        }
        return null;
    }
}
