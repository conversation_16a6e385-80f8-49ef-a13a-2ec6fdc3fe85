<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\DataObject\Account;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Traits\Body\HasJsonBody;
use App\Http\Integrations\VivaWallet\Enums\WebhookEventType;
use \Throwable;

class CreateWebhook extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/isv/v1/webhooks';
    }

    public function __construct(
        protected string $url,
        protected WebhookEventType $eventTypeId,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'url' => $this->url,
            'eventTypeId' => $this->eventTypeId->value,
        ];
    }

    public function createDtoFromResponse(Response $response): string
    {
        if ($response->status() == 204) {
            return __('pos_strings.viva.success_response');
        }

        throw new InternalServerErrorException($response, $response->json()['message'] ?? __('pos_strings.viva.internal_server_error'), $response->status());
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, $response->json()['message'] ?? __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        return null;
    }
}
