<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\Exceptions\ConflictException;
use App\Http\Integrations\VivaWallet\Exceptions\SessionAlreadyExistException;
use App\Http\Integrations\VivaWallet\ValueObject\Customer;
use Exception;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\ForbiddenException;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class CreatePaymentOrder extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/checkout/v2/isv/orders';
    }

    public function __construct(
        protected string $merchantId,

        // integer <int64> >= 30
        // The amount associated with this payment order. Must be a positive, non-zero number.
        // The amount will be in the currency of the merchant account using the currency's smallest unit of measurement (e.g. cents of a euro).
        protected int $amount,

        // integer or null <int64>
        // Default: null
        // The amount which will be paid out to the ISV partner.
        protected ?int $isvAmount = 0,

        // Customer
        // Information about the customer
        protected Customer $customer,

        // string <= 2048
        // This optional parameter adds a friendly description to the payment order that you want to display to
        // the customer on the payment form. It should be a short description of the items/services being purchased.
        protected ?string $customerTrns = '',

        // string <= 13
        // A descriptor tailored to the transaction, ensuring clarity and recognition for the buyer on their bank statement and 3DS verification page.
        // This parameter generates a dynamic descriptor for bank statements when processing a payment through smart checkout.
        protected ?string $dynamicDescriptor = '',

        // seconds
        // The time given to the customer to complete the payment.
        protected ?int $paymentTimeout = 1800,

        // Default: "Gets the merchant currency"
        // The numeric code of the order's currency as defined in ISO 4712[https://en.wikipedia.org/wiki/ISO_4217].
        protected string $currencyCode,

        // Default: false
        // If you wish to create a payment order, and then send out an email to the customer to request payment,
        // rather than immediately redirect the customer to the payment page to pay now, set the value to true,
        protected ?bool $paymentNotification = false,

        // boolean
        // Default: false
        // This will hold the selected amount as unavailable (without the customer being charged) for a period of time.
        // No email receipt is sent out from us in this case as it is not a charge.
        protected ?bool $preauth = false,

        // Default: false
        // If this parameter is set to true, then any amount specified in the payment order is ignored (although still mandatory),
        // and the customer is asked to indicate the amount they will pay.
        // Note that if set to true, there will not be the option to pay with certain payment methods.
        protected ?bool $disableExactAmount = false,

        // Default: true
        // If this parameter is set to true, the customer will not have the option to pay in cash at a Viva Spot,
        // and the checkout page will not display the Cash (Viva Spot) and e-banking (ΔΙΑΣ) options.
        protected ?bool $disableCash = true,

        // Default: false
        // If this parameter is set to true, the customer will not have the option to pay using their Viva personal account (wallet),
        // and the checkout page will not display the Viva Wallet option.
        protected ?bool $disableWallet = true,

        // Default: 'Default', case-sensitive
        // This is the code of the payment source associated with the merchant.
        // If the merchant has defined multiple payment sources in their account,
        // you need to define the sourceCode parameter when creating the payment order,
        // so that the system selects the appropriate payment source.
        protected ?string $sourceCode = null,

        // Default: null, array<string>
        // This is the code of the payment source associated with the merchant.
        // If the merchant has defined multiple payment sources in their account,
        // you need to define the sourceCode parameter when creating the payment order,
        // so that the system selects the appropriate payment source.
        protected ?array $tags = null,

        // Default: 'Default', case-sensitive
        // This is the code of the payment source associated with the ISV partner (not with the merchant).
        // If the ISV has defined multiple payment sources(navigate to the STORES menu to see all payment sources) in their account,
        // you need to define the resellerSourceCode parameter when creating the payment order,
        // so that the system selects the appropriate payment source.
        protected ?string $resellerSourceCode = null,

        protected ?int $tipAmount = 0,

        // string [ 1 .. 2048 ]
        // Default: null
        // This can be either an ID or a short description that helps you uniquely identify the transaction in the viva banking app.
        // For example, this can be the customer order reference number.
        // After logging in, go to Sales > Sales Transactions and find the transaction.
        // Click on the Info button against the item to display the Transaction Details dialog box.
        // The contents of the merchantTrns field will be displayed on the line below the timestamp information.
        protected string $merchantTrns,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultQuery(): array
    {
        return [
            'merchantId' => $this->merchantId,
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'amount' => $this->amount,
            'currencyCode' => $this->currencyCode,
            'merchantReference' => $this->merchantTrns,
            'customerTrns' => $this->customerTrns ?? '',
            'tipAmount' => $this->tipAmount,
            "customer" => [
                "email" => $this?->customer?->email,
                "fullName" => $this?->customer?->fullName,
                "phone" => $this?->customer?->phone,
                "countryCode" => $this?->customer?->countryCode,
                "requestLang" => $this?->customer?->requestLang,
            ],
            "dynamicDescriptor" => $this->dynamicDescriptor,
            "paymentTimeout" => $this->paymentTimeout,
            "preauth" => $this->preauth,
            "paymentNotification" => $this->paymentNotification,
            "tipAmount" => $this->tipAmount,
            "disableExactAmount" => $this->disableExactAmount,
            "disableCash" => $this->disableCash,
            "disableWallet" => $this->disableWallet,
            "isvAmount" => $this->isvAmount,
            "sourceCode" => $this->sourceCode,
            "merchantTrns" => $this->merchantTrns,
            "tags" => $this->tags,
            "resellerSourceCode" => $this->resellerSourceCode,
        ];
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        $exception = $this->getRequestException($response, null);
        if ($exception) {
            throw $exception;
        }

        try {
            if ($response->status() == 200) {
                return $response->object();
            }

            throw new Exception(__('pos_strings.viva.error'));
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function getRequestException(Response $response, ?Throwable $senderException = null): ?Throwable
    {
        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }

        if ($response->status() == 401) {
            return new UnauthorizedException($response, __('pos_strings.viva.unauthorized'), $response->status(), $senderException);
        }

        if ($response->status() == 403) {
            return new ForbiddenException($response, __('pos_strings.viva.forbidden'), $response->status(), $senderException);
        }

        if ($response->status() == 409) {
            return new ConflictException($response, __('pos_strings.viva.conflict'), $response->status(), $senderException);
        }

        return null;
    }
}
