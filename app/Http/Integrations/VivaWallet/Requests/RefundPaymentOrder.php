<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\DataObject\RefundTransaction;
use App\Http\Integrations\VivaWallet\Exceptions\ConflictException;
use App\Http\Integrations\VivaWallet\Exceptions\SessionAlreadyExistException;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use \Throwable;

class RefundPaymentOrder extends Request
{
    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::DELETE;

    /**
     * Define the Base Url
     *
     * @var string
     */
    protected string $baseUrl = '';

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return $this->baseUrl . "/api/transactions/{$this->transactionId}";
    }

    function setBaseUrl(string $url): self
    {
        $this->baseUrl = $url;

        return $this;
    }

    public function __construct(
        protected string $transactionId,
        protected int $amount,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultQuery(): array
    {
        return [
            'amount' => $this->amount,
        ];
    }

    public function createDtoFromResponse(Response $response): RefundTransaction
    {
        $exception = $this->getRequestException($response, null);
        if ($exception) {
            throw $exception;
        }

        return RefundTransaction::fromResponse($response);
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        if ($response->status() == 401) {
            return new UnauthorizedException($response, __('pos_strings.viva.unauthorized'), $response->status(), $senderException);
        }
        if ($response->status() == 409) {
            return new ConflictException($response, __('pos_strings.viva.conflict'), $response->status(), $senderException);
        }
        return null;
    }
}
