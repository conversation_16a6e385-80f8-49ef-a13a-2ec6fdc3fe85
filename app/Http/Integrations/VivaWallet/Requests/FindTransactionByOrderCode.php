<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\DataObject\TransactionResponse;
use \Throwable;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;

class FindTransactionByOrderCode extends Request
{
    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the Base Url
     *
     * @var string
     */
    protected string $baseUrl = '';

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return $this->baseUrl . "/api/transactions/?orderCode={$this->orderCode}";

    }

    function setBaseUrl(string $url): self
    {
        $this->baseUrl = $url;

        return $this;
    }

    public function __construct(
        protected string $orderCode,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function createDtoFromResponse(Response $response): TransactionResponse
    {
        return TransactionResponse::fromResponse($response);
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if($response->status() >= 500) {
            return new InternalServerErrorException($response, $response->json()['message'] ?? __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        return null;
    }
}
