<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\Exceptions\SessionAlreadyExistException;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class CreateTransaction extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/ecr/isv/v1/transactions:sale';
    }

    public function __construct(
        protected string $merchantId,
        protected string $sessionId,
        protected string $terminalId,
        protected string $cashRegisterId,
        protected int $amount,
        protected string $currencyCode,
        protected string $merchantReference,
        protected ?string $customerTrns = '',
        protected ?int $maxInstalments = 0,
        protected ?int $tipAmount = 0,
        protected ?int $isvAmount = 0,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'sessionId' => $this->sessionId,
            'terminalId' => $this->terminalId,
            'cashRegisterId' => $this->cashRegisterId,
            'amount' => $this->amount,
            'currencyCode' => $this->currencyCode,
            'merchantReference' => $this->merchantReference,
            'customerTrns' => mb_convert_encoding($this->customerTrns ?? '', "UTF-8", "UTF-8"),
            'maxInstalments' => $this->maxInstalments,
            'tipAmount' => $this->tipAmount,
            "isvDetails" => [
                "amount" => $this->isvAmount,
                "terminalMerchantId" => $this->merchantId,
            ],
        ];
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        return $response->status() == 200 ? __('pos_strings.viva.success_response') : __('pos_strings.viva.error');
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, $response->json()['detail'] ?? __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        if ($response->status() == 401) {
            return new UnauthorizedException($response, $response->json()['detail'] ?? __('pos_strings.viva.unauthorized'), $response->status(), $senderException);
        }
        if ($response->status() == 409) {
            return new SessionAlreadyExistException($response, $response->json()['detail'] ?? __('pos_strings.viva.session_already_exist'), $response->status(), $senderException);
        }
        return null;
    }
}
