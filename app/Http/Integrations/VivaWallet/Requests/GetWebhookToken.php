<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use \Throwable;

class GetWebhookToken extends Request
{

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/isv/v1/webhooks/token';
    }

    public function __construct(
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, $response->json()['message'] ?? __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }
        return null;
    }
}
