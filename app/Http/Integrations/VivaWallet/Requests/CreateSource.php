<?php

namespace App\Http\Integrations\VivaWallet\Requests;

use App\Http\Integrations\VivaWallet\Exceptions\ConflictException;
use App\Http\Integrations\VivaWallet\Exceptions\SessionAlreadyExistException;
use Saloon\Contracts\Body\HasBody;
use \Throwable;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Traits\Body\HasJsonBody;

class CreateSource extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the Base Url
     *
     * @var string
     */
    protected string $baseUrl = '';

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return $this->baseUrl . "/api/sources";
    }

    function setBaseUrl(string $url): self
    {
        $this->baseUrl = $url;

        return $this;
    }

    public function __construct(
        protected string $domain,
        protected bool $isSecure,
        protected string $name,
        protected string $pathFail,
        protected string $pathSuccess,
        protected string $sourceCode,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'domain' => $this->domain,
            'isSecure' => $this->isSecure,
            'name' => $this->name,
            'pathFail' => $this->pathFail,
            'pathSuccess' => $this->pathSuccess,
            'sourceCode' => $this->sourceCode,
        ];
    }

    public function createDtoFromResponse(Response $response): string
    {
        return $response->status() == 200 ? __('pos_strings.viva.success_response') : __('pos_strings.viva.error');
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        if ($response->status() >= 500) {
            return new InternalServerErrorException($response, __('pos_strings.viva.internal_server_error'), $response->status(), $senderException);
        }

        if ($response->status() == 401) {
            return new UnauthorizedException($response, __('pos_strings.viva.unauthorized'), $response->status(), $senderException);
        }

        if ($response->status() == 409) {
            throw new ConflictException($response, __('pos_strings.viva.conflict'), $response->status(), $senderException);
        }

        return null;
    }
}
