<?php

namespace App\Http\Integrations\POSTaxControlUnit;

use App\Http\Integrations\POSInfrasec\Enums\Environment;
use App\Http\Integrations\POSTaxControlUnit\Contracts\POSTaxControlUnit;
use App\Http\Integrations\POSInfrasec\InfrasecConnector;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class POSTaxControlUnitServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(POSTaxControlUnit::class, function ($app) {
            return new InfrasecConnector(
                environment: Environment::from($app->make('config')->get('infrasec.environment')),
                registerCounterNumber: config('infrasec.register_counter_number'),
                registerMake: config("infrasec.register_make"),
                registerModel: config("infrasec.register_model"),
                partnerName: config("infrasec.partner_name"),
                partnerCode: config("infrasec.partner_code"),
                posAuthorityCode: config("infrasec.pos_authority_code"),

                certPath: config("infrasec.enroll_cert_path"),
                caPath: config("infrasec.enroll_key_path"),
                certPassword: config("infrasec.enroll_cert_password"),

                ccucertPath: config("infrasec.ccu_cert_path"),
                ccucaPath: config("infrasec.ccu_key_path"),
                ccucertPassword: config("infrasec.ccu_cert_password"),
            );
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [POSTaxControlUnit::class];
    }
}
