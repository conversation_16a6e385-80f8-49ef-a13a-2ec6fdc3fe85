<?php

namespace App\Http\Integrations\POSTaxControlUnit\Facades;

use App\Http\Integrations\POSTaxControlUnit\Contracts\POSTaxControlUnit as ContractsPOSTaxControlUnit;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Http\Integrations\POSInfrasec\Resources\EnrollmentResource enrollment(?string $registerId)
 * @method static \App\Http\Integrations\POSInfrasec\Resources\CCUResource ccu(?string $registerId)
 *
 * @see \App\Http\Integrations\POSTaxControlUnit\Contracts\POSTaxControlUnit
 */

class POSTaxControlUnit extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return ContractsPOSTaxControlUnit::class;
    }
}
