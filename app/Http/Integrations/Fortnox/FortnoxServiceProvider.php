<?php

namespace App\Http\Integrations\Fortnox;

use App\Http\Integrations\Fortnox\Contracts\FortnoxConnector as ContractsFortnoxConnector;
use App\Http\Integrations\Fortnox\Contracts\FortnoxAuthConnector as ContractsFortnoxAuthConnector;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class FortnoxServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(ContractsFortnoxConnector::class, function ($app) {
            return new FortnoxConnector(
                baseUrl: strval($app->make('config')->get('fortnox.base_url')),
            );
        });

        $this->app->bind(ContractsFortnoxAuthConnector::class, function ($app) {
            return new FortnoxAuthConnector(
                oauthUrl: strval($app->make('config')->get('fortnox.oauth_url')),
                clientId: strval($app->make('config')->get('fortnox.client_id')),
                clientSecret: strval($app->make('config')->get('fortnox.client_secret')),
                redirectUri: strval($app->make('config')->get('fortnox.redirect_url')),
            );
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [ContractsFortnoxConnector::class, ContractsFortnoxAuthConnector::class];
    }
}
