<?php

namespace App\Http\Integrations\Fortnox\Controller;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WebhookController extends Controller
{
    /**
     * Verify a webhook hmac.
     *
     * @see 
     *
     */
    private function verifyWebhookSignature(Request $request): bool
    {
        return true;
    }

    /**
     * <PERSON>le requests from Viva Wallet.
     *
     * @see 
     */
    public function handle(Request $request): JsonResponse
    {
        return response()->json();
    }
}
