<?php

namespace App\Http\Integrations\Fortnox\Facades;

use App\Http\Integrations\Fortnox\Contracts\FortnoxConnector as ContractsFortnoxConnector;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Http\Integrations\Fortnox\Resources\CustomerResource customer(?string $customer_number)
 *
 * @see \App\Http\Integrations\Fortnox\Contracts\FortnoxConnector
 */

class Fortnox extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        self::clearResolvedInstance(ContractsFortnoxConnector::class);

        return ContractsFortnoxConnector::class;
    }
}
