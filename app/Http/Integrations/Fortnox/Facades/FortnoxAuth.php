<?php

namespace App\Http\Integrations\Fortnox\Facades;

use App\Http\Integrations\Fortnox\Contracts\FortnoxAuthConnector as ContractsFortnoxAuthConnector;
use Illuminate\Support\Facades\Facade;

/**
 * @method static string getAuthorizationUrl(array $scopes = [], string $state = null, string $scopeSeparator = ' ', array $additionalQueryParameters = [])
 * @method static \Saloon\Http\OAuth2\AccessTokenAuthenticator getAccessToken(string $code, string $state = null, string $expectedState = null, callable $requestModifier = null, bool $returnResponse = false)
 * @method static \Saloon\Http\OAuth2\AccessTokenAuthenticator refreshAccessToken(\Saloon\Http\OAuth2\AccessTokenAuthenticator $authenticator, callable $requestModifier = null, bool $returnResponse = false)
 * @method static \Saloon\Http\OAuth2\AccessTokenAuthenticator createOAuthAuthenticatorFromResponse(\Saloon\Http\Response $response, string $fallbackRefreshToken = null)
 * @method static \Saloon\Http\OAuth2\AccessTokenAuthenticator createOAuthAuthenticator(string $accessToken, string $refreshToken, \DateTimeImmutable $expiresAt)
 * @method static \Saloon\Http\Response getUser(\Saloon\Http\OAuth2\AccessTokenAuthenticator $authenticator, callable $requestModifier = null)
 * @method static string getState()
 * @method static \Saloon\Http\Request resolveAccessTokenRequest(string $code, \Saloon\Helpers\OAuth2\OAuthConfig $oauthConfig)
 * @method static \Saloon\Http\Request resolveRefreshTokenRequest(\Saloon\Helpers\OAuth2\OAuthConfig $oauthConfig, string $refreshToken)
 * @method static \Saloon\Http\Request resolveUserRequest(\Saloon\Helpers\OAuth2\OAuthConfig $oauthConfig)
 *
 * @see \App\Http\Integrations\Fortnox\Contracts\FortnoxAuthConnector
 */
class FortnoxAuth extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        self::clearResolvedInstance(ContractsFortnoxAuthConnector::class);

        return ContractsFortnoxAuthConnector::class;
    }
}
