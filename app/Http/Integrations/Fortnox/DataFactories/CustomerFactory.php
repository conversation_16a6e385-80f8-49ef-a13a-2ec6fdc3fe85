<?php

namespace App\Http\Integrations\Fortnox\DataFactories;

use App\Http\Integrations\Fortnox\DataObject\Customer;
use Illuminate\Support\Collection;

final class CustomerFactory
{
    public static function new(array $attributes): Customer
    {
        return (new self)->make(
            attributes: $attributes,
        );
    }

    public function make(array $attributes): Customer
    {
        return Customer::fromArray($attributes);
    }

    public static function collection(array $webhooks): Collection
    {
        return (new Collection(
            items: $webhooks,
        ))->map(fn($webhook): Customer => self::new(attributes: $webhook));
    }
}
