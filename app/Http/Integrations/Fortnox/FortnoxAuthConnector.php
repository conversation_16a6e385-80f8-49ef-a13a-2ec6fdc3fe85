<?php

namespace App\Http\Integrations\Fortnox;

use App\Http\Integrations\Fortnox\Contracts\FortnoxAuthConnector as ContractsFortnoxAuthConnector;
use App\Http\Integrations\Fortnox\Requests\GetAccessToken;
use App\Http\Integrations\Fortnox\Requests\GetRefreshToken;
use Saloon\Contracts\Response;
use Saloon\Contracts\Sender;
use Saloon\Http\Connector;
use Saloon\Http\Senders\GuzzleSender;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;
use Saloon\Exceptions\Request\ClientException;
use Saloon\Exceptions\Request\ServerException;
use Saloon\Exceptions\Request\RequestException;
use Saloon\Exceptions\Request\Statuses\NotFoundException;
use Saloon\Exceptions\Request\Statuses\ForbiddenException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Exceptions\Request\Statuses\GatewayTimeoutException;
use Saloon\Exceptions\Request\Statuses\RequestTimeOutException;
use Saloon\Exceptions\Request\Statuses\TooManyRequestsException;
use Saloon\Exceptions\Request\Statuses\MethodNotAllowedException;
use Saloon\Exceptions\Request\Statuses\ServiceUnavailableException;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\UnprocessableEntityException;
use Saloon\Helpers\OAuth2\OAuthConfig;
use Saloon\Http\Request;
use Saloon\Traits\OAuth2\AuthorizationCodeGrant;
use \Throwable;

class FortnoxAuthConnector extends Connector implements ContractsFortnoxAuthConnector
{
    use AlwaysThrowOnErrors, AuthorizationCodeGrant;

    public function __construct(
        public string $oauthUrl,
        public string $clientId,
        public string $clientSecret,
        public string $redirectUri,
    ) {}

    protected function defaultSender(): Sender
    {
        return resolve(GuzzleSender::class);
    }

    /**
     * The Base URL of the API
     *
     * @return string
     */
    public function resolveBaseUrl(): string
    {
        return $this->oauthUrl;
    }

    /**
     * Define the default OAuth2 Config.
     *
     * @return OAuthConfig
     */
    protected function defaultOauthConfig(): OAuthConfig
    {
        return OAuthConfig::make()
            ->setClientId($this->clientId)
            ->setClientSecret($this->clientSecret)
            ->setRedirectUri($this->redirectUri)
            ->setTokenEndpoint('oauth-v1/token')
            ->setAuthorizeEndpoint('oauth-v1/auth');
    }

    /**
     * Resolve the access token request
     *
     * @param string $code
     * @param OAuthConfig $oauthConfig
     * @return Request
     */
    protected function resolveAccessTokenRequest(string $code, OAuthConfig $oauthConfig): Request
    {
        return new GetAccessToken($code, $oauthConfig);
    }

    /**
     * Resolve the refresh token request
     *
     * @param OAuthConfig $oauthConfig
     * @param string $refreshToken
     * @return Request
     */
    protected function resolveRefreshTokenRequest(OAuthConfig $oauthConfig, string $refreshToken): Request
    {
        return new GetRefreshToken($oauthConfig, $refreshToken);
    }

    /**
     * Handle the request exception.
     *
     * @param \Saloon\Contracts\Response $response
     * @param \Throwable|null $senderException
     * @return \Throwable|null
     */
    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        $status = $response->status();

        $requestException = match (true) {
            // Built-in exceptions
            $status === 401 => UnauthorizedException::class,
            $status === 403 => ForbiddenException::class,
            $status === 404 => NotFoundException::class,
            $status === 405 => MethodNotAllowedException::class,
            $status === 408 => RequestTimeOutException::class,
            $status === 422 => UnprocessableEntityException::class,
            $status === 429 => TooManyRequestsException::class,
            $status === 500 => InternalServerErrorException::class,
            $status === 503 => ServiceUnavailableException::class,
            $status === 504 => GatewayTimeoutException::class,

            // Fall-back exceptions
            $response->serverError() => ServerException::class,
            $response->clientError() => ClientException::class,
            default => RequestException::class,
        };

        $data = $response->json();
        $message = null;
        $code = 0;

        if (isset($data['error_description'])) {
            $message = $data['error_description'];
        }

        if (isset($data['ErrorInformation'])) {
            $message = $data['ErrorInformation']['Message'];
            $code = $data['ErrorInformation']['Code'];
        }

        return new $requestException($response, $message, $code, $senderException);
    }
}
