<?php

namespace App\Http\Integrations\Fortnox;

use App\Http\Integrations\Fortnox\Contracts\FortnoxConnector as ContractsFortnoxConnector;
use App\Http\Integrations\Fortnox\Resources\CustomerResource;
use Saloon\Contracts\Response;
use Saloon\Contracts\Sender;
use Saloon\Http\Connector;
use Saloon\Http\Senders\GuzzleSender;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;
use Saloon\Exceptions\Request\ClientException;
use Saloon\Exceptions\Request\ServerException;
use Saloon\Exceptions\Request\RequestException;
use Saloon\Exceptions\Request\Statuses\NotFoundException;
use Saloon\Exceptions\Request\Statuses\ForbiddenException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Saloon\Exceptions\Request\Statuses\GatewayTimeoutException;
use Saloon\Exceptions\Request\Statuses\RequestTimeOutException;
use Saloon\Exceptions\Request\Statuses\TooManyRequestsException;
use Saloon\Exceptions\Request\Statuses\MethodNotAllowedException;
use Saloon\Exceptions\Request\Statuses\ServiceUnavailableException;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Exceptions\Request\Statuses\UnprocessableEntityException;
use \Throwable;

class FortnoxConnector extends Connector implements ContractsFortnoxConnector
{
    use AlwaysThrowOnErrors;

    public function __construct(
        public string $baseUrl,
    ) {}

    protected function defaultSender(): Sender
    {
        return resolve(GuzzleSender::class);
    }

    /**
     * The Base URL of the API
     *
     * @return string
     */
    public function resolveBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function customer(string|null $customer_number): CustomerResource
    {
        return new CustomerResource($this, $customer_number);
    }

    /**
     * Default headers for every request
     *
     * @return string[]
     */
    protected function defaultHeaders(): array
    {
        return [];
    }

    /**
     * Default HTTP client options
     *
     * @return string[]
     */
    protected function defaultConfig(): array
    {
        return [];
    }

    /**
     * Handle the request exception.
     *
     * @param \Saloon\Contracts\Response $response
     * @param \Throwable|null $senderException
     * @return \Throwable|null
     */
    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        $status = $response->status();

        $requestException = match (true) {
            // Built-in exceptions
            $status === 401 => UnauthorizedException::class,
            $status === 403 => ForbiddenException::class,
            $status === 404 => NotFoundException::class,
            $status === 405 => MethodNotAllowedException::class,
            $status === 408 => RequestTimeOutException::class,
            $status === 422 => UnprocessableEntityException::class,
            $status === 429 => TooManyRequestsException::class,
            $status === 500 => InternalServerErrorException::class,
            $status === 503 => ServiceUnavailableException::class,
            $status === 504 => GatewayTimeoutException::class,

            // Fall-back exceptions
            $response->serverError() => ServerException::class,
            $response->clientError() => ClientException::class,
            default => RequestException::class,
        };

        $data = $response->json();
        $message = null;
        $code = 0;

        if (isset($data['error_description'])) {
            $message = $data['error_description'];
        }

        if (isset($data['error']) && !!$data['error']) {
            $message = $data['message'];
        }

        if (isset($data['ErrorInformation']) && isset($data['ErrorInformation']['Message'])) {
            $message = $data['ErrorInformation']['Message'];
            $code = $data['ErrorInformation']['Code'];
        }

        if (isset($data['ErrorInformation']) && isset($data['ErrorInformation']['message'])) {
            $message = $data['ErrorInformation']['message'];
            $code = $data['ErrorInformation']['code'];
        }

        return new $requestException($response, $message, $code, $senderException);
    }
}
