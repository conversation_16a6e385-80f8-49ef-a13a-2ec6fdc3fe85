<?php

namespace App\Http\Integrations\Fortnox\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Webhook implements WithResponse
{
    use HasResponse;

    public function __construct() {}

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return self::fromArray($data);
    }

    public static function fromArray(array $data): self
    {
        return new static();
    }
}
