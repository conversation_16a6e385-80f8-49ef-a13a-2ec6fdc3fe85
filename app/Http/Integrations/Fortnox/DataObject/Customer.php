<?php

namespace App\Http\Integrations\Fortnox\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Customer implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $id,
        readonly public string $url,
        readonly public string $name,
        readonly public string $email,
        readonly public string $email_invoice,
        readonly public ?string $invoice_default_delivery_type,
        readonly public ?string $address1,
        readonly public ?string $address2,
        readonly public ?string $organisation_number,
        readonly public ?string $phone,
        readonly public ?string $zip_code,
        readonly public ?string $city,
    ) {}

    public static function fromResponse(Response $response): self
    {
        $data = $response->json()['Customer'];

        return new static(
            id: $data['CustomerNumber'],
            url: $data['@url'],
            name: $data['Name'],
            email: $data['Email'],
            email_invoice: isset($data['EmailInvoice']) ? $data['EmailInvoice'] : $data['Email'],
            // check isset
            invoice_default_delivery_type: isset($data['DefaultDeliveryTypes']['Invoice']) ? $data['DefaultDeliveryTypes']['Invoice'] : null,
            address1: isset($data['Address1']) ? $data['Address1'] : null,
            address2: isset($data['Address2']) ? $data['Address2'] : null,
            organisation_number: isset($data['OrganisationNumber']) ? $data['OrganisationNumber'] : null,
            phone: isset($data['Phone1']) ? $data['Phone1'] : null,
            zip_code: isset($data['ZipCode']) ? $data['ZipCode'] : null,
            city: isset($data['City']) ? $data['City'] : null,
        );
    }

    public static function fromArray(array $data): self
    {
        return new static(
            id: $data['CustomerNumber'],
            url: $data['@url'],
            name: $data['Name'],
            email: $data['Email'],
            email_invoice: isset($data['EmailInvoice']) ? $data['EmailInvoice'] : $data['Email'],
            // check isset
            invoice_default_delivery_type: isset($data['DefaultDeliveryTypes']['Invoice']) ? $data['DefaultDeliveryTypes']['Invoice'] : null,
            address1: isset($data['Address1']) ? $data['Address1'] : null,
            address2: isset($data['Address2']) ? $data['Address2'] : null,
            organisation_number: isset($data['OrganisationNumber']) ? $data['OrganisationNumber'] : null,
            phone: isset($data['Phone1']) ? $data['Phone1'] : null,
            zip_code: isset($data['ZipCode']) ? $data['ZipCode'] : null,
            city: isset($data['City']) ? $data['City'] : null,
        );
    }
}
