<?php

namespace App\Http\Integrations\Fortnox\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Invoice implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $url,
        readonly public string $document_number,
        readonly public string $customer_number,
        readonly public string $customer_name,
        readonly public string $invoice_date,
        readonly public string $due_date,
        readonly public string $currency,
        readonly public float $total,
        readonly public float $total_vat,
        readonly public float $balance,
        readonly public bool $booked,
        readonly public bool $sent,
        readonly public bool $cancelled,
        readonly public string $ocr,
        readonly public float $net,
        readonly public float $gross,
        readonly public array $invoice_rows,
        readonly public ?string $address1 = null,
        readonly public ?string $city = null,
        readonly public ?string $zip_code = null,
        readonly public ?string $country = null,
        readonly public ?string $organisation_number = null,
        readonly public ?string $email = null,
        readonly public ?string $our_reference = null,
        readonly public ?string $your_reference = null,
        readonly public ?string $comments = null,
    ) {}

    public static function fromResponse(Response $response): self
    {
        $data = $response->json()['Invoice'];

        return new static(
            url: $data['@url'],
            document_number: $data['DocumentNumber'],
            customer_number: $data['CustomerNumber'],
            customer_name: $data['CustomerName'],
            invoice_date: $data['InvoiceDate'],
            due_date: $data['DueDate'],
            currency: $data['Currency'],
            total: (float) $data['Total'],
            total_vat: (float) $data['TotalVAT'],
            balance: (float) $data['Balance'],
            booked: (bool) $data['Booked'],
            sent: (bool) $data['Sent'],
            cancelled: (bool) $data['Cancelled'],
            ocr: $data['OCR'],
            invoice_rows: $data['InvoiceRows'],
            address1: $data['Address1'] ?? null,
            city: $data['City'] ?? null,
            zip_code: $data['ZipCode'] ?? null,
            country: $data['Country'] ?? null,
            organisation_number: $data['OrganisationNumber'] ?? null,
            email: $data['EmailInformation']['EmailAddressTo'] ?? null,
            our_reference: $data['OurReference'] ?? null,
            your_reference: $data['YourReference'] ?? null,
            comments: $data['Comments'] ?? null,
            net: (float) $data['Net'],
            gross: (float) $data['Gross'],
        );
    }

    public static function fromArray(array $data): self
    {
        return new static(
            url: $data['@url'],
            document_number: $data['DocumentNumber'],
            customer_number: $data['CustomerNumber'],
            customer_name: $data['CustomerName'],
            invoice_date: $data['InvoiceDate'],
            due_date: $data['DueDate'],
            currency: $data['Currency'],
            total: (float) $data['Total'],
            total_vat: (float) $data['TotalVAT'],
            balance: (float) $data['Balance'],
            booked: (bool) $data['Booked'],
            sent: (bool) $data['Sent'],
            cancelled: (bool) $data['Cancelled'],
            ocr: $data['OCR'],
            invoice_rows: $data['InvoiceRows'],
            address1: $data['Address1'] ?? null,
            city: $data['City'] ?? null,
            zip_code: $data['ZipCode'] ?? null,
            country: $data['Country'] ?? null,
            organisation_number: $data['OrganisationNumber'] ?? null,
            email: $data['EmailInformation']['EmailAddressTo'] ?? null,
            our_reference: $data['OurReference'] ?? null,
            your_reference: $data['YourReference'] ?? null,
            comments: $data['Comments'] ?? null,
            net: (float) $data['Net'],
            gross: (float) $data['Gross'],
        );
    }
}
