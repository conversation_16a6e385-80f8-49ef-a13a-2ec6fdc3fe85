<?php

namespace App\Http\Integrations\Fortnox\Resources;

use App\Http\Integrations\Fortnox\DataObject\Invoice;
use App\Http\Integrations\Fortnox\Requests\CreateInvoice;
use App\Http\Integrations\Fortnox\Requests\PrintInvoice;
use Psr\Http\Message\StreamInterface;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class InvoiceResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected string $customer_number,
    ) {}

    /**
     * Create a new invoice.
     *
     *
     * @return string
     */
    public function create(
        array $items,
        ?string $remarks = null,
    ): Invoice {
        $response = $this->connector
            ->send(new CreateInvoice(
                customer_number: $this->customer_number,
                items: $items,
                remarks: $remarks,
            ));

        return $response->dto();
    }

    /**
     * Download invoice.
     *
     *
     * @return string
     */
    public function download(
        string $invoiceNumber,
    ): StreamInterface {
        $response = $this->connector
            ->send(new PrintInvoice(
                $invoiceNumber,
            ));

        return $response->dto();
    }
}
