<?php

// namespace App\Http\Integrations\Fortnox\Resources;

// use App\Http\Integrations\Fortnox\Contracts\FortnoxConnector;
// use App\Http\Integrations\Fortnox\Requests\GetAccessToken;
// use App\Http\Integrations\Fortnox\Requests\GetAccessTokenFromRefreshToken;

// /**
//  * @property FortnoxConnector $connector
//  */
// class AuthResource extends Resource
// {
//     public function __construct(
//         protected FortnoxConnector $connector,
//     ) {}

//     /**
//      * Get the auth url for the user.
//      *
//      *
//      * @return string
//      */
//     public function url(
//         string $client_id,
//         string $redirect_uri,
//         string $state,
//         string $scope = 'customer invoice',
//         string $access_type = 'offline',
//         string $response_type = 'code',
//     ): string {
//         $auth_url = $this->connector->getOAuthUrl();

//         // return the url with above params but properly understandable code
//         return "$auth_url?client_id=$client_id&scope=$scope&state=$state&access_type=$access_type&response_type=$response_type&redirect_uri=$redirect_uri";
//     }

//     /**
//      * Get the access token from the code.
//      *
//      *
//      * @return object {
//      *     "access_token": string,
//      *     "refresh_token": string,
//      *     "expires_in": int,
//      *     "token_type": string,
//      *     "scope": string,
//      * }
//      */
//     public function access_token(
//         string $code,
//         string $redirect_url,
//     ): object {
//         $response = $this->connector
//             ->send(new GetAccessToken(
//                 code: $code,
//                 redirect_url: $redirect_url,
//             ));

//         return $response->dto();
//     }

//     /**
//      * Get the access token from the refresh token.
//      *
//      *
//      * @return object {
//      *     "access_token": string,
//      *     "refresh_token": string,
//      *     "expires_in": int,
//      *     "token_type": string,
//      *     "scope": string,
//      * }
//      */
//     public function access_token_from_refresh_token(
//         string $refresh_token,
//     ): object {
//         $response = $this->connector
//             ->send(new GetAccessTokenFromRefreshToken(
//                 refresh_token: $refresh_token,
//             ));

//         return $response->dto();
//     }
// }
