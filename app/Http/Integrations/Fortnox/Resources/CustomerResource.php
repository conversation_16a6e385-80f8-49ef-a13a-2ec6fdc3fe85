<?php

namespace App\Http\Integrations\Fortnox\Resources;

use App\Http\Integrations\Customer\DataObject\Room;
use App\Http\Integrations\Fortnox\DataObject\Customer;
use App\Http\Integrations\Fortnox\Requests\CreateCustomer;
use App\Http\Integrations\Fortnox\Requests\FetchCustomer;
use App\Http\Integrations\Fortnox\Requests\ListCustomer;
use Illuminate\Support\Collection;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class CustomerResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected ?string $customer_number = null,
    ) {}

    /**
     * Create a new customer.
     *
     *
     * @return Room
     */
    public function create(
        ?string $name = null,
        ?string $email = null,
        ?string $email_invoice = null,
        ?string $currency = 'SEK',
        ?string $invoice_default_delivery_type = 'EMAIL',
        ?string $address1 = null,
        ?string $address2 = null,
        ?string $organisation_number = null,
        ?string $phone = null,
        ?string $zip_code = null,
        ?string $country_code = null,
        ?string $city = null,
    ): Customer {
        $response = $this->connector
            ->send(new CreateCustomer(
                name: $name,
                email: $email,
                email_invoice: $email_invoice,
                currency: $currency,
                invoice_default_delivery_type: $invoice_default_delivery_type,
                address1: $address1,
                address2: $address2,
                organisation_number: $organisation_number,
                phone: $phone,
                zip_code: $zip_code,
                country_code: $country_code,
                city: $city,
            ));

        return $response->dto();
    }

    /**
     * Fetch all customers.
     *
     *
     * @return Collection
     */
    public function list(): Collection
    {
        $response = $this->connector
            ->send(new ListCustomer());

        return $response->dto();
    }

    /**
     * Fetch customer detail.
     *
     *
     * @return Customer
     */
    public function detail(): Customer
    {
        if (!$this->customer_number) {
            throw new \Exception('Customer number is required');
        }

        $response = $this->connector
            ->send(new FetchCustomer(
                customerNumber: $this->customer_number,
            ));

        return $response->dto();
    }

    /**
     * customer invoices.
     *
     * @return InvoiceResource
     */
    public function invoices(): InvoiceResource
    {
        return new InvoiceResource($this->connector, $this->customer_number);
    }
}
