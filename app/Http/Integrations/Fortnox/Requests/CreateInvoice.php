<?php

namespace App\Http\Integrations\Fortnox\Requests;

use App\Http\Integrations\Fortnox\DataObject\Invoice;
use Carbon\Carbon;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class CreateInvoice extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/3/invoices';
    }

    public function __construct(
        protected string $customer_number,
        protected array $items,
        // {
        //     "DeliveredQuantity":1,
        //     "Description":"Event name - date of event (2 talent(s): 160 each)",
        //     "HouseWork":false,
        //     "Unit":"%",
        //     "VAT":"25",
        //     "Price":420,
        // },
    ) {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            "Invoice" => [
                "CustomerNumber" => $this->customer_number,
                "InvoiceRows" => $this->items,
            ]
        ];
    }

    public function createDtoFromResponse(Response $response): Invoice
    {
        return Invoice::fromResponse($response);
    }
}
