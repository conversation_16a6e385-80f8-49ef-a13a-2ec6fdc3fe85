<?php

namespace App\Http\Integrations\Fortnox\Requests;

use App\Http\Integrations\Fortnox\DataObject\Customer;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use <PERSON>oon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class CreateCustomer extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/3/customers/';
    }

    public function __construct(
        protected string $name,
        protected string $email,
        protected string $email_invoice,
        protected string $currency,
        protected ?string $invoice_default_delivery_type = "EMAIL",
        protected ?string $address1 = null,
        protected ?string $address2 = null,
        protected ?string $organisation_number = null,
        protected ?string $phone = null,
        protected ?string $zip_code = null,
        protected ?string $country_code = null,
        protected ?string $city = null,
    ) {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            "Customer" => [
                "Address1" => $this->address1,
                "Address2" => $this->address2,
                "CountryCode" => $this->country_code,
                "OrganisationNumber" => $this->organisation_number,
                "ZipCode" => $this->zip_code,
                "City" => $this->city,
                "DefaultDeliveryTypes" => [
                    "Invoice" => $this->invoice_default_delivery_type,
                    "Order" => "EMAIL",
                    "Offer" => "EMAIL"
                ],
                "Phone1" => $this->phone,
                "Currency" => $this->currency,
                "Email" => $this->email,
                "EmailInvoice" => $this->email,
                "Name" => $this->name,
            ],
        ];
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        return Customer::fromResponse($response);
    }
}
