<?php

namespace App\Http\Integrations\Fortnox\Requests;

use App\Http\Integrations\Fortnox\DataObject\Customer;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class FetchCustomer extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    protected string $id = '';

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/3/customers/{$this->id}";
    }

    public function __construct(int|string $customerNumber)
    {
        $this->id = $customerNumber;
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function createDtoFromResponse(Response $response): Customer
    {
        return Customer::fromResponse($response);
    }
}
