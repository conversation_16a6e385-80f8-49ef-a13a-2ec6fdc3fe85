<?php

namespace App\Http\Integrations\Fortnox\Requests;

use Psr\Http\Message\StreamInterface;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class PrintInvoice extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    protected string $id = '';

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/3/invoices/{$this->id}/preview";
    }

    public function __construct(int|string $invoiceNumber)
    {
        $this->id = $invoiceNumber;
    }

    protected function defaultHeaders(): array
    {
        return [];
    }

    public function createDtoFromResponse(Response $response): StreamInterface
    {
        return $response->stream();
    }
}
