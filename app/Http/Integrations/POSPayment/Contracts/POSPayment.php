<?php

namespace App\Http\Integrations\POSPayment\Contracts;

use App\Http\Integrations\VivaWallet\Resources\AccountResource;
use App\Http\Integrations\VivaWallet\Resources\SessionResource;
use App\Http\Integrations\VivaWallet\Resources\WebhookResource;

interface POSPayment
{
    public function account(?string $merchantId = null): AccountResource;

    public function session(): SessionResource;

    public function webhook(): WebhookResource;
}
