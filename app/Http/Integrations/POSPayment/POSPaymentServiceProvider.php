<?php

namespace App\Http\Integrations\POSPayment;

use App\Http\Integrations\POSPayment\Contracts\POSPayment;
use App\Http\Integrations\VivaWallet\Enums\Environment;
use App\Http\Integrations\VivaWallet\VivaWalletConnector;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class POSPaymentServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(POSPayment::class, function ($app) {
            return new VivaWalletConnector(
                environment: Environment::from($app->make('config')->get('viva_wallet.environment')),
                merchantId: strval($app->make('config')->get('viva_wallet.merchant_id')),
                resellerId: strval($app->make('config')->get('viva_wallet.reseller_id')),
                apiKey: strval($app->make('config')->get('viva_wallet.api_key')),
                clientId: strval($app->make('config')->get('viva_wallet.client_id')),
                clientSecret: strval($app->make('config')->get('viva_wallet.client_secret')),
            );
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [POSPayment::class];
    }
}
