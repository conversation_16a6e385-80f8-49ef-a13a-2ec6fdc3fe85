<?php

namespace App\Http\Integrations\POSPayment\Facades;

use App\Http\Integrations\POSPayment\Contracts\POSPayment as ContractsPOSPayment;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Http\Integrations\VivaWallet\Resources\AccountResource account(?string $merchantId)
 * @method static \App\Http\Integrations\VivaWallet\Resources\SessionResource session()
 * @method static \App\Http\Integrations\VivaWallet\Resources\WebhookResource webhook()

 *
 * @see \App\Http\Integrations\POSPayment\Contracts\POSPayment
 */

class POSPayment extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        self::clearResolvedInstance(ContractsPOSPayment::class);

        return ContractsPOSPayment::class;
    }
}
