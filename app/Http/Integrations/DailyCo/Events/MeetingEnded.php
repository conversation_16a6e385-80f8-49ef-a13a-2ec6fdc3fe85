<?php

namespace App\Http\Integrations\DailyCo\Events;

use Carbon\Carbon;

/** @see https://docs.daily.co/reference/rest-api/webhooks/events/meeting-ended */
class MeetingEnded
{
    public function __construct(
        public readonly Carbon $start_at,
        public readonly Carbon $end_at,
        public readonly string $meeting_id,
        public readonly string $room,
    ) {}

    public static function create(array $attributes): self
    {
        return new self(
            start_at: Carbon::createFromTimestamp($attributes['start_ts']),
            end_at: Carbon::createFromTimestamp($attributes['end_ts']),
            meeting_id: $attributes['meeting_id'],
            room: $attributes['room'],
        );
    }
}
