<?php

namespace App\Http\Integrations\DailyCo\Events;

use App\Http\Integrations\DailyCo\Enums\WebhookEventType;
use Carbon\Carbon;

/**
 * @template TEventData of object
 */
class WebhookEvent
{
    public function __construct(
        public readonly string $id,
        public readonly string $version,
        public readonly object $data,
        public readonly string $type,
        public readonly WebhookEventType $eventType,
        public readonly Carbon $event_at,
    ) {}

    /**
     * @phpstan-param  WebhookEventArray  $attributes
     * @phpstan-return self<TEventData>
     */
    public static function create(array $attributes): self
    {
        $eventType = WebhookEventType::from($attributes['type']);

        $eventData = match ($eventType) {
            /** @phpstan-ignore-next-line */
            WebhookEventType::MeetingStarted => MeetingStarted::create($attributes['payload']),
            /** @phpstan-ignore-next-line */
            WebhookEventType::MeetingEnded => MeetingEnded::create($attributes['payload']),
            /** @phpstan-ignore-next-line */
            WebhookEventType::ParticipantJoined => ParticipantJoined::create($attributes['payload']),
            /** @phpstan-ignore-next-line */
            WebhookEventType::ParticipantLeft => ParticipantLeft::create($attributes['payload']),
            default => (object) $attributes['payload'],
        };

        /** @phpstan-ignore-next-line */
        return new self(...[
            'type' => $attributes['type'],
            'eventType' => $eventType,
            'data' => $eventData,
            'id' => $attributes['id'],
            'version' => $attributes['version'],
            'event_at' => Carbon::createFromTimestamp($attributes['event_ts']),
        ]);
    }
}
