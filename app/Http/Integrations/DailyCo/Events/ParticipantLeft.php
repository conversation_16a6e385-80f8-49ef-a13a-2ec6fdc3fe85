<?php

namespace App\Http\Integrations\DailyCo\Events;

use Carbon\Carbon;

/** @see https://docs.daily.co/reference/rest-api/webhooks/events/participant-left */
class ParticipantLeft
{
    public function __construct(
        public readonly string $room,
        public readonly ?string $user_id,
        public readonly ?string $user_name,
        public readonly string $session_id,
        public readonly Carbon $joined_at,
        // in seconds
        public readonly int $duration,
        public readonly ?Carbon $will_eject_at,
        public readonly bool $owner,
        public readonly bool $hasPresence,
        public readonly bool $canSend,
        public readonly bool $canAdmin,
    ) {}

    public static function create(array $attributes): self
    {
        return new self(
            room: $attributes['room'],
            user_id: $attributes['user_id'],
            user_name: $attributes['user_name'],
            session_id: $attributes['session_id'],
            joined_at: Carbon::createFromTimestamp($attributes['joined_at']),
            will_eject_at: $attributes['will_eject_at'] ? Carbon::createFromTimestamp($attributes['will_eject_at']) : null,
            owner: $attributes['owner'],
            duration: $attributes['duration'],
            hasPresence: isset($attributes['permissions']['hasPresence']) ? $attributes['permissions']['hasPresence'] : false,
            canSend: isset($attributes['permissions']['canSend']) ? $attributes['permissions']['canSend'] : false,
            canAdmin: isset($attributes['permissions']['canAdmin']) ? $attributes['permissions']['canAdmin'] : false,
        );
    }
}
