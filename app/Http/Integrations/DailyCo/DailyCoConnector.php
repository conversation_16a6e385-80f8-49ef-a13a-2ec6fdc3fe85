<?php

namespace App\Http\Integrations\DailyCo;

use App\Http\Integrations\DailyCo\Contracts\DailyCo;
use App\Http\Integrations\DailyCo\Resources\RoomResource;
use App\Http\Integrations\DailyCo\Resources\WebhookResource;
use Saloon\Contracts\Sender;
use Saloon\Http\Auth\TokenAuthenticator;
use Saloon\Http\Connector;
use Saloon\Http\Senders\GuzzleSender;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class DailyCoConnector extends Connector implements DailyCo
{
    use AlwaysThrowOnErrors;

    public function __construct(
        public string $baseUrl,
        public string $apiKey,
    ) {}

    protected function defaultSender(): Sender
    {
        return resolve(GuzzleSender::class);
    }

    /**
     * The Base URL of the API
     *
     * @return string
     */
    public function resolveBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function room(string $room_name = null): RoomResource
    {
        return new RoomResource($this, $room_name);
    }

    public function webhook(): WebhookResource
    {
        return new WebhookResource($this);
    }

    protected function defaultAuth(): TokenAuthenticator
    {
        return new TokenAuthenticator($this->apiKey);
    }

     /**
     * Default headers for every request
     *
     * @return string[]
     */
    protected function defaultHeaders(): array
    {
        return [];
    }

    /**
     * Default HTTP client options
     *
     * @return string[]
     */
    protected function defaultConfig(): array
    {
        return [];
    }
}
