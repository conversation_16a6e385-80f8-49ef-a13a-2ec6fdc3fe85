<?php

namespace App\Http\Integrations\DailyCo\Controller;

use App\Http\Controllers\Controller;
use App\Http\Integrations\DailyCo\Events\MeetingEnded;
use App\Http\Integrations\DailyCo\Events\MeetingStarted;
use App\Http\Integrations\DailyCo\Events\ParticipantJoined;
use App\Http\Integrations\DailyCo\Events\ParticipantLeft;
use App\Http\Integrations\DailyCo\Events\WebhookEvent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    /**
     * Verify a webhook hmac.
     *
     * @see https://docs.daily.co/reference/rest-api/webhooks#hmac
     *
     */
    private function verifyWebhookSignature(Request $request): bool
    {
        // The HMAC secret key provided when the webhook was created
        $hmacSecret = config('dailyco.webhook_secret');

        // Extract the signature and timestamp from the headers
        $webhookTimestamp = $request->header('X-Webhook-Timestamp');
        $webhookSignature = $request->header('X-Webhook-Signature');

        if (!$webhookTimestamp || !$webhookSignature) {
            Log::channel('slack')->critical('Missing required webhook headers.');
            return false;
        }

        // Prepare the signature string
        $signature = $webhookTimestamp . '.' . json_encode($request->all());

        // Decode the HMAC secret key
        $base64DecodedSecret = base64_decode($hmacSecret);

        // Compute the HMAC signature
        $computedSignature = base64_encode(hash_hmac('sha256', $signature, $base64DecodedSecret, true));

        // Compare the computed signature with the provided one
        return hash_equals($computedSignature, $webhookSignature);
    }

    /**
     * Handle requests from Viva Wallet.
     *
     * @see https://developer.vivawallet.com/webhooks-for-payments/#handle-requests-from-viva-wallet
     */
    public function handle(Request $request): JsonResponse
    {
        Log::channel('slack')->critical("Daily Payload: ",  $request->all());

        if (!$this->verifyWebhookSignature($request)) {
            $headers = collect($request->header())->transform(function ($item) {
                return $item[0];
            })->toArray();

            Log::channel('slack')->critical('Invalid signature',  array_merge($headers, $request->all()));
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid signature',
            ], 400);
        }

        if ($request->has('type')) {
            /** @phpstan-ignore-next-line */
            $event = WebhookEvent::create($request->all());

            event($event);

            match ($event->data::class) {
                MeetingStarted::class => event($event->data),
                MeetingEnded::class => event($event->data),
                ParticipantJoined::class => event($event->data),
                ParticipantLeft::class => event($event->data),
                default => null,
            };
        }

        return response()->json();
    }
}
