<?php

namespace App\Http\Integrations\DailyCo\Facades;

use App\Http\Integrations\DailyCo\Contracts\DailyCo as ContractsDailyCo;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Http\Integrations\DailyCo\Resources\RoomResource room(?string $room_name)
 * @method static \App\Http\Integrations\DailyCo\Resources\WebhookResource webhook()

 *
 * @see \App\Http\Integrations\DailyCo\Contracts\DailyCo
 */

class DailyCo extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        self::clearResolvedInstance(ContractsDailyCo::class);

        return ContractsDailyCo::class;
    }
}
