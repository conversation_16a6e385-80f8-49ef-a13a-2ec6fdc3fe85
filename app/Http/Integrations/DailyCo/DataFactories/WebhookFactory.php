<?php

namespace App\Http\Integrations\DailyCo\DataFactories;

use App\Http\Integrations\DailyCo\DataObject\Webhook;
use Illuminate\Support\Collection;

final class WebhookFactory
{
    public static function new(array $attributes): Webhook
    {
        return (new self)->make(
            attributes: $attributes,
        );
    }

    public function make(array $attributes): Webhook
    {
        return Webhook::fromArray($attributes);
    }

    public static function collection(array $webhooks): Collection
    {
        return (new Collection(
            items: $webhooks,
        ))->map(fn($webhook): Webhook => self::new(attributes: $webhook));
    }
}
