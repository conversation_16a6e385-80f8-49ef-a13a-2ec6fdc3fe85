<?php

namespace App\Http\Integrations\DailyCo;

use App\Http\Integrations\DailyCo\Contracts\DailyCo;
use App\Http\Integrations\DailyCo\DailyCoConnector;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class DailyCoServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(DailyCo::class, function ($app) {
            return new DailyCoConnector(
                baseUrl: strval($app->make('config')->get('dailyco.base_url')),
                apiKey: strval($app->make('config')->get('dailyco.api_key')),
            );
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [DailyCo::class];
    }
}
