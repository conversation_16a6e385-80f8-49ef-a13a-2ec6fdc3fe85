<?php

namespace App\Http\Integrations\DailyCo\Resources;

use App\Http\Integrations\DailyCo\Requests\CreateMeetingToken;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class MeetingTokenResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected string $room_name,
    ) {}

    /**
     * Create a new meeting token.
     *
     *
     * @return string
     */
    public function create(
        string $user_name,
        string|int $user_id,
        bool $is_owner,
    ): string {
        $response = $this->connector
            ->send(new CreateMeetingToken(
                room_name: $this->room_name,
                user_name: $user_name,
                user_id: $user_id,
                is_owner: $is_owner,
            ));

        return $response->dto();
    }
}
