<?php

namespace App\Http\Integrations\DailyCo\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class CreateMeetingToken extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/meeting-tokens';
    }

    public function __construct(
        protected string $room_name,
        protected string $user_name,
        protected string|int $user_id,
        protected bool $is_owner,
    ) {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            "properties" => [
                "room_name" => $this->room_name,
                "is_owner" => $this->is_owner,
                "user_id" => $this->user_id,
                "user_name" => $this->user_name,
            ]
        ];
    }

    public function createDtoFromResponse(Response $response): string
    {
        return $response->json('token');
    }
}
