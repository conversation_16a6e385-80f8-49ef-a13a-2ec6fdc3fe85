<?php

namespace App\Http\Integrations\DailyCo\Requests;

use App\Http\Integrations\DailyCo\DataObject\Webhook;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class CreateWebhook extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/webhooks';
    }

    public function __construct(
        protected string $url,
        // meeting.started
        // meeting.ended
        // participant.joined
        // participant.left
        // waiting-participant.joined
        // waiting-participant.left
        // recording.started
        // recording.ready-to-download
        // recording.error
        // streaming.started
        // streaming.updated
        // streaming.ended
        // streaming.error
        // batch-processor.job-finished
        // batch-processor.error
        // dialout.connected
        // dialout.answered
        // dialout.stopped
        // dialout.warning
        // dialout.error
        // dialin.connected
        // dialin.stopped
        // dialin.warning
        // dialin.error
        protected array $eventTypes,
    ) {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            "url" => $this->url,
            "hmac" => config('dailyco.webhook_secret') ?? null,
            "eventTypes" => collect($this->eventTypes)->map(fn($et) => $et->value)->values()->all(),
            "retryType" => "exponential",
        ];
    }

    public function createDtoFromResponse(Response $response): Webhook
    {
        return Webhook::fromResponse($response);
    }
}
