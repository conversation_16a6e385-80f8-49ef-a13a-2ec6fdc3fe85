<?php

namespace App\Http\Integrations\DailyCo\Requests;

use App\Http\Integrations\DailyCo\DataFactories\WebhookFactory;
use Illuminate\Support\Collection;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class FetchWebhook extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::GET;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/webhooks';
    }

    public function __construct() {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    public function createDtoFromResponse(Response $response): Collection
    {
        return WebhookFactory::collection($response->json());
    }
}
