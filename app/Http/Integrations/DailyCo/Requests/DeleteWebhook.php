<?php

namespace App\Http\Integrations\DailyCo\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class DeleteWebhook extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::DELETE;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/webhooks/{$this->uuid}";
    }

    public function __construct(
        protected string $uuid,
    ) {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }


    public function createDtoFromResponse(Response $response): bool
    {
        if ($response->ok()) {
            return true;
        }

        return false;
    }
}
