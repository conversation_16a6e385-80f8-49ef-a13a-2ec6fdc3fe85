<?php

namespace App\Http\Integrations\DailyCo\DataObject;

use Carbon\Carbon;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Webhook implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $uuid,
        readonly public string $url,
        readonly public string $hmac,
        readonly public ?string $basicAuth,
        readonly public ?string $domainId,
        readonly public ?string $state,
        readonly public ?string $retryType,
        readonly public array $eventTypes,
        readonly public int $failedCount,

        readonly public Carbon $createdAt,
        readonly public ?Carbon $lastMomentPushed,
        readonly public ?Carbon $updatedAt,
    ) {}

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return self::fromArray($data);
    }

    public static function fromArray(array $data): self
    {
        return new static(
            uuid: $data['uuid'],
            url: $data['url'],
            hmac: $data['hmac'],
            basicAuth: isset($data['basicAuth']) ? $data['basicAuth'] : null,
            domainId: $data['domainId'],
            state: $data['state'],
            retryType: $data['retryType'],
            eventTypes: $data['eventTypes'] ?? [],
            failedCount: $data['failedCount'] ?? 0,
            createdAt: Carbon::parse($data['createdAt']),
            lastMomentPushed: isset($data['lastMomentPushed']) ? Carbon::parse($data['lastMomentPushed']) : null,
            updatedAt: isset($data['updatedAt']) ? Carbon::parse($data['updatedAt']) : null,
        );
    }
}
