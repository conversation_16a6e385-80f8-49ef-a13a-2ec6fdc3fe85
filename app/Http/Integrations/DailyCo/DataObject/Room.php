<?php

namespace App\Http\Integrations\DailyCo\DataObject;

use Carbon\Carbon;
use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class Room implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public string $id,
        readonly public string $name,
        readonly public string $privacy,
        readonly public string $url,
        readonly public Carbon $created_at,

        readonly public bool $api_created,
        readonly public bool $start_audio_off,
        readonly public bool $start_video_off,
        readonly public ?object $config = null,
    ) {}

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            id: $data['id'],
            name: $data['name'],
            api_created: !!$data['api_created'],
            privacy: $data['privacy'],
            url: $data['url'],
            created_at: Carbon::parse($data['created_at']),
            start_audio_off: isset($data['config']['start_audio_off']) ? $data['config']['start_audio_off'] : false,
            start_video_off: isset($data['config']['start_video_off']) ? $data['config']['start_video_off'] : false,
            config: isset($data['config']) ? (object) $data['config'] : null,
        );
    }
}
