<?php

namespace App\Http\Integrations\DailyCo\Enums;

enum WebhookEventType: string
{
    /** A meeting started event emits when <PERSON> begins a call. This occurs when a participant first joins a room. */
    case MeetingStarted = "meeting.started";

    /** A meeting ended event emits when Daily ends a call. This occurs when a participant leaves a room. */
    case MeetingEnded = "meeting.ended";

    /** A participant joined event emits when a participant joins a room. */
    case ParticipantJoined = "participant.joined";

    /** A participant left event emits when a participant leaves a room. */
    case ParticipantLeft = "participant.left";
}
