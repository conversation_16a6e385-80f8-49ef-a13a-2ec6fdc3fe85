<?php

namespace App\Http\Integrations\POSInfrasec\Events;


/** @see https://developer.vivawallet.com/webhooks-for-payments/account-connected/ */
class AccountEnrolled
{
    public function __construct(
        public readonly ?string $CCUId,
    ) {
    }

    public static function create(array $attributes): self
    {
        return new self(
            CCUId: $attributes['CCUId'] ?? null,
        );
    }
}
