<?php

namespace App\Http\Integrations\POSInfrasec\Resources;

use App\Http\Integrations\POSInfrasec\Requests\GetCCUControlCode;
use App\Http\Integrations\POSInfrasec\ValueObject\VatAmounts;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use Saloon\Contracts\Connector;
use Stripe\Refund;

/**
 * @property Connector $connector
 */
class CCUResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        public ?string $register_id,
    ) {
    }

    /**
     * CCU.
     *
     *
     * @return array
     */
    public function getCCUCode(
        string $date_time,
        string $organization_number,
        string $sequence_number,
        string $receipt_type,
        string $sale_amount,
        string $vat_25_percentage,
        string $vat_12_percentage,
        string $vat_6_percentage,
        string $vat_0_percentage,
        bool $refund = false,
    ): array {
        $response = $this->connector
            ->send(new GetCCUControlCode(
                date_time: $date_time,
                organization_number: $organization_number,
                register_id: $this->register_id,
                sequence_number: $sequence_number,
                receipt_type: $receipt_type,
                sale_amount: $sale_amount,
                vat_25_percentage: $vat_25_percentage,
                vat_12_percentage: $vat_12_percentage,
                vat_6_percentage: $vat_6_percentage,
                vat_0_percentage: $vat_0_percentage,
                refund:$refund,
            ));
        return $response->dto();
    }
}
