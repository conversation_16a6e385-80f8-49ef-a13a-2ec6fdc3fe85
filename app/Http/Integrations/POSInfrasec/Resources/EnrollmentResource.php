<?php

namespace App\Http\Integrations\POSInfrasec\Resources;

use App\Http\Integrations\POSInfrasec\Requests\ChangeEnrollment;
use App\Http\Integrations\POSInfrasec\Requests\CheckStatusEnrollment;
use App\Http\Integrations\POSInfrasec\Requests\CloseEnrollment;
use App\Http\Integrations\POSInfrasec\Requests\CreateNewEnrollment;
use App\Http\Integrations\POSInfrasec\Requests\OpenEnrollment;
use App\Http\Integrations\POSInfrasec\ValueObject\StoreInfo;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class EnrollmentResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        public string $registerCounterNumber,
        public string $registerMake,
        public string $registerModel,
        public string $partnerName,
        public string $partnerCode,
        public string $posAuthorityCode,
        public ?string $registerId,
    ) {
    }

    /**
     * Create a new enrollment.
     *
     *
     * @return string
     */
    public function create(
        string $companyName,
        string $organizationNumber,
        StoreInfo $store,
    ): string {
        $response = $this->connector
            ->send(new CreateNewEnrollment(
                PartnerName: $this->partnerName,
                PartnerCode: $this->partnerCode,
                POSAuthorityCode: $this->posAuthorityCode,

                StoreID: $store->id,
                StoreName: $store->name,
                StoreEmail: $store->email,
                StoreCellphone: $store->cellphone,
                StoreAddress: $store->address,
                StoreZipcode: $store->zipcode,
                StoreCity: $store->city,

                CompanyInfoOrganizationNumber: $organizationNumber,
                CompanyInfoCompany: $companyName,
                CompanyInfoAddress: $store->address,
                CompanyInfoZipcode: $store->zipcode,
                CompanyInfoCity: $store->city,

                RegisterID: '',
                RegisterMake: $this->registerMake,
                RegisterModel: $this->registerModel,
                RegisterLocalAlias: "", //optional
                RegisterCounterNumber: $this->registerCounterNumber, // Optional If not given next available number for the store will be allocated
                RegisterAddress: $store->address,
                RegisterZipcode: $store->zipcode,
                RegisterCity: $store->city,

                JournalLocationCompany: $store->name,
                JournalLocationAddress: $store->address,
                JournalLocationZipcode: $store->zipcode,
                JournalLocationCity: $store->city,

                OperationLocationCompany: $store->name,
                OperationLocationAddress: $store->address,
                OperationLocationZipcode: $store->zipcode,
                OperationLocationCity: $store->city,
            ));

        return $response->dto();
    }

    /**
     * change Branch/Chain/Store/Register with service-related information. .
     *
     *
     * @return string
     */
    public function change(
        string $companyName,
        string $organizationNumber,
        StoreInfo $store,
    ): string {
        $response = $this->connector
            ->send(new ChangeEnrollment(
                PartnerName: $this->partnerName,
                PartnerCode: $this->partnerCode,
                POSAuthorityCode: $this->posAuthorityCode,

                StoreID: $store->id,
                StoreName: $store->name,
                StoreEmail: $store->email,
                StoreCellphone: $store->cellphone,
                StoreAddress: $store->address,
                StoreZipcode: $store->zipcode,
                StoreCity: $store->city,

                CompanyInfoOrganizationNumber: $organizationNumber,
                CompanyInfoCompany: $companyName,
                CompanyInfoAddress: $store->address,
                CompanyInfoZipcode: $store->zipcode,
                CompanyInfoCity: $store->city,

                RegisterID: $this->registerId,
                RegisterMake: $this->registerMake,
                RegisterModel: $this->registerModel,
                RegisterLocalAlias: "", //optional
                RegisterCounterNumber: $this->registerCounterNumber, // Optional If not given next available number for the store will be allocated
                RegisterAddress: $store->address,
                RegisterZipcode: $store->zipcode,
                RegisterCity: $store->city,

                JournalLocationCompany: $store->name,
                JournalLocationAddress: $store->address,
                JournalLocationZipcode: $store->zipcode,
                JournalLocationCity: $store->city,

                OperationLocationCompany: $store->name,
                OperationLocationAddress: $store->address,
                OperationLocationZipcode: $store->zipcode,
                OperationLocationCity: $store->city,
            ));

        return $response->dto();
    }

    /**
     * Reopen an already closed and registered Register for CCU slot.
     *
     *
     * @return string
     */
    public function open(): string
    {
        $response = $this->connector
            ->send(new OpenEnrollment(
                registerId: $this->registerId,
            ));

        return $response->dto();
    }


    /**
     * Close a Register with CCU slot and or related services.
     *
     * @return string
     */
    public function close(): string
    {
        $response = $this->connector
            ->send(new CloseEnrollment(
                registerId: $this->registerId,
            ));
        return $response->dto();
    }

    /**
     * check a status of enrollment using register id.
     *
     * @return array
     */
    public function checkStatus(): array
    {
        $response = $this->connector
            ->send(new CheckStatusEnrollment(
                registerId: $this->registerId,
            ));

        return $response->dto();
    }
}
