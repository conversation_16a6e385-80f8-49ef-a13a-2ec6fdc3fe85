<?php

namespace App\Http\Integrations\POSInfrasec\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class ChangeEnrollment extends Request implements HasBody
{
    use HasJsonBody;


    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return 'api/EnrollCCU.php';
    }

    public function __construct(
        protected string $PartnerName,
        protected string $PartnerCode,
        protected string $POSAuthorityCode,

        protected int $StoreID,
        protected string $StoreName,
        protected string $StoreEmail,
        protected string $StoreCellphone,
        protected string $StoreAddress,
        protected string $StoreZipcode,
        protected string $StoreCity,

        protected string $CompanyInfoOrganizationNumber,
        protected string $CompanyInfoCompany,
        protected string $CompanyInfoAddress,
        protected string $CompanyInfoZipcode,
        protected string $CompanyInfoCity,

        protected string $RegisterID,
        protected string $RegisterMake,
        protected string $RegisterModel,
        protected string $RegisterLocalAlias,
        protected string $RegisterCounterNumber,
        protected string $RegisterAddress,
        protected string $RegisterZipcode,
        protected string $RegisterCity,

        protected string $OperationLocationCompany,
        protected string $OperationLocationAddress,
        protected string $OperationLocationZipcode,
        protected string $OperationLocationCity,

        protected string $JournalLocationCompany,
        protected string $JournalLocationAddress,
        protected string $JournalLocationZipcode,
        protected string $JournalLocationCity,

    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'IdmRequest' => [
                'EnrollData' => [
                    'Action' => 'CHANGE',
                    'PartnerAuthorit' => [
                        "PartnerName" => $this->PartnerName,
                        "PartnerCode" => $this->PartnerCode,
                        "POSAuthorityCode" => $this->POSAuthorityCode,
                    ],
                    'StoreInfo' => [
                        "StoreID" => $this->StoreID,
                        "StoreName" => $this->StoreName,
                        "Email" => $this->StoreEmail,
                        "Cellphone" => $this->StoreCellphone,
                        "Address" => $this->StoreAddress,
                        "Zipcode" => $this->StoreZipcode,
                        "City" => $this->StoreCity,
                    ],
                    'CompanyInfo' => [
                        "OrganizationNumber" => $this->CompanyInfoOrganizationNumber,
                        "Company" => $this->CompanyInfoCompany,
                        "Address" => $this->CompanyInfoAddress,
                        "Zipcode" => $this->CompanyInfoZipcode,
                        "City" => $this->CompanyInfoCity,
                    ],
                    'RegisterInfo' => [
                        "RegisterID" => $this->RegisterID,
                        "RegisterMake" => $this->RegisterMake,
                        "RegisterModel" => $this->RegisterModel,
                        "LocalAlias" => $this->RegisterLocalAlias,
                        "CounterNumber" => $this->RegisterCounterNumber,
                        "Address" => $this->RegisterAddress,
                        "Zipcode" => $this->RegisterZipcode,
                        "City" => $this->RegisterCity,
                    ],
                    'JournalLocation' => [
                        "Company" => $this->JournalLocationCompany,
                        "Address" => $this->JournalLocationAddress,
                        "Zipcode" => $this->JournalLocationZipcode,
                        "City" => $this->JournalLocationCity,
                    ],
                    'OperationLocation' => [
                        "Company" => $this->OperationLocationCompany,
                        "Address" => $this->OperationLocationAddress,
                        "Zipcode" => $this->OperationLocationZipcode,
                        "City" => $this->OperationLocationCity,
                    ],
                    'PcxService' => [
                        'CCU' => [
                            'Enable' => 'Yes'
                        ]
                    ],
                    'Swish' => [
                        "Enable" => 'No',
                        "SwishNr" => '',
                        "SwishType" => '2',
                    ]
                ],
            ],
        ];
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        $exception = $this->getRequestException($response, null);

        if ($exception) {
            throw $exception;
        }
        return $response->json()['IdmResponse']['RegisterID'];
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        $data = $response->json()['IdmResponse'];
        //TODO::need to handle this errors and response
        if($data['ResponseCode'] == 181) {
            return new InternalServerErrorException($response, __('validation.unique',['attribute'=>'company name']), $data['ResponseCode'], $senderException);
        }

        if(!in_array($data['ResponseCode'],[0,197])) {
            $error_reason = $data['ResponseReason'];
            $error_message = $data['ResponseMessage'];
            return new InternalServerErrorException($response, $error_reason ?? $error_message, $data['ResponseCode'], $senderException);
        }
        return null;
    }
}
