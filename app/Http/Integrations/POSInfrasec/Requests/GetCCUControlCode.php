<?php

namespace App\Http\Integrations\POSInfrasec\Requests;

use Illuminate\Support\Facades\Log;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Traits\Body\HasXmlBody;
use \Throwable;

class GetCCUControlCode extends Request implements HasBody
{
    use HasXmlBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return '/ctuserver';
    }

    public function __construct(
        protected string $register_id,
        protected string $date_time,
        protected string $organization_number,
        protected string $sequence_number,
        protected string $receipt_type,
        protected string $sale_amount,
        protected string $vat_25_percentage,
        protected string $vat_12_percentage,
        protected string $vat_6_percentage,
        protected string $vat_0_percentage,
        protected bool $refund,
    ) {
        // dd($refund);
        // dd($this->defaultBody());
    }

    protected function defaultBody(): string
    {
        $number_format = function ($number) {
            return number_format($number, 2, ",", "");
        };
        if ($this->refund) {
            return $this->refundBody();
        }
        return <<<XML
            <CtuRequest>
                <ApplicationID>Meridiq Back Office</ApplicationID>
                <RequestID>$this->sequence_number</RequestID>
                <ControlData>
                    <DateTime>$this->date_time</DateTime>
                    <OrgNr>$this->organization_number</OrgNr>
                    <ManRegisterID>$this->register_id</ManRegisterID>
                    <SequenceNumber>$this->sequence_number</SequenceNumber>
                    <ReceiptType Type="$this->receipt_type" />
                    <SaleAmount>{$number_format($this->sale_amount)}</SaleAmount>
                    <VAT1>
                        <Percent>25,00</Percent>
                        <Amount>{$number_format($this->vat_25_percentage)}</Amount>
                    </VAT1>
                    <VAT2>
                        <Percent>12,00</Percent>
                        <Amount>{$number_format($this->vat_12_percentage)}</Amount>
                    </VAT2>
                    <VAT3>
                        <Percent>6,00</Percent>
                        <Amount>{$number_format($this->vat_6_percentage)}</Amount>
                    </VAT3>
                    <VAT4>
                        <Percent>0,00</Percent>
                        <Amount>{$number_format($this->vat_0_percentage)}</Amount>
                    </VAT4>
                </ControlData>
            </CtuRequest>
        XML;
    }

    protected function refundBody(): string
    {

        $number_format = function ($number) {
            return number_format($number, 2, ",", "");
        };

        return <<<XML
            <CtuRequest>
                <ApplicationID>Meridiq Back Office</ApplicationID>
                <RequestID>$this->sequence_number</RequestID>
                <ControlData>
                    <DateTime>$this->date_time</DateTime>
                    <OrgNr>$this->organization_number</OrgNr>
                    <ManRegisterID>$this->register_id</ManRegisterID>
                    <SequenceNumber>$this->sequence_number</SequenceNumber>
                    <ReceiptType Type="$this->receipt_type"/>
                    <RefundAmount>{$number_format($this->sale_amount)}</RefundAmount>
                    <VAT1>
                        <Percent>25,00</Percent>
                        <Amount>-{$number_format($this->vat_25_percentage)}</Amount>
                    </VAT1>
                    <VAT2>
                        <Percent>12,00</Percent>
                        <Amount>-{$number_format($this->vat_12_percentage)}</Amount>
                    </VAT2>
                    <VAT3>
                        <Percent>6,00</Percent>
                        <Amount>-{$number_format($this->vat_6_percentage)}</Amount>
                    </VAT3>
                    <VAT4>
                        <Percent>0,00</Percent>
                        <Amount>-{$number_format($this->vat_0_percentage)}</Amount>
                    </VAT4>
                </ControlData>
            </CtuRequest>
        XML;
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        $exception = $this->getRequestException($response, null);
        if ($exception) {
            throw $exception;
        }
        $json = json_encode($response->xml());
        $data = json_decode($json, TRUE);

        if (app()->environment() == 'testing') {
            Log::channel('slack')->critical($this->defaultBody());
        }

        return $data['ControlCode'];
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        $json = json_encode($response->xml());
        $data = json_decode($json, TRUE);

        if (!in_array($data['ResponseCode'], [0])) {
            $error_reason = $data['ResponseReason'];
            $error_message = $data['ResponseMessage'];
            return new InternalServerErrorException($response, $error_message ?? $error_reason, $data['ResponseCode'], $senderException);
        }

        return null;
    }
}
