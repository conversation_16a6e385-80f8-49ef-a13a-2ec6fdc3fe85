<?php

namespace App\Http\Integrations\POSInfrasec\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Exceptions\Request\Statuses\InternalServerErrorException;
use Saloon\Traits\Body\HasJsonBody;
use \Throwable;

class CheckStatusEnrollment extends Request implements HasBody
{
    use HasJsonBody;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return 'api/EnrollCCU.php';
    }

    public function __construct(
        protected string $registerId,
    ) {
    }

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'IdmRequest' => [
                'EnrollData' => [
                    'Action' => 'STATUS',
                    'RegisterInfo' => [
                        "RegisterID" => $this->registerId,
                    ],
                ],
            ],
        ];
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        $exception = $this->getRequestException($response, null);
        if ($exception) {
            throw $exception;
        }
        return $response->json()['IdmResponse'];
    }

    public function getRequestException(Response $response, ?Throwable $senderException): ?Throwable
    {
        $data = $response->json()['IdmResponse'];
        //TODO::need to handle this errors and response

        if(!in_array($data['ResponseCode'],[0,197])) {
            $error_reason = $data['ResponseReason'];
            $error_message = $data['ResponseMessage'];
            return new InternalServerErrorException($response, $error_reason ?? $error_message, $data['ResponseCode'], $senderException);
        }

        return null;
    }
}
