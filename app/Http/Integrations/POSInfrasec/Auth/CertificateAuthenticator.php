<?php

declare(strict_types=1);

namespace App\Http\Integrations\POSInfrasec\Auth;

use GuzzleHttp\RequestOptions;
use Saloon\Contracts\Authenticator;
use Saloon\Contracts\PendingRequest;
use Saloon\Http\Senders\GuzzleSender;
use Saloon\Exceptions\SaloonException;

class CertificateAuthenticator implements Authenticator
{
    /**
     * Constructor
     */
    public function __construct(
        public string  $certPath,
        public ?string  $caPath,
        public ?string $password = null,
    ) {
        //
    }

    /**
     * Apply the authentication to the request.
     *
     * @throws \Saloon\Exceptions\SaloonException
     */
    public function set(PendingRequest $pendingRequest): void
    {
        if (! $pendingRequest->getConnector()->sender() instanceof GuzzleSender) {
            throw new SaloonException('The CertificateAuthenticator is only supported when using the GuzzleSender.');
        }

        // See: https://docs.guzzlephp.org/en/stable/request-options.html#cert

        $caPath = $this->caPath;
        $certPath = $this->certPath;
        $password = $this->password;

        $certificate = is_string($password) ? [$certPath, $password] : $certPath;
        if(!empty($caPath)) {
            $pendingRequest->config()->add(RequestOptions::VERIFY, $caPath);
        }
        $pendingRequest->config()->add(RequestOptions::CERT, $certificate);
    }
}