<?php

namespace App\Http\Integrations\POSInfrasec;

use App\Http\Integrations\POSInfrasec\Auth\CertificateAuthenticator;
use App\Http\Integrations\POSTaxControlUnit\Contracts\POSTaxControlUnit;
use App\Http\Integrations\POSInfrasec\Enums\Environment;
use App\Http\Integrations\POSInfrasec\Requests\GetCCUControlCode;
use App\Http\Integrations\POSInfrasec\Resources\CCUResource;
use App\Http\Integrations\POSInfrasec\Resources\EnrollmentResource;
use GuzzleHttp\Psr7\Uri;
use Psr\Http\Message\UriInterface;
use Saloon\Contracts\PendingRequest;
use Saloon\Helpers\URLHelper;
use Saloon\Http\Connector;
use Saloon\Http\PendingRequest as HttpPendingRequest;
use Saloon\Traits\OAuth2\ClientCredentialsGrant;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class InfrasecConnector extends Connector implements POSTaxControlUnit
{
    use ClientCredentialsGrant, AlwaysThrowOnErrors;

    /**
     * Demo environment Signup URL.
     */
    public const DEMO_API_URL = 'https://idm-verify.infrasec.se';

    /**
     * Demo environment Signup URL.
     */
    public const DEMO_CCU_API_URL = 'https://ccu-verify.infrasec.se';

    /**
     * Production environment URL.
     */
    public const PRODUCTION_API_URL = 'https://ccu-idm.infrasec.se';

    /**
     * Production environment URL.
     */
    public const PRODUCTION_CCU_API_URL = 'https://ccu.infrasec.se';




    public function __construct(
        protected Environment $environment,
        protected string $registerCounterNumber,
        protected string $registerMake,
        protected string $registerModel,
        protected string $partnerName,
        protected string $partnerCode,
        protected string $posAuthorityCode,

        protected string $certPath,
        protected string $caPath,
        protected string $certPassword,

        protected string $ccucertPath,
        protected string $ccucaPath,
        protected string $ccucertPassword,
    ) {
    }

    public function boot(PendingRequest $pendingRequest): void
    {
        if ($pendingRequest instanceof HttpPendingRequest) {
            if ($pendingRequest->getRequest() instanceof GetCCUControlCode) {
                $pendingRequest->setUrl(URLHelper::join($this->getCCUUrl(), $pendingRequest->getRequest()->resolveEndpoint()));
                $pendingRequest->getConnector()->authenticate($this->ccuAuth());
                $pendingRequest->authenticate($this->ccuAuth());
            }
        }
    }

    /**
     * Use the production or demo environment.
     */
    public function withEnvironment(Environment|string $environment): self
    {
        $this->environment = is_string($environment) ? Environment::from($environment) : $environment;

        return $this;
    }

    /**
     * The Base URL of the API
     *
     * @return string
     */
    public function resolveBaseUrl(): string
    {
        return $this->getApiUrl();
    }
    /**
     * Get the API URL.
     */
    public function getApiUrl(): UriInterface
    {
        return new Uri(match ($this->environment) {
            Environment::Production => self::PRODUCTION_API_URL,
            Environment::Demo => self::DEMO_API_URL,
        });
    }


    public function getCCUUrl(): UriInterface
    {
        return new Uri(match ($this->environment) {
            Environment::Production => self::PRODUCTION_CCU_API_URL,
            Environment::Demo => self::DEMO_CCU_API_URL,
        });
    }

    protected function defaultAuth(): CertificateAuthenticator
    {
        return $this->apiAuth();
    }

    protected function apiAuth(): CertificateAuthenticator
    {
        return new CertificateAuthenticator(
            certPath: $this->certPath,
            caPath: $this->caPath,
            password: $this->certPassword,
        );
    }

    protected function ccuAuth(): CertificateAuthenticator
    {
        return new CertificateAuthenticator(
            certPath: $this->ccucertPath,
            caPath: $this->ccucaPath,
            password: $this->ccucertPassword,
        );
    }

    public function enrollment(?string $registerId = null): EnrollmentResource
    {
        return new EnrollmentResource(
            connector: $this,
            registerId: $registerId,
            registerCounterNumber: $this->registerCounterNumber,
            registerMake: $this->registerMake,
            registerModel: $this->registerModel,
            partnerName: $this->partnerName,
            partnerCode: $this->partnerCode,
            posAuthorityCode: $this->posAuthorityCode
        );
    }

    public function ccu(?string $registerId = null): CCUResource
    {
        return new CCUResource(
            connector: $this,
            register_id: $registerId,
        );
    }
}
