<?php

namespace App\Http\Integrations\POSInfrasec\DataObject;

use Saloon\Contracts\DataObjects\WithResponse;
use Saloon\Contracts\Response;
use Saloon\Traits\Responses\HasResponse;

class NewEnrollment implements WithResponse
{
    use HasResponse;

    public function __construct(
        readonly public ?string $CCUId,
        readonly public string $registerId,
    ) {
    }

    public static function fromResponse(Response $response): self
    {
        $data = $response->json();

        return new static(
            CCUId: $data['CCUId'] ?? null,
            registerId: $data['registerId'] ?? null,
        );
    }
}
