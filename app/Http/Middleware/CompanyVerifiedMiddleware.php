<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CompanyVerifiedMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!auth()?->user()?->company?->verified()) {
            return response()->json([
                'message' => 'Account is not verified',
                'status' => '0'
            ], 403);
        }

        return $next($request);
    }
}
