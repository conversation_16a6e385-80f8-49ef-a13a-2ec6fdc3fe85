<?php

namespace App\Http\Middleware;

use Closure;
use Laravel\Passport\Passport;

class PassportCookieToHeader
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!$request->bearerToken()) {
            if ($request->hasCookie(Passport::cookie())) {
                $token = $request->cookie(Passport::cookie());
                $request->headers->add(['Authorization' => 'Bearer ' . $token]);
            }
        }

        return $next($request);
    }
}
