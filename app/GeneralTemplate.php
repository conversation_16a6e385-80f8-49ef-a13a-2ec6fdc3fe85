<?php

namespace App;

use App\Models\GeneralTemplateQuestion;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GeneralTemplate extends Model
{
    use HasFactory;

    const PRESCRIPTION = "PRESCRIPTION", SMS = "SMS", EMAIL = "EMAIL", MANAGEMENT_DOCUMENT = "MANAGEMENT_DOCUMENT";

    const PATIENT_SAFETY="PATIENT_SAFETY",DEVIATION_MGMT="DEVIATION_MGMT",THE_BUSINESS="THE_BUSINESS";

    //TABLE
    public $table = 'general_templates';

    //FILLABLES
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'is_active',
        'type',
        'action',
        'language',
        'process',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    public function questions()
    {
        if (request()->has('orderBy')) {
            return $this->hasMany(GeneralTemplateQuestion::class);
        }
        return $this->hasMany(GeneralTemplateQuestion::class)->orderBy('order');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}

    static function getGeneralTemplate($user, $type)
    {
        $general_templates = GeneralTemplate::where(['type' => $type, 'is_active' => 1])->get();

        switch ($type) {
            case 'PRESCRIPTION':
                $treatments = Treatment::withTrashed()->where('company_id', $user->company_id)->cursor();

                $templates_to_add = [];
                foreach ($general_templates as $general_prescription_template) {
                    $filtered_treatment = $treatments->filter(function ($treatment) use ($general_prescription_template) {
                        if ($treatment->name == $general_prescription_template->name) {
                            return true;
                        } else {
                            return false;
                        }
                    });
                    if (count($filtered_treatment) <= 0) {
                        array_push($templates_to_add, $general_prescription_template);
                    } else {
                        foreach ($filtered_treatment as $treatment) {
                            if (!$treatment->is_changed) {
                                $treatment->notes = $general_prescription_template->description;
                                $treatment->save();
                            }
                        }
                    }
                }

                foreach ($templates_to_add as $template_to_add) {
                    Treatment::create([
                        'company_id' => $user->company_id,
                        'name' => $template_to_add->name,
                        'color' => "#000000",
                        'unit' => "usd",
                        'notes' => $template_to_add->description,
                    ]);
                }
                break;
            case 'SMS':
                $sms_templates = SMSTemplate::where('company_id', $user->company_id)->cursor();

                $templates_to_add = [];
                foreach ($general_templates as $general_sms_template) {
                    $filtered_treatment = $sms_templates->filter(function ($sms_template) use ($general_sms_template) {
                        if ($sms_template->title == $general_sms_template->name) {
                            return true;
                        } else {
                            return false;
                        }
                    });
                    if (count($filtered_treatment) <= 0) {
                        array_push($templates_to_add, $general_sms_template);
                    } else {
                        foreach ($filtered_treatment as $treatment) {
                            if (!$treatment->is_changed) {
                                $treatment->description = $general_sms_template->description;
                                $treatment->save();
                            }
                        }
                    }
                }

                foreach ($templates_to_add as $template_to_add) {
                    SMSTemplate::create([
                        'company_id' => $user->company_id,
                        'title' => $template_to_add->name,
                        'description' => $template_to_add->description,
                    ]);
                }
                break;
            case 'EMAIL':
                $templates = EmailTemplate::where('company_id', $user->company_id)->cursor();

                $templates_to_add = [];
                foreach ($general_templates as $general_sms_template) {
                    $filtered_treatment = $templates->filter(function ($sms_template) use ($general_sms_template) {
                        if ($sms_template->title == $general_sms_template->name) {
                            return true;
                        } else {
                            return false;
                        }
                    });
                    if (count($filtered_treatment) <= 0) {
                        array_push($templates_to_add, $general_sms_template);
                    } else {
                        foreach ($filtered_treatment as $treatment) {
                            if (!$treatment->is_changed) {
                                $treatment->description = $general_sms_template->description;
                                $treatment->save();
                            }
                        }
                    }
                }

                foreach ($templates_to_add as $template_to_add) {
                    EmailTemplate::create([
                        'company_id' => $user->company_id,
                        'title' => $template_to_add->name,
                        'description' => $template_to_add->description,
                    ]);
                }
                break;
            default:
                break;
        }
    }
}
