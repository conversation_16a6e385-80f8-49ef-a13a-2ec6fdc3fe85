<?php

namespace App;

use App\Traits\Encryptable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CompanyReceiptRefundItem extends Model
{
    use HasFactory, Encryptable;

    const TAX_TYPE_INCLUDING = 'including',
        TAX_TYPE_EXCLUDING = 'excluding';

    //TABLE
    // public $table = '';

    //FILLABLES
    protected $fillable = [
        'refund_id',
        'receipt_item_id',
        'name',
        'selling_price',
        'quantity',
        'tax_information',
        'refundable_type',
        'refundable_id',
    ];

    protected $encrypted = [
        'name',
        'selling_price',
        'quantity',
        'tax_information',
    ];


    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];


    public function refundable(): MorphTo
    {
        return $this->morphTo();
    }

    //RELATIONSHIPS
    public function receipt_item()
    {
        return $this->belongsTo(CompanyReceiptItem::class, 'receipt_item_id', 'id');
    }

    //ATTRIBUTES
    public function getTaxTypeAttribute()
    {
        return self::TAX_TYPE_INCLUDING;
    }

    //ATTRIBUTES
    public function getTaxAmountAttribute()
    {
        $tax_percentage = ($this->tax_information ?? 0);

        $tax = $this->selling_price * ($tax_percentage / 100);

        return CompanyReceipt::roundValue($tax);
    }

    //ATTRIBUTES
    public function getPriceWithTaxAttribute()
    {
        return CompanyReceipt::roundValue($this->selling_price + $this->tax_amount);
    }

    //ATTRIBUTES
    public function getTotalTaxAmountAttribute()
    {
        return CompanyReceipt::roundValue($this->tax_amount * $this->quantity);
    }

    //ATTRIBUTES
    public function getTotalAttribute()
    {
        return CompanyReceipt::roundValue($this->sub_total + $this->total_tax_amount);
    }

    //ATTRIBUTES
    public function getSubTotalAttribute()
    {
        if ($this->tax_type == self::TAX_TYPE_INCLUDING) {
            return CompanyReceipt::roundValue(($this->selling_price - $this->tax_amount) * $this->quantity);
        }

        return CompanyReceipt::roundValue($this->selling_price * $this->quantity);
    }
}
