<!DOCTYPE html>
<html lang="@{{ $lang ?? 'en' }}" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <title>@{{ $title ?? '' }}</title>

  <!-- HTML in your document's head -->
  <link rel="preconnect" href="https://rsms.me/">
  <link rel="stylesheet" href="https://rsms.me/inter/inter.css">

  <style>
    :root {
      font-family: Inter, sans-serif;
      font-feature-settings: 'liga' 1, 'calt' 1; /* fix for Chrome */
    }
    @supports (font-variation-settings: normal) {
      :root { font-family: InterVariable, sans-serif; }
    }

    {{{ page.css }}}
  </style>
  <stack name="head" />
</head>
<body class="m-0 p-0 w-full [word-break:break-word] bg-slate-50 [-webkit-font-smoothing:antialiased]">
  @if($preheader ?? null)
    <div class="hidden">
      @{{ $preheader ?? '' }}
      <each loop="item in Array.from(Array(150))">&#8199;&#65279;&#847; </each>
    </div>
  @endif
  <div role="article" aria-roledescription="email" aria-label="@{{ $title ?? '' }}" lang="@{{ $lang ?? 'en' }}">
    <content />
  </div>
</body>
</html>
