<x-main>
  <div class="bg-white text-dimGray1">
    <table align="center">
      <tr>
        <td class="w-[600px] max-w-full ">
          <div class="rounded-2xl border-primary border-4 border-solid my-12 overflow-hidden">

            <table class="w-full table-fixed">

              <tr>
                <td colspan="2">
                  <table role="presentation" class="px-8" width="100%" cellpadding="0" cellspacing="0" border="0">
                    <tr class="md:table w-full">
                      <td class="text-left md:table-row w-full">
                        <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
                          <tr class="md:table w-full">
                            @if(isset($logo) && $logo)
                            <td class="text-left">
                              <div class="m-0 py-7 md:pb-5">
                                <img src="@{{ urldecode($logo) }}" class="inline max-w-14 max-h-14 mr-4" onerror="this.closest('td').remove()" />
                              </div>
                            </td>
                            @endif
                            <td class="text-left w-full">
                              <div class="m-0 py-7 md:pb-5">
                                <p class="inline align-middle text-black font-black text-2xl">@{{ $company_name ?? "" }}</p>
                              </div>
                            </td>
                          </tr>
                        </table>
                      </td>
                      @if(isset($video_call_url) && $video_call_url)
                      <td class="text-right md:text-center md:table-row w-full">
                        <div class="ml-8 md:ml-0 py-7 md:pt-5">
                          <a target="_blank" class="outline-none no-underline cursor-pointer inline-block" href="@{{ $video_call_url }}">
                            <button class="block outline-none select-none no-underline cursor-pointer border font-medium whitespace-nowrap 
                            rounded-lg border-transparent px-4 py-2 text-sm  bg-primary text-white">
                              @{{ __('strings.join_video_call') }}
                            </button>
                          </a>
                        </div>
                      </td>
                      @endif
                    </tr>
                  </table>
                </td>
              </tr>

              <tr role="separator">
                <td colspan="2" class="">
                  <x-divider space-y="0px" class="bg-lightPurple mx-8" />
                </td>
              </tr>

              <tr>
                <td colspan="2" class="p-8 text-base rounded">
                  @if(!isset($showIntro) || $showIntro === null || $showIntro === true)
                  <h1 class="m-0 mb-6  text-lg text-dimGray1 font-medium">
                    @{{ __('strings.hello') }} @{{ $name ?? '' }},
                  </h1>
                  @endif

                  <div class="text-dimGray1">
                    <content />
                  </div>

                  @if(!isset($showOutro) || $showOutro === null || $showOutro === true)
                  <p class="mt-8 text-start text-dimGray1">
                    <span class="block font-normal">@{{ __('strings.best_regards') }},</span>
                    <span class="block mt-1 text-lightBlack font-bold">@{{ $company_name ?? '' }} Team</span>
                  </p>
                  @endif
                </td>
              </tr>

              <tr role="separator">
                <td colspan="2" class="">
                  <x-divider space-y="0px" class="bg-lightPurple mx-8" />
                </td>
              </tr>

              <tr class="text-base text-dimGray  ">
                <td colspan="2" class="text-center text-xs p-6">
                  <p class="m-0 italic">
                    @{{ __('strings.email_data.you_are_receiving_email_beacause',['company_name' => $company_name ?? '']) }}
                  </p>

                  <p class="m-0 italic">
                    @{{ __('strings.email_data.copyright_year',['year' => date('Y')]) }}
                  </p>

                </td>
              </tr>
            </table>
          </div>

        </td>
      </tr>
    </table>
  </div>
</x-main>