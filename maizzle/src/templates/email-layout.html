<x-common>
  @yield('content',"")

  @if (isset($should_show_url) && $should_show_url)
  <div style="text-align: center;">
    <table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
      <tr>
        <td align="center" style="padding: 10px;">
          <a target="_blank" href="@{{ $add_calendar_url }}" class="underline text-sm" style="color: #1a1b1c;">
            @{{ __('strings.generic_booking_mail_add_calendar') }}
          </a>
        </td>
        <td align="center" style="padding: 10px;">
          <a target="_blank" href="@{{ $cancel_url }}" class="underline text-sm" style="color: #1a1b1c;">
            @{{ __('strings.generic_booking_cancel_booking') }}
          </a>
        </td>
        @if (!$group_booking)
        <td align="center" style="padding: 10px;">
          <a target="_blank" href="@{{ $modify_url }}" class="underline text-sm" style="color: #1a1b1c;">
            @{{ __('strings.generic_booking_modify_booking') }}
          </a>
        </td>
        @endif
      </tr>
    </table>
  </div>
  @endif
</x-common>
