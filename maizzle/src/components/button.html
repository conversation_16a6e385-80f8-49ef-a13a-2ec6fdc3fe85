<script props>
  // https://maizzle.com/docs/components/button
  module.exports = {
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
    }[props.align],
    href: props.href,
    msoPt: props['mso-pt'] || '16px',
    msoPb: props['mso-pb'] || '30px',
    color: props.color || '#f8fafc',
    bgColor: props['bg-color'] || '#4338ca',
  }
</script>

<div class="{{ align }}">
  <a
    attributes
    href="{{ href }}"
    class="inline-block px-6 py-4 font-semibold no-underline rounded text-base/none"
    style="{{ color && `color: ${color};` }} {{ bgColor && `background-color: ${bgColor};` }}"
  >
    <outlook>
      <i class="mso-font-width-[150%]" style="mso-text-raise: {{ msoPb }};" hidden>&emsp;</i>
    </outlook>
    <span style="mso-text-raise: {{ msoPt }}"><content /></span>
    <outlook>
      <i class="mso-font-width-[150%]" hidden>&emsp;&#8203;</i>
    </outlook>
  </a>
</div>
