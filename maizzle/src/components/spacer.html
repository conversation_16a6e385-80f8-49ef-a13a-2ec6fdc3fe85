<script props>
  // https://maizzle.com/docs/components/spacer
  module.exports = {
    height: props.height,
    msoHeight: props['mso-height'],
  }
</script>

<if condition="height">
  <div
    attributes
    role="separator"
    style="{{ height && `line-height: ${height}` }};
      {{ msoHeight && `mso-line-height-alt: ${msoHeight}` }};
    "
  >&zwj;</div>
</if>
<else>
  <div role="separator">&zwj;</div>
</else>
