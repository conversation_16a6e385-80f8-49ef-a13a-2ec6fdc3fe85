<script props>
  module.exports = {
    width: props.width || '600px',
    height: props.height || '400px',
    stroke: props.stroke || 'f',
    strokecolor: props.strokecolor || 'none',
    inset: props.inset || '0,0,0,0',
    image: props.image || 'https://via.placeholder.com/600x400'
  }
</script>

<!--[if mso]>
<v:image src="{{{ image }}}" style="width: {{ width }}; height: {{ height }};" xmlns:v="urn:schemas-microsoft-com:vml" />
<v:rect fill="f" stroke="{{ strokecolor ? 't' : stroke }}"{{{ strokecolor ? ` strokecolor="${strokecolor}"` : '' }}} style="position: absolute; width: {{ width }}; height: {{ height }};" xmlns:v="urn:schemas-microsoft-com:vml">
<v:textbox inset="{{ inset }}"><div><![endif]-->
<content />
<!--[if mso]></div></v:textbox></v:rect><![endif]-->
