<?php

use App\Models\GeneralTemplateQuestion;

return [
    'type' => [
        GeneralTemplateQuestion::YES_NO,
        GeneralTemplateQuestion::YES_NO_TEXTBOX,
        GeneralTemplateQuestion::TEXTBOX,
        GeneralTemplateQuestion::FILE_UPLOAD,
        GeneralTemplateQuestion::HTML_EDITOR,
        // GeneralTemplateQuestion::IMAGE,
        // GeneralTemplateQuestion::SELECT,
        // GeneralTemplateQuestion::MULTI_SELECT,
    ],

    'type_with_answer_validation' => [
        GeneralTemplateQuestion::YES_NO => [
            "required" => [
                "data._index" => "required",
                "data._index.value" => "in:yes,no|required",
            ],
            "normal" => [
                "data._index" => "nullable",
                "data._index.value" => "in:yes,no,null|nullable",
            ]
        ],
        GeneralTemplateQuestion::YES_NO_TEXTBOX => [
            "required" => [
                "data._index" => "required",
                "data._index.value" => "in:yes,no|required",
                "data._index.text" => "required_if:data._index.value,yes|max:1000",
            ],
            "normal" => [
                "data._index" => "required",
                "data._index.value" => "in:yes,no,null|nullable",
                "data._index.text" => "nullable|max:1000",
            ]
        ],
        GeneralTemplateQuestion::TEXTBOX => [
            "required" => [
                "data._index" => "max:1000|required",
            ],
            "normal" => [
                "data._index" => "max:1000|nullable",
            ]
        ],
        GeneralTemplateQuestion::IMAGE => [
            "required" => [
                "data._index" => "image|required",
            ],
            "normal" => [
                "data._index" => "image|nullable",
            ]
        ],
        GeneralTemplateQuestion::SELECT => [
            "required" => [
                "data._index" => "in:_options|required",
            ],
            "normal" => [
                "data._index" => "in:_options|nullable",
            ],
        ],
        GeneralTemplateQuestion::MULTI_SELECT => [
            "required" => [
                "data._index" => "array|min:1|required",
                "data._index.*" => "in:_options|distint|required"
            ],
            "normal" => [
                "data._index" => "array",
                "data._index.*" => "in:_options|distint"
            ]
        ],
    ],

    'type_with_options' => [
        GeneralTemplateQuestion::IMAGE => [
            "create" => [
                'required',
                'image',
            ],
            "update" => [
                'image',
            ],
        ],
        GeneralTemplateQuestion::SELECT => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
        GeneralTemplateQuestion::MULTI_SELECT => [
            "create" => [
                'required',
                'min:2',
                'array',
            ],
            "update" => [
                'required',
                'min:2',
                'array',
            ],
        ],
    ],

    'type_with_image' => [
        GeneralTemplateQuestion::IMAGE,
    ],
];
