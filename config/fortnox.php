<?php

return [

    /*
    |--------------------------------------------------------------------------
    | FORTNOX_BASE_URL
    |--------------------------------------------------------------------------
    |
    | base url for fortnox
    |
    */
    'base_url' => secret_env('FORTNOX_BASE_URL', 'https://api.fortnox.se'),

    /*
    |--------------------------------------------------------------------------
    | FORTNOX_OAUTH_URL
    |--------------------------------------------------------------------------
    |
    | oauth url for fortnox
    |
    */
    'oauth_url' => secret_env('FORTNOX_OAUTH_URL', 'https://apps.fortnox.se'),

    /*
    |--------------------------------------------------------------------------
    | FORTNOX_CLIENT_ID
    |--------------------------------------------------------------------------
    |
    | client id for fortnox
    |
    */
    'client_id' => secret_env('FORTNOX_CLIENT_ID', ''),

    /*
    |--------------------------------------------------------------------------
    | FORTNOX_CLIENT_SECRET
    |--------------------------------------------------------------------------
    |
    | client secret for fortnox
    |
    */
    'client_secret' => secret_env('FORTNOX_CLIENT_SECRET', ''),

    /*
    |--------------------------------------------------------------------------
    | FORTNOX_REDIRECT_URL
    |--------------------------------------------------------------------------
    |
    | redirect url for fortnox
    |
    */
    'redirect_url' => secret_env('FORTNOX_REDIRECT_URL', ''),

];
