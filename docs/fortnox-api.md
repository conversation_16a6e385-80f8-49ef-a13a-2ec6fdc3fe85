# Fortnox API Integration (v3)

This document describes how to use the Fortnox API integration in the Meridiq application.

## Overview

The Fortnox integration allows companies to:
- Connect their Fortnox account via OAuth (company-level authentication)
- Automatically create Fortnox customers from clients
- Generate invoices in Fortnox for clients
- Manage authentication and tokens

## Authentication Flow

### 1. Connect to Fortnox

**Endpoint:** `GET /api/fortnox/auth/connect`

**Parameters:**
- `scopes` (optional): Array of scopes to request (default: `['customer', 'invoice']`)

**Response:**
```json
{
    "status": "1",
    "data": {
        "authorization_url": "https://apps.fortnox.se/oauth-v1/auth?...",
        "message": "Please visit the authorization URL to connect your Fortnox account"
    }
}
```

### 2. Handle OAuth Callback

**Endpoint:** `GET /api/fortnox/auth/callback`

**Parameters:**
- `code`: Authorization code from Fortnox
- `state`: State parameter for security validation

**Response:**
```json
{
    "status": "1",
    "data": {
        "message": "Successfully connected to Fortnox",
        "connected": true
    }
}
```

### 3. Check Connection Status

**Endpoint:** `GET /api/fortnox/auth/status`

**Response:**
```json
{
    "status": "1",
    "data": {
        "connected": true,
        "token_expired": false,
        "expires_at": "2024-12-31T23:59:59.000Z"
    }
}
```

### 4. Disconnect from Fortnox

**Endpoint:** `DELETE /api/fortnox/auth/disconnect`

**Response:**
```json
{
    "status": "1",
    "data": {
        "message": "Successfully disconnected from Fortnox",
        "connected": false
    }
}
```

## Invoice Management

### 1. Create Invoice

**Endpoint:** `POST /api/fortnox/clients/{client}/invoices`

**Request Body:**
```json
{
    "due_date": "2024-12-31",
    "items": [
        {
            "Description": "Consultation Service",
            "Price": 500.00,
            "DeliveredQuantity": 1,
            "Unit": "st",
            "VAT": 25,
            "HouseWork": false
        },
        {
            "Description": "Treatment Package",
            "Price": 1200.00,
            "DeliveredQuantity": 1
        }
    ],
    "our_reference": "REF-001",
    "your_reference": "CLIENT-REF",
    "comments": "Thank you for your business!"
}
```

**Response:**
```json
{
    "status": "1",
    "data": {
        "message": "Invoice created successfully",
        "invoice": {
            "document_number": "12345",
            "customer_number": "67890",
            "customer_name": "John Doe",
            "invoice_date": "2024-01-15",
            "due_date": "2024-12-31",
            "currency": "SEK",
            "total": 1700.00,
            "total_vat": 425.00,
            "balance": 1700.00,
            "ocr": "123456789",
            "url": "https://...",
            "booked": false,
            "sent": false,
            "cancelled": false
        }
    }
}
```

### 2. List Invoices

**Endpoint:** `GET /api/fortnox/clients/{client}/invoices`

**Response:**
```json
{
    "status": "1",
    "data": {
        "message": "Invoices retrieved successfully",
        "customer_id": "67890",
        "invoices": []
    }
}
```

### 3. Get Invoice Details

**Endpoint:** `GET /api/fortnox/clients/{client}/invoices/{documentNumber}`

**Response:**
```json
{
    "status": "1",
    "data": {
        "message": "Invoice retrieved successfully",
        "document_number": "12345"
    }
}
```

## Automatic Customer Creation

When creating the first invoice for a client, the system will automatically:

1. Check if the company is connected to Fortnox
2. Check if the client has a Fortnox customer ID
3. If not, create a new customer in Fortnox using client data:
   - Name (first_name + last_name)
   - Email
   - Phone
   - Address information
   - Default currency (SEK)
4. Store the customer ID in the client record
5. Proceed with invoice creation

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Authentication required or invalid
- `403 Forbidden`: Access denied to the client
- `409 Conflict`: Customer already exists
- `500 Internal Server Error`: Server-side error

## Security

- All endpoints require API authentication (`auth:sanctum` middleware)
- Company access is authorized using Laravel policies
- Client access is authorized using Laravel policies
- OAuth state parameters are validated for security
- Access tokens are automatically refreshed when expired

## Invoice Item Structure

Each invoice item must include:

- `Description` (required): Item description
- `Price` (required): Item price (numeric)
- `DeliveredQuantity` (required): Quantity delivered (numeric, > 0)
- `Unit` (optional): Unit of measurement (default: "st")
- `VAT` (optional): VAT percentage (default: 25)
- `HouseWork` (optional): House work flag (default: false)

## Usage Example

```bash
# 1. Connect company to Fortnox
GET /api/fortnox/auth/connect

# 2. Handle callback (after user authorization)
GET /api/fortnox/auth/callback?code=ABC&state=XYZ

# 3. Create invoice for a client (automatically creates customer if needed)
POST /api/fortnox/clients/123/invoices
{
    "due_date": "2024-12-31",
    "items": [
        {
            "Description": "Consultation",
            "Price": 500.00,
            "DeliveredQuantity": 1
        }
    ]
}
```

## Notes

- **Company-level authentication**: Fortnox authentication is stored at the company level, not per client
- **Automatic customer creation**: Clients are automatically created as Fortnox customers when first invoice is generated
- The integration uses the client's primary address for customer creation
- Default currency is set to SEK (Swedish Krona)
- Invoice delivery type defaults to EMAIL
- Access tokens are automatically refreshed when needed
- All operations are logged for debugging and audit purposes
