{"conventionalCommits.scopes": ["USER ACTIVATION", "PLAN DOWNGRADE", "GENERAL NOTES", "NEW REGISTRATION", "MINOR UPDATE", "MAILS", "TEMP", "ACCESS CONTROL", "SLOT AUTO AVAILABLE", "MAILS", "PRIVATE TIME"], "cSpell.words": ["<PERSON>id"], "editor.defaultFormatter": "bmewburn.vscode-intelephense-client", "php-cs-fixer.allowRisky": true, "php-cs-fixer.autoFixByBracket": true, "php-cs-fixer.autoFixBySemicolon": true, "php-cs-fixer.exclude": ["*.blade.php"], "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[php]": {"editor.defaultFormatter": "bmewburn.vscode-intelephense-client"}, "[edge]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true}