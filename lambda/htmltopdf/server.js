import chromium from "@sparticuz/chromium";
import { Edge } from 'edge.js';
import express from 'express';
import puppeteer from "puppeteer-core";
import { getIn } from "./utils/getIn.mjs";
import { getLang } from "./utils/getLang.mjs";
import { getPath } from "./utils/getPath.mjs";

const edge = Edge.create();
edge.mount(new URL('./views', import.meta.url));

const app = express();
const port = 8080;

app.get('/generate-pdf', async (req, res) => {
    let browser = null;

    const view = req.query.view;

    const payload = req.query.payload;
    console.log(payload);

    const lang = req.query.lang;

    const footer = req.query.footer;

    const strings = getLang(lang);

    const t = (key) => getIn(strings, key, '');

    edge.global('t', t);

    try {
        await chromium.font(getPath('font', 'Inter.ttf'));

        browser = await puppeteer.launch({
            args: [
                ...chromium.args,
                '--font-render-hinting=none',
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--disable-setuid-sandbox',
                '--no-first-run',
                '--no-sandbox',
                '--no-zygote',
                '--deterministic-fetch',
                '--disable-features=IsolateOrigins',
                '--disable-site-isolation-trials',
            ],
            defaultViewport: chromium.defaultViewport,
            executablePath: await chromium.executablePath(),
            headless: chromium.headless,
            ignoreHTTPSErrors: true,
        });

        let page = await browser.newPage();

        const html = await edge.render(view, payload)

        await page.setContent(html, {
            waitUntil: 'load'
        });
        await page.addStyleTag({ path: 'css/global.min.css' });

        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: !!footer,
            footerTemplate: footer,
            headerTemplate: `-`,
        });

        console.log(pdf.byteLength / 1024, "KB");

        // Set headers to return the PDF in response
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'attachment; filename=custom.pdf');
        res.send(pdf);
    } catch (error) {
        console.log("error: ", error);

        res.json({
            statusCode: 500,
            body: JSON.stringify({
                "status": 0,
                'message': error?.message ?? "Server Error",
            }),
        });
    } finally {
        if (browser !== null) {
            try {
                await browser.close();
            } catch (error) {
            }
        }
    }
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
});
