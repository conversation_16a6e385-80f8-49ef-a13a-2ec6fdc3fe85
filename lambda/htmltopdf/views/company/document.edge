<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        @page {
            margin-top: 6mm;
            margin-bottom: 10mm;
            margin-left: 15mm;
            margin-right: 15mm;
        }
    </style>
</head>

<body>
    @if(watermark)
    <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-50deg);
        font-size: 56px;
        color: rgba(0, 0, 0, 0.1);
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
        width: 100%;
        text-align: center;">
        {{ watermark }}
    </div>
    @endif

    <h2 class="text-2xl font-semibold py-5">{{ title }} - {{ created_at ?? '' }} (v{{ version }})</h2>

    <p class="text-lg font-medium">{{ t('company.document.process') }}: {{ process_name }}</p>

    <div class="px-8">
        <ol class="list-decimal list-outside">
            @each((question, index) in questions)
            <li class="py-3">
                <p><b>{{ question.question ?? '' }}</b></p>

                @if(question.type == "yes_no")
                <p>{{ datas?.[index]?.['value'] && datas?.[index]?.['value'] != 'null' ? t(`questionary.custom.${datas?.[index]?.['value']}`) : "" }}</p>
                @endif

                @if(question.type == "yes_no_textbox")
                <p>{{ datas?.[index]?.['value'] && datas?.[index]?.['value'] != 'null' ? t(`questionary.custom.${datas?.[index]?.['value']}`) : "" }}</p>

                @if(datas?.[index]?.['text'] && datas?.[index]?.['text'] != 'null')
                <p>{{ t('questionary.custom.more_info') }} : {{ datas?.[index]?.['text'] ?? ''}}</p>
                @endif
                @endif

                @if(question.type == "textbox")
                @if(datas?.[index] && datas?.[index] != 'null')
                <p>{{ datas?.[index] ?? '' }}</p>
                @endif
                @endif

                @if(question.type == "html_editor")
                @if(datas?.[index]?.['text'] && datas?.[index]?.['text'] != 'null')
                <div class="prose">{{{ datas?.[index]?.['text'] ?? '' }}}</div>
                @endif
                @endif

                @if(question.type == "select")
                <p>{{ datas?.[index] ?? '' }}</p>
                @endif

                @if(question.type == "multi_select" && Array.isArray(datas?.[index]))
                <p>{{ datas?.[index]?.join(', ') ?? '' }}</p>
                @endif

                @if(question.type == "image")
                <img class="w-1/2 break-inside-avoid" src="{{ datas?.[index] ?? '' }}" alt="">
                @endif

                @if(question.type == "file_upload")
                @each((file_data, index) in (datas?.[index]?.files ?? []))
                <p>{{ file_data.file_name ?? '' }}</p>
                @endeach
                @endif
            </li>
            @endeach
        </ol>
    </div>

    <div class="px-8 py-4">
        @if (sign && signed_by)
        <p><b>{{ signed_by?.first_name ?? '' }} {{ signed_by?.last_name ?? '' }}</b></p><br />
        <img class="break-inside-avoid" width="25%" src="{{ sign }}" /><br />
        <p><b>{{ signed_at }}</b></p>
        @else
        <b>Not Signed</b>
        @endif
    </div>
</body>

</html>