<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Medical Device</title>
    <style>
        @page {
            margin-top: 6mm;
            margin-bottom: 10mm;
            margin-left: 15mm;
            margin-right: 15mm;
        }
    </style>
</head>

<body>
    @if(watermark)
    <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-50deg);
        font-size: 56px;
        color: rgba(0, 0, 0, 0.1);
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
        width: 100%;
        text-align: center;">
        {{ watermark }}
    </div>
    @endif

    <table class="w-full table-fixed border-collapse">
        <tr>
            <td>
                <h2 class="text-2xl font-semibold underline">{{ t('company.medical_device.medical_device') }}</h2>
                <div class="h-5 w-full"></div>
            </td>
        </tr>
    </table>

    <div class="grid grid-cols-3 gap-x-4 gap-y-6">
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.DeviceName') }}</h4>
            <p>{{ product_name ?? '' }}</p>
        </div>
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.Model') }}</h4>
            <p>{{ model ?? '' }}</p>
        </div>
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.Brand') }}</h4>
            <p>{{ brand ?? '' }}</p>
        </div>
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.SerialNumber') }}</h4>
            <p>{{ serial_number ?? '' }}</p>
        </div>
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.Supplier') }}</h4>
            <p>{{ supplier ?? '' }}</p>
        </div>
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.ComplianceDeclared') }}</h4>
            <p>{{ compliance_declared ? t('company.medical_device.Yes') : t('company.medical_device.No') }}</p>
        </div>
        <div class="flex flex-col gap-4">
            <h4 class="text-left font-semibold">{{ t('company.medical_device.AddedOn') }}</h4>
            <p>{{ created_at ?? '' }}</p>
        </div>
    </div>
</body>

</html>