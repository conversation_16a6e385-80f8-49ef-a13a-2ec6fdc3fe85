<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DPA Agreement</title>
  <style>
    @page { margin: 20mm; }
    body {
      font-family: sans-serif;
      color: #000;
      font-size: 15px;
      line-height: 1.7;
    }
    h1 {
      color: #4B0082;
      font-weight: bold;
      font-size: 22px;
      margin-top: 40px;
      margin-bottom: 5px;
    }
    h2 {
      color: #4B0082;
      font-weight: bold;
      font-size: 17px;
      margin-top: 30px;
    }
    .section {
      margin-bottom: 25px;
    }
    .title-line {
      border: none;
      border-top: 1px solid #999;
      margin: 10px 0 30px;
    }
    .signature {
      margin-top: 50px;
    }
  </style>
</head>
<body>

  <h1>DPA AGREEMENT</h1>

  <div class="section">
    <p><strong>Between:</strong></p>
    <p><strong>Supplier:</strong> MERIDIQ AB<br>
       Address: Fornbyvägen 37, 174 41 Sundbyberg, Sweden<br>
       Email: <EMAIL><br>
       (hereinafter referred to as "the Supplier")</p>

    <p><strong>and</strong></p>
    <p><strong>Customer:</strong> {{ data.company_name }}<br>
       Address: {{ data.company_address }}<br>
       Email: {{ data.company_email }}<br>
       (hereinafter referred to as "the Data Controller")</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>1. Definitions and Application</h2>
    <p><strong>1.1 Personal Data:</strong> Any information related to an identified or identifiable natural person.</p>
    <p><strong>1.2 Data Processing:</strong> Any operation performed on personal data, whether automated or not.</p>
    <p><strong>1.3 Personal Data Breach:</strong> A security incident leading to unauthorized access, loss, or alteration of personal data, impacting their confidentiality, integrity, or availability.</p>
    <p>This agreement is an addendum to the primary service agreement (e.g., SAAS services) between the parties and governs the processing of personal data performed by the Data Processor on behalf of the Data Controller.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>2. Processing of Personal Data</h2>
    <p><strong>2.1 Purpose of Processing:</strong> The Data Processor processes personal data on behalf of the Data Controller solely to enable the use of the IT platform specified in the main agreement.</p>
    <p><strong>2.2 Type of Personal Data:</strong> The personal data processed may include, but is not limited to, names, contact information, user data, account information, and other information related to the registered user.</p>
    <p><strong>2.3 Categories of Data Subjects:</strong> Processing may involve personal data of users or customers registered or managed by the Data Controller via the platform, including employees, customers, or suppliers.</p>
    <p><strong>2.4 Processing Measures:</strong> The Data Processor shall implement appropriate technical and organizational measures to ensure the secure processing of personal data, including preventing unauthorized access, loss, or alteration.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>3. Processing Instructions</h2>
    <p><strong>3.1 Processing Only as Instructed:</strong> The Data Processor shall only process personal data according to the instructions provided by the Data Controller and may not use the personal data for its purposes. If the Data Processor believes any instruction violates applicable data protection laws, it shall immediately notify the Data Controller.</p>
    <p><strong>3.2 Additional Instructions:</strong> If the Data Processor needs to process personal data in ways not   covered by this agreement, it must obtain written instructions from the Data Controller.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>4. Subprocessors</h2>
    <p><strong>4.1 Approval of Subprocessors:</strong> The Data Processor may not engage any subprocessor for personal data processing without prior written approval from the Data Controller.</p>
    <p><strong>4.2 Responsibility for Subprocessors:</strong> If the Data Processor engages a subprocessor, it must ensure that the same data protection terms apply in the subprocessor agreement, ensuring compliance with GDPR requirements.</p>
    <p><strong>4.3 List of Subprocessors:</strong> The Data Processor shall maintain an updated list of all subprocessors used for personal data processing. The Data Controller has the right to request a copy of this list.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>5. Security</h2>
    <p><strong>5.1 Security Measures:</strong> The Data Processor shall take appropriate technical and organizational measures to protect personal data, including but not limited to encryption, pseudonymization, access controls, and backups.</p>
    <p><strong>5.2 Security Incidents:</strong> In the event of a personal data breach, the Data Processor shall immediately inform the Data Controller and take all necessary actions to manage and mitigate the consequences of the incident. The Data Processor shall cooperate with the Data Controller to fulfill GDPR reporting obligations.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>6. Rights of Data Subjects</h2>
    <p><strong>6.1 Access, Correction, and Deletion:</strong> The Data Processor shall assist the Data Controller in fulfilling obligations to provide data subjects access to their personal data and the right to correct, delete, or restrict processing upon request.</p>
    <p><strong>6.2 Processing on Behalf of the Controller:</strong> If a data subject requests deletion or correction of personal data, the Data Processor shall assist the Data Controller in fulfilling such requests in accordance with the Data Controller’s instructions.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>7. Transfer of Personal Data</h2>
    <p><strong>7.1 International Transfers:</strong> The Data Processor may not transfer personal data outside the EU/EEA without taking appropriate measures to ensure that such transfers comply with GDPR requirements, e.g., using standard contractual clauses or Privacy Shield certification.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>8. Audits and Inspections</h2>
    <p><strong>8.1 Right to Audit:</strong> The Data Controller has the right to request audits of the Data Processor to verify compliance with this agreement and applicable data protection laws.</p>
    <p><strong>8.2 Documentation:</strong> The Data Processor shall provide all necessary documentation to demonstrate compliance with this DPA and GDPR.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>9. Term and Termination</h2>
    <p><strong>9.1 Agreement Validity:</strong> This agreement enters into force on the date both parties sign it and remains valid as long as the Data Processor processes personal data on behalf of the Data Controller.</p>
    <p><strong>9.2 Termination:</strong> Upon termination of the main agreement, this DPA shall also terminate. The Data Processor must immediately cease processing personal data and return or delete all personal data per the Data Controller’s instructions.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>10. Governing Law and Dispute Resolution</h2>
    <p><strong>10.1 Governing Law:</strong> This agreement is governed by Swedish law and interpreted in accordance with GDPR and other applicable data protection laws.</p>
    <p><strong>10.2 Disputes:</strong> Any disputes arising from this agreement shall be resolved in Swedish courts under Swedish law.</p>
  </div>
  <hr class="title-line">
  <div class="signature">
    <p><strong>Signatures</strong></p>
    <br>
    <p><strong>For the Data Processor:</strong></p>
    <p>Name: Rickard Nurlin<br>Date: {{ data.date_signed }}</p>

    <p><strong>For the Data Controller:</strong></p>
    <p>Name: {{ data.representative_name }}<br>Date: {{ data.date_signed }}</p>
  </div>
  <hr class="title-line">
</body>
</html>
