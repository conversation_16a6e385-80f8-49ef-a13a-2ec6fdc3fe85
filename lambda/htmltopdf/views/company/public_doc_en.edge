<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PUBLIC AGREEMENT</title>
  <style>
    @page { margin: 20mm; }
    body {
      font-family: sans-serif;
      color: #000;
      font-size: 15px;
      line-height: 1.7;
    }
    h1 {
      color: #4B0082;
      font-weight: bold;
      font-size: 22px;
      margin-top: 40px;
      margin-bottom: 5px;
    }
    h2 {
      color: #4B0082;
      font-weight: bold;
      font-size: 17px;
      margin-top: 30px;
    }
    .section {
      margin-bottom: 25px;
    }
    .title-line {
      border: none;
      border-top: 1px solid #999;
      margin: 10px 0 30px;
    }
    .signature {
      margin-top: 50px;
    }
  </style>
</head>
<body>

  <h1>PUBLIC AGREEMENT</h1>

  <div class="section">
    <p><strong>Between:</strong></p>
    <p><strong>Supplier:</strong> MERIDIQ AB<br>
       Address: Fornbyvägen 37, 174 41 Sundbyberg, Sweden<br>
       Email: <EMAIL><br>
       (hereinafter referred to as the "Supplier")</p>

    <p><strong>And:</strong></p>
    <p><strong>Customer:</strong> {{ data.company_name }}<br>
       Address: {{ data.company_address }}<br>
       Email: {{ data.company_email }}<br>
       (hereinafter referred to as the "Customer")</p>
  </div>
    <hr class="title-line">
  <div class="section">
    <h2>1. Purpose and Scope of the Agreement</h2>
    <p><strong>1.1</strong> The Supplier hereby agrees to provide the IT platform MERIDIQ (hereinafter referred to as the "Platform") to the Customer under the terms specified in this agreement. The Platform is a cloud-based service that enables the Customer to {{ data.platform_description }}.</p>
    <p><strong>1.2</strong> The Platform is provided as an internet-based service under a Software as a Service (SAAS) model, granting the Customer the right to use the Platform's features in accordance with the agreement. </p>
  </div>
<hr class="title-line">
  <div class="section">
    <h2>2. License and Usage</h2>
    <p><strong>2.1</strong> The Supplier grants the Customer a non-exclusive, non-transferable, and limited right to use the Platform during the agreement period for the purposes stated in section 1.1. The Customer may only use the Platform for internal purposes and may not sublicense, sell, transfer, or otherwise distribute the Platform or its functionalities</p>
    <p><strong>2.2</strong> The Customer agrees not to:</p>
    <ul class="list-disc list-inside">
      <li>Modify, decompile, or otherwise attempt to extract the Platform's source code</li>
      <li>Use the Platform in a manner that could damage, overload, or disrupt the service.</li>
    </ul>
    <p><strong>2.3</strong> The Customer may authorize access only to users within their organization who need the Platform for their work duties. The number of users may be regulated under a separate agreement (e.g., per license, user, or volume).</p>
  </div>
  <hr class="title-line">

  <div class="section">
    <h2>3. Provision of Services</h2>
    <p><strong>3.1</strong> The Supplier will make reasonable efforts to ensure that the Platform is available to the Customer but cannot guarantee uninterrupted availability.</p>
    <p><strong>3.2</strong> The Supplier is responsible for providing technical support and maintenance to the best of its ability.</p>
    <p><strong>3.3</strong> The Supplier reserves the right to update and improve the Platform during the term of the agreement, including changes to enhance security and functionality, provided such changes do not materially affect the Customer's use of the Platform.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>4. Payment and Fees</h2>
    <p><strong>4.1</strong> The Customer shall pay for the use of the Platform in accordance with the prices and terms listed on the Supplier's online price list. Prices may be based on the number of users, functionalities, or other agreed factors.</p>
    <p><strong>4.2</strong> Payments shall be made monthly or annually in advance.</p>
    <p><strong>4.3</strong> In case of delayed payment, the Supplier has the right to charge interest according to the applicable interest law and may suspend the Customer's access to the Platform.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>5. Confidentiality and Data Protection</h2>
    <p><strong>5.1</strong> Both parties agree to keep all confidential information obtained under this agreement secret and not share it with third parties without prior written consent.</p>
    <p><strong>5.2</strong> The Supplier shall implement appropriate security measures to protect personal data processed through the Platform.</p>
    <p><strong>5.3</strong> If necessary to meet data protection requirements, the parties shall establish a separate Data Processing Agreement (DPA) for the personal data processing conducted within the Platform's use.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>6. Liability and Warranty</h2>
    <p><strong>6.1</strong> The Supplier guarantees that the Platform will operate and be available to the Customer. If the service does not function as agreed, the Supplier will make reasonable efforts to resolve the issue as quickly as possible.</p>
    <p><strong>6.2</strong> The Supplier is not liable for indirect damages, lost profits, or other consequential damages arising from the use of the Platform.</p>
    <p><strong>6.3</strong> The Customer is responsible for not using the Platform in a way that could harm other users' data or services.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>7. Term and Termination</h2>
    <p><strong>7.1</strong> This agreement is effective from the license start date and continues until the Customer terminates their license.</p>
    <p><strong>7.2</strong> The Supplier has the right to terminate the agreement immediately if the Customer fails to meet payment obligations or otherwise breaches the agreement.</p>
    <p><strong>7.3</strong> Upon termination, the Customer must cease using the Platform, and all licenses granted under this agreement will be revoked.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>8. Dispute Resolution and Governing Law</h2>
    <p><strong>8.1</strong> Any disputes arising in connection with this agreement shall first be resolved through negotiations between the parties.</p>
    <p><strong>8.2</strong> If a dispute cannot be resolved through negotiations, it shall be settled by Swedish courts under Swedish law.</p>
  </div>
  <hr class="title-line">
  <div class="signature">
    <p><strong>Signatures</strong></p>
    <br>
    <p><strong>For the Supplier:</strong></p>
    <p>Name: Rickard Nurlin<br>Date: {{ data.date_signed }}</p>

    <p><strong>For the Customer:</strong></p>
    <p>Name: {{ data.superuser_name }}<br>Date: {{ data.date_signed }}</p>
  </div>
  <hr class="title-line">
</body>
</html>
