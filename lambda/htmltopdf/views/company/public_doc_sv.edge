<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PUB-AVTAL</title>
  <style>
    @page { margin: 20mm; }
    body {
      font-family: sans-serif;
      color: #000;
      font-size: 15px;
      line-height: 1.7;
    }
    h1 {
      color: #4B0082;
      font-weight: bold;
      font-size: 22px;
      margin-top: 40px;
      margin-bottom: 5px;
    }
    h2 {
      color: #4B0082;
      font-weight: bold;
      font-size: 17px;
      margin-top: 30px;
    }
    .section {
      margin-bottom: 25px;
    }
    .title-line {
      border: none;
      border-top: 1px solid #999;
      margin: 10px 0 30px;
    }
    .signature {
      margin-top: 50px;
    }
  </style>
</head>
<body>

  <h1>PUB-AVTAL</h1>

  <div class="section">
    <p><strong>Mellan:</strong></p>
    <p><strong>Leverantör:</strong> MERIDIQ AB<br>
       Adress: Fornbyvägen 37, 174 41 Sundbyberg, Sweden<br>
       E-post: <EMAIL><br>
       (hädanefter "Leverantören")</p>

    <p><strong>och</strong></p>
    <p><strong>Kund:</strong> {{ data.company_name }}<br>
        Adress: {{ data.company_address }}<br>
       E-post: {{ data.company_email }}<br>
       (hädanefter "Kunden")</p>
  </div>
    <hr class="title-line">
  <div class="section">
    <h2>1. Avtalets syfte och omfattning</h2>
    <p><strong>1.1</strong> Leverantören förbinder sig härmed att tillhandahålla IT-plattformen MERIDIQ, hädanefter kallad "Plattformen", till Kunden under de villkor som anges i detta avtal. Plattformen är en molnbaserad tjänst som gör det möjligt för Kunden att {{ data.platform_description }}.</p>
    <p><strong>1.2</strong> Plattformen tillhandahålls som en tjänst via internet enligt en Software as a Service (SAAS)-modell och ger Kunden rätt att använda funktionerna i Plattformen i enlighet med avtalet.</p>
  </div>
<hr class="title-line">
  <div class="section">
    <h2>2. Licens och användning</h2>
    <p><strong>2.1</strong> Leverantören beviljar Kunden en icke-exklusiv, icke-överlåtbar och begränsad rätt att använda Plattformen under avtalsperioden för de syften som anges i avtalets 1.1. Kunden får endast använda Plattformen för internt bruk och får inte vidarelicensiera, sälja, överlåta eller på annat sätt distribuera Plattformen eller dess funktioner.</p>
    <p><strong>2.2</strong> Kunden förbinder sig att inte:</p>
    <ul class="list-disc list-inside">
      <li>Modifiera, dekompilera eller på annat sätt försöka extrahera källkoden för Plattformen.</li>
      <li>Använda Plattformen på ett sätt som kan skada, överbelasta eller störa tjänsten.</li>
    </ul>
    <p><strong>2.3</strong> Kunden får endast ge behörighet till användare inom sin organisation som behöver tillgång till Plattformen för att utföra sina arbetsuppgifter. Antalet användare kan vara reglerat enligt separat överenskommelse (t.ex. per licens, användare eller volym).</p>
  </div>
  <hr class="title-line">

  <div class="section">
    <h2>3. Tjänstens tillhandahållande</h2>
    <p><strong>3.1</strong> Leverantören ska med bäst möjliga ansträngning säkerställa att Plattformen är tillgänglig för Kunden, men kan inte garantera att den alltid är tillgänglig utan avbrott.</p>
    <p><strong>3.2</strong> Leverantören ansvarar för att säkerställa att all teknisk support och underhåll tillhandahålls med bästa möjliga ansträngning.</p>
    <p><strong>3.3</strong> Leverantören förbehåller sig rätten att uppdatera och förbättra Plattformen under avtalets löptid, inklusive att genomföra förändringar för att förbättra säkerheten och funktionaliteten, förutsatt att sådana förändringar inte väsentligt påverkar Kundens användning av Plattformen.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>4. Betalning och avgifter</h2>
    <p><strong>4.1</strong> Kunden ska betala för användningen av Plattformen i enlighet med de priser och villkor som finns på Leverantörens prislista på nätet. Priser kan baseras på antalet användare, funktionalitet eller annan avtalad faktorer.</p>
    <p><strong>4.2</strong> Betalningar ska ske per månad eller årligen i förskott.</p>
    <p><strong>4.3</strong> Vid försenad betalning äger Leverantören rätt att ta ut dröjsmålsränta enligt gällande räntelag, samt eventuellt stänga av Kundens åtkomst till Plattformen.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>5. Sekretess och dataskydd</h2>
    <p><strong>5.1</strong> Båda parter förbinder sig att hålla all konfidentiell information som erhålls genom avtalet hemlig och inte dela denna med tredje part utan föregående skriftligt godkännande.</p>
    <p><strong>5.2</strong> Leverantören ska vidta lämpliga säkerhetsåtgärder för att skydda de personuppgifter som behandlas genom Plattformen.</p>
    <p><strong>5.3</strong> Parterna ska upprätta ett separat DPA (Data Processing Agreement) om detta är nödvändigt för att uppfylla dataskyddskraven för den personuppgiftsbehandling som sker inom ramen för Plattformens användning.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>6. Ansvar och garanti</h2>
    <p><strong>6.1</strong> Leverantören garanterar att Plattformen kommer att fungera och vara tillgänglig för Kunden. Om tjänsten inte fungerar som överenskommet, ska Leverantören göra sitt bästa för att rätta till problemet så snabbt som möjligt.</p>
    <p><strong>6.2</strong> Leverantören är inte ansvarig för indirekta skador, förlorad vinst eller andra följdskador som kan uppstå till följd av användning av Plattformen.</p>
    <p><strong>6.3</strong> Kunden är ansvarig för att inte använda Plattformen på ett sätt som kan orsaka skador på andra användares data eller tjänster.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>7. Avtalstid och uppsägning</h2>
    <p><strong>7.1</strong> Detta avtal gäller från och med startdatum av licensen och löper vidare tills Kunden avslutar sin licens.</p>
    <p><strong>7.2</strong> Leverantören äger rätt att säga upp avtalet om Kunden inte uppfyller sina betalningsskyldigheter eller på annat sätt bryter mot avtalet, med omedelbar verkan.</p>
    <p><strong>7.3</strong> Vid uppsägning ska Kunden sluta använda Plattformen och alla licenser som beviljats enligt detta avtal upphör.</p>
  </div>
  <hr class="title-line">
  <div class="section">
    <h2>8. Tvister och tillämplig lag</h2>
    <p><strong>8.1</strong> Eventuella tvister som uppstår i samband med detta avtal ska först lösas genom förhandlingar mellan parterna.</p>
    <p><strong>8.2</strong> Om tvist inte kan lösas genom förhandlingar, ska tvisten avgöras av svensk domstol enligt svensk lag.</p>
  </div>
  <hr class="title-line">
  <div class="signature">
    <p><strong>Underskrifter</strong></p>
    <br>
    <p><strong>För Leverantören:</strong></p>
    <p>Namn: Rickard Nurlin<br>Datum: {{ data.date_signed }}</p>

    <p><strong>För Kunden:</strong></p>
    <p>Namn: {{ data.superuser_name }}<br>Datum: {{ data.date_signed }}</p>
  </div>
  <hr class="title-line">
</body>
</html>
