<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style>
        @page {
            margin: 15mm;
        }
    </style>
</head>

<body>
    @include('client/partial/info')

    <h2 class="text-2xl font-semibold py-5 underline">{{ letter_of_consent.consent_title }}</h2>

    <table class="w-full table-fixed border-collapse">

        @if(letter_of_consent.is_cancelled)
        <tr style="color: red;">
            <th class="text-left py-3">{{ t('client.loc.CancelledBy') }}</th>
            <th class="text-left py-3">{{ t('client.loc.CancelledAt') }}</th>
        </tr>
        <tr style="color: red;">
            <td class="pb-2 break-words align-top">
                @if (letter_of_consent.cancelled_by)
                <p><b>{{ letter_of_consent.cancelled_by.first_name }} {{ letter_of_consent.cancelled_by.last_name }}</b></p><br />
                <p>{{ t('client.loc.CancelledNote') }} - <b>{{ letter_of_consent.cancel_note }}</b></p><br />
                @else
                No Data
                @endif
            </td>
            <td class="pb-2 break-words align-top">
                @if (letter_of_consent.cancelled_at)
                <p><b>{{ letter_of_consent.cancelled_at }}</b></p>
                @else
                No Data
                @endif
            </td>
        </tr>
        @endif
        <tr>
            <th class="text-left py-3">{{ t('client.loc.SignedBy') }}</th>
            <th class="text-left py-3">{{ t('client.loc.SignedAt') }}</th>
        </tr>
        <tr>
            <td class="pb-2 break-words align-top">
                @if (letter_of_consent.verified_signed_by)
                    <p>{{ letter_of_consent?.verified_signed_by?.name ?? '' }}</p><br />
                    @if(letter_of_consent.is_signed_by_bank_id)
                        @if(!letter_of_consent.sign)
                        <p><b>{{ letter_of_consent?.signed_by_bank_id }}<b></p>
                        <p>{{ t('client.prescription.SignedByBankId') }}</p><br/>
                        @endif
                    @endif
                    @if(letter_of_consent.sign)
                        <img class="break-inside-avoid" width="24%" src="{{ letter_of_consent.verified_sign }}" />
                    @endif
                @else
                    Not Signed
                @endif
            </td>
            <td class="pb-2 break-words align-top">
                @if (letter_of_consent.verified_signed_at)
                    <p>{{ letter_of_consent.verified_signed_at }}</p>
                @else
                    Not Signed
                @endif
            </td>
        </tr>
        <tr>
            <th class="text-left py-3">{{ t('client.loc.Date') }}</th>
            <th class="text-left py-3">{{ t('client.loc.AllowShare') }}</th>
        </tr>
        <tr>
            <td class="pb-2 break-words align-top">{{ letter_of_consent?.created_at ?? '' }}</td>
            <td class="pb-2 break-words align-top">{{ letter_of_consent.is_publish_before_after_pictures ? t('client.loc.Yes') : t('client.loc.No') }}</td>
        </tr>
    </table>

    <div class="h-5 w-full"></div>

    <p class="font-semibold pb-1">{{ t('client.loc.Letter') }}</p>
    <div class="prose whitespace-pre-wrap break-words">{{{ letter_of_consent?.letter ?? '' }}}</div>

    <div class="h-5 w-full"></div>
</body>

</html>