<?php

use App\Http\Controllers\Api\v3\FortnoxAuthController;
use App\Http\Controllers\Api\v3\FortnoxInvoiceController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Fortnox API Routes
|--------------------------------------------------------------------------
|
| Here are the routes for Fortnox integration
|
*/

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified']], function () {

    // Fortnox Authentication Routes
    Route::prefix('fortnox/auth')->group(function () {
        // Get authorization URL to redirect user to Fortnox
        Route::get('/connect', [FortnoxAuthController::class, 'connect'])
            ->name('fortnox.auth.connect');

        // Handle OAuth callback from Fortnox
        Route::get('/callback', [FortnoxAuthController::class, 'callback'])
            ->name('fortnox.auth.callback');

        // Disconnect/revoke Fortnox authentication
        Route::delete('/disconnect', [FortnoxAuthController::class, 'disconnect'])
            ->name('fortnox.auth.disconnect');

        // Check authentication status
        Route::get('/status', [FortnoxAuthController::class, 'status'])
            ->name('fortnox.auth.status');

        // Refresh access token
        Route::post('/refresh', [FortnoxAuthController::class, 'refresh'])
            ->name('fortnox.auth.refresh');
    });

    // Fortnox Invoice Routes
    Route::prefix('fortnox')->group(function () {
        // List all invoices for the company
        Route::get('/invoices', [FortnoxInvoiceController::class, 'index'])
            ->name('fortnox.invoices.index');

        // Get invoice details by FortnoxInvoice ID
        Route::get('/invoices/{fortnoxInvoice}', [FortnoxInvoiceController::class, 'show'])
            ->name('fortnox.invoices.show');

        // Download invoice PDF by FortnoxInvoice ID
        Route::get('/invoices/{fortnoxInvoice}/download', [FortnoxInvoiceController::class, 'download'])
            ->name('fortnox.invoices.download');
    });

    // Client-specific invoice creation
    Route::prefix('fortnox/clients/{client}')->group(function () {
        // Create a new invoice for a client (will create customer first if needed)
        Route::post('/invoices', [FortnoxInvoiceController::class, 'store'])
            ->name('fortnox.invoices.store');
    });
});
