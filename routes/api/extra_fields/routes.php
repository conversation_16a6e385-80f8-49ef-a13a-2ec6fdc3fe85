<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Extra Fields API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified']], function () {
  Route::group(['prefix' => 'company/extra'], function () {
    Route::get('{key}/show', 'v3\CompanyExtraFieldController@index');
    Route::post('/store', 'v3\CompanyExtraFieldController@update');
  });
});
