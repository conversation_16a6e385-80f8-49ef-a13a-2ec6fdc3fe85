<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| POS API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified', 'subscription_required:management']], function () {
    Route::group(['prefix' => 'management-devices'], function () {
        Route::get('/', 'v3\MedicalDeviceController@index');
        Route::post('/download', 'v3\MedicalDeviceController@download');
        Route::post('/store', 'v3\MedicalDeviceController@store');
        Route::post('{medical_device}/edit', 'v3\MedicalDeviceController@update');
        Route::delete('{medical_device}/destroy', 'v3\MedicalDeviceController@deleteMedicalDevice');
        Route::delete('{medical_device}/trash', 'v3\MedicalDeviceController@destroy');
        Route::get('restore', 'v3\MedicalDeviceController@restore');
        Route::get('/{medical_device}/logs', 'v3\MedicalDeviceController@logs');

        Route::get('{medical_device}/list-maintenance', 'v3\MedicalDeviceController@listMaintenance');
        Route::post('{medical_device}/create-maintenance', 'v3\MedicalDeviceController@createMaintenance');
    });

    Route::group(['prefix' => 'template-questionary'], function () {
        Route::group(['prefix' => '{template}/question'], function () {
            Route::get('/', 'v3\TemplateQuestionController@index');
            Route::post('/store', 'v3\TemplateQuestionController@store');
            Route::post('{template_question}/update', 'v3\TemplateQuestionController@update');
            Route::post('{template_question}/shift', 'v3\TemplateQuestionController@shift');
            Route::delete('{template_question}/delete', 'v3\TemplateQuestionController@destroy');
        });
    });

    Route::group(['prefix' => 'company'], function () {
        Route::group(['prefix' => 'documents'], function () {
            Route::get('/', 'v3\CompanyDocumentController@index');
            Route::post('/download', 'v3\CompanyDocumentController@download');
            Route::post('/create', 'v3\CompanyDocumentController@store');
            Route::post('/{company_document}/update', 'v3\CompanyDocumentController@update');
            Route::get('/{company_document_id}', 'v3\CompanyDocumentController@get');
            Route::post('/{company_document}/delete', 'v3\CompanyDocumentController@delete');
            Route::post('/restore', 'v3\CompanyDocumentController@restore');
            Route::get('/{company_document}/logs', 'v3\CompanyDocumentController@logs');

            Route::group(['prefix' => '{company_document}/versions'], function () {
                Route::get('/', 'v3\CompanyDocumentVersionController@index');
            });
        });
    });
});
