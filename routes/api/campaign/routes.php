<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| CAMPAIGN API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified']], function () {
    Route::group(['prefix' => 'campaign'], function () {
        // SMS Campaign
        Route::group(['prefix' => 'sms'], function () {
            Route::get('/', 'v3\CompanyCampaignSMSController@index');
            Route::get('/{companyCampaignSMS}/list', 'v3\CompanyCampaignSMSController@list');
            Route::post('store', 'v3\CompanyCampaignSMSController@store');
            Route::get('credit/required', 'v3\CompanyCampaignSMSController@creditRequired');
        });
    });
});
