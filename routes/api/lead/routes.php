<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| POS API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['middleware' => ['auth:sanctum', 'auth_blocked', 'verified']], function () {
    Route::group(['prefix' => 'leads'], function () {
        Route::group(['prefix' => 'notes'], function () {
            Route::get('/', 'v3\LeadNoteController@index');
        });

        Route::get('/', 'v3\LeadController@index');
        Route::post('/store', 'v3\LeadController@store');
        Route::get('/{lead}', 'v3\LeadController@show');
        Route::post('/{lead}/update', 'v3\LeadController@update');
        Route::delete('/{lead}', 'v3\LeadController@destroy');

        Route::group(['prefix' => '{lead}/notes'], function () {
            Route::post('/store', 'v3\LeadNoteController@store');
            Route::get('/{leadNote}', 'v3\LeadNoteController@show');
            Route::post('/{leadNote}/update', 'v3\LeadNoteController@update');
            Route::delete('/{leadNote}', 'v3\LeadNoteController@destroy');
        });
    });
});
